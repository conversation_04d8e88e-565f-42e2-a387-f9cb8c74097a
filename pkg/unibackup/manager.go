package unibackup

import (
	"context"
	"fmt"
	"path/filepath"

	"git.gobies.org/fobrain/unibackup/internal/api"
	"git.gobies.org/fobrain/unibackup/internal/config"
	"git.gobies.org/fobrain/unibackup/internal/lock"
	"git.gobies.org/fobrain/unibackup/internal/provider"
	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/internal/task"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// BackupManager 是暴露给外部使用者（如命令行工具或API服务器）的高层接口（重构版本）。
// 重构后简化了接口，使用完全重建策略，提供更安全的默认行为。
type BackupManager interface {
	// === 异步操作接口 ===

	// BackupAsync 异步备份操作，返回任务ID
	BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error)

	// RestoreAsync 异步恢复操作（使用完全重建策略），返回任务ID
	RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error)

	// BackupAllAsync 异步分组备份操作，返回任务ID
	BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error)

	// RestoreAllAsync 异步分组恢复操作（使用完全重建策略），返回任务ID
	RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error)

	// === 任务管理接口 ===

	// GetTask 获取任务详情
	GetTask(taskID string) (*types.Task, error)

	// ListTasks 列出任务
	ListTasks() ([]*types.Task, error)

	// CancelTask 取消任务
	CancelTask(taskID string) error

	// ClearOldTasks 清理旧任务
	ClearOldTasks() error

	// === 同步操作接口（简化版本） ===

	// Backup 启动一个备份操作。
	// sourceName 是用于锁定的唯一资源名，例如 "mysql-prod" 或 "es-cluster-1"。
	// 它封装了所有复杂的逻辑，如决定是全量还是增量、创建元数据、执行任务和更新状态。
	Backup(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (*types.BackupRecord, *types.BackupError)

	// Restore 启动一个恢复操作（使用完全重建策略）。
	// 重构后的行为：
	// - MySQL: 删除并重建目标数据库，然后恢复数据
	// - Elasticsearch: 删除所有现有索引，然后恢复快照
	// 这种方法确保恢复到确定的、干净的状态，避免数据冲突问题。
	Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError

	// === 系统管理接口 ===

	// Shutdown 优雅关闭
	Shutdown() error

	// UpdateConfig 动态更新配置，支持运行时配置变更
	UpdateConfig(cfg *types.Config) error

	// TestCloudStorageConnectivity 测试云存储连通性
	// 参数：
	//   ctx: 上下文，用于超时控制
	//   config: 云存储配置（可以是临时配置）
	// 返回：
	//   *types.ConnectivityTestResult: 详细的测试结果
	//   error: 测试过程中的错误
	TestCloudStorageConnectivity(ctx context.Context, config *types.CloudStorageConfig) (*types.ConnectivityTestResult, error)

	// === 备份管理接口 ===

	// === 新的统一查询接口（推荐使用）===

	// ListAllBackups 基于tasks.json的统一备份查询接口。
	// 提供高性能的内存查询，支持过滤、搜索和分页功能。
	// 相比传统接口，查询性能提升显著，支持更灵活的过滤条件。
	ListAllBackups(ctx context.Context, filter types.BackupFilter) (*types.BackupListResult, error)

	// GetBackupDetails 获取备份任务的详细信息。
	// 支持单个备份和分组备份的详情获取，分组备份会递归显示所有子任务。
	// 自动验证备份文件的存在性，标记缺失的备份文件。
	GetBackupDetails(ctx context.Context, taskID string) (*types.Task, error)

	// DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型。
	// 重要说明：
	// - 归档备份：直接删除单个备份
	// - 增量备份：删除整条增量链（保持数据完整性）
	// - 分组备份：删除所有子任务的备份数据
	// SDK调用方应该在UI层面向用户明确说明删除行为，特别是增量备份的影响。
	DeleteBackupByTaskID(ctx context.Context, taskID string) error

	// GetBackupDeletionInfo 获取备份删除的影响信息。
	// 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等。
	// 建议在执行删除前调用此方法，向用户展示删除影响并获得确认。
	GetBackupDeletionInfo(ctx context.Context, taskID string) (*types.BackupDeletionInfo, error)

	// RestoreByTaskID 基于taskID恢复备份到原始位置（不带回调）。
	// 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置。
	// 重要说明：
	// - 自动识别备份类型（归档/增量链）并构建正确的恢复链
	// - 恢复到备份时的原始数据源名称
	// - 使用完全重建策略确保数据一致性
	// 返回恢复任务ID，可用于监控恢复进度。
	RestoreByTaskID(ctx context.Context, taskID string, force bool) (string, error)

	// RestoreByTaskIDWithCallback 基于taskID恢复备份到原始位置（带回调）。
	// 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置。
	// 重要说明：
	// - 自动识别备份类型（归档/增量链）并构建正确的恢复链
	// - 恢复到备份时的原始数据源名称
	// - 使用完全重建策略确保数据一致性
	// - 自动判断任务类型并设置相应的回调：单个备份任务回调在该数据源恢复完成时触发，分组备份任务回调在整个分组恢复完成时触发
	// 返回恢复任务ID，可用于监控恢复进度。
	RestoreByTaskIDWithCallback(ctx context.Context, taskID string, force bool, callback types.RestoreSuccessCallback) (string, error)

	// === 原有接口（保留支持，但推荐使用新接口）===

	// Deprecated: 使用 ListAllBackups 替代，性能更好且支持更灵活的过滤条件
	// ListArchivalBackups 列出指定数据源的所有独立归档备份。
	ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError)

	// Deprecated: 使用 ListAllBackups 替代，性能更好且支持更灵活的过滤条件
	// ListIncrementalChains 列出指定数据源的所有受保护的增量恢复链。
	ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError)

	// Deprecated: 使用 DeleteBackupByTaskID 替代，提供更好的用户体验和错误处理
	// DeleteBackup 删除指定的备份。
	// 如果备份是独立的归档备份，则直接删除。
	// 如果备份是增量链的一部分，此操作将删除整条链以保证数据完整性。
	DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) *types.BackupError
}

// NewManager 是 UniBackup SDK 的主入口点。
// 它负责初始化所有内部组件（配置、存储、任务管理器、提供者），
// 并将它们组装成一个实现了 BackupManager 公共接口的实例。
// 这是创建SDK实例的唯一正确方式。
func NewManager(cfg *types.Config) (BackupManager, error) {
	// 1. 检查配置是否为nil
	if cfg == nil {
		return nil, fmt.Errorf("配置不能为nil")
	}

	// 2. 设置默认值并校验配置
	config.SetDefaults(cfg)
	if err := config.Validate(cfg); err != nil {
		return nil, err
	}
	logger := cfg.Logger

	// 3. 初始化存储后端
	backend, err := storage.NewBackend(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("初始化存储后端失败: %w", err)
	}

	// 4. 初始化任务管理器（只创建一次，初始providers为空）
	tasksFilePath := filepath.Join(cfg.BackupRoot, "tasks.json")
	taskManager, err := task.NewManager(cfg, nil, nil, tasksFilePath, logger, cfg.MaxConcurrentTasks, cfg.TaskRetentionDays, cfg.MaxTaskHistory)
	if err != nil {
		return nil, fmt.Errorf("初始化任务管理器失败: %w", err)
	}

	// 5. 初始化存储管理器（注入TaskManager依赖）
	storageManager := storage.NewManager(backend, cfg, logger, taskManager)

	// 6. 初始化锁管理器
	locker := lock.NewLocker()

	// 7. 创建API层管理器（初始providers为空）
	apiManager, err := api.NewManager(cfg, storageManager, taskManager, locker, make(map[types.SourceType]provider.BackupProvider))
	if err != nil {
		return nil, fmt.Errorf("初始化API管理器失败: %w", err)
	}

	// 8. 使用UpdateConfig完成完整初始化（统一的配置更新逻辑）
	// 这将创建Providers并更新所有组件，消除了重复初始化
	if err := apiManager.UpdateConfig(cfg); err != nil {
		return nil, fmt.Errorf("完成初始化失败: %w", err)
	}

	// 9. 返回BackupManager接口
	return BackupManager(apiManager), nil
}
