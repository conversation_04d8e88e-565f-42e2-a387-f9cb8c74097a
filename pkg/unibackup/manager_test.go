package unibackup

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewManager_Success 测试NewManager函数的成功场景
func TestNewManager_Success(t *testing.T) {
	if _, err := os.Stat("/bin/true"); err != nil {
		t.Skip("/bin/true not found, skip test")
	}
	tests := []struct {
		name        string
		config      *types.Config
		wantError   bool
		description string
	}{
		{
			name: "MySQL配置成功",
			config: &types.Config{
				BackupRoot: t.TempDir(),
				MySQL: &types.MySQLConfig{
					Host:   "localhost",
					Port:   3306,
					User:   "test_user",
					DBName: "test_db",
					ToolsPath: types.MySQLToolsPath{
						Mysqldump:   "/bin/true",
						Mysql:       "/bin/true",
						Mysqlbinlog: "/bin/true",
						Mysqladmin:  "/bin/true",
					},
				},
			},
			wantError:   false,
			description: "使用有效的MySQL配置应该成功创建manager",
		},
		{
			name: "Elasticsearch配置成功",
			config: &types.Config{
				BackupRoot: t.TempDir(),
				ES: &types.ESConfig{
					Addresses:        []string{"http://localhost:9200"},
					ArchivalRepoName: "test-archival",
					ManagedRepoName:  "test-managed",
					AutoCreateRepos:  true,
					RepoBasePath:     t.TempDir(),
				},
			},
			wantError:   false,
			description: "使用有效的ES配置应该成功创建manager",
		},
		{
			name: "混合配置成功",
			config: &types.Config{
				BackupRoot:         t.TempDir(),
				MaxConcurrentTasks: 3,
				TaskRetentionDays:  7,
				MaxTaskHistory:     100,
				MySQL: &types.MySQLConfig{
					Host:   "localhost",
					Port:   3306,
					User:   "test_user",
					DBName: "test_db",
					ToolsPath: types.MySQLToolsPath{
						Mysqldump:   "/bin/true",
						Mysql:       "/bin/true",
						Mysqlbinlog: "/bin/true",
						Mysqladmin:  "/bin/true",
					},
				},
				ES: &types.ESConfig{
					Addresses:        []string{"http://localhost:9200"},
					ArchivalRepoName: "test-archival",
					ManagedRepoName:  "test-managed",
					AutoCreateRepos:  true,
					RepoBasePath:     t.TempDir(),
				},
			},
			wantError:   false,
			description: "使用MySQL和ES混合配置应该成功创建manager",
		},
		{
			name: "仅基本配置成功",
			config: &types.Config{
				BackupRoot: t.TempDir(),
				MySQL: &types.MySQLConfig{
					Host:   "localhost",
					Port:   3306,
					User:   "test_user",
					DBName: "test_db",
				},
			},
			wantError:   false,
			description: "仅基本配置应该成功，默认值将被设置",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manager, err := NewManager(tt.config)

			if tt.wantError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, manager)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, manager)

				// 验证manager实现了BackupManager接口
				_, ok := manager.(BackupManager)
				assert.True(t, ok, "返回的实例应该实现BackupManager接口")

				// 清理
				if manager != nil {
					err := manager.Shutdown()
					assert.NoError(t, err, "Shutdown应该成功")
				}
			}
		})
	}
}

// TestNewManager_Errors 测试NewManager函数的错误场景
func TestNewManager_Errors(t *testing.T) {
	tests := []struct {
		name        string
		config      *types.Config
		expectError string
		description string
	}{
		{
			name:        "nil配置",
			config:      nil,
			expectError: "配置不能为nil",
			description: "传入nil配置应该返回错误",
		},
		{
			name: "空备份根目录",
			config: &types.Config{
				BackupRoot: "",
				MySQL: &types.MySQLConfig{
					Host:   "localhost",
					Port:   3306,
					User:   "test_user",
					DBName: "test_db",
				},
			},
			expectError: "BackupRoot",
			description: "空的备份根目录应该导致验证失败",
		},

		{
			name: "无效MySQL端口",
			config: &types.Config{
				BackupRoot: t.TempDir(),
				MySQL: &types.MySQLConfig{
					Host:   "localhost",
					Port:   99999, // 无效端口
					User:   "test_user",
					DBName: "test_db",
				},
			},
			expectError: "Port必须在1-65535范围内",
			description: "无效的MySQL端口应该导致验证失败",
		},
		{
			name: "空ES地址列表",
			config: &types.Config{
				BackupRoot: t.TempDir(),
				ES: &types.ESConfig{
					Addresses:        []string{}, // 空地址列表
					ArchivalRepoName: "test-archival",
					ManagedRepoName:  "test-managed",
				},
			},
			expectError: "Addresses列表不能为空",
			description: "空的ES地址列表应该导致验证失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manager, err := NewManager(tt.config)

			assert.Error(t, err, tt.description)
			assert.Nil(t, manager)
			if err != nil {
				assert.Contains(t, err.Error(), tt.expectError,
					fmt.Sprintf("错误信息应该包含'%s'，实际得到: %v", tt.expectError, err))
			}
		})
	}
}

// TestBackupManager_Interface 测试BackupManager接口的完整性
func TestBackupManager_Interface(t *testing.T) {
	if _, err := os.Stat("/bin/true"); err != nil {
		t.Skip("/bin/true not found, skip test")
	}
	// 创建一个有效的配置
	tempDir := t.TempDir()
	config := &types.Config{
		BackupRoot: tempDir,
		Logger:     zap.NewNop(),
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "test_user",
			DBName: "test_db",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/bin/true",
				Mysql:       "/bin/true",
				Mysqlbinlog: "/bin/true",
				Mysqladmin:  "/bin/true",
			},
		},
	}

	manager, err := NewManager(config)
	require.NoError(t, err)
	require.NotNil(t, manager)
	defer func() {
		err := manager.Shutdown()
		assert.NoError(t, err)
	}()

	// 验证所有接口方法是否可调用（不需要真正执行，只是验证接口）
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	t.Run("AsyncOperations", func(t *testing.T) {
		// 这些调用可能会失败（因为没有真实的数据库），但不应该panic
		// 主要是验证接口签名正确性

		// 异步备份接口
		_, err := manager.BackupAsync(ctx, types.MySQL, "test-source", types.BackupTypeArchival, "test backup")
		_ = err // 不检查err，因为可能由于环境问题失败

		// 异步恢复接口（使用新的RestoreConfig）
		restoreConfig := types.RestoreConfig{
			SourceType: types.MySQL,
			SourceName: "test-source",
			BackupID:   "test-backup-id",
		}
		_, err = manager.RestoreAsync(ctx, restoreConfig)
		_ = err // 不检查err

		// 分组备份接口
		backupAllReq := types.BackupAllRequest{
			Sources: []types.BackupRequest{
				{
					SourceType:  types.MySQL,
					SourceName:  "test-source",
					BackupType:  types.BackupTypeArchival,
					Description: "test backup",
				},
			},
			Description: "test group backup",
		}
		_, err = manager.BackupAllAsync(ctx, backupAllReq)
		_ = err // 不检查err

		// 分组恢复接口
		restoreAllConfig := types.BatchRestoreConfig{
			// BatchRestoreConfig的正确字段名
		}
		_, err = manager.RestoreAllAsync(ctx, restoreAllConfig)
		_ = err // 不检查err
	})

	t.Run("TaskManagement", func(t *testing.T) {
		// 任务管理接口
		tasks, err := manager.ListTasks()
		assert.NoError(t, err) // 这个应该总是成功的
		assert.NotNil(t, tasks)

		// GetTask - 用一个不存在的ID测试
		task, err := manager.GetTask("non-existent-id")
		// 可能返回错误，这是正常的
		_ = task
		_ = err

		// CancelTask - 用一个不存在的ID测试
		err = manager.CancelTask("non-existent-id")
		_ = err // 可能返回错误，这是正常的

		// ClearOldTasks
		err = manager.ClearOldTasks()
		assert.NoError(t, err) // 这个应该总是成功的
	})

	t.Run("SyncOperations", func(t *testing.T) {
		// 同步操作接口 - 这些可能会失败，但接口应该存在
		_, err := manager.Backup(ctx, types.MySQL, "test-source", types.BackupTypeArchival, "test backup")
		_ = err // 不检查err，因为可能由于环境问题失败

		restoreConfig := types.RestoreConfig{
			SourceType: types.MySQL,
			SourceName: "test-source",
			BackupID:   "test-backup-id",
		}
		err = manager.Restore(ctx, restoreConfig)
		_ = err // 不检查err
	})

	t.Run("BackupManagement", func(t *testing.T) {
		// 备份管理接口
		archivalBackups, err := manager.ListArchivalBackups(ctx, types.MySQL)
		// 可能返回错误，这是正常的
		_ = archivalBackups
		_ = err

		chains, err := manager.ListIncrementalChains(ctx, types.MySQL)
		// 可能返回错误，这是正常的
		_ = chains
		_ = err

		err = manager.DeleteBackup(ctx, types.MySQL, "non-existent-backup")
		// 可能返回错误，这是正常的
		_ = err
	})
}

// TestNewManager_DefaultValues 测试默认值设置
func TestNewManager_DefaultValues(t *testing.T) {
	tempDir := t.TempDir()
	config := &types.Config{
		BackupRoot: tempDir,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "test_user",
			DBName: "test_db",
		},
		// 不设置MaxConcurrentTasks等，测试默认值
	}

	manager, err := NewManager(config)
	require.NoError(t, err)
	require.NotNil(t, manager)
	defer func() {
		err := manager.Shutdown()
		assert.NoError(t, err)
	}()

	// 验证默认值已被设置（通过检查配置对象）
	assert.Equal(t, 5, config.MaxConcurrentTasks, "默认并发任务数应该是5")
	assert.Equal(t, 365, config.TaskRetentionDays, "默认任务保留天数应该是365")
	assert.Equal(t, 0, config.MaxTaskHistory, "默认最大任务历史应该是0")
	assert.False(t, config.CleanupBackupData, "默认不清理备份数据")
}

// TestNewManager_DirectoryCreation 测试目录创建
func TestNewManager_DirectoryCreation(t *testing.T) {
	if _, err := os.Stat("/bin/true"); err != nil {
		t.Skip("/bin/true not found, skip test")
	}
	tempDir := t.TempDir()
	backupRoot := filepath.Join(tempDir, "backup-root")

	config := &types.Config{
		BackupRoot: backupRoot,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "test_user",
			DBName: "test_db",
		},
	}

	manager, err := NewManager(config)
	require.NoError(t, err)
	require.NotNil(t, manager)
	defer func() {
		err := manager.Shutdown()
		assert.NoError(t, err)
	}()

	// 验证备份根目录是否被创建
	_, err = os.Stat(backupRoot)
	assert.NoError(t, err, "备份根目录应该被创建")
}

// TestNewManager_LoggerHandling 测试日志记录器处理
func TestNewManager_LoggerHandling(t *testing.T) {
	tempDir := t.TempDir()

	t.Run("使用自定义Logger", func(t *testing.T) {
		customLogger := zap.NewNop()

		config := &types.Config{
			BackupRoot: tempDir,
			Logger:     customLogger,
			MySQL: &types.MySQLConfig{
				Host:   "localhost",
				Port:   3306,
				User:   "test_user",
				DBName: "test_db",
			},
		}

		manager, err := NewManager(config)
		require.NoError(t, err)
		require.NotNil(t, manager)
		defer func() {
			err := manager.Shutdown()
			assert.NoError(t, err)
		}()

		// 验证自定义logger被使用
		assert.Equal(t, customLogger, config.Logger)
	})

	t.Run("nil Logger会被设置默认值", func(t *testing.T) {
		config := &types.Config{
			BackupRoot: tempDir,
			Logger:     nil, // 明确设置为nil
			MySQL: &types.MySQLConfig{
				Host:   "localhost",
				Port:   3306,
				User:   "test_user",
				DBName: "test_db",
			},
		}

		manager, err := NewManager(config)
		require.NoError(t, err)
		require.NotNil(t, manager)
		defer func() {
			err := manager.Shutdown()
			assert.NoError(t, err)
		}()

		// 验证默认logger被设置
		assert.NotNil(t, config.Logger, "nil logger应该被设置为默认值")
	})
}

// TestNewManager_ConcurrentInitialization 测试并发初始化
func TestNewManager_ConcurrentInitialization(t *testing.T) {
	tempDir := t.TempDir()

	const numRoutines = 10
	results := make(chan error, numRoutines)

	for i := 0; i < numRoutines; i++ {
		go func(index int) {
			// 每个goroutine使用不同的子目录
			subDir := filepath.Join(tempDir, fmt.Sprintf("backup-%d", index))

			// 创建子目录
			if err := os.MkdirAll(subDir, 0755); err != nil {
				results <- fmt.Errorf("goroutine %d failed to create dir: %w", index, err)
				return
			}

			config := &types.Config{
				BackupRoot: subDir,
				MySQL: &types.MySQLConfig{
					Host:   "localhost",
					Port:   3306,
					User:   "test_user",
					DBName: "test_db",
				},
			}

			manager, err := NewManager(config)
			if err != nil {
				results <- fmt.Errorf("goroutine %d failed: %w", index, err)
				return
			}

			if manager == nil {
				results <- fmt.Errorf("goroutine %d got nil manager", index)
				return
			}

			// 立即关闭以释放资源
			if shutdownErr := manager.Shutdown(); shutdownErr != nil {
				results <- fmt.Errorf("goroutine %d shutdown failed: %w", index, shutdownErr)
				return
			}

			results <- nil
		}(i)
	}

	// 收集所有结果
	for i := 0; i < numRoutines; i++ {
		err := <-results
		assert.NoError(t, err, fmt.Sprintf("并发初始化goroutine %d应该成功", i))
	}
}

// BenchmarkNewManager 性能基准测试
func BenchmarkNewManager(b *testing.B) {
	tempDir := b.TempDir()

	config := &types.Config{
		BackupRoot: tempDir,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "test_user",
			DBName: "test_db",
		},
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		manager, err := NewManager(config)
		if err != nil {
			b.Fatalf("NewManager失败: %v", err)
		}

		if manager == nil {
			b.Fatal("NewManager返回nil")
		}

		// 立即关闭以释放资源
		if err := manager.Shutdown(); err != nil {
			b.Fatalf("Shutdown失败: %v", err)
		}
	}
}
