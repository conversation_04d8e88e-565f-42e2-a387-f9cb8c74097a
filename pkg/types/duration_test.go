package types

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestDuration_JSONSerialization 测试Duration的JSON序列化和反序列化
func TestDuration_JSONSerialization(t *testing.T) {
	t.Run("should serialize to string format", func(t *testing.T) {
		d := Duration(2*time.Hour + 30*time.Minute)
		data, err := json.<PERSON>(d)
		assert.NoError(t, err)
		assert.Equal(t, `"2h30m0s"`, string(data))
	})

	t.Run("should deserialize from string format", func(t *testing.T) {
		var d Duration
		err := json.Unmarshal([]byte(`"1h30m"`), &d)
		assert.NoError(t, err)
		assert.Equal(t, Duration(1*time.Hour+30*time.Minute), d)
		assert.Equal(t, 1*time.Hour+30*time.Minute, d.ToDuration())
	})

	t.Run("should reject numeric format", func(t *testing.T) {
		var d Duration
		err := json.Unmarshal([]byte(`3600000000000`), &d)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "duration must be a string")
	})

	t.Run("should reject invalid string format", func(t *testing.T) {
		var d Duration
		err := json.Unmarshal([]byte(`"invalid"`), &d)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid duration format")
	})

	t.Run("should handle various valid formats", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected time.Duration
		}{
			{`"1h"`, 1 * time.Hour},
			{`"30m"`, 30 * time.Minute},
			{`"45s"`, 45 * time.Second},
			{`"100ms"`, 100 * time.Millisecond},
			{`"2h30m45s"`, 2*time.Hour + 30*time.Minute + 45*time.Second},
		}

		for _, tc := range testCases {
			var d Duration
			err := json.Unmarshal([]byte(tc.input), &d)
			assert.NoError(t, err, "Failed to parse %s", tc.input)
			assert.Equal(t, tc.expected, d.ToDuration())
		}
	})
}

// TestConfig_DurationFields 测试Config中Duration字段的序列化
func TestConfig_DurationFields(t *testing.T) {
	t.Run("should serialize config with duration fields", func(t *testing.T) {
		config := Config{
			BackupTimeout:  Duration(24 * time.Hour),
			RestoreTimeout: Duration(1 * time.Hour),
		}

		data, err := json.MarshalIndent(config, "", "  ")
		assert.NoError(t, err)

		expected := `{
  "backup_root": "",
  "backup_timeout": "24h0m0s",
  "restore_timeout": "1h0m0s"
}`
		assert.Equal(t, expected, string(data))
	})

	t.Run("should deserialize config with string duration", func(t *testing.T) {
		jsonData := `{
			"backup_timeout": "2h",
			"restore_timeout": "30m"
		}`

		var config Config
		err := json.Unmarshal([]byte(jsonData), &config)
		assert.NoError(t, err)
		assert.Equal(t, 2*time.Hour, config.BackupTimeout.ToDuration())
		assert.Equal(t, 30*time.Minute, config.RestoreTimeout.ToDuration())
	})

	t.Run("should reject config with numeric duration", func(t *testing.T) {
		jsonData := `{
			"backup_timeout": 7200000000000,
			"restore_timeout": "30m"
		}`

		var config Config
		err := json.Unmarshal([]byte(jsonData), &config)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "duration must be a string")
	})
}
