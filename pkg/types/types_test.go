package types

import (
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestTaskStatus_IsTerminal 测试任务状态的终态判断方法
func TestTaskStatus_IsTerminal(t *testing.T) {
	tests := []struct {
		name     string
		status   TaskStatus
		expected bool
	}{
		{"已完成状态是终态", TaskStatusCompleted, true},
		{"失败状态是终态", TaskStatusFailed, true},
		{"已取消状态是终态", TaskStatusCancelled, true},
		{"等待状态不是终态", TaskStatusPending, false},
		{"运行状态不是终态", TaskStatusRunning, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.IsTerminal(); got != tt.expected {
				t.Errorf("TaskStatus.IsTerminal() = %v, want %v", got, tt.expected)
			}
		})
	}
}

// TestBackupRequest_Validation 测试备份请求的验证逻辑
func TestBackupRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		req     BackupRequest
		wantErr bool
	}{
		{
			name: "有效的MySQL备份请求",
			req: BackupRequest{
				SourceType:  MySQL,
				SourceName:  "test_db",
				BackupType:  BackupTypeArchival,
				Description: "测试备份",
			},
			wantErr: false,
		},
		{
			name: "有效的ES备份请求",
			req: BackupRequest{
				SourceType:  Elasticsearch,
				SourceName:  "test_cluster",
				BackupType:  BackupTypeChainInitial,
				Description: "测试ES备份",
			},
			wantErr: false,
		},
		{
			name: "空的数据源名称",
			req: BackupRequest{
				SourceType: MySQL,
				SourceName: "",
				BackupType: BackupTypeArchival,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateBackupRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateBackupRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestRestoreRequest_Validation 测试恢复请求的验证逻辑
func TestRestoreRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		req     RestoreRequest
		wantErr bool
	}{
		{
			name: "有效的恢复请求",
			req: RestoreRequest{
				SourceType:  MySQL,
				SourceName:  "test_db",
				BackupID:    "backup_123",
				Description: "测试恢复",
			},
			wantErr: false,
		},
		{
			name: "空的备份ID",
			req: RestoreRequest{
				SourceType: MySQL,
				SourceName: "test_db",
				BackupID:   "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRestoreRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateRestoreRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestTask_Creation 测试任务对象的创建
func TestTask_Creation(t *testing.T) {
	task := &Task{
		ID:          "test_task_123",
		Type:        BackupTask,
		Source:      MySQL,
		Status:      TaskStatusPending,
		Progress:    0,
		StartTime:   time.Now(),
		Description: "测试任务",
		Metadata:    make(map[string]interface{}),
	}

	if task.ID != "test_task_123" {
		t.Errorf("期望任务ID为 'test_task_123'，实际得到 '%s'", task.ID)
	}

	if task.Type != BackupTask {
		t.Errorf("期望任务类型为 BackupTask，实际得到 %v", task.Type)
	}

	if task.Status.IsTerminal() {
		t.Errorf("期望等待状态的任务不是终态")
	}
}

// TestBackupAllRequest_Validation 测试全量备份请求的验证逻辑
func TestBackupAllRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		req     BackupAllRequest
		wantErr bool
	}{
		{
			name: "有效的全量备份请求",
			req: BackupAllRequest{
				Sources: []BackupRequest{
					{
						SourceType:  MySQL,
						SourceName:  "db1",
						BackupType:  BackupTypeArchival,
						Description: "数据库1备份",
					},
					{
						SourceType:  Elasticsearch,
						SourceName:  "cluster1",
						BackupType:  BackupTypeArchival,
						Description: "ES备份",
					},
				},
				Description:      "组合备份",
				CleanupOnFailure: true,
			},
			wantErr: false,
		},
		{
			name: "空的数据源列表",
			req: BackupAllRequest{
				Sources:     []BackupRequest{},
				Description: "空备份",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateBackupAllRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateBackupAllRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}



// 验证辅助函数
func validateBackupRequest(req BackupRequest) error {
	if req.SourceName == "" {
		return fmt.Errorf("数据源名称不能为空")
	}
	return nil
}

func validateRestoreRequest(req RestoreRequest) error {
	if req.BackupID == "" {
		return fmt.Errorf("备份ID不能为空")
	}
	return nil
}

func validateBackupAllRequest(req BackupAllRequest) error {
	if len(req.Sources) == 0 {
		return fmt.Errorf("数据源列表不能为空")
	}
	return nil
}



// TestBackupError 测试BackupError的方法
func TestBackupError(t *testing.T) {
	t.Run("Error方法应该返回消息", func(t *testing.T) {
		err := &BackupError{
			Code:      "TEST_ERROR",
			Message:   "测试错误消息",
			Component: "测试组件",
			Operation: "测试操作",
		}

		assert.Equal(t, "测试错误消息", err.Error())
	})

	t.Run("Unwrap方法应该返回原始错误", func(t *testing.T) {
		originalErr := errors.New("原始错误")
		err := &BackupError{
			Code:    "WRAPPED_ERROR",
			Message: "包装错误",
			OrigErr: originalErr,
		}

		assert.Equal(t, originalErr, err.Unwrap())
	})

	t.Run("当没有原始错误时Unwrap方法应该返回nil", func(t *testing.T) {
		err := &BackupError{
			Code:    "NO_ORIG_ERROR",
			Message: "没有原始错误",
		}

		assert.Nil(t, err.Unwrap())
	})
}

// TestBackupRecord_IsPartOfChain 测试IsPartOfChain方法
func TestBackupRecord_IsPartOfChain(t *testing.T) {
	t.Run("当ChainID不为空时应该返回true", func(t *testing.T) {
		record := &BackupRecord{
			ChainID: "chain-123",
		}
		assert.True(t, record.IsPartOfChain())
	})

	t.Run("当ChainID为空时应该返回false", func(t *testing.T) {
		record := &BackupRecord{
			ChainID: "",
		}
		assert.False(t, record.IsPartOfChain())
	})
}

// TestBackupType_Validation 测试备份类型验证
func TestBackupType_Validation(t *testing.T) {
	validTypes := []BackupType{
		BackupTypeArchival,
		BackupTypeChainInitial,
		BackupTypeChainIncremental,
	}

	for _, backupType := range validTypes {
		t.Run(fmt.Sprintf("Valid backup type: %s", backupType), func(t *testing.T) {
			assert.NotEmpty(t, string(backupType))
		})
	}
}

// TestSourceType_Validation 测试数据源类型验证
func TestSourceType_Validation(t *testing.T) {
	validTypes := []SourceType{
		MySQL,
		Elasticsearch,
	}

	for _, sourceType := range validTypes {
		t.Run(fmt.Sprintf("Valid source type: %s", sourceType), func(t *testing.T) {
			assert.NotEmpty(t, string(sourceType))
		})
	}
}

// TestBackupStatus_Validation 测试备份状态验证
func TestBackupStatus_Validation(t *testing.T) {
	validStatuses := []BackupStatus{
		BackupStatusInProgress,
		BackupStatusCompleted,
		BackupStatusFailed,
	}

	for _, status := range validStatuses {
		t.Run(fmt.Sprintf("Valid backup status: %s", status), func(t *testing.T) {
			assert.NotEmpty(t, string(status))
		})
	}
}

// TestTaskType_Validation 测试任务类型验证
func TestTaskType_Validation(t *testing.T) {
	validTypes := []TaskType{
		BackupTask,
		RestoreTask,
		BackupAllTask,
		RestoreAllTask,
	}

	for _, taskType := range validTypes {
		t.Run(fmt.Sprintf("Valid task type: %s", taskType), func(t *testing.T) {
			assert.NotEmpty(t, string(taskType))
		})
	}
}

// TestBackupRecord_Serialization 测试备份记录序列化
func TestBackupRecord_Serialization(t *testing.T) {
	t.Run("BackupRecord should serialize and deserialize correctly", func(t *testing.T) {
		original := &BackupRecord{
			ID:            "test-backup-123",
			Timestamp:     time.Now().Truncate(time.Second), // 截断到秒，避免精度问题
			Source:        MySQL,
			Type:          BackupTypeArchival,
			Path:          "/test/path",
			Description:   "测试备份",
			ChainID:       "chain-123",
			ParentID:      "parent-123",
			Status:        BackupStatusCompleted,
			Error:         "",
			Size:          1024,
			ExecutionTime: Duration(5 * time.Second),
			BinlogFile:    "mysql-bin.000001",
			BinlogPos:     12345,
			Extra:         `{"test": "data"}`,
		}

		// 序列化
		data, err := json.Marshal(original)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		// 反序列化
		var restored BackupRecord
		err = json.Unmarshal(data, &restored)
		assert.NoError(t, err)

		// 验证字段
		assert.Equal(t, original.ID, restored.ID)
		assert.Equal(t, original.Source, restored.Source)
		assert.Equal(t, original.Type, restored.Type)
		assert.Equal(t, original.Path, restored.Path)
		assert.Equal(t, original.Description, restored.Description)
		assert.Equal(t, original.ChainID, restored.ChainID)
		assert.Equal(t, original.ParentID, restored.ParentID)
		assert.Equal(t, original.Status, restored.Status)
		assert.Equal(t, original.Size, restored.Size)
		assert.Equal(t, original.BinlogFile, restored.BinlogFile)
		assert.Equal(t, original.BinlogPos, restored.BinlogPos)
		assert.Equal(t, original.Extra, restored.Extra)
	})
}

// TestTask_Serialization 测试任务序列化
func TestTask_Serialization(t *testing.T) {
	t.Run("Task should serialize and deserialize correctly", func(t *testing.T) {
		original := &Task{
			ID:          "task-123",
			Type:        BackupTask,
			Source:      MySQL,
			Status:      TaskStatusCompleted,
			Progress:    100.0,
			Error:       "",
			StartTime:   time.Now().Truncate(time.Second),
			EndTime:     time.Now().Add(time.Hour).Truncate(time.Second),
			SubTaskIDs:  []string{"sub-1", "sub-2"},
			Description: "测试任务",
			Metadata: map[string]interface{}{
				"test":   "value",
				"number": 42,
			},
		}

		// 序列化
		data, err := json.Marshal(original)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		// 反序列化
		var restored Task
		err = json.Unmarshal(data, &restored)
		assert.NoError(t, err)

		// 验证字段
		assert.Equal(t, original.ID, restored.ID)
		assert.Equal(t, original.Type, restored.Type)
		assert.Equal(t, original.Source, restored.Source)
		assert.Equal(t, original.Status, restored.Status)
		assert.Equal(t, original.Progress, restored.Progress)
		assert.Equal(t, original.Error, restored.Error)
		assert.Equal(t, original.Description, restored.Description)
		assert.Equal(t, len(original.SubTaskIDs), len(restored.SubTaskIDs))
		assert.Equal(t, len(original.Metadata), len(restored.Metadata))
	})
}

// TestBackupError_ErrorInterface 测试 BackupError 实现 error 接口
func TestBackupError_ErrorInterface(t *testing.T) {
	t.Run("BackupError should implement error interface", func(t *testing.T) {
		err := &BackupError{
			Code:      "TEST_ERROR",
			Message:   "测试错误",
			Component: "TestComponent",
			Operation: "TestOperation",
			Timestamp: time.Now(),
		}

		// 验证实现了 error 接口
		var _ error = err
		assert.Equal(t, "测试错误", err.Error())
	})

	t.Run("BackupError should support error wrapping", func(t *testing.T) {
		originalErr := errors.New("原始错误")
		err := &BackupError{
			Code:      "WRAPPED_ERROR",
			Message:   "包装错误",
			Component: "TestComponent",
			Operation: "TestOperation",
			Timestamp: time.Now(),
			OrigErr:   originalErr,
		}

		// 验证 Unwrap 方法
		assert.Equal(t, originalErr, err.Unwrap())
		assert.True(t, errors.Is(err, originalErr))
	})
}

// TestConfig_Validation 测试配置结构验证
func TestConfig_Validation(t *testing.T) {
	t.Run("Config with valid values", func(t *testing.T) {
		cfg := &Config{
			BackupRoot:         "/tmp/backup",
			MaxConcurrentTasks: 5,
			TaskRetentionDays:  7,
			MaxTaskHistory:     1000,
		}

		assert.NotEmpty(t, cfg.BackupRoot)
		assert.Greater(t, cfg.MaxConcurrentTasks, 0)
		assert.Greater(t, cfg.TaskRetentionDays, 0)
		assert.Greater(t, cfg.MaxTaskHistory, 0)
	})
}

// TestMySQLConfig_Validation 测试 MySQL 配置验证
func TestMySQLConfig_Validation(t *testing.T) {
	t.Run("Valid MySQL config", func(t *testing.T) {
		cfg := &MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "root",
			Password: "password",
			DBName:   "test",
			ToolsPath: MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		}

		assert.NotEmpty(t, cfg.Host)
		assert.Greater(t, cfg.Port, 0)
		assert.NotEmpty(t, cfg.User)
		assert.NotEmpty(t, cfg.DBName)
	})
}

// TestESConfig_Validation 测试 Elasticsearch 配置验证
func TestESConfig_Validation(t *testing.T) {
	t.Run("Valid ES config", func(t *testing.T) {
		cfg := &ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			User:             "elastic",
			Password:         "changeme",
			ArchivalRepoName: "archival",
			ManagedRepoName:  "managed",
		}

		assert.NotEmpty(t, cfg.Addresses)
		assert.NotEmpty(t, cfg.ArchivalRepoName)
		assert.NotEmpty(t, cfg.ManagedRepoName)
		assert.NotEqual(t, cfg.ArchivalRepoName, cfg.ManagedRepoName)
	})
}

// TestChainMeta_Validation 测试链元数据验证
func TestChainMeta_Validation(t *testing.T) {
	t.Run("Valid chain meta", func(t *testing.T) {
		meta := &ChainMeta{
			ChainID:   "chain-123",
			BackupIDs: []string{"backup-1", "backup-2", "backup-3"},
		}

		assert.NotEmpty(t, meta.ChainID)
		assert.NotEmpty(t, meta.BackupIDs)
		assert.Greater(t, len(meta.BackupIDs), 0)
	})

	t.Run("Empty chain meta", func(t *testing.T) {
		meta := &ChainMeta{
			ChainID:   "",
			BackupIDs: []string{},
		}

		assert.Empty(t, meta.ChainID)
		assert.Empty(t, meta.BackupIDs)
	})
}

// TestIncrementalChain_Validation 测试增量链验证
func TestIncrementalChain_Validation(t *testing.T) {
	t.Run("Valid incremental chain", func(t *testing.T) {
		chain := &IncrementalChain{
			ChainID: "chain-123",
			Backups: []*BackupRecord{
				{
					ID:     "backup-1",
					Type:   BackupTypeChainInitial,
					Status: BackupStatusCompleted,
				},
				{
					ID:       "backup-2",
					Type:     BackupTypeChainIncremental,
					ParentID: "backup-1",
					Status:   BackupStatusCompleted,
				},
			},
		}

		assert.NotEmpty(t, chain.ChainID)
		assert.NotEmpty(t, chain.Backups)
		assert.Equal(t, BackupTypeChainInitial, chain.Backups[0].Type)
		assert.Equal(t, BackupTypeChainIncremental, chain.Backups[1].Type)
		assert.Equal(t, chain.Backups[0].ID, chain.Backups[1].ParentID)
	})
}

// TestArchivalBackup_Validation 测试归档备份验证
func TestArchivalBackup_Validation(t *testing.T) {
	t.Run("Valid archival backup", func(t *testing.T) {
		archival := &ArchivalBackup{
			Record: &BackupRecord{
				ID:     "backup-123",
				Type:   BackupTypeArchival,
				Status: BackupStatusCompleted,
			},
		}

		assert.NotNil(t, archival.Record)
		assert.Equal(t, BackupTypeArchival, archival.Record.Type)
		assert.Empty(t, archival.Record.ChainID) // 归档备份不应该有链ID
	})
}

// 测试Config的GetAllSourceTypes方法
func TestConfig_GetAllSourceTypes(t *testing.T) {
	t.Run("测试GetAllSourceTypes方法", func(t *testing.T) {
		// 测试空配置
		emptyConfig := &Config{}
		sources := emptyConfig.GetAllSourceTypes()
		assert.Empty(t, sources, "空配置应该返回空的数据源列表")

		// 测试只有MySQL配置
		mysqlConfig := &Config{
			MySQL: &MySQLConfig{
				Host:   "localhost",
				Port:   3306,
				User:   "test",
				DBName: "test",
			},
		}
		sources = mysqlConfig.GetAllSourceTypes()
		assert.Len(t, sources, 1, "只有MySQL配置应该返回1个数据源")
		assert.Contains(t, sources, MySQL, "应该包含MySQL数据源")

		// 测试只有ES配置
		esConfig := &Config{
			ES: &ESConfig{
				Addresses:        []string{"http://localhost:9200"},
				ArchivalRepoName: "archival",
				ManagedRepoName:  "managed",
			},
		}
		sources = esConfig.GetAllSourceTypes()
		assert.Len(t, sources, 1, "只有ES配置应该返回1个数据源")
		assert.Contains(t, sources, Elasticsearch, "应该包含Elasticsearch数据源")

		// 测试完整配置
		fullConfig := &Config{
			MySQL: &MySQLConfig{
				Host:   "localhost",
				Port:   3306,
				User:   "test",
				DBName: "test",
			},
			ES: &ESConfig{
				Addresses:        []string{"http://localhost:9200"},
				ArchivalRepoName: "archival",
				ManagedRepoName:  "managed",
			},
		}
		sources = fullConfig.GetAllSourceTypes()
		assert.Len(t, sources, 2, "完整配置应该返回2个数据源")
		assert.Contains(t, sources, MySQL, "应该包含MySQL数据源")
		assert.Contains(t, sources, Elasticsearch, "应该包含Elasticsearch数据源")
	})
}
