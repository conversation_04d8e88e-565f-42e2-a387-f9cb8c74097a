// Package types 定义了UniBackup SDK中使用的所有共享数据结构和常量。
// 这个包作为数据模型的唯一真实来源，确保API层、内部逻辑和持久化元数据之间的一致性。
//
// 主要组件：
// - 错误处理：BackupError 结构化错误类型，提供丰富的错误上下文
// - 数据源类型：SourceType 枚举，支持MySQL、Elasticsearch等
// - 备份类型：BackupType 枚举，支持归档备份和增量链备份
// - 配置结构：Config, MySQLConfig, ESConfig 等配置类型
// - 备份记录：BackupRecord 核心数据结构，记录备份的完整信息
// - 任务管理：Task, TaskStatus 等任务相关类型
// - API请求响应：各种请求和响应结构，用于客户端-服务端通信
package types

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// RestoreSuccessCallback 恢复成功完成时的回调函数类型
type RestoreSuccessCallback func(ctx context.Context) error

// Duration 是time.Duration的包装类型，提供更友好的JSON序列化
// 只支持字符串格式，如 "1h", "30m", "45s", "2h30m"
type Duration time.Duration

// MarshalJSON 实现JSON序列化，输出易读的字符串格式
func (d Duration) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Duration(d).String())
}

// UnmarshalJSON 实现JSON反序列化，只支持字符串格式（如"1h", "30m", "45s"）
func (d *Duration) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return fmt.Errorf("duration must be a string, got %s", string(data))
	}

	duration, err := time.ParseDuration(str)
	if err != nil {
		return fmt.Errorf("invalid duration format '%s': %v", str, err)
	}

	*d = Duration(duration)
	return nil
}

// UnmarshalYAML 实现YAML反序列化，只支持字符串格式（如"1h", "30m", "45s"）
func (d *Duration) UnmarshalYAML(unmarshal func(interface{}) error) error {
	var str string
	if err := unmarshal(&str); err != nil {
		return fmt.Errorf("duration must be a string: %v", err)
	}

	duration, err := time.ParseDuration(str)
	if err != nil {
		return fmt.Errorf("invalid duration format '%s': %v", str, err)
	}

	*d = Duration(duration)
	return nil
}

// MarshalYAML 实现YAML序列化，输出易读的字符串格式
func (d Duration) MarshalYAML() (interface{}, error) {
	return time.Duration(d).String(), nil
}

// ToDuration 转换为标准的time.Duration
func (d Duration) ToDuration() time.Duration {
	return time.Duration(d)
}

// BackupError 代表一个结构化的错误。
// 相比简单的字符串错误，它提供了更丰富的上下文，便于程序化处理、日志记录和UI展示。
type BackupError struct {
	Code      string    `json:"code"`                // 错误代码 (例如, "DB_CONN_FAILED", "PERMISSION_DENIED")
	Message   string    `json:"message"`             // 错误的摘要信息
	Component string    `json:"component,omitempty"` // 错误发生的组件 (例如, "MySQLProvider", "StorageManager")
	Operation string    `json:"operation,omitempty"` // 失败的操作 (例如, "Backup", "Restore")
	Timestamp time.Time `json:"timestamp"`           // 错误发生的时间
	Details   string    `json:"details,omitempty"`   // 详细的错误描述或堆栈跟踪
	Retryable bool      `json:"retryable,omitempty"` // 标识错误是否为临时的、可重试的错误

	// OrigErr 存储了原始的、未被修改的错误。
	// 这对于使用 errors.Is 或 errors.As 进行错误链检查至关重要。
	// 它不应被序列化为JSON。
	OrigErr error `json:"-"`
}

// Error 实现了 Go 标准的 error 接口。
func (e *BackupError) Error() string {
	return e.Message
}

// Unwrap 实现了 Go 1.13 引入的错误包装接口。
// 这使得可以使用 errors.Is 和 errors.As 来检查链中的底层错误。
func (e *BackupError) Unwrap() error {
	return e.OrigErr
}

// SourceType 定义了所支持的数据源类型。
// 使用字符串类型别名以增强可读性和类型安全。
type SourceType string

// 定义支持的数据源常量
const (
	MySQL         SourceType = "mysql"         // 代表 MySQL 数据源
	Elasticsearch SourceType = "elasticsearch" // 代表 Elasticsearch 数据源
)

// TaskType 定义了任务的类型。
// 用于区分异步任务是用于备份、恢复还是其他操作。
type TaskType string

// 定义任务类型常量
const (
	BackupTask     TaskType = "backup"      // 单一数据源的备份任务
	RestoreTask    TaskType = "restore"     // 单一数据源的恢复任务
	DeleteTask     TaskType = "delete"      // 单一数据源的删除任务
	BackupAllTask  TaskType = "backup_all"  // 跨数据源的分组备份任务
	RestoreAllTask TaskType = "restore_all" // 跨数据源的分组恢复任务
)

// TaskStatus 定义了任务所有可能的状态。
// 用于跟踪异步任务的生命周期。
type TaskStatus string

// 定义任务状态常量
const (
	TaskStatusPending   TaskStatus = "pending"   // 任务已创建，等待执行
	TaskStatusRunning   TaskStatus = "running"   // 任务正在执行中
	TaskStatusCompleted TaskStatus = "completed" // 任务成功完成
	TaskStatusFailed    TaskStatus = "failed"    // 任务执行失败
	TaskStatusCancelled TaskStatus = "cancelled" // 任务被取消
)

// IsTerminal 检查任务状态是否为终结状态（即不会再发生变化）。
func (status TaskStatus) IsTerminal() bool {
	return status == TaskStatusCompleted ||
		status == TaskStatusFailed ||
		status == TaskStatusCancelled
}

// IsBackupTask 检查任务类型是否为备份任务
func (t TaskType) IsBackupTask() bool {
	return t == BackupTask || t == BackupAllTask
}

// IsRestoreTask 检查任务类型是否为恢复任务
func (t TaskType) IsRestoreTask() bool {
	return t == RestoreTask || t == RestoreAllTask
}

// BackupType 定义了备份产物的具体类型。
// 这对于区分备份文件的处理方式至关重要，特别是在增量备份链中。
type BackupType string

// 定义备份类型常量
const (
	// 归档全量备份：一个完全独立的、自包含的备份。可以随时被安全删除，不影响任何其他备份。
	BackupTypeArchival BackupType = "archival"
	// 增量链的初始备份：一条新增量链的第一个节点，它是一个全量备份。
	BackupTypeChainInitial BackupType = "chain_initial"
	// 增量链的增量备份：依赖于链中上一个备份点的增量数据。
	BackupTypeChainIncremental BackupType = "chain_incremental"
)

// BackupStatus 定义了单个备份记录的可能状态。
type BackupStatus string

// 定义备份记录状态常量
const (
	// BackupStatusInProgress 表示备份正在进行中。
	BackupStatusInProgress BackupStatus = "InProgress"
	// BackupStatusCompleted 表示备份已成功完成。
	BackupStatusCompleted BackupStatus = "Completed"
	// BackupStatusFailed 表示备份失败。
	BackupStatusFailed BackupStatus = "Failed"
)

// Config 是SDK的顶层配置结构体。
// 它聚合了所有必要的配置信息，作为初始化 BackupManager 的唯一入口。
//
// 使用说明：
// 1. BackupRoot 必须是一个存在且可写的目录路径
// 2. 至少配置一个数据源（MySQL 或 ES）
// 3. 建议配置 Logger 以便监控备份过程
// 4. 根据系统资源调整 MaxConcurrentTasks（仅影响恢复任务）
//
// 最佳实践：
// - 生产环境建议设置 TaskRetentionDays 为 30-365 天，默认为365天
// - MaxTaskHistory 建议设置为 0（不限制）或 1000-5000，默认为0
// - CleanupBackupData 建议根据存储空间需求设置，默认为false（保留备份数据）
// - 备份目录应有足够的磁盘空间，建议至少为数据库大小的 3-5 倍
type Config struct {
	// BackupRoot 是所有备份文件和元数据的根目录，必须提供。
	// 注意：此目录必须存在且具有读写权限，SDK会在此目录下创建子目录结构。
	BackupRoot string `json:"backup_root" yaml:"backup_root"`

	// CloudStorage 是云存储配置，可选。
	// 当配置了云存储且 enabled 为 true 时，将优先使用云存储进行实际存储。
	// BackupRoot 在云存储模式下主要用于路径生成。
	CloudStorage *CloudStorageConfig `json:"cloud_storage,omitempty" yaml:"cloud_storage,omitempty"`

	// Logger 是一个可选的、符合zap标准的结构化日志记录器。
	// 如果为nil，SDK内部将不会输出任何日志。
	// 建议在生产环境中配置日志记录器以便监控和故障排查。
	Logger *zap.Logger `json:"-" yaml:"-"`

	// MySQL 是针对MySQL数据源的特定配置。如果不需要MySQL备份，可以为nil。
	// 注意：配置的用户必须具有足够的权限执行备份和恢复操作。
	MySQL *MySQLConfig `json:"mysql,omitempty" yaml:"mysql,omitempty"`

	// ES 是针对Elasticsearch数据源的特定配置。如果不需要ES备份，可以为nil。
	// 注意：需要确保ES集群配置了快照仓库路径。
	ES *ESConfig `json:"es,omitempty" yaml:"es,omitempty"`

	// MaxConcurrentTasks 是任务管理器允许的最大并发恢复任务数。默认为5。
	// 注意：此配置仅限制恢复任务的并发数，备份任务可以无限制并发。
	// 建议根据系统资源调整：CPU核心数的1-2倍，但不超过10。
	MaxConcurrentTasks int `json:"max_concurrent_tasks,omitempty" yaml:"max_concurrent_tasks,omitempty"`

	// TaskRetentionDays 是任务记录的保留天数。只有超过此天数的已完成任务才会被清理。默认为365天（一年）。
	// 生产环境建议设置为30-365天，以便保留足够的操作历史用于审计。
	TaskRetentionDays int `json:"task_retention_days,omitempty" yaml:"task_retention_days,omitempty"`

	// MaxTaskHistory 是内存和文件中保留的最大任务历史数量。当任务数量超过此值时，最旧的已完成任务将被清理。默认为0（不限制数量）。
	// 建议根据内存大小调整：小型系统500-1000，大型系统2000-5000。设置为0表示只按时间清理，不限制数量。
	MaxTaskHistory int `json:"max_task_history,omitempty" yaml:"max_task_history,omitempty"`

	// CleanupBackupData 是否在清理任务记录时同时清理对应的备份数据。默认为false（只清理任务记录，保留备份数据）。
	// 启用此选项后，超过保留期的成功备份任务的备份数据也会被自动清理，释放存储空间。
	// 注意：增量备份会保持链完整性，不会破坏增量链的连续性。
	CleanupBackupData bool `json:"cleanup_backup_data,omitempty" yaml:"cleanup_backup_data,omitempty"`

	// BackupTimeout 是备份操作的超时时间。默认为24小时。
	// 建议根据数据规模调整：小型数据库1-6小时，大型数据库12-48小时。
	// 支持字符串格式如 "24h", "30m", "45s", "2h30m"
	BackupTimeout Duration `json:"backup_timeout,omitempty" yaml:"backup_timeout,omitempty"`

	// RestoreTimeout 是恢复操作的超时时间。默认为24小时。
	// 建议根据数据规模调整：小型数据库1-6小时，大型数据库12-48小时。
	// 支持字符串格式如 "24h", "30m", "45s", "2h30m"
	RestoreTimeout Duration `json:"restore_timeout,omitempty" yaml:"restore_timeout,omitempty"`
}

// GetAllSourceTypes 返回一个包含所有已配置数据源类型的切片。
// 这对于需要遍历所有可能数据源的内部逻辑（如跨类型查找）非常有用。
func (c *Config) GetAllSourceTypes() []SourceType {
	var sources []SourceType
	if c.MySQL != nil {
		sources = append(sources, MySQL)
	}
	if c.ES != nil {
		sources = append(sources, Elasticsearch)
	}
	return sources
}

// MySQLConfig 包含了连接MySQL和执行备份所需的所有信息。
//
// 权限要求：
// 配置的用户必须具有以下权限：
// - SELECT: 读取数据进行备份
// - RELOAD: 刷新日志文件（用于增量备份）
// - LOCK TABLES: 确保备份一致性
// - REPLICATION CLIENT: 获取binlog位置信息
// - SHOW DATABASES: 列出数据库
//
// 最佳实践：
// - 建议创建专用的备份用户，避免使用root用户
// - 确保MySQL开启了binlog（用于增量备份）
// - 建议使用强密码并定期更换
type MySQLConfig struct {
	// Host 是数据库的主机名或IP地址。
	// 支持域名、IPv4和IPv6地址。
	Host string `json:"host" yaml:"host"`

	// Port 是数据库的端口号。
	// MySQL默认端口为3306。
	Port int `json:"port" yaml:"port"`

	// User 是数据库用户名。
	// 该用户必须具有备份所需的权限（见上方权限要求）。
	User string `json:"user" yaml:"user"`

	// Password 是数据库密码。
	// 出于安全考虑，实际生产中建议通过环境变量传递密码。
	Password string `json:"password" yaml:"password"`

	// DBName 是需要备份的数据库名称。
	// 必须是一个存在的数据库名称。
	DBName string `json:"db_name" yaml:"db_name"`

	// ToolsPath 指定了MySQL工具的绝对路径。
	// 如果系统PATH中已包含这些工具，可以留空使用默认路径。
	ToolsPath MySQLToolsPath `json:"tools_path,omitempty" yaml:"tools_path,omitempty"`

	// BinlogBasePath 是binlog文件的基础路径（增量备份用）。
	// 当使用数据卷挂载时，指定binlog文件在容器内的挂载路径。
	// 如果为空，系统会自动检测常见路径。
	// 例如："/mysql-data", "/var/lib/mysql", "/shared/mysql-binlogs"
	BinlogBasePath string `json:"binlog_base_path,omitempty" yaml:"binlog_base_path,omitempty"`
}

// MySQLToolsPath 定义了MySQL命令行工具的路径。
// 它允许用户在工具未加入系统PATH时指定自定义路径。
//
// 工具说明：
// - mysqldump: 用于创建数据库的逻辑备份，支持全量备份
// - mysql: 命令行客户端，用于执行SQL查询和数据恢复
// - mysqlbinlog: 用于处理二进制日志文件，实现增量备份
// - mysqladmin: 用于执行管理操作，如刷新日志文件
//
// 使用建议：
// - 如果工具在系统PATH中，可以留空使用默认路径
// - 建议使用绝对路径以确保一致性
// - 确保指定的工具版本与MySQL服务器版本兼容
type MySQLToolsPath struct {
	// Mysqldump 工具的完整路径，用于创建数据库的逻辑备份。
	// 如果为空，将使用系统PATH中的mysqldump。
	Mysqldump string `json:"mysqldump,omitempty"`

	// Mysql 命令行客户端的完整路径，用于执行查询和恢复。
	// 如果为空，将使用系统PATH中的mysql。
	Mysql string `json:"mysql,omitempty"`

	// Mysqlbinlog 工具的完整路径，用于处理二进制日志文件（增量备份）。
	// 如果为空，将使用系统PATH中的mysqlbinlog。
	Mysqlbinlog string `json:"mysqlbinlog,omitempty"`

	// Mysqladmin 工具的完整路径，用于执行管理操作，如刷新日志。
	// 如果为空，将使用系统PATH中的mysqladmin。
	Mysqladmin string `json:"mysqladmin,omitempty"`
}

// ESConfig 包含了连接Elasticsearch所需的所有信息。
//
// 认证方式：
// 支持以下认证方式（按优先级）：
// 1. API Key认证（推荐）：设置APIKey字段
// 2. 基本认证：设置User和Password字段
// 3. 无认证：所有认证字段留空
//
// 权限要求：
// 配置的用户或API Key必须具有以下权限：
// - cluster:admin/repository/*: 管理快照仓库
// - cluster:admin/snapshot/*: 管理快照
// - indices:admin/create: 创建索引（恢复时）
// - indices:data/read/*: 读取索引数据
//
// 最佳实践：
// - 生产环境建议使用API Key认证
// - 确保ES集群配置了path.repo设置
// - 建议配置多个节点地址以提高可用性
type ESConfig struct {
	// Addresses 是ES集群节点的地址列表，例如 ["http://localhost:9200"]。
	// 建议配置多个节点地址以提高可用性，SDK会自动进行负载均衡。
	Addresses []string `json:"addresses" yaml:"addresses"`

	// APIKey 是用于认证的API密钥（可选）。
	// 格式：base64编码的"id:api_key"字符串。
	// 注意：API Key不会被序列化到JSON中，确保安全性。
	APIKey string `json:"-" yaml:"-"`

	// User 是用于基本认证的用户名（可选）。
	// 仅在未设置APIKey时使用。该用户必须具有快照管理权限。
	User string `json:"user,omitempty" yaml:"user,omitempty"`

	// Password 是用于基本认证的密码（可选）。
	// 仅在未设置APIKey时使用。
	// 注意：密码不会被序列化到JSON中，确保安全性。
	Password string `json:"-" yaml:"-"`
	// ArchivalRepoName 是用于归档全量备份的快照仓库名称。
	// 如果AutoCreateRepos为true，系统会自动创建此仓库；否则必须在ES中预先创建。
	// 建议使用描述性名称，如"unibackup-archival"。
	ArchivalRepoName string `json:"archival_repo_name" yaml:"archival_repo_name"`

	// ManagedRepoName 是用于受保护的增量链的快照仓库名称。
	// 如果AutoCreateRepos为true，系统会自动创建此仓库；否则必须在ES中预先创建。
	// 建议使用描述性名称，如"unibackup-managed"。
	ManagedRepoName string `json:"managed_repo_name" yaml:"managed_repo_name"`

	// AutoCreateRepos 是否自动创建快照仓库。推荐设置为true以确保统一的备份存储。
	//
	// 部署配置说明：
	// 1. 原生ES部署：
	//    - 在elasticsearch.yml中配置：path.repo: ["/your/backup/path"]
	//    - 重启ES集群使配置生效
	//    - 确保ES进程对备份路径有读写权限
	//
	// 2. Docker部署：
	//    - 容器启动时映射卷：-v /host/backup/root/elasticsearch:/usr/share/elasticsearch/snapshots
	//    - 在容器中配置：path.repo: ["/usr/share/elasticsearch/snapshots"]
	//    - 确保宿主机备份目录权限正确（通常需要elasticsearch用户权限）
	//
	// 3. Kubernetes部署：
	//    - 使用PersistentVolume挂载备份目录
	//    - 配置适当的SecurityContext和fsGroup
	//
	// 最佳实践：
	// - 设置为true后，ES快照将与MySQL备份存储在同一根目录下，便于统一的灾难恢复
	// - 确保备份目录有足够的磁盘空间
	// - 定期监控快照仓库的健康状态
	AutoCreateRepos bool `json:"auto_create_repos,omitempty" yaml:"auto_create_repos,omitempty"`

	// RepoBasePath 快照仓库的基础路径。
	// 如果AutoCreateRepos为true，将在此路径下创建archival和managed子目录。
	// 如果为空，默认使用 BackupRoot/elasticsearch 作为基础路径。
	//
	// 路径配置注意事项：
	// - Docker部署：此路径应该是容器内的路径，对应的宿主机路径需要通过卷映射配置
	// - 原生部署：使用绝对路径，确保ES进程有访问权限
	// - 网络存储：支持NFS等网络文件系统，但需要考虑性能影响
	RepoBasePath string `json:"repo_base_path,omitempty" yaml:"repo_base_path,omitempty"`
}

// Task 代表一个异步任务的完整状态。
// 这个结构体会被序列化为JSON并持久化，用于跟踪任务进度和故障恢复。
type Task struct {
	// ID 是任务的唯一标识符，通常是UUID。
	ID string `json:"id"`
	// Type 是任务的类型，如备份或恢复。
	Type TaskType `json:"type"`
	// Source 表示任务操作的数据源类型。对于 BackupAll 等分组任务，此字段可能为空。
	Source SourceType `json:"source,omitempty"`
	// Status 是任务的当前状态。
	Status TaskStatus `json:"status"`
	// Progress 是任务的完成进度, 范围是 0-100。
	Progress float64 `json:"progress"`
	// Error 存储任务失败时的错误信息。
	Error string `json:"error,omitempty"`
	// ErrorDetail 存储任务失败时的结构化错误信息。
	ErrorDetail *BackupError `json:"error_detail,omitempty"`
	// StartTime 是任务开始执行的时间。
	StartTime time.Time `json:"start_time"`
	// EndTime 是任务完成或失败的时间。
	EndTime time.Time `json:"end_time,omitempty"`
	// SubTaskIDs 用于分组任务（如BackupAll），记录其包含的所有子任务的ID。
	SubTaskIDs []string `json:"sub_task_ids,omitempty"`
	// Description 是用户提供的任务描述。
	Description string `json:"description,omitempty"`
	// Metadata 用于存储与任务相关的元数据（例如，成功备份后产生的备份记录ID）。
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// === 统一查询相关类型 ===

// BackupFilter 定义了任务查询的过滤条件。
// 默认查询所有任务类型，按开始时间倒序排列（最新的在前）。
// 所有过滤条件都是AND逻辑，即必须同时满足所有指定的条件。
type BackupFilter struct {
	// === 核心过滤字段（支持多值） ===
	TaskTypes   []TaskType   `json:"task_types,omitempty"`   // 任务类型过滤（backup, restore, backup_all, restore_all）
	SourceTypes []SourceType `json:"source_types,omitempty"` // 数据源类型过滤（mysql, elasticsearch）
	BackupTypes []BackupType `json:"backup_types,omitempty"` // 备份类型过滤（archival, chain_initial, chain_incremental）
	Statuses    []TaskStatus `json:"statuses,omitempty"`     // 任务状态过滤（pending, running, completed, failed, cancelled）

	// === 时间范围查询 ===
	StartTime *time.Time `json:"start_time,omitempty"` // 开始时间（包含）
	EndTime   *time.Time `json:"end_time,omitempty"`   // 结束时间（包含）

	// === 搜索功能 ===
	SearchText string `json:"search_text,omitempty"` // 对任务描述进行模糊搜索

	// === 分页参数 ===
	Limit  int `json:"limit,omitempty"`  // 分页大小（默认50，最大1000）
	Offset int `json:"offset,omitempty"` // 偏移量
}

// BackupListResult 定义了任务查询的结果。
// 包含任务列表、总数量和分页信息。
type BackupListResult struct {
	Tasks   []*Task      `json:"tasks"`   // 任务列表（按开始时间倒序排列，最新的在前）
	Total   int          `json:"total"`   // 符合条件的总数量
	HasMore bool         `json:"has_more"` // 是否还有更多数据
	Filter  BackupFilter `json:"filter"`  // 实际使用的过滤条件（包含默认值）
}

// BackupRequest 代表一个单一的备份请求。
type BackupRequest struct {
	SourceType    SourceType `json:"source_type"`               // 要备份的数据源类型
	SourceName    string     `json:"source_name"`               // 要备份的数据源实例名称
	BackupType    BackupType `json:"backup_type"`               // 备份类型
	Description   string     `json:"description"`               // 备份描述
	TargetChainID string     `json:"target_chain_id,omitempty"` // 可选：指定要追加到的增量链ID（仅对BackupTypeChainIncremental有效）
}

// BackupAllRequest 代表一个分组备份请求。
//
// 使用场景：
// - 需要同时备份多个数据源以保证数据一致性
// - 创建跨系统的一致性备份点
// - 批量执行备份操作以提高效率
//
// 原子性说明：
// - Atomic=true: 要么所有备份都成功，要么全部失败
// - CleanupOnFailure=true: 失败时自动清理已完成的备份
// - 对于增量备份，即使失败也不会清理，以保护增量链完整性
type BackupAllRequest struct {
	// Sources 要备份的数据源列表。
	// 可以包含不同类型的数据源（MySQL、ES）和不同的备份类型。
	Sources []BackupRequest `json:"sources"`

	// Description 分组备份的整体描述。
	// 建议包含备份目的、时间点等信息，便于后续管理。
	Description string `json:"description"`

	// Atomic 表示是否需要原子性保证。
	// true: 任一子备份失败时，整个分组备份被标记为失败
	// false: 各子备份独立执行，部分失败不影响其他备份
	Atomic bool `json:"atomic"`

	// CleanupOnFailure 组内任一备份失败时，是否清理已完成的备份。
	// 清理行为说明：
	// - BackupTypeArchival: 失败时会被清理
	// - BackupTypeChainInitial: 失败时会被清理
	// - BackupTypeChainIncremental: 为保护增量链完整性，通常不会被清理
	//
	// 注意：此选项仅在Atomic=true时有效。
	CleanupOnFailure bool `json:"cleanup_on_failure"`
}

// RestoreRequest 代表一个单一的恢复请求。
type RestoreRequest struct {
	SourceType  SourceType `json:"source_type"` // 要恢复的数据源类型
	SourceName  string     `json:"source_name"` // 要恢复到的数据源实例名称
	BackupID    string     `json:"backup_id"`   // 用于恢复的备份记录ID
	Description string     `json:"description"` // 恢复描述
}



// BackupRecord 代表一个物理备份产物的元数据。
// 这个结构体会被序列化为 metadata.json 文件，与备份数据一同存放。
// 它是连接逻辑记录和物理文件的桥梁。
type BackupRecord struct {
	// --- 核心标识信息 ---
	ID          string     `json:"id"`          // 备份产物的唯一标识符
	Timestamp   time.Time  `json:"timestamp"`   // 备份创建的时间戳
	Source      SourceType `json:"source"`      // 数据源类型 (MySQL, ES)
	SourceName  string     `json:"source_name"` // 数据源实例名称 (如 db1, db2)
	Type        BackupType `json:"type"`        // 备份的具体类型 (归档, 链初始, 链增量)
	Path        string     `json:"path"`        // 备份产物在文件系统中的绝对路径
	Description string     `json:"description,omitempty"`

	// --- 增量链专用字段 ---
	// ChainID 标识此备份属于哪条增量链。对于归档备份，此字段为空。
	ChainID string `json:"chain_id,omitempty"`
	// ParentID 指向链中的上一个备份记录ID。对于链的初始备份，此字段为空。
	ParentID string `json:"parent_id,omitempty"`

	// --- 状态和执行信息 ---
	Status        BackupStatus `json:"status"`                 // 备份的最终状态
	Error         string       `json:"error,omitempty"`        // 备份失败时的错误信息
	ErrorDetail   *BackupError `json:"error_detail,omitempty"` // 结构化的错误信息
	Size          int64        `json:"size,omitempty"`         // 备份产物的大小（字节）
	ExecutionTime Duration     `json:"execution_time"`         // 备份操作的执行耗时

	// --- MySQL专用字段 ---
	// BinlogFile 记录了全量备份结束时或增量备份的结束点的binlog文件名。
	BinlogFile string `json:"binlog_file,omitempty"`
	// BinlogPos 记录了对应的binlog文件中的位置。
	BinlogPos uint32 `json:"binlog_pos,omitempty"`

	// --- 提供者特定的元数据 ---
	// Extra 用于存储任意 provider-specific 的元数据 (例如, ES快照的名称)。
	// 通常是一个序列化后的JSON字符串。
	Extra string `json:"extra,omitempty"`
}

// IsPartOfChain 是一个辅助方法，用于判断一个备份记录是否属于某个增量链。
func (br *BackupRecord) IsPartOfChain() bool {
	return br.ChainID != ""
}

// ChainMeta 代表一条增量备份链的聚合元数据，通常存储在链目录下的 chain_meta.json 文件中。
// 它的主要目的是为了快速查询链的信息，而无需读取链内所有备份记录的元数据。
type ChainMeta struct {
	ChainID   string   `json:"chain_id"`   // 链的唯一标识符
	BackupIDs []string `json:"backup_ids"` // 有序的备份记录ID列表，从初始全量备份到最后的增量备份。
}

// IncrementalChain 是一个动态构建的结构体，代表一个完整的、可用于恢复的增量备份链。
// 它通常在API层面（如 ListIncrementalChains）被构建，用于向用户展示。
type IncrementalChain struct {
	ChainID string          `json:"chain_id"` // 链的唯一ID
	Backups []*BackupRecord `json:"backups"`  // 链内所有备份记录的完整信息，按时间顺序排列。
}

// ArchivalBackup 是一个动态构建的结构体，代表一个独立的归档备份。
// 它在API层面被构建，用于向用户清晰地展示独立的备份点。
type ArchivalBackup struct {
	Record *BackupRecord `json:"record"`
}

// --- 新的恢复配置类型（重构新增） ---

// RestoreConfig 统一的恢复配置。
// 这是重构后的核心恢复配置类型，简化了恢复操作的配置。
//
// 设计理念：
// - 默认安全：CreateSafetyBackup 和 RollbackOnFailure 默认为 true
// - 完全重建：默认使用完全重建策略（MySQL删除重建数据库，ES删除所有索引）
// - 简单易用：大部分情况下使用默认配置即可
type RestoreConfig struct {
	// 基础信息
	SourceType  SourceType `json:"source_type"`           // 数据源类型（MySQL, Elasticsearch）
	SourceName  string     `json:"source_name"`           // 数据源名称（数据库名/索引名）
	BackupID    string     `json:"backup_id"`             // 备份记录ID
	Description string     `json:"description,omitempty"` // 恢复描述

	// 安全配置（默认启用）
	CreateSafetyBackup bool `json:"create_safety_backup"` // 恢复前创建安全备份（默认 true）
	RollbackOnFailure  bool `json:"rollback_on_failure"`  // 失败时自动回滚（默认 true）

	// 高级选项
	SkipDataCleanup bool `json:"skip_data_cleanup"` // 跳过数据清理，保持旧行为（默认 false）
	Force           bool `json:"force,omitempty"`   // 强制恢复：中断正在运行的备份任务（默认 false）

	// 回调配置
	OnSuccess RestoreSuccessCallback `json:"-"` // 恢复成功时的回调函数，不序列化
}

// BatchRestoreConfig 批量恢复配置。
// 用于同时恢复多个数据源，支持原子性操作。
//
// 与旧的 RestoreAllRequest 相比的改进：
// - 每个恢复都有独立的安全配置
// - 简化了全局配置选项
// - 统一了单个恢复和批量恢复的行为
type BatchRestoreConfig struct {
	Restores    []RestoreConfig `json:"restores"`    // 要恢复的数据源列表
	Description string          `json:"description"` // 批量恢复描述
	Atomic      bool            `json:"atomic"`      // 是否原子操作（默认 true）
	Parallel    bool            `json:"parallel"`    // 是否并行执行（仅在非原子时有效，默认 false）
	Force       bool            `json:"force,omitempty"` // 强制恢复：中断正在运行的备份任务（默认 false）

	// 回调配置
	OnSuccess RestoreSuccessCallback `json:"-"` // 分组恢复成功时的回调函数，不序列化
}

// NewRestoreConfig 创建一个具有安全默认值的恢复配置。
// 这是推荐的创建恢复配置的方式。
//
// 默认配置：
// - CreateSafetyBackup: true  （恢复前创建安全备份）
// - RollbackOnFailure: true   （失败时自动回滚）
// - SkipDataCleanup: false    （执行完全重建）
func NewRestoreConfig(sourceType SourceType, sourceName, backupID string) RestoreConfig {
	return RestoreConfig{
		SourceType:         sourceType,
		SourceName:         sourceName,
		BackupID:           backupID,
		CreateSafetyBackup: true,  // 默认创建安全备份
		RollbackOnFailure:  true,  // 默认失败回滚
		SkipDataCleanup:    false, // 默认执行完全重建
	}
}

// NewBatchRestoreConfig 创建一个批量恢复配置。
//
// 参数：
// - restores: 恢复配置列表
// - atomic: 是否需要原子性（true=要么全成功要么全失败，false=独立执行）
func NewBatchRestoreConfig(restores []RestoreConfig, atomic bool) BatchRestoreConfig {
	return BatchRestoreConfig{
		Restores: restores,
		Atomic:   atomic,
		Parallel: false, // 默认串行执行更安全
	}
}

// WithDescription 为恢复配置添加描述（链式调用）。
func (c RestoreConfig) WithDescription(desc string) RestoreConfig {
	c.Description = desc
	return c
}

// WithoutSafetyBackup 禁用安全备份（不推荐，仅在特殊情况下使用）。
func (c RestoreConfig) WithoutSafetyBackup() RestoreConfig {
	c.CreateSafetyBackup = false
	return c
}

// WithoutRollback 禁用失败回滚（不推荐，仅在特殊情况下使用）。
func (c RestoreConfig) WithoutRollback() RestoreConfig {
	c.RollbackOnFailure = false
	return c
}

// SkipCleanup 跳过数据清理，保持旧的恢复行为（不推荐）。
func (c RestoreConfig) SkipCleanup() RestoreConfig {
	c.SkipDataCleanup = true
	return c
}

// StorageObject 表示存储对象的元数据信息。
// 用于 Backend.List 方法的返回值，提供对象的基本信息。
// 这个结构体支持本地文件系统和云存储的统一抽象。
type StorageObject struct {
	// Key 是对象的唯一标识符，通常是相对路径
	// 例如："mysql/backup-123/data.sql.gz", "metadata/backup-123/metadata.json"
	Key string `json:"key"`

	// Size 是对象的大小（字节）
	Size int64 `json:"size"`

	// LastModified 是对象的最后修改时间
	LastModified time.Time `json:"last_modified"`

	// ETag 是实体标签，用于版本控制和完整性检查
	// 对于本地文件系统可能为空，对于云存储通常是文件的哈希值
	ETag string `json:"etag,omitempty"`
}

// CloudStorageConfig 是云存储的配置结构体。
// 支持 S3、GCS、Azure 三种云存储类型，使用扁平化设计包含所有类型的配置字段。
// 用户根据 Type 字段选择云存储类型，只需配置对应类型的字段。
type CloudStorageConfig struct {
	// Enabled 控制是否启用云存储
	Enabled bool `json:"enabled" yaml:"enabled"`

	// Type 指定云存储类型，支持 "s3", "gcs", "azure"
	Type string `json:"type" yaml:"type"`

	// S3 配置字段
	// Bucket 是 S3 存储桶名称（S3 和 GCS 共用）
	Bucket string `json:"bucket,omitempty" yaml:"bucket,omitempty"`
	// Region 是 S3 区域
	Region string `json:"region,omitempty" yaml:"region,omitempty"`
	// AccessKey 是 S3 访问密钥 ID
	AccessKey string `json:"access_key,omitempty" yaml:"access_key,omitempty"`
	// SecretKey 是 S3 秘密访问密钥（敏感字段）
	SecretKey string `json:"-" yaml:"secret_key,omitempty"`
	// Endpoint 是 S3 兼容存储的自定义端点（如 MinIO）
	Endpoint string `json:"endpoint,omitempty" yaml:"endpoint,omitempty"`

	// GCS 配置字段
	// ProjectID 是 GCS 项目 ID
	ProjectID string `json:"project_id,omitempty" yaml:"project_id,omitempty"`
	// CredentialsFile 是 GCS 服务账户凭据文件路径
	CredentialsFile string `json:"credentials_file,omitempty" yaml:"credentials_file,omitempty"`

	// Azure 配置字段
	// Container 是 Azure 容器名称
	Container string `json:"container,omitempty" yaml:"container,omitempty"`
	// AccountName 是 Azure 存储账户名称
	AccountName string `json:"account_name,omitempty" yaml:"account_name,omitempty"`
	// AccountKey 是 Azure 存储账户密钥（敏感字段）
	AccountKey string `json:"-" yaml:"account_key,omitempty"`
}

// ConnectivityTestResult 云存储连通性测试结果
type ConnectivityTestResult struct {
	Success     bool                 `json:"success"`
	Duration    time.Duration        `json:"duration"`
	Steps       []ConnectivityStep   `json:"steps"`
	Error       string              `json:"error,omitempty"`
	Suggestions []string            `json:"suggestions,omitempty"`
	StorageType string              `json:"storage_type"`
}

// ConnectivityStep 连通性测试步骤
type ConnectivityStep struct {
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Success     bool          `json:"success"`
	Duration    time.Duration `json:"duration"`
	Error       string        `json:"error,omitempty"`
}

// === 备份名称生成和格式化函数 ===

// GenerateBackupName 为单个备份生成标准化的名称。
// 格式：{数据源类型}_{数据源名称}_{备份类型}_{时间戳}
// 例如：mysql_mydb_归档_2024-01-15_14:30
func GenerateBackupName(sourceType SourceType, sourceName string, backupType BackupType, timestamp time.Time) string {
	timeStr := timestamp.Format("2006-01-02_15:04")
	typeStr := map[BackupType]string{
		BackupTypeArchival:         "归档",
		BackupTypeChainInitial:     "增量链初始",
		BackupTypeChainIncremental: "增量",
	}[backupType]
	return fmt.Sprintf("%s_%s_%s_%s", sourceType, sourceName, typeStr, timeStr)
}

// GenerateGroupBackupName 为分组备份生成标准化的名称。
// 格式：分组备份_{数据源数量}个数据源_{时间戳}
// 例如：分组备份_3个数据源_2024-01-15_14:30
func GenerateGroupBackupName(sourceCount int, timestamp time.Time) string {
	timeStr := timestamp.Format("2006-01-02_15:04")
	return fmt.Sprintf("分组备份_%d个数据源_%s", sourceCount, timeStr)
}

// FormatBackupSize 将字节数格式化为可读的大小字符串。
// 例如：1024 -> "1.0KB", 1048576 -> "1.0MB", 1073741824 -> "1.0GB"
func FormatBackupSize(sizeBytes int64) string {
	if sizeBytes == 0 {
		return "0B"
	}

	const unit = 1024
	if sizeBytes < unit {
		return fmt.Sprintf("%dB", sizeBytes)
	}

	div, exp := int64(unit), 0
	for n := sizeBytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"KB", "MB", "GB", "TB", "PB"}
	return fmt.Sprintf("%.1f%s", float64(sizeBytes)/float64(div), units[exp])
}

// === 备份删除相关类型 ===

// BackupDeletionInfo 备份删除影响信息。
// 用于帮助SDK调用方向用户展示删除操作的详细影响。
type BackupDeletionInfo struct {
	TaskID        string                      `json:"task_id"`         // 任务ID
	TaskType      TaskType                    `json:"task_type"`       // 任务类型
	SourceType    SourceType                  `json:"source_type"`     // 数据源类型
	Status        TaskStatus                  `json:"status"`          // 任务状态
	HasBackupData bool                        `json:"has_backup_data"` // 是否有备份数据
	Error         string                      `json:"error,omitempty"` // 错误信息

	// 分组备份信息
	IsGroupBackup bool                        `json:"is_group_backup"`           // 是否分组备份
	SubTaskCount  int                         `json:"sub_task_count,omitempty"`  // 子任务数量
	SubBackups    []BackupRecordDeletionInfo  `json:"sub_backups,omitempty"`     // 子备份信息

	// 单个备份信息（嵌入）
	BackupRecordDeletionInfo
}

// BackupRecordDeletionInfo 单个备份记录的删除信息。
// 包含备份的基本信息和增量链相关信息。
type BackupRecordDeletionInfo struct {
	BackupID          string       `json:"backup_id"`           // 备份ID
	BackupType        BackupType   `json:"backup_type"`         // 备份类型
	HasBackupData     bool         `json:"has_backup_data"`     // 是否有备份数据
	Timestamp         time.Time    `json:"timestamp"`           // 备份时间

	// 增量链信息
	IsIncrementalChain bool         `json:"is_incremental_chain"`           // 是否增量链
	ChainID           string        `json:"chain_id,omitempty"`             // 链ID
	ChainBackupCount  int           `json:"chain_backup_count,omitempty"`   // 链中备份数量
	ChainStartTime    time.Time     `json:"chain_start_time,omitempty"`     // 链开始时间
	ChainEndTime      time.Time     `json:"chain_end_time,omitempty"`       // 链结束时间
}
