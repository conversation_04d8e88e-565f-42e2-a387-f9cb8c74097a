#!/bin/bash

# 备份系统单元测试运行脚本
# 提供完整的测试覆盖率报告和测试结果

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}🧪 运行备份系统单元测试${NC}"
echo "=================================================="

# 创建测试输出目录
mkdir -p test_output

# 1. 运行所有单元测试
echo -e "\n${YELLOW}📋 运行所有单元测试...${NC}"
if go test -v ./... -coverprofile=test_output/coverage.out -covermode=atomic > test_output/test_results.txt 2>&1; then
    echo -e "${GREEN}✅ 所有测试通过${NC}"
    cat test_output/test_results.txt
else
    echo -e "${RED}❌ 测试失败${NC}"
    cat test_output/test_results.txt
    exit 1
fi

# 2. 生成覆盖率报告
echo -e "\n${YELLOW}📊 生成测试覆盖率报告...${NC}"
go tool cover -html=test_output/coverage.out -o test_output/coverage.html

# 3. 显示覆盖率统计
echo -e "\n${YELLOW}📈 测试覆盖率统计:${NC}"
go tool cover -func=test_output/coverage.out | tail -1

# 4. 按模块显示覆盖率
echo -e "\n${YELLOW}📦 各模块覆盖率详情:${NC}"
echo "=================================================="
go tool cover -func=test_output/coverage.out | grep -E "(pkg/types|internal/)" | sort

# 5. 运行基准测试
echo -e "\n${YELLOW}⚡ 运行性能基准测试...${NC}"
go test -bench=. -benchmem ./... > test_output/benchmark_results.txt 2>&1
echo "基准测试结果已保存到 test_output/benchmark_results.txt"

# 6. 运行竞态条件检测
echo -e "\n${YELLOW}🔍 运行竞态条件检测...${NC}"
if go test -race ./... > test_output/race_results.txt 2>&1; then
    echo -e "${GREEN}✅ 未发现竞态条件${NC}"
else
    echo -e "${RED}⚠️  发现潜在竞态条件，请查看 test_output/race_results.txt${NC}"
fi

# 7. 生成测试报告摘要
echo -e "\n${YELLOW}📋 生成测试报告摘要...${NC}"
cat > test_output/test_summary.md << EOF
# 备份系统测试报告

## 测试执行时间
- 执行时间: $(date)
- Git提交: $(git rev-parse --short HEAD 2>/dev/null || echo "N/A")

## 测试覆盖率
\`\`\`
$(go tool cover -func=test_output/coverage.out | tail -1)
\`\`\`

## 各模块覆盖率
\`\`\`
$(go tool cover -func=test_output/coverage.out | grep -E "(pkg/types|internal/)" | sort)
\`\`\`

## 测试文件列表
$(find . -name "*_test.go" -not -path "./vendor/*" | sort)

## 生成的文件
- 详细覆盖率报告: test_output/coverage.html
- 测试结果: test_output/test_results.txt
- 基准测试: test_output/benchmark_results.txt
- 竞态检测: test_output/race_results.txt
EOF

echo -e "\n${GREEN}🎉 测试完成！${NC}"
echo "=================================================="
echo "📁 测试输出目录: test_output/"
echo "🌐 覆盖率报告: test_output/coverage.html"
echo "📄 测试摘要: test_output/test_summary.md"
echo ""
echo -e "${BLUE}💡 提示:${NC}"
echo "- 在浏览器中打开 test_output/coverage.html 查看详细覆盖率"
echo "- 查看 test_output/test_summary.md 获取完整测试报告"
echo "- 运行 'go test -v ./pkg/types' 测试特定模块"
