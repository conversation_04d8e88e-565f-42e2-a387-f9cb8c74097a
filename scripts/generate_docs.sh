#!/bin/bash

# UniBackup API文档生成脚本
# 使用godoc生成标准的Go API文档

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出目录
DOCS_DIR="docs/api"
PORT=6060

echo -e "${BLUE}=== UniBackup API文档生成工具 ===${NC}"

# 检查godoc是否安装
GODOC_PATH=""
if command -v godoc &> /dev/null; then
    GODOC_PATH="godoc"
elif [ -f "$HOME/go/bin/godoc" ]; then
    GODOC_PATH="$HOME/go/bin/godoc"
else
    echo -e "${YELLOW}⚠️  godoc未安装，正在安装...${NC}"
    go install golang.org/x/tools/cmd/godoc@latest
    
    if [ -f "$HOME/go/bin/godoc" ]; then
        GODOC_PATH="$HOME/go/bin/godoc"
    else
        echo -e "${RED}❌ godoc安装失败，请手动安装：go install golang.org/x/tools/cmd/godoc@latest${NC}"
        exit 1
    fi
fi

# 创建文档目录
mkdir -p "$DOCS_DIR"

echo -e "${YELLOW}📚 生成API文档...${NC}"

# 生成包文档
echo -e "${BLUE}生成包级别文档...${NC}"

# 主包文档
echo "# UniBackup API 文档" > "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"
echo "本目录包含UniBackup SDK的API文档。" >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"
echo "## 查看方式" >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"
echo "### 1. 在线查看" >> "$DOCS_DIR/README.md"
echo "启动本地godoc服务器：" >> "$DOCS_DIR/README.md"
echo '```bash' >> "$DOCS_DIR/README.md"
echo "godoc -http=:$PORT" >> "$DOCS_DIR/README.md"
echo '```' >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"
echo "然后在浏览器中访问：http://localhost:$PORT/pkg/unibackup/" >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"
echo "### 2. 命令行查看" >> "$DOCS_DIR/README.md"
echo '```bash' >> "$DOCS_DIR/README.md"
echo "go doc unibackup" >> "$DOCS_DIR/README.md"
echo "go doc unibackup/pkg/types" >> "$DOCS_DIR/README.md"
echo "go doc unibackup.BackupManager" >> "$DOCS_DIR/README.md"
echo '```' >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"

# 生成核心包的文档
echo -e "${BLUE}生成核心包文档...${NC}"

# 主包
echo "## 主包 (unibackup)" >> "$DOCS_DIR/README.md"
echo '```' >> "$DOCS_DIR/README.md"
go doc . >> "$DOCS_DIR/README.md" 2>/dev/null || echo "无法生成主包文档"
echo '```' >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"

# types包
echo "## 类型定义 (pkg/types)" >> "$DOCS_DIR/README.md"
echo '```' >> "$DOCS_DIR/README.md"
go doc ./pkg/types >> "$DOCS_DIR/README.md" 2>/dev/null || echo "无法生成types包文档"
echo '```' >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"

# BackupManager接口
echo "## BackupManager接口" >> "$DOCS_DIR/README.md"
echo '```' >> "$DOCS_DIR/README.md"
go doc ./pkg/unibackup.BackupManager >> "$DOCS_DIR/README.md" 2>/dev/null || echo "无法生成BackupManager文档"
echo '```' >> "$DOCS_DIR/README.md"
echo "" >> "$DOCS_DIR/README.md"

# 生成快速参考
echo -e "${BLUE}生成快速参考...${NC}"
cat > "$DOCS_DIR/quick_reference.md" << 'EOF'
# UniBackup API 快速参考

## 核心接口

### BackupManager
主要的备份管理接口，提供所有备份和恢复功能。

#### 异步操作
- `BackupAsync(ctx, sourceType, sourceName, backupType, description) (taskID, error)`
- `RestoreAsync(ctx, sourceType, sourceName, backupID) (taskID, error)`
- `BackupAllAsync(ctx, request) (taskID, error)`
- `RestoreAllAsync(ctx, request) (taskID, error)`

#### 任务管理
- `GetTask(taskID) (*Task, error)`
- `ListTasks() ([]*Task, error)`
- `CancelTask(taskID) error`
- `ClearOldTasks() error`

#### 同步操作（向后兼容）
- `Backup(ctx, sourceType, sourceName, backupType, description) (*BackupRecord, *BackupError)`
- `Restore(ctx, sourceType, sourceName, backupID) *BackupError`

#### 备份管理
- `ListArchivalBackups(ctx, sourceType) ([]ArchivalBackup, *BackupError)`
- `ListIncrementalChains(ctx, sourceType) ([]IncrementalChain, *BackupError)`
- `DeleteBackup(ctx, sourceType, backupID) error`

#### 系统管理
- `Shutdown() error`

## 核心类型

### SourceType
- `MySQL` - MySQL数据源
- `Elasticsearch` - Elasticsearch数据源

### BackupType
- `BackupTypeArchival` - 归档全量备份
- `BackupTypeChainInitial` - 增量链初始备份
- `BackupTypeChainIncremental` - 增量链增量备份

### TaskStatus
- `TaskStatusPending` - 等待执行
- `TaskStatusRunning` - 正在执行
- `TaskStatusCompleted` - 执行成功
- `TaskStatusFailed` - 执行失败
- `TaskStatusCancelled` - 已取消

## 配置结构

### Config
主配置结构，包含所有必要的配置信息。

### MySQLConfig
MySQL数据源配置，包含连接信息和工具路径。

### ESConfig
Elasticsearch数据源配置，包含集群地址和仓库设置。

## 使用示例

```go
// 创建配置
cfg := &types.Config{
    BackupRoot: "/data/backups",
    Logger:     slog.Default(),
    MySQL: &types.MySQLConfig{
        Host:     "localhost",
        Port:     3306,
        User:     "backup_user",
        Password: "password",
        DBName:   "mydb",
    },
}

// 创建管理器
manager, err := unibackup.NewManager(cfg)
if err != nil {
    log.Fatal(err)
}
defer manager.Shutdown()

// 异步备份
taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "每日备份")
if err != nil {
    log.Fatal(err)
}

// 监控任务
task, err := manager.GetTask(taskID)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("任务状态: %s\n", task.Status)
```
EOF

echo -e "${GREEN}✅ API文档生成完成！${NC}"
echo -e "${BLUE}文档位置: $DOCS_DIR/${NC}"
echo ""
echo -e "${YELLOW}📖 查看文档的方式：${NC}"
echo -e "1. 查看生成的文档: cat $DOCS_DIR/README.md"
echo -e "2. 查看快速参考: cat $DOCS_DIR/quick_reference.md"
echo -e "3. 启动godoc服务器: godoc -http=:$PORT"
echo -e "4. 浏览器访问: http://localhost:$PORT/pkg/unibackup/"
echo ""
echo -e "${GREEN}🎉 文档生成完成！${NC}"
