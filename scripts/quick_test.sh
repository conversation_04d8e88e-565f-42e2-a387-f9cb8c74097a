#!/bin/bash

# 快速测试脚本 - 用于开发过程中的快速验证

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}⚡ 快速测试模式${NC}"
echo "=========================="

# 解析命令行参数
MODULE=""
VERBOSE=false
COVERAGE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--module)
            MODULE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  -m, --module MODULE    只测试指定模块 (如: pkg/types, internal/task)"
            echo "  -v, --verbose         显示详细输出"
            echo "  -c, --coverage        显示覆盖率"
            echo "  -h, --help           显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                    # 运行所有测试"
            echo "  $0 -m pkg/types       # 只测试types模块"
            echo "  $0 -v -c              # 详细输出并显示覆盖率"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 -h 或 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 构建测试命令
TEST_CMD="go test"

if [ "$VERBOSE" = true ]; then
    TEST_CMD="$TEST_CMD -v"
fi

if [ "$COVERAGE" = true ]; then
    TEST_CMD="$TEST_CMD -cover"
fi

# 确定测试目标
if [ -n "$MODULE" ]; then
    TEST_TARGET="./$MODULE"
    echo -e "${YELLOW}🎯 测试模块: $MODULE${NC}"
else
    TEST_TARGET="./..."
    echo -e "${YELLOW}🎯 测试所有模块${NC}"
fi

# 运行测试
echo -e "\n${YELLOW}🧪 执行测试...${NC}"
if eval "$TEST_CMD $TEST_TARGET"; then
    echo -e "\n${GREEN}✅ 测试通过！${NC}"
else
    echo -e "\n${RED}❌ 测试失败！${NC}"
    exit 1
fi

# 如果指定了模块，显示该模块的测试文件
if [ -n "$MODULE" ]; then
    echo -e "\n${BLUE}📁 $MODULE 模块的测试文件:${NC}"
    find "./$MODULE" -name "*_test.go" 2>/dev/null | sort || echo "未找到测试文件"
fi

echo -e "\n${BLUE}💡 提示:${NC}"
echo "- 运行完整测试: ./scripts/run_tests.sh"
echo "- 查看帮助: $0 --help"
echo "- 测试特定函数: go test -v -run TestFunctionName ./module"
