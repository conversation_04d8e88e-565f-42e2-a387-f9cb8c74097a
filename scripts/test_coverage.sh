#!/bin/bash

# UniBackup 测试覆盖率报告生成脚本
# 此脚本用于生成详细的测试覆盖率报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 输出目录
OUTPUT_DIR="test_output"
mkdir -p "$OUTPUT_DIR"

echo -e "${BLUE}🧪 UniBackup 测试覆盖率报告生成${NC}"
echo "=================================================="

# 清理之前的输出
echo -e "${YELLOW}🧹 清理之前的测试输出...${NC}"
rm -f "$OUTPUT_DIR"/*.out "$OUTPUT_DIR"/*.html "$OUTPUT_DIR"/*.txt

# 运行测试并生成覆盖率数据
echo -e "${YELLOW}🔬 运行测试并收集覆盖率数据...${NC}"
go test -v -race -coverprofile="$OUTPUT_DIR/coverage.out" -covermode=atomic ./... > "$OUTPUT_DIR/test_results.txt" 2>&1

# 检查测试是否成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 测试执行成功${NC}"
else
    echo -e "${RED}❌ 测试执行失败${NC}"
    echo "查看详细错误信息: cat $OUTPUT_DIR/test_results.txt"
    exit 1
fi

# 生成 HTML 覆盖率报告
echo -e "${YELLOW}📊 生成 HTML 覆盖率报告...${NC}"
go tool cover -html="$OUTPUT_DIR/coverage.out" -o "$OUTPUT_DIR/coverage.html"

# 生成覆盖率统计
echo -e "${YELLOW}📈 生成覆盖率统计...${NC}"
go tool cover -func="$OUTPUT_DIR/coverage.out" > "$OUTPUT_DIR/coverage_stats.txt"

# 解析总体覆盖率
TOTAL_COVERAGE=$(go tool cover -func="$OUTPUT_DIR/coverage.out" | grep "total:" | awk '{print $3}')

echo ""
echo "=================================================="
echo -e "${BLUE}📋 测试覆盖率报告${NC}"
echo "=================================================="

# 显示总体覆盖率
if [ ! -z "$TOTAL_COVERAGE" ]; then
    COVERAGE_NUM=$(echo "$TOTAL_COVERAGE" | sed 's/%//')
    if (( $(echo "$COVERAGE_NUM >= 80" | bc -l) )); then
        echo -e "${GREEN}🎯 总体覆盖率: $TOTAL_COVERAGE${NC}"
    elif (( $(echo "$COVERAGE_NUM >= 60" | bc -l) )); then
        echo -e "${YELLOW}⚠️  总体覆盖率: $TOTAL_COVERAGE${NC}"
    else
        echo -e "${RED}🚨 总体覆盖率: $TOTAL_COVERAGE${NC}"
    fi
else
    echo -e "${RED}❌ 无法获取总体覆盖率${NC}"
fi

echo ""
echo -e "${BLUE}📦 各模块覆盖率详情:${NC}"
echo "--------------------------------------------------"

# 按模块显示覆盖率
go tool cover -func="$OUTPUT_DIR/coverage.out" | grep -E "(pkg|internal)" | while read line; do
    if [[ $line == *".go:"* ]]; then
        file=$(echo "$line" | awk '{print $1}')
        func=$(echo "$line" | awk '{print $2}')
        coverage=$(echo "$line" | awk '{print $3}')
        
        # 提取模块名
        module=$(echo "$file" | cut -d'/' -f1-2)
        
        # 根据覆盖率着色
        coverage_num=$(echo "$coverage" | sed 's/%//')
        if (( $(echo "$coverage_num >= 80" | bc -l) )); then
            color=$GREEN
        elif (( $(echo "$coverage_num >= 60" | bc -l) )); then
            color=$YELLOW
        else
            color=$RED
        fi
        
        printf "${color}%-40s %s${NC}\n" "$module" "$coverage"
    fi
done | sort | uniq

echo ""
echo "--------------------------------------------------"

# 生成模块级别的覆盖率统计
echo -e "${BLUE}🎯 模块覆盖率统计:${NC}"
echo "--------------------------------------------------"

# 主要模块列表
modules=(
    "pkg/types"
    "pkg/unibackup"
    "internal/api"
    "internal/task"
    "internal/storage"
    "internal/config"
    "internal/lock"
    "internal/provider"
)

for module in "${modules[@]}"; do
    if [ -d "$module" ]; then
        # 计算模块覆盖率
        module_coverage=$(go test -coverprofile="/tmp/${module//\//_}_coverage.out" "./$module" 2>/dev/null && go tool cover -func="/tmp/${module//\//_}_coverage.out" | grep "total:" | awk '{print $3}' || echo "0.0%")
        
        # 清理临时文件
        rm -f "/tmp/${module//\//_}_coverage.out"
        
        # 根据覆盖率着色
        coverage_num=$(echo "$module_coverage" | sed 's/%//')
        if (( $(echo "$coverage_num >= 80" | bc -l) )); then
            color=$GREEN
            status="优秀"
        elif (( $(echo "$coverage_num >= 60" | bc -l) )); then
            color=$YELLOW
            status="良好"
        elif (( $(echo "$coverage_num >= 40" | bc -l) )); then
            color=$YELLOW
            status="一般"
        else
            color=$RED
            status="需改进"
        fi
        
        printf "${color}%-25s %8s (%s)${NC}\n" "$module" "$module_coverage" "$status"
    fi
done

echo ""
echo "--------------------------------------------------"

# 查找覆盖率较低的函数
echo -e "${BLUE}🔍 需要改进的函数 (覆盖率 < 50%):${NC}"
echo "--------------------------------------------------"

go tool cover -func="$OUTPUT_DIR/coverage.out" | awk '$3 != "total:" && $3 != "" {
    coverage = $3
    gsub(/%/, "", coverage)
    if (coverage < 50 && coverage > 0) {
        printf "%-50s %s\n", $1 ":" $2, $3
    }
}' | head -20

echo ""
echo "--------------------------------------------------"

# 生成测试建议
echo -e "${BLUE}💡 测试改进建议:${NC}"
echo "--------------------------------------------------"

# 检查是否有未测试的文件
echo "🔍 检查未测试的文件..."
find . -name "*.go" -not -path "./vendor/*" -not -path "./.git/*" -not -name "*_test.go" | while read file; do
    test_file="${file%%.go}_test.go"
    if [ ! -f "$test_file" ]; then
        echo "  ⚠️  缺少测试文件: $test_file (对应 $file)"
    fi
done

echo ""
echo "📊 测试统计:"
total_tests=$(grep -c "^=== RUN" "$OUTPUT_DIR/test_results.txt" || echo "0")
passed_tests=$(grep -c "^--- PASS:" "$OUTPUT_DIR/test_results.txt" || echo "0")
failed_tests=$(grep -c "^--- FAIL:" "$OUTPUT_DIR/test_results.txt" || echo "0")

echo "  总测试数: $total_tests"
echo "  通过: $passed_tests"
echo "  失败: $failed_tests"

echo ""
echo "=================================================="
echo -e "${GREEN}✅ 覆盖率报告生成完成${NC}"
echo "=================================================="
echo ""
echo "📁 生成的文件:"
echo "  - HTML 报告: $OUTPUT_DIR/coverage.html"
echo "  - 覆盖率数据: $OUTPUT_DIR/coverage.out"
echo "  - 详细统计: $OUTPUT_DIR/coverage_stats.txt"
echo "  - 测试结果: $OUTPUT_DIR/test_results.txt"
echo ""
echo "🌐 查看 HTML 报告:"
echo "  open $OUTPUT_DIR/coverage.html"
echo ""

# 如果是 macOS，自动打开 HTML 报告
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🚀 自动打开 HTML 报告..."
    open "$OUTPUT_DIR/coverage.html"
fi

echo -e "${BLUE}🎯 目标覆盖率: 80%+${NC}"
if [ ! -z "$TOTAL_COVERAGE" ]; then
    coverage_num=$(echo "$TOTAL_COVERAGE" | sed 's/%//')
    if (( $(echo "$coverage_num >= 80" | bc -l) )); then
        echo -e "${GREEN}🏆 恭喜！已达到目标覆盖率！${NC}"
    else
        remaining=$(echo "80 - $coverage_num" | bc -l)
        echo -e "${YELLOW}📈 还需提升 ${remaining}% 覆盖率达到目标${NC}"
    fi
fi
