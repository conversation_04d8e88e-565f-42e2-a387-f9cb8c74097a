package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"
)

// TestMatrixValidationSuite 测试矩阵验证套件
type TestMatrixValidationSuite struct {
	logger  *zap.Logger
	manager unibackup.BackupManager
	config  *types.Config
}

// TestMatrix 测试矩阵定义
type TestMatrix struct {
	DataSource   types.SourceType
	StorageType  string
	BackupType   types.BackupType
	Operations   []string
	ExpectedPass bool
	Description  string
}

// NewTestMatrixValidationSuite 创建测试矩阵验证套件
func NewTestMatrixValidationSuite(logger *zap.Logger, config *types.Config) *TestMatrixValidationSuite {
	return &TestMatrixValidationSuite{
		logger: logger,
		config: config,
	}
}

// Setup 初始化测试环境
func (suite *TestMatrixValidationSuite) Setup() error {
	manager, err := unibackup.NewManager(suite.config)
	if err != nil {
		return fmt.Errorf("创建备份管理器失败: %w", err)
	}
	suite.manager = manager
	return nil
}

// Cleanup 清理测试环境
func (suite *TestMatrixValidationSuite) Cleanup() {
	if suite.manager != nil {
		suite.manager.Shutdown()
	}
}

// GetTestMatrix 获取完整的测试矩阵
func (suite *TestMatrixValidationSuite) GetTestMatrix() []TestMatrix {
	return []TestMatrix{
		// MySQL × 本地存储 × 各种操作
		{
			DataSource:   types.MySQL,
			StorageType:  "local",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "MySQL本地存储归档备份完整操作",
		},
		{
			DataSource:   types.MySQL,
			StorageType:  "local",
			BackupType:   types.BackupTypeChainInitial,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "MySQL本地存储增量链初始备份完整操作",
		},
		{
			DataSource:   types.MySQL,
			StorageType:  "local",
			BackupType:   types.BackupTypeChainIncremental,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "MySQL本地存储增量链增量备份完整操作",
		},

		// MySQL × 云存储 × 各种操作
		{
			DataSource:   types.MySQL,
			StorageType:  "cloud",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "MySQL云存储归档备份完整操作",
		},
		{
			DataSource:   types.MySQL,
			StorageType:  "cloud",
			BackupType:   types.BackupTypeChainInitial,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "MySQL云存储增量链备份完整操作",
		},

		// Elasticsearch × 本地存储 × 各种操作
		{
			DataSource:   types.Elasticsearch,
			StorageType:  "local",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "Elasticsearch本地存储归档快照完整操作",
		},

		// Elasticsearch × 云存储 × 各种操作
		{
			DataSource:   types.Elasticsearch,
			StorageType:  "cloud",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"backup", "restore", "delete", "list"},
			ExpectedPass: true,
			Description:  "Elasticsearch云存储归档快照完整操作",
		},

		// 分组操作 × 混合存储
		{
			DataSource:   types.MySQL, // 代表分组操作
			StorageType:  "mixed",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"group-backup", "group-restore", "group-delete"},
			ExpectedPass: true,
			Description:  "分组操作混合存储协同测试",
		},

		// 边界情况测试
		{
			DataSource:   types.MySQL,
			StorageType:  "local",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"concurrent-backup", "stress-test"},
			ExpectedPass: true,
			Description:  "MySQL并发和压力测试",
		},
		{
			DataSource:   types.Elasticsearch,
			StorageType:  "cloud",
			BackupType:   types.BackupTypeArchival,
			Operations:   []string{"large-index", "multi-index"},
			ExpectedPass: true,
			Description:  "Elasticsearch大索引和多索引测试",
		},
	}
}

// RunMatrixValidation 运行测试矩阵验证
func (suite *TestMatrixValidationSuite) RunMatrixValidation() error {
	matrix := suite.GetTestMatrix()

	suite.logger.Info("开始测试矩阵验证", zap.Int("total_tests", len(matrix)))

	passedTests := 0
	failedTests := 0
	skippedTests := 0

	for i, test := range matrix {
		suite.logger.Info("执行测试矩阵项",
			zap.Int("index", i+1),
			zap.Int("total", len(matrix)),
			zap.String("description", test.Description),
			zap.String("data_source", string(test.DataSource)),
			zap.String("storage_type", test.StorageType),
			zap.String("backup_type", string(test.BackupType)))

		result, err := suite.executeMatrixTest(test)
		if err != nil {
			if test.ExpectedPass {
				suite.logger.Error("测试矩阵项失败",
					zap.Error(err),
					zap.String("description", test.Description),
					zap.Int("test_index", i+1),
					zap.String("result", result))
				failedTests++
			} else {
				suite.logger.Info("测试矩阵项按预期失败", zap.String("description", test.Description))
				passedTests++
			}
		} else {
			if test.ExpectedPass {
				suite.logger.Info("测试矩阵项通过",
					zap.String("description", test.Description),
					zap.Int("test_index", i+1),
					zap.String("result", result))
				passedTests++
			} else {
				suite.logger.Warn("测试矩阵项意外通过", zap.String("description", test.Description))
				failedTests++
			}
		}

		if result == "skipped" {
			skippedTests++
		}
	}

	suite.logger.Info("测试矩阵验证完成",
		zap.Int("total", len(matrix)),
		zap.Int("passed", passedTests),
		zap.Int("failed", failedTests),
		zap.Int("skipped", skippedTests),
	)

	if failedTests > 0 {
		return fmt.Errorf("测试矩阵验证失败: %d个测试失败", failedTests)
	}

	return nil
}

// executeMatrixTest 执行单个矩阵测试
func (suite *TestMatrixValidationSuite) executeMatrixTest(test TestMatrix) (string, error) {
	ctx := context.Background()

	// 根据存储类型调整配置
	if err := suite.adjustConfigForStorageType(test.StorageType); err != nil {
		return "skipped", fmt.Errorf("调整存储配置失败: %w", err)
	}

	// 执行测试操作
	for i, operation := range test.Operations {
		// 对于增量链增量备份，确保先创建一个初始备份
		if operation == "backup" && test.BackupType == types.BackupTypeChainIncremental && i == 0 {
			// 先创建一个初始链备份
			initialTest := TestMatrix{
				DataSource:   test.DataSource,
				StorageType:  test.StorageType,
				BackupType:   types.BackupTypeChainInitial,
				Operations:   []string{"backup"},
				ExpectedPass: true,
				Description:  "创建增量链初始备份",
			}

			// 执行初始备份
			if err := suite.executeOperation(ctx, initialTest, "backup"); err != nil {
				return "failed", fmt.Errorf("创建增量链初始备份失败: %w", err)
			}

			// 为增量备份创建一些数据变更（仅对MySQL）
			if test.DataSource == types.MySQL {
				if err := suite.insertTestData(ctx, "incremental_test_data"); err != nil {
					suite.logger.Warn("插入测试数据失败，但继续执行增量备份", zap.Error(err))
				}
				// 等待一小段时间确保数据写入
				time.Sleep(2 * time.Second)
			}
		}

		// 执行当前操作
		if err := suite.executeOperation(ctx, test, operation); err != nil {
			return "failed", fmt.Errorf("操作失败 [%s]: %w", operation, err)
		}
	}

	return "passed", nil
}

// adjustConfigForStorageType 根据存储类型调整配置
func (suite *TestMatrixValidationSuite) adjustConfigForStorageType(storageType string) error {
	// 记录当前配置状态
	currentCloudEnabled := suite.config.CloudStorage != nil && suite.config.CloudStorage.Enabled

	switch storageType {
	case "local":
		// 确保使用本地存储
		if suite.config.CloudStorage != nil {
			suite.config.CloudStorage.Enabled = false
		}
		// 如果配置发生变化，重新创建管理器
		if currentCloudEnabled {
			manager, err := unibackup.NewManager(suite.config)
			if err != nil {
				return fmt.Errorf("重新创建管理器失败: %w", err)
			}
			suite.manager = manager
		}
	case "cloud":
		// 确保使用云存储
		if suite.config.CloudStorage == nil {
			return fmt.Errorf("云存储配置未设置")
		}
		suite.config.CloudStorage.Enabled = true
		// 如果配置发生变化，重新创建管理器
		if !currentCloudEnabled {
			manager, err := unibackup.NewManager(suite.config)
			if err != nil {
				return fmt.Errorf("重新创建管理器失败: %w", err)
			}
			suite.manager = manager
		}
	case "mixed":
		// 混合存储配置：需要同时支持本地和云存储
		if suite.config.CloudStorage == nil {
			return fmt.Errorf("混合存储模式需要云存储配置")
		}
		suite.config.CloudStorage.Enabled = true
		// 如果配置发生变化，重新创建管理器
		if !currentCloudEnabled {
			manager, err := unibackup.NewManager(suite.config)
			if err != nil {
				return fmt.Errorf("重新创建管理器失败: %w", err)
			}
			suite.manager = manager
		}
	}
	return nil
}

// executeOperation 执行具体操作
func (suite *TestMatrixValidationSuite) executeOperation(ctx context.Context, test TestMatrix, operation string) error {
	switch operation {
	case "backup":
		return suite.executeBackupOperation(ctx, test)
	case "restore":
		return suite.executeRestoreOperation(ctx, test)
	case "delete":
		return suite.executeDeleteOperation(ctx, test)
	case "list":
		return suite.executeListOperation(ctx, test)
	case "group-backup":
		return suite.executeGroupBackupOperation(ctx, test)
	case "group-restore":
		return suite.executeGroupRestoreOperation(ctx, test)
	case "group-delete":
		return suite.executeGroupDeleteOperation(ctx, test)
	case "concurrent-backup":
		return suite.executeConcurrentBackupOperation(ctx, test)
	case "stress-test":
		return suite.executeStressTestOperation(ctx, test)
	case "large-index":
		return suite.executeLargeIndexOperation(ctx, test)
	case "multi-index":
		return suite.executeMultiIndexOperation(ctx, test)
	default:
		return fmt.Errorf("未知操作: %s", operation)
	}
}

// executeBackupOperation 执行备份操作
func (suite *TestMatrixValidationSuite) executeBackupOperation(ctx context.Context, test TestMatrix) error {
	var target string
	switch test.DataSource {
	case types.MySQL:
		target = "testdb"
	case types.Elasticsearch:
		target = "test-index"
	default:
		return fmt.Errorf("不支持的数据源: %v", test.DataSource)
	}

	taskID, err := suite.manager.BackupAsync(ctx, test.DataSource, target, test.BackupType, fmt.Sprintf("矩阵测试备份_%s_%s", test.StorageType, test.BackupType))
	if err != nil {
		return fmt.Errorf("启动备份失败: %w", err)
	}

	return suite.waitForTaskCompletion(taskID, 60*time.Second)
}

// executeRestoreOperation 执行恢复操作
func (suite *TestMatrixValidationSuite) executeRestoreOperation(ctx context.Context, test TestMatrix) error {
	// 使用统一查询接口获取最新的备份任务，只查询已完成的备份任务
	filter := types.BackupFilter{
		SourceTypes: []types.SourceType{test.DataSource},
		TaskTypes:   []types.TaskType{types.BackupTask},
		Statuses:    []types.TaskStatus{types.TaskStatusCompleted},
		Limit:       1,
	}

	result, err := suite.manager.ListAllBackups(ctx, filter)
	if err != nil {
		return fmt.Errorf("列出备份失败: %w", err)
	}

	if len(result.Tasks) == 0 {
		return fmt.Errorf("没有找到已完成的备份任务")
	}

	latestTask := result.Tasks[0]

	// 验证任务元数据中的source_name是否匹配
	var sourceName string
	switch test.DataSource {
	case types.MySQL:
		sourceName = "testdb"
	case types.Elasticsearch:
		sourceName = "test-index"
	default:
		return fmt.Errorf("不支持的数据源: %v", test.DataSource)
	}

	if taskSourceName, ok := latestTask.Metadata["source_name"].(string); ok {
		if taskSourceName != sourceName {
			return fmt.Errorf("备份任务的数据源名称不匹配，期望: %s，实际: %s", sourceName, taskSourceName)
		}
	}

	// 使用 RestoreByTaskID 方法，这是正确的通过任务ID恢复的方式
	taskID, err := suite.manager.RestoreByTaskID(ctx, latestTask.ID, false)
	if err != nil {
		return fmt.Errorf("启动基于任务ID的恢复失败: %w", err)
	}

	// 恢复操作通常比备份操作需要更长时间，特别是云存储
	timeout := 120 * time.Second
	if test.StorageType == "cloud" {
		timeout = 180 * time.Second
	}
	return suite.waitForTaskCompletion(taskID, timeout)
}

// executeDeleteOperation 执行删除操作
func (suite *TestMatrixValidationSuite) executeDeleteOperation(ctx context.Context, test TestMatrix) error {
	// 使用统一查询接口获取备份，只查询已完成的备份任务
	filter := types.BackupFilter{
		SourceTypes: []types.SourceType{test.DataSource},
		TaskTypes:   []types.TaskType{types.BackupTask},
		Statuses:    []types.TaskStatus{types.TaskStatusCompleted},
		Limit:       1,
	}

	result, err := suite.manager.ListAllBackups(ctx, filter)
	if err != nil {
		return fmt.Errorf("列出备份失败: %w", err)
	}

	if len(result.Tasks) == 0 {
		// 没有备份可删除，不算错误
		return nil
	}

	// 删除最新的备份（ListAllBackups默认按时间倒序排列）
	latestTask := result.Tasks[0]
	err = suite.manager.DeleteBackupByTaskID(ctx, latestTask.ID)
	if err != nil {
		return fmt.Errorf("删除备份失败: %w", err)
	}

	return nil
}

// executeListOperation 执行列表操作
func (suite *TestMatrixValidationSuite) executeListOperation(ctx context.Context, test TestMatrix) error {
	filter := types.BackupFilter{
		SourceTypes: []types.SourceType{test.DataSource},
		Limit:       10,
	}

	_, err := suite.manager.ListAllBackups(ctx, filter)
	if err != nil {
		return fmt.Errorf("列出备份失败: %w", err)
	}

	return nil
}

// executeGroupBackupOperation 执行分组备份操作
func (suite *TestMatrixValidationSuite) executeGroupBackupOperation(ctx context.Context, test TestMatrix) error {
	// 使用真正的分组备份API
	sources := []types.BackupRequest{
		{
			SourceType:  types.MySQL,
			SourceName:  "testdb",
			BackupType:  test.BackupType,
			Description: "分组备份测试-MySQL",
		},
		{
			SourceType:  types.Elasticsearch,
			SourceName:  "test-index",
			BackupType:  test.BackupType,
			Description: "分组备份测试-ES",
		},
	}

	req := types.BackupAllRequest{
		Sources:          sources,
		Description:      "测试矩阵分组备份",
		Atomic:           true,
		CleanupOnFailure: true,
	}

	taskID, err := suite.manager.BackupAllAsync(ctx, req)
	if err != nil {
		return fmt.Errorf("启动分组备份失败: %w", err)
	}

	return suite.waitForTaskCompletion(taskID, 120*time.Second)
}

// executeGroupRestoreOperation 执行分组恢复操作
func (suite *TestMatrixValidationSuite) executeGroupRestoreOperation(ctx context.Context, test TestMatrix) error {
	// 查找最新的分组备份任务
	filter := types.BackupFilter{
		TaskTypes: []types.TaskType{types.BackupAllTask},
		Statuses:  []types.TaskStatus{types.TaskStatusCompleted},
		Limit:     1,
	}

	result, err := suite.manager.ListAllBackups(ctx, filter)
	if err != nil {
		return fmt.Errorf("列出分组备份失败: %w", err)
	}

	if len(result.Tasks) == 0 {
		return fmt.Errorf("没有找到已完成的分组备份任务")
	}

	groupTask := result.Tasks[0]

	// 使用RestoreByTaskID来恢复分组备份
	taskID, err := suite.manager.RestoreByTaskID(ctx, groupTask.ID, false)
	if err != nil {
		return fmt.Errorf("启动分组恢复失败: %w", err)
	}

	return suite.waitForTaskCompletion(taskID, 120*time.Second)
}

// executeGroupDeleteOperation 执行分组删除操作
func (suite *TestMatrixValidationSuite) executeGroupDeleteOperation(ctx context.Context, test TestMatrix) error {
	// 查找最旧的分组备份任务进行删除
	filter := types.BackupFilter{
		TaskTypes: []types.TaskType{types.BackupAllTask},
		Statuses:  []types.TaskStatus{types.TaskStatusCompleted},
		Limit:     1,
	}

	result, err := suite.manager.ListAllBackups(ctx, filter)
	if err != nil {
		return fmt.Errorf("列出分组备份失败: %w", err)
	}

	if len(result.Tasks) == 0 {
		// 没有分组备份可删除，不算错误
		return nil
	}

	groupTask := result.Tasks[0]

	// 使用DeleteBackupByTaskID来删除分组备份
	err = suite.manager.DeleteBackupByTaskID(ctx, groupTask.ID)
	if err != nil {
		return fmt.Errorf("删除分组备份失败: %w", err)
	}

	return nil
}

// executeConcurrentBackupOperation 执行并发备份操作
func (suite *TestMatrixValidationSuite) executeConcurrentBackupOperation(ctx context.Context, test TestMatrix) error {
	// 启动多个并发备份任务
	var taskIDs []string
	var wg sync.WaitGroup
	var mu sync.Mutex
	var errors []error

	targets := []string{"testdb", "test-index"}
	for i, target := range targets {
		wg.Add(1)
		go func(index int, targetName string) {
			defer wg.Done()

			var sourceType types.SourceType
			if targetName == "testdb" {
				sourceType = types.MySQL
			} else {
				sourceType = types.Elasticsearch
			}

			taskID, err := suite.manager.BackupAsync(ctx, sourceType, targetName, test.BackupType, fmt.Sprintf("并发备份测试_%d", index))
			if err != nil {
				mu.Lock()
				errors = append(errors, fmt.Errorf("并发备份失败 [%s]: %w", targetName, err))
				mu.Unlock()
				return
			}

			mu.Lock()
			taskIDs = append(taskIDs, taskID)
			mu.Unlock()
		}(i, target)
	}

	wg.Wait()

	if len(errors) > 0 {
		return fmt.Errorf("并发备份有错误: %v", errors)
	}

	// 等待所有任务完成
	for _, taskID := range taskIDs {
		if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
			return fmt.Errorf("等待并发备份完成失败 [%s]: %w", taskID, err)
		}
	}

	return nil
}

// executeStressTestOperation 执行压力测试操作
func (suite *TestMatrixValidationSuite) executeStressTestOperation(ctx context.Context, test TestMatrix) error {
	// 执行多轮备份来模拟压力测试
	rounds := 3
	for i := 0; i < rounds; i++ {
		var target string
		switch test.DataSource {
		case types.MySQL:
			target = "testdb"
		case types.Elasticsearch:
			target = "test-index"
		default:
			return fmt.Errorf("不支持的数据源: %v", test.DataSource)
		}

		taskID, err := suite.manager.BackupAsync(ctx, test.DataSource, target, test.BackupType, fmt.Sprintf("压力测试备份_轮次%d", i+1))
		if err != nil {
			return fmt.Errorf("压力测试第%d轮备份失败: %w", i+1, err)
		}

		if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
			return fmt.Errorf("压力测试第%d轮等待完成失败: %w", i+1, err)
		}
	}
	return nil
}

// executeLargeIndexOperation 执行大索引操作
func (suite *TestMatrixValidationSuite) executeLargeIndexOperation(ctx context.Context, test TestMatrix) error {
	// 只对Elasticsearch有意义
	if test.DataSource != types.Elasticsearch {
		return suite.executeBackupOperation(ctx, test)
	}

	// 使用特殊的大索引名称
	taskID, err := suite.manager.BackupAsync(ctx, types.Elasticsearch, "large-test-index", test.BackupType, "大索引备份测试")
	if err != nil {
		return fmt.Errorf("大索引备份失败: %w", err)
	}

	return suite.waitForTaskCompletion(taskID, 120*time.Second)
}

// executeMultiIndexOperation 执行多索引操作
func (suite *TestMatrixValidationSuite) executeMultiIndexOperation(ctx context.Context, test TestMatrix) error {
	// 只对Elasticsearch有意义
	if test.DataSource != types.Elasticsearch {
		return suite.executeBackupOperation(ctx, test)
	}

	// 备份多个索引
	indices := []string{"test-index", "test-index-2", "test-index-3"}
	for i, index := range indices {
		taskID, err := suite.manager.BackupAsync(ctx, types.Elasticsearch, index, test.BackupType, fmt.Sprintf("多索引备份测试_%d", i+1))
		if err != nil {
			return fmt.Errorf("多索引备份失败 [%s]: %w", index, err)
		}

		if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
			return fmt.Errorf("多索引备份等待完成失败 [%s]: %w", index, err)
		}
	}
	return nil
}

// waitForTaskCompletion 等待任务完成
func (suite *TestMatrixValidationSuite) waitForTaskCompletion(taskID string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		task, err := suite.manager.GetTask(taskID)
		if err != nil {
			return fmt.Errorf("获取任务状态失败: %w", err)
		}

		switch task.Status {
		case types.TaskStatusCompleted:
			return nil
		case types.TaskStatusFailed:
			return fmt.Errorf("任务失败: %s", task.Error)
		case types.TaskStatusCancelled:
			return fmt.Errorf("任务被取消")
		}

		time.Sleep(2 * time.Second)
	}
	return fmt.Errorf("任务超时")
}

// insertTestData 插入测试数据以生成数据变更
func (suite *TestMatrixValidationSuite) insertTestData(ctx context.Context, testName string) error {
	// 这里可以添加实际的数据插入逻辑
	// 为了简化，我们只是记录日志
	suite.logger.Info("模拟插入测试数据", zap.String("test_name", testName))
	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: test-matrix-validation-suite <test-type>")
		fmt.Println("测试类型:")
		fmt.Println("  matrix              - 运行完整测试矩阵验证")
		fmt.Println("  mysql-local         - MySQL本地存储测试")
		fmt.Println("  mysql-cloud         - MySQL云存储测试")
		fmt.Println("  es-local            - Elasticsearch本地存储测试")
		fmt.Println("  es-cloud            - Elasticsearch云存储测试")
		fmt.Println("  group-operations    - 分组操作测试")
		fmt.Println("  boundary-cases      - 边界情况测试")
		os.Exit(1)
	}

	testType := os.Args[1]

	logger, _ := zap.NewDevelopment()

	fmt.Printf("🎯 开始测试矩阵验证: %s\n", testType)
	fmt.Println(strings.Repeat("=", 60))

	// 创建配置
	config := &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             logger,
		MaxConcurrentTasks: 3,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		MySQL: &types.MySQLConfig{
			Host:     "unibackup-test-mysql",
			Port:     3306,
			User:     "backup_user",
			Password: "backup_pass",
			DBName:   "testdb",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://unibackup-test-elasticsearch:9200"},
			ArchivalRepoName: "unibackup-test-archival",
			ManagedRepoName:  "unibackup-test-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     "/usr/share/elasticsearch/snapshots",
		},

		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "unibackup-test-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://minio:9000",
		},
	}

	// 创建测试套件
	suite := NewTestMatrixValidationSuite(logger, config)

	if err := suite.Setup(); err != nil {
		fmt.Printf("❌ 测试环境初始化失败: %v\n", err)
		os.Exit(1)
	}
	defer suite.Cleanup()

	// 根据测试类型执行测试
	var err error
	switch testType {
	case "matrix":
		err = suite.RunMatrixValidation()
	case "mysql-local":
		err = suite.runFilteredMatrixTests(func(test TestMatrix) bool {
			return test.DataSource == types.MySQL && test.StorageType == "local"
		})
	case "mysql-cloud":
		err = suite.runFilteredMatrixTests(func(test TestMatrix) bool {
			return test.DataSource == types.MySQL && test.StorageType == "cloud"
		})
	case "es-local":
		err = suite.runFilteredMatrixTests(func(test TestMatrix) bool {
			return test.DataSource == types.Elasticsearch && test.StorageType == "local"
		})
	case "es-cloud":
		err = suite.runFilteredMatrixTests(func(test TestMatrix) bool {
			return test.DataSource == types.Elasticsearch && test.StorageType == "cloud"
		})
	case "group-operations":
		err = suite.runFilteredMatrixTests(func(test TestMatrix) bool {
			return test.StorageType == "mixed"
		})
	case "boundary-cases":
		err = suite.runFilteredMatrixTests(func(test TestMatrix) bool {
			for _, op := range test.Operations {
				if strings.Contains(op, "concurrent") || strings.Contains(op, "stress") ||
					strings.Contains(op, "large") || strings.Contains(op, "multi") {
					return true
				}
			}
			return false
		})
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		os.Exit(1)
	}

	if err != nil {
		fmt.Printf("❌ 测试矩阵验证失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 测试矩阵验证通过")
}

// runFilteredMatrixTests 运行过滤后的矩阵测试
func (suite *TestMatrixValidationSuite) runFilteredMatrixTests(filter func(TestMatrix) bool) error {
	matrix := suite.GetTestMatrix()
	filteredMatrix := make([]TestMatrix, 0)

	for _, test := range matrix {
		if filter(test) {
			filteredMatrix = append(filteredMatrix, test)
		}
	}

	if len(filteredMatrix) == 0 {
		return fmt.Errorf("没有找到匹配的测试")
	}

	suite.logger.Info("开始过滤后的测试矩阵验证", zap.Int("filtered_tests", len(filteredMatrix)))

	passedTests := 0
	failedTests := 0

	for i, test := range filteredMatrix {
		suite.logger.Info("执行过滤测试矩阵项",
			zap.Int("index", i+1),
			zap.Int("total", len(filteredMatrix)),
			zap.String("description", test.Description),
		)

		_, err := suite.executeMatrixTest(test)
		if err != nil {
			if test.ExpectedPass {
				suite.logger.Error("过滤测试矩阵项失败", zap.Error(err), zap.String("description", test.Description))
				failedTests++
			} else {
				suite.logger.Info("过滤测试矩阵项按预期失败", zap.String("description", test.Description))
				passedTests++
			}
		} else {
			if test.ExpectedPass {
				suite.logger.Info("过滤测试矩阵项通过", zap.String("description", test.Description))
				passedTests++
			} else {
				suite.logger.Warn("过滤测试矩阵项意外通过", zap.String("description", test.Description))
				failedTests++
			}
		}
	}

	suite.logger.Info("过滤测试矩阵验证完成",
		zap.Int("total", len(filteredMatrix)),
		zap.Int("passed", passedTests),
		zap.Int("failed", failedTests),
	)

	if failedTests > 0 {
		return fmt.Errorf("过滤测试矩阵验证失败: %d个测试失败", failedTests)
	}

	return nil
}
