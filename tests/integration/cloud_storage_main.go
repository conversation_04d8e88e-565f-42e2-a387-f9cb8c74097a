package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"
)

// loadTestConfig 加载测试配置文件
func loadTestConfig(configPath string) (*types.Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config types.Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return &config, nil
}

// showUsage 显示使用说明
func showUsage() {
	fmt.Println("UniBackup 云存储测试套件")
	fmt.Println("使用方法: cloud-storage-test <测试类型> [配置文件]")
	fmt.Println("")
	fmt.Println("测试类型:")
	fmt.Println("  unit         - 运行云存储单元测试")
	fmt.Println("  integration  - 运行云存储集成测试")
	fmt.Println("  config       - 运行云存储配置测试")
	fmt.Println("  all          - 运行所有云存储测试")
	fmt.Println("")
	fmt.Println("配置文件:")
	fmt.Println("  默认: /tests/config/test_config_cloud_storage.json")
	fmt.Println("  可选: /tests/config/test_config_s3_mock.json")
	fmt.Println("       /tests/config/test_config_gcs_mock.json")
	fmt.Println("       /tests/config/test_config_azure_mock.json")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  cloud-storage-test unit")
	fmt.Println("  cloud-storage-test integration /tests/config/test_config_s3_mock.json")
	fmt.Println("  cloud-storage-test all")
}

func main() {
	if len(os.Args) < 2 {
		showUsage()
		os.Exit(1)
	}

	testType := os.Args[1]

	// 确定配置文件路径
	configPath := "/tests/config/test_config_cloud_storage.json"
	if len(os.Args) >= 3 {
		configPath = os.Args[2]
	}

	// 创建日志记录器
	logger, _ := zap.NewDevelopment()

	fmt.Printf("🚀 开始云存储测试: %s\n", testType)
	fmt.Printf("📁 配置文件: %s\n", configPath)
	fmt.Println(strings.Repeat("=", 80))

	startTime := time.Now()
	var err error



	switch testType {
	case "unit":
		fmt.Println("🧪 运行云存储单元测试...")
		err = runCloudStorageUnitTests(logger, configPath)
	case "integration":
		fmt.Println("🔗 运行云存储集成测试...")
		err = runCloudStorageIntegrationTests(logger, configPath)
	case "config":
		fmt.Println("⚙️ 运行云存储配置测试...")
		err = runCloudStorageConfigTests(logger)
	case "backend-consistency":
		fmt.Println("🔧 运行Backend一致性测试...")
		err = runBackendConsistencyTests(logger)
	case "all":
		fmt.Println("☁️ 运行所有可用的存储相关测试...")
		err = runAvailableStorageTests(logger, configPath)
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		showUsage()
		os.Exit(1)
	}

	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("❌ 云存储测试失败: %v\n", err)
		fmt.Printf("⏱️  测试耗时: %v\n", duration)
		os.Exit(1)
	}

	fmt.Println(strings.Repeat("=", 80))
	fmt.Printf("✅ 云存储测试成功完成\n")
	fmt.Printf("⏱️  测试耗时: %v\n", duration)
}

// runAvailableStorageTests 运行可用的存储相关测试
func runAvailableStorageTests(logger *zap.Logger, configPath string) error {
	logger.Info("🚀 开始运行可用的存储相关测试")

	// 加载配置
	config, err := loadTestConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载测试配置失败: %w", err)
	}

	// 基础存储功能测试
	if err := testBasicStorageOperations(logger, config); err != nil {
		return fmt.Errorf("基础存储操作测试失败: %w", err)
	}

	// 云存储连接测试
	if err := testCloudStorageConnection(logger, config); err != nil {
		return fmt.Errorf("云存储连接测试失败: %w", err)
	}

	// Backend一致性测试
	if err := runBackendConsistencyTests(logger); err != nil {
		return fmt.Errorf("Backend一致性测试失败: %w", err)
	}

	logger.Info("✅ 所有可用的存储测试完成")
	return nil
}

// validateTestEnvironment 验证测试环境
func validateTestEnvironment() error {
	// 检查必要的目录
	requiredDirs := []string{
		"/tests/data/backup",
		"/tests/config",
		"/tests/reports",
	}

	for _, dir := range requiredDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建目录失败 [%s]: %w", dir, err)
			}
		}
	}

	// 检查配置文件
	configFiles := []string{
		"/tests/config/test_config_cloud_storage.json",
	}

	for _, file := range configFiles {
		if _, err := os.Stat(file); os.IsNotExist(err) {
			return fmt.Errorf("配置文件不存在: %s", file)
		}
	}

	return nil
}

// cleanupTestData 清理测试数据
func cleanupTestData(logger *zap.Logger) error {
	logger.Info("🧹 开始清理测试数据")

	// 清理备份目录中的测试文件
	backupDir := "/tests/data/backup"
	entries, err := os.ReadDir(backupDir)
	if err != nil {
		return fmt.Errorf("读取备份目录失败: %w", err)
	}

	for _, entry := range entries {
		if strings.Contains(entry.Name(), "test-") ||
			strings.Contains(entry.Name(), "cloud-storage-test-") {
			path := fmt.Sprintf("%s/%s", backupDir, entry.Name())
			if err := os.RemoveAll(path); err != nil {
				logger.Warn("删除测试文件失败", zap.String("path", path), zap.Error(err))
			} else {
				logger.Info("删除测试文件", zap.String("path", path))
			}
		}
	}

	logger.Info("✅ 测试数据清理完成")
	return nil
}

// testBasicStorageOperations 测试基础存储操作
func testBasicStorageOperations(logger *zap.Logger, config *types.Config) error {
	logger.Info("🔧 测试基础存储操作")

	// 创建存储后端
	backend, err := storage.NewBackend(config, logger)
	if err != nil {
		return fmt.Errorf("创建存储后端失败: %w", err)
	}

	ctx := context.Background()

	// 测试数据
	testData := map[string]string{
		"test/basic/small.txt":  "小文件测试数据",
		"test/basic/medium.txt": strings.Repeat("中等文件测试数据\n", 100),
		"test/basic/large.txt":  strings.Repeat("大文件测试数据内容\n", 1000),
	}

	// 1. 测试 Put 操作
	logger.Info("📝 测试 Put 操作")
	for key, data := range testData {
		written, err := backend.Put(ctx, key, strings.NewReader(data))
		if err != nil {
			return fmt.Errorf("Put操作失败 [%s]: %w", key, err)
		}
		if written != int64(len(data)) {
			return fmt.Errorf("写入字节数不匹配 [%s]: 期望 %d, 实际 %d", key, len(data), written)
		}
		logger.Info("Put操作成功", zap.String("key", key), zap.Int64("size", written))
	}

	// 2. 测试 Exists 操作
	logger.Info("🔍 测试 Exists 操作")
	for key := range testData {
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			return fmt.Errorf("Exists操作失败 [%s]: %w", key, err)
		}
		if !exists {
			return fmt.Errorf("文件应该存在但Exists返回false [%s]", key)
		}
		logger.Info("Exists操作成功", zap.String("key", key), zap.Bool("exists", exists))
	}

	// 3. 测试 Get 操作
	logger.Info("📖 测试 Get 操作")
	for key, expectedData := range testData {
		reader, err := backend.Get(ctx, key)
		if err != nil {
			return fmt.Errorf("Get操作失败 [%s]: %w", key, err)
		}
		defer reader.Close()

		actualData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取数据失败 [%s]: %w", key, err)
		}

		if string(actualData) != expectedData {
			return fmt.Errorf("数据不匹配 [%s]: 期望长度 %d, 实际长度 %d", key, len(expectedData), len(actualData))
		}
		logger.Info("Get操作成功", zap.String("key", key), zap.Int("size", len(actualData)))
	}

	// 4. 测试 List 操作
	logger.Info("📋 测试 List 操作")
	objects, err := backend.List(ctx, "test/basic/")
	if err != nil {
		return fmt.Errorf("List操作失败: %w", err)
	}
	if len(objects) != len(testData) {
		return fmt.Errorf("List返回的对象数量不匹配: 期望 %d, 实际 %d", len(testData), len(objects))
	}
	logger.Info("List操作成功", zap.Int("count", len(objects)))

	// 5. 测试 Delete 操作
	logger.Info("🗑️ 测试 Delete 操作")
	for key := range testData {
		err := backend.Delete(ctx, key)
		if err != nil {
			return fmt.Errorf("Delete操作失败 [%s]: %w", key, err)
		}

		// 验证文件已删除
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			return fmt.Errorf("删除后Exists检查失败 [%s]: %w", key, err)
		}
		if exists {
			return fmt.Errorf("文件应该已删除但仍然存在 [%s]", key)
		}
		logger.Info("Delete操作成功", zap.String("key", key))
	}

	logger.Info("✅ 基础存储操作测试通过")
	return nil
}

// testCloudStorageConnection 测试云存储连接
func testCloudStorageConnection(logger *zap.Logger, config *types.Config) error {
	logger.Info("☁️ 测试云存储连接")

	if config.CloudStorage == nil || !config.CloudStorage.Enabled {
		logger.Info("⚠️ 云存储未启用，跳过连接测试")
		return nil
	}

	// 创建云存储后端
	backend, err := storage.NewBackend(config, logger)
	if err != nil {
		return fmt.Errorf("创建云存储后端失败: %w", err)
	}

	ctx := context.Background()

	// 1. 健康检查
	logger.Info("🏥 执行健康检查")
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("云存储健康检查失败: %w", err)
	}
	logger.Info("健康检查通过")

	// 2. 连接性测试 - 写入小文件
	logger.Info("📡 测试连接性")
	testKey := fmt.Sprintf("test/connection/ping-%d.txt", time.Now().Unix())
	testData := fmt.Sprintf("连接测试数据 - %s", time.Now().Format("2006-01-02 15:04:05"))

	written, err := backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("连接测试写入失败: %w", err)
	}
	logger.Info("连接测试写入成功", zap.String("key", testKey), zap.Int64("size", written))

	// 3. 读取验证
	reader, err := backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("连接测试读取失败: %w", err)
	}
	defer reader.Close()

	actualData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("连接测试读取数据失败: %w", err)
	}

	if string(actualData) != testData {
		return fmt.Errorf("连接测试数据不匹配")
	}
	logger.Info("连接测试读取验证成功")

	// 4. 清理测试文件
	if err := backend.Delete(ctx, testKey); err != nil {
		logger.Warn("清理连接测试文件失败", zap.String("key", testKey), zap.Error(err))
	} else {
		logger.Info("连接测试文件清理成功", zap.String("key", testKey))
	}

	// 5. 认证验证 - 通过列出根目录来验证权限
	logger.Info("🔐 验证认证和权限")
	_, err = backend.List(ctx, "")
	if err != nil {
		return fmt.Errorf("认证验证失败，无法列出根目录: %w", err)
	}
	logger.Info("认证和权限验证通过")

	logger.Info("✅ 云存储连接测试通过")
	return nil
}



// runBackendConsistencyTests 运行Backend一致性测试（简化版，集成到云存储测试中）
func runBackendConsistencyTests(logger *zap.Logger) error {
	logger.Info("🔧 开始Backend接口一致性测试")

	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "cloud-backend-consistency-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	defer os.RemoveAll(testDir)

	// 创建本地Backend用于对比测试
	localBackend, err := storage.NewBackend(&types.Config{
		BackupRoot: testDir,
	}, logger)
	if err != nil {
		return fmt.Errorf("创建本地Backend失败: %w", err)
	}

	// 创建云存储Backend
	cloudConfig := &types.Config{
		BackupRoot: "/tests/data/backend-consistency",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "unibackup-test-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}
	cloudBackend, err := storage.NewBackend(cloudConfig, logger)
	if err != nil {
		return fmt.Errorf("创建云存储Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := "consistency-test/test-file.txt"
	testData := "Backend一致性测试数据"

	// 测试基本操作一致性
	logger.Info("测试基本操作一致性")

	// 1. 测试Put操作
	localWritten, err := localBackend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("本地Backend Put操作失败: %w", err)
	}

	cloudWritten, err := cloudBackend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("云存储Backend Put操作失败: %w", err)
	}

	if localWritten != cloudWritten {
		return fmt.Errorf("Put操作返回值不一致: 本地=%d, 云存储=%d", localWritten, cloudWritten)
	}

	// 2. 测试Exists操作
	localExists, err := localBackend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("本地Backend Exists操作失败: %w", err)
	}

	cloudExists, err := cloudBackend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("云存储Backend Exists操作失败: %w", err)
	}

	if localExists != cloudExists {
		return fmt.Errorf("Exists操作返回值不一致: 本地=%t, 云存储=%t", localExists, cloudExists)
	}

	// 3. 测试Get操作
	localReader, err := localBackend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("本地Backend Get操作失败: %w", err)
	}
	defer localReader.Close()

	cloudReader, err := cloudBackend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("云存储Backend Get操作失败: %w", err)
	}
	defer cloudReader.Close()

	localData, err := io.ReadAll(localReader)
	if err != nil {
		return fmt.Errorf("读取本地数据失败: %w", err)
	}

	cloudData, err := io.ReadAll(cloudReader)
	if err != nil {
		return fmt.Errorf("读取云存储数据失败: %w", err)
	}

	if string(localData) != string(cloudData) {
		return fmt.Errorf("Get操作返回数据不一致: 本地=%s, 云存储=%s", string(localData), string(cloudData))
	}

	// 4. 测试Delete操作
	if err := localBackend.Delete(ctx, testKey); err != nil {
		return fmt.Errorf("本地Backend Delete操作失败: %w", err)
	}

	if err := cloudBackend.Delete(ctx, testKey); err != nil {
		return fmt.Errorf("云存储Backend Delete操作失败: %w", err)
	}

	// 验证删除后文件不存在
	localExists, _ = localBackend.Exists(ctx, testKey)
	cloudExists, _ = cloudBackend.Exists(ctx, testKey)

	if localExists || cloudExists {
		return fmt.Errorf("删除后文件仍然存在: 本地=%t, 云存储=%t", localExists, cloudExists)
	}

	logger.Info("✅ Backend接口一致性测试通过")
	return nil
}

// init 初始化函数
func init() {
	// 验证测试环境
	if err := validateTestEnvironment(); err != nil {
		fmt.Printf("❌ 测试环境验证失败: %v\n", err)
		os.Exit(1)
	}
}
