package main

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
	"go.uber.org/zap"
)

// BackendConsistencyTestSuite 测试不同Backend实现的一致性
type BackendConsistencyTestSuite struct {
	logger   *zap.Logger
	testDir  string
	backends map[string]interfaces.Backend
	testData map[string][]byte
}

// NewBackendConsistencyTestSuite 创建Backend一致性测试套件
func NewBackendConsistencyTestSuite(logger *zap.Logger) *BackendConsistencyTestSuite {
	return &BackendConsistencyTestSuite{
		logger:   logger,
		backends: make(map[string]interfaces.Backend),
		testData: make(map[string][]byte),
	}
}

// Setup 初始化测试环境
func (suite *BackendConsistencyTestSuite) Setup() error {
	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "backend-consistency-test-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	suite.testDir = testDir

	// 创建多个LocalBackend实例（模拟不同Backend实现）
	localBackend1, err := local.NewLocalBackend(filepath.Join(testDir, "backend1"))
	if err != nil {
		return fmt.Errorf("创建LocalBackend1失败: %w", err)
	}
	suite.backends["local1"] = localBackend1

	localBackend2, err := local.NewLocalBackend(filepath.Join(testDir, "backend2"))
	if err != nil {
		return fmt.Errorf("创建LocalBackend2失败: %w", err)
	}
	suite.backends["local2"] = localBackend2

	// 准备测试数据
	suite.testData["small"] = []byte("小文件测试数据")
	suite.testData["medium"] = make([]byte, 1024*1024) // 1MB
	for i := range suite.testData["medium"] {
		suite.testData["medium"][i] = byte(i % 256)
	}
	suite.testData["large"] = make([]byte, 10*1024*1024) // 10MB
	for i := range suite.testData["large"] {
		suite.testData["large"][i] = byte(i % 256)
	}

	suite.logger.Info("Backend一致性测试环境初始化完成", zap.String("test_dir", testDir))
	return nil
}

// Cleanup 清理测试环境
func (suite *BackendConsistencyTestSuite) Cleanup() error {
	if suite.testDir != "" {
		return os.RemoveAll(suite.testDir)
	}
	return nil
}

// RunAllTests 运行所有Backend一致性测试
func (suite *BackendConsistencyTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"基础CRUD操作一致性", suite.testBasicCRUDConsistency},
		{"错误处理一致性", suite.testErrorHandlingConsistency},
		{"并发操作一致性", suite.testConcurrentOperationConsistency},
		{"大文件操作一致性", suite.testLargeFileConsistency},
		{"边界条件一致性", suite.testBoundaryConditionConsistency},
		{"性能特征一致性", suite.testPerformanceConsistency},
	}

	for _, test := range tests {
		suite.logger.Info("开始测试", zap.String("test", test.name))
		if err := test.test(); err != nil {
			return fmt.Errorf("测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("测试通过", zap.String("test", test.name))
	}

	return nil
}

// testBasicCRUDConsistency 测试基础CRUD操作的一致性
func (suite *BackendConsistencyTestSuite) testBasicCRUDConsistency() error {
	ctx := context.Background()
	testKey := "test/basic-crud.txt"
	testData := suite.testData["small"]

	// 测试所有Backend的Put操作
	putResults := make(map[string]int64)
	for name, backend := range suite.backends {
		written, err := backend.Put(ctx, testKey, strings.NewReader(string(testData)))
		if err != nil {
			return fmt.Errorf("Backend %s Put操作失败: %w", name, err)
		}
		putResults[name] = written
	}

	// 验证Put操作结果一致性
	var expectedSize int64 = int64(len(testData))
	for name, written := range putResults {
		if written != expectedSize {
			return fmt.Errorf("Backend %s Put操作写入字节数不一致: 期望 %d, 实际 %d", name, expectedSize, written)
		}
	}

	// 测试所有Backend的Get操作
	for name, backend := range suite.backends {
		reader, err := backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s Get操作失败: %w", name, err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("Backend %s 读取数据失败: %w", name, err)
		}

		if string(data) != string(testData) {
			return fmt.Errorf("Backend %s 读取数据不一致", name)
		}
	}

	// 测试所有Backend的Exists操作
	for name, backend := range suite.backends {
		exists, err := backend.Exists(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s Exists操作失败: %w", name, err)
		}
		if !exists {
			return fmt.Errorf("Backend %s Exists操作返回false，期望true", name)
		}
	}

	// 测试所有Backend的Delete操作
	for name, backend := range suite.backends {
		err := backend.Delete(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s Delete操作失败: %w", name, err)
		}

		// 验证删除后文件不存在
		exists, err := backend.Exists(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s 删除后Exists检查失败: %w", name, err)
		}
		if exists {
			return fmt.Errorf("Backend %s 删除后文件仍然存在", name)
		}
	}

	return nil
}

// testErrorHandlingConsistency 测试错误处理的一致性
func (suite *BackendConsistencyTestSuite) testErrorHandlingConsistency() error {
	ctx := context.Background()

	// 测试不存在文件的Get操作
	nonExistentKey := "test/non-existent.txt"
	for name, backend := range suite.backends {
		_, err := backend.Get(ctx, nonExistentKey)
		if err == nil {
			return fmt.Errorf("Backend %s 对不存在文件的Get操作应该返回错误", name)
		}
		if !interfaces.IsNotFound(err) {
			return fmt.Errorf("Backend %s 应该返回NotFound错误，实际: %v", name, err)
		}
	}

	// 测试无效key的操作
	invalidKeys := []string{
		"",          // 空key
		"../../../", // 路径遍历
	}

	for _, invalidKey := range invalidKeys {
		for name, backend := range suite.backends {
			_, err := backend.Put(ctx, invalidKey, strings.NewReader("test"))
			if err == nil {
				return fmt.Errorf("Backend %s 对无效key '%s' 的Put操作应该返回错误", name, invalidKey)
			}
			if !interfaces.IsInvalidKey(err) {
				return fmt.Errorf("Backend %s 应该返回InvalidKey错误，实际: %v", name, err)
			}
		}
	}

	// 测试包含特殊字符的key（这些可能会导致系统级错误而不是InvalidKey错误）
	specialKeys := []string{
		"test\x00", // 包含null字符
		"test\n",   // 包含换行符
		"test\r",   // 包含回车符
	}

	for _, specialKey := range specialKeys {
		for name, backend := range suite.backends {
			_, err := backend.Put(ctx, specialKey, strings.NewReader("test"))
			if err == nil {
				suite.logger.Warn("Backend允许特殊字符key",
					zap.String("backend", name),
					zap.String("key", fmt.Sprintf("%q", specialKey)))
			} else {
				// 对于特殊字符，我们接受任何类型的错误（可能是系统级错误）
				suite.logger.Info("Backend正确拒绝特殊字符key",
					zap.String("backend", name),
					zap.String("key", fmt.Sprintf("%q", specialKey)),
					zap.String("error", err.Error()))
			}
		}
	}

	return nil
}

// testConcurrentOperationConsistency 测试并发操作的一致性
func (suite *BackendConsistencyTestSuite) testConcurrentOperationConsistency() error {
	ctx := context.Background()
	concurrency := 10
	testKey := "test/concurrent.txt"

	for name, backend := range suite.backends {
		// 并发写入测试
		errChan := make(chan error, concurrency)
		for i := 0; i < concurrency; i++ {
			go func(index int) {
				key := fmt.Sprintf("%s-%d", testKey, index)
				data := fmt.Sprintf("concurrent-data-%d", index)
				_, err := backend.Put(ctx, key, strings.NewReader(data))
				errChan <- err
			}(i)
		}

		// 检查并发写入结果
		for i := 0; i < concurrency; i++ {
			if err := <-errChan; err != nil {
				return fmt.Errorf("Backend %s 并发写入失败: %w", name, err)
			}
		}

		// 验证所有文件都存在
		for i := 0; i < concurrency; i++ {
			key := fmt.Sprintf("%s-%d", testKey, i)
			exists, err := backend.Exists(ctx, key)
			if err != nil {
				return fmt.Errorf("Backend %s 并发写入后检查存在性失败: %w", name, err)
			}
			if !exists {
				return fmt.Errorf("Backend %s 并发写入的文件 %s 不存在", name, key)
			}
		}

		// 清理测试文件
		for i := 0; i < concurrency; i++ {
			key := fmt.Sprintf("%s-%d", testKey, i)
			backend.Delete(ctx, key)
		}
	}

	return nil
}

// testLargeFileConsistency 测试大文件操作的一致性
func (suite *BackendConsistencyTestSuite) testLargeFileConsistency() error {
	ctx := context.Background()
	testKey := "test/large-file.bin"
	testData := suite.testData["large"]

	for name, backend := range suite.backends {
		// 写入大文件
		written, err := backend.Put(ctx, testKey, strings.NewReader(string(testData)))
		if err != nil {
			return fmt.Errorf("Backend %s 大文件写入失败: %w", name, err)
		}

		expectedSize := int64(len(testData))
		if written != expectedSize {
			return fmt.Errorf("Backend %s 大文件写入字节数不一致: 期望 %d, 实际 %d", name, expectedSize, written)
		}

		// 读取大文件
		reader, err := backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s 大文件读取失败: %w", name, err)
		}
		defer reader.Close()

		// 分块读取以避免内存问题
		buffer := make([]byte, 1024*1024) // 1MB buffer
		totalRead := 0
		for {
			n, err := reader.Read(buffer)
			if err == io.EOF {
				break
			}
			if err != nil {
				return fmt.Errorf("Backend %s 大文件读取过程中出错: %w", name, err)
			}
			totalRead += n
		}

		if totalRead != len(testData) {
			return fmt.Errorf("Backend %s 大文件读取字节数不一致: 期望 %d, 实际 %d", name, len(testData), totalRead)
		}

		// 清理大文件
		err = backend.Delete(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s 大文件删除失败: %w", name, err)
		}
	}

	return nil
}

// testBoundaryConditionConsistency 测试边界条件的一致性
func (suite *BackendConsistencyTestSuite) testBoundaryConditionConsistency() error {
	ctx := context.Background()

	// 测试空文件
	emptyKey := "test/empty.txt"
	for name, backend := range suite.backends {
		written, err := backend.Put(ctx, emptyKey, strings.NewReader(""))
		if err != nil {
			return fmt.Errorf("Backend %s 空文件写入失败: %w", name, err)
		}
		if written != 0 {
			return fmt.Errorf("Backend %s 空文件写入字节数应为0，实际: %d", name, written)
		}

		reader, err := backend.Get(ctx, emptyKey)
		if err != nil {
			return fmt.Errorf("Backend %s 空文件读取失败: %w", name, err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("Backend %s 空文件读取数据失败: %w", name, err)
		}
		if len(data) != 0 {
			return fmt.Errorf("Backend %s 空文件读取数据长度应为0，实际: %d", name, len(data))
		}

		backend.Delete(ctx, emptyKey)
	}

	// 测试深层目录结构
	deepKey := "test/level1/level2/level3/level4/deep.txt"
	testData := "深层目录测试数据"
	for name, backend := range suite.backends {
		_, err := backend.Put(ctx, deepKey, strings.NewReader(testData))
		if err != nil {
			return fmt.Errorf("Backend %s 深层目录文件写入失败: %w", name, err)
		}

		exists, err := backend.Exists(ctx, deepKey)
		if err != nil {
			return fmt.Errorf("Backend %s 深层目录文件存在性检查失败: %w", name, err)
		}
		if !exists {
			return fmt.Errorf("Backend %s 深层目录文件不存在", name)
		}

		backend.Delete(ctx, deepKey)
	}

	return nil
}

// testPerformanceConsistency 测试性能特征的一致性
func (suite *BackendConsistencyTestSuite) testPerformanceConsistency() error {
	ctx := context.Background()
	testKey := "test/performance.txt"
	testData := suite.testData["medium"]

	performanceResults := make(map[string]time.Duration)

	for name, backend := range suite.backends {
		start := time.Now()

		// 执行一系列操作
		_, err := backend.Put(ctx, testKey, strings.NewReader(string(testData)))
		if err != nil {
			return fmt.Errorf("Backend %s 性能测试Put失败: %w", name, err)
		}

		_, err = backend.Exists(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s 性能测试Exists失败: %w", name, err)
		}

		reader, err := backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s 性能测试Get失败: %w", name, err)
		}
		io.ReadAll(reader)
		reader.Close()

		err = backend.Delete(ctx, testKey)
		if err != nil {
			return fmt.Errorf("Backend %s 性能测试Delete失败: %w", name, err)
		}

		duration := time.Since(start)
		performanceResults[name] = duration

		suite.logger.Info("Backend性能测试完成",
			zap.String("backend", name),
			zap.String("duration", duration.String()))
	}

	// 检查性能差异是否在合理范围内（同类型Backend性能应该相近）
	var baseDuration time.Duration
	for _, duration := range performanceResults {
		if baseDuration == 0 {
			baseDuration = duration
		} else {
			ratio := float64(duration) / float64(baseDuration)
			if ratio > 2.0 || ratio < 0.5 {
				suite.logger.Warn("Backend性能差异较大",
					zap.String("base_duration", baseDuration.String()),
					zap.String("current_duration", duration.String()),
					zap.Float64("ratio", ratio))
			}
		}
	}

	return nil
}

// runBackendConsistencyTests 运行Backend一致性测试的主函数
func runBackendConsistencyTests(logger *zap.Logger) error {
	logger.Info("🔧 开始Backend接口一致性测试")

	suite := NewBackendConsistencyTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("Backend一致性测试失败: %w", err)
	}

	logger.Info("✅ Backend接口一致性测试全部通过")
	return nil
}

// main 函数，使Backend一致性测试可以独立运行
func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: backend-consistency-test <test-type>")
		fmt.Println("测试类型:")
		fmt.Println("  all        - 运行所有Backend一致性测试")
		fmt.Println("  basic      - 运行基础操作测试")
		fmt.Println("  error      - 运行错误处理测试")
		fmt.Println("  concurrent - 运行并发测试")
		os.Exit(1)
	}

	testType := os.Args[1]
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	fmt.Printf("🧪 开始Backend一致性测试: %s\n", testType)
	fmt.Println("============================================================")

	switch testType {
	case "all":
		if err := runBackendConsistencyTests(logger); err != nil {
			logger.Error("❌ Backend一致性测试失败", zap.Error(err))
			os.Exit(1)
		}
		fmt.Println("🎉 Backend一致性测试全部通过")
	case "basic":
		fmt.Println("🔧 运行基础操作测试...")
		suite := NewBackendConsistencyTestSuite(logger)
		if err := suite.Setup(); err != nil {
			logger.Error("❌ 测试环境初始化失败", zap.Error(err))
			os.Exit(1)
		}
		defer suite.Cleanup()

		if err := suite.testBasicCRUDConsistency(); err != nil {
			logger.Error("❌ 基础操作测试失败", zap.Error(err))
			os.Exit(1)
		}
		fmt.Println("✅ 基础操作测试通过")
	case "error":
		fmt.Println("🔧 运行错误处理测试...")
		suite := NewBackendConsistencyTestSuite(logger)
		if err := suite.Setup(); err != nil {
			logger.Error("❌ 测试环境初始化失败", zap.Error(err))
			os.Exit(1)
		}
		defer suite.Cleanup()

		if err := suite.testErrorHandlingConsistency(); err != nil {
			logger.Error("❌ 错误处理测试失败", zap.Error(err))
			os.Exit(1)
		}
		fmt.Println("✅ 错误处理测试通过")
	case "concurrent":
		fmt.Println("🔧 运行并发测试...")
		suite := NewBackendConsistencyTestSuite(logger)
		if err := suite.Setup(); err != nil {
			logger.Error("❌ 测试环境初始化失败", zap.Error(err))
			os.Exit(1)
		}
		defer suite.Cleanup()

		if err := suite.testConcurrentOperationConsistency(); err != nil {
			logger.Error("❌ 并发测试失败", zap.Error(err))
			os.Exit(1)
		}
		fmt.Println("✅ 并发测试通过")
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		os.Exit(1)
	}
}
