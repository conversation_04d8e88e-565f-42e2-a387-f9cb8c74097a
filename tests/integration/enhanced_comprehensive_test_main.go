package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"
)

func main() {
	if len(os.Args) < 2 {
		showEnhancedComprehensiveUsage()
		os.Exit(1)
	}

	testType := os.Args[1]
	logger := zap.NewExample()

	fmt.Printf("🧪 增强综合测试套件: %s\n", testType)
	fmt.Println(strings.Repeat("=", 80))

	startTime := time.Now()
	var err error

	switch testType {
	case "database-structure":
		fmt.Println("🏗️ 运行数据库结构验证测试...")
		err = runDatabaseStructureValidationTests(logger)
	case "large-volume":
		fmt.Println("📊 运行大数据量测试...")
		err = runLargeDataVolumeTests(logger)
	case "cross-cloud":
		fmt.Println("🌐 运行跨云存储兼容性测试...")
		err = runCrossCloudCompatibilityTests(logger)
	case "precise-recovery":
		fmt.Println("⏰ 运行精确时间点恢复测试...")
		err = runPrecisePointInTimeRecoveryTests(logger)
	case "concurrent-consistency":
		fmt.Println("🔄 运行并发一致性测试...")
		err = runConcurrentConsistencyTests(logger)
	case "all-enhanced":
		fmt.Println("🎯 运行所有增强测试...")
		err = runAllEnhancedTests(logger)
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		showEnhancedComprehensiveUsage()
		os.Exit(1)
	}

	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("\n❌ 测试失败: %v\n", err)
		fmt.Printf("⏱️ 测试耗时: %v\n", duration)
		os.Exit(1)
	}

	fmt.Printf("\n✅ 测试成功完成！\n")
	fmt.Printf("⏱️ 测试耗时: %v\n", duration)
}

// runAllEnhancedTests 运行所有增强测试
func runAllEnhancedTests(logger *zap.Logger) error {
	tests := []struct {
		name string
		fn   func(*zap.Logger) error
	}{
		{"数据库结构验证测试", runDatabaseStructureValidationTests},
		{"大数据量测试", runLargeDataVolumeTests},
		{"跨云存储兼容性测试", runCrossCloudCompatibilityTests},
		{"精确时间点恢复测试", runPrecisePointInTimeRecoveryTests},
		{"并发一致性测试", runConcurrentConsistencyTests},
	}

	logger.Info("🎯 开始运行所有增强测试", zap.Int("total_tests", len(tests)))

	for i, test := range tests {
		logger.Info("开始执行增强测试",
			zap.String("test", test.name),
			zap.String("progress", fmt.Sprintf("%d/%d", i+1, len(tests))))

		testStartTime := time.Now()
		if err := test.fn(logger); err != nil {
			return fmt.Errorf("增强测试失败 [%s]: %w", test.name, err)
		}

		testDuration := time.Since(testStartTime)
		logger.Info("增强测试通过",
			zap.String("test", test.name),
			zap.Duration("duration", testDuration))
	}

	logger.Info("🎉 所有增强测试全部通过")
	return nil
}

// showEnhancedComprehensiveUsage 显示增强综合测试使用说明
func showEnhancedComprehensiveUsage() {
	fmt.Println("UniBackup 增强综合测试套件")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run enhanced_comprehensive_test_main.go <test_type>")
	fmt.Println()
	fmt.Println("测试类型:")
	fmt.Println("  database-structure    - 数据库结构验证测试")
	fmt.Println("                         验证表结构、索引、触发器、存储过程的完整性")
	fmt.Println()
	fmt.Println("  large-volume         - 大数据量测试")
	fmt.Println("                         测试10万-500万条记录的备份恢复性能和完整性")
	fmt.Println()
	fmt.Println("  cross-cloud          - 跨云存储兼容性测试")
	fmt.Println("                         验证S3/GCS/Azure之间的备份格式兼容性")
	fmt.Println()
	fmt.Println("  precise-recovery     - 精确时间点恢复测试")
	fmt.Println("                         验证秒级/毫秒级/微秒级时间点恢复准确性")
	fmt.Println()
	fmt.Println("  concurrent-consistency - 并发一致性测试")
	fmt.Println("                         验证并发备份恢复场景下的数据一致性")
	fmt.Println()
	fmt.Println("  all-enhanced         - 运行所有增强测试")
	fmt.Println("                         按顺序执行上述所有测试")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 运行数据库结构验证测试")
	fmt.Println("  go run enhanced_comprehensive_test_main.go database-structure")
	fmt.Println()
	fmt.Println("  # 运行大数据量测试")
	fmt.Println("  go run enhanced_comprehensive_test_main.go large-volume")
	fmt.Println()
	fmt.Println("  # 运行所有增强测试")
	fmt.Println("  go run enhanced_comprehensive_test_main.go all-enhanced")
	fmt.Println()
	fmt.Println("注意事项:")
	fmt.Println("  - 确保MySQL和Elasticsearch测试环境已启动")
	fmt.Println("  - 大数据量测试可能需要较长时间（10-30分钟）")
	fmt.Println("  - 跨云存储测试需要配置相应的云存储环境")
	fmt.Println("  - 并发测试可能会产生大量日志输出")
	fmt.Println()
	fmt.Println("环境要求:")
	fmt.Println("  - MySQL 8.0+ 测试实例")
	fmt.Println("  - Elasticsearch 7.0+ 测试实例")
	fmt.Println("  - 足够的磁盘空间（建议10GB+用于大数据量测试）")
	fmt.Println("  - 云存储测试环境（可选，用于跨云兼容性测试）")
}
