package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	if len(os.Args) < 2 {
		showUsage()
		os.Exit(1)
	}

	testType := os.Args[1]
	logger := zap.NewExample()

	fmt.Printf("🎯 分组备份恢复统一测试套件: %s\n", testType)
	fmt.Println(strings.Repeat("=", 70))

	// 创建配置 - 支持MySQL和ES
	cfg := &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             logger,
		MaxConcurrentTasks: 5, // 分组操作需要更多并发
		TaskRetentionDays:  7,
		MaxTaskHistory:     200,

		MySQL: &types.MySQLConfig{
			Host:           "unibackup-test-mysql",
			Port:           3306,
			User:           "backup_user",
			Password:       "backup_pass",
			DBName:         "testdb",
			BinlogBasePath: "/tests/mysql-binlogs",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://unibackup-test-elasticsearch:9200"},
			ArchivalRepoName: "unibackup-test-archival",
			ManagedRepoName:  "unibackup-test-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     "/usr/share/elasticsearch/snapshots",
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		fmt.Printf("❌ 创建备份管理器失败: %v\n", err)
		os.Exit(1)
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 根据测试类型执行测试
	switch testType {
	case "group-archival-backup":
		testGroupArchivalBackup(ctx, manager)
	case "group-incremental-backup":
		testGroupIncrementalBackup(ctx, manager)
	case "group-restore":
		testGroupRestore(ctx, manager)
	case "group-task-management":
		testGroupTaskManagement(ctx, manager)
	case "group-backup-list":
		testGroupBackupList(ctx, manager)
	case "group-atomic-operations":
		testGroupAtomicOperations(ctx, manager)
	case "group-failure-scenarios":
		testGroupFailureScenarios(ctx, manager)
	case "comprehensive":
		runComprehensiveGroupTest(logger, manager)
	case "all":
		runAllGroupTests(logger, manager)
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		showUsage()
		os.Exit(1)
	}

	fmt.Println("✅ 分组测试完成")
}

// 测试分组归档备份
func testGroupArchivalBackup(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📦 测试分组归档备份")

	// 1. 准备测试数据
	fmt.Println("  📝 准备测试数据...")
	prepareTestData("group-archival")

	// 2. 创建分组备份请求
	backupAllReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeArchival,
				Description: "MySQL分组归档备份",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "group-test-es-index",
				BackupType:  types.BackupTypeArchival,
				Description: "ES分组归档备份",
			},
		},
		Description:      "跨数据源分组归档备份测试",
		Atomic:           false, // 非原子操作，允许部分成功
		CleanupOnFailure: false,
	}

	// 3. 执行分组备份
	fmt.Println("  🚀 启动分组备份...")
	taskID, err := manager.BackupAllAsync(ctx, backupAllReq)
	if err != nil {
		fmt.Printf("❌ 启动分组备份失败: %v\n", err)
		return
	}

	// 4. 监控分组任务
	task := waitForGroupTask(manager, taskID, "分组归档备份")
	if task != nil && task.Status == types.TaskStatusCompleted {
		fmt.Printf("  ✅ 分组备份成功，任务ID: %s\n", taskID)
		fmt.Printf("  📊 子任务数量: %d\n", len(task.SubTaskIDs))

		// 显示子任务详情
		for i, subTaskID := range task.SubTaskIDs {
			subTask, _ := manager.GetTask(subTaskID)
			if subTask != nil {
				backupID := ""
				if subTask.Metadata != nil && subTask.Metadata["backup_record_id"] != nil {
					backupID = subTask.Metadata["backup_record_id"].(string)
				}
				fmt.Printf("    %d. 子任务: %s, 状态: %s, 备份ID: %s\n",
					i+1, subTask.Description, subTask.Status, backupID)
			}
		}
	}
}

// 测试分组增量备份
func testGroupIncrementalBackup(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📈 测试分组增量备份")

	// 1. 准备初始数据并创建增量链初始备份
	fmt.Println("  📝 准备初始数据...")
	prepareTestData("group-incremental-initial")

	fmt.Println("  📦 创建分组增量链初始备份...")
	initialReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeChainInitial,
				Description: "MySQL增量链初始备份",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "group-test-es-incremental",
				BackupType:  types.BackupTypeChainInitial,
				Description: "ES增量链初始备份",
			},
		},
		Description:      "分组增量链初始备份",
		Atomic:           true, // 原子操作，保证链的一致性
		CleanupOnFailure: true,
	}

	// 2. 执行初始备份
	initialTaskID, err := manager.BackupAllAsync(ctx, initialReq)
	if err != nil {
		fmt.Printf("❌ 启动分组初始备份失败: %v\n", err)
		return
	}

	initialTask := waitForGroupTask(manager, initialTaskID, "分组增量链初始备份")
	if initialTask == nil || initialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 分组初始备份失败")
		return
	}

	fmt.Printf("  ✅ 分组初始备份成功，子任务数: %d\n", len(initialTask.SubTaskIDs))

	// 3. 等待并添加增量数据
	time.Sleep(3 * time.Second)
	fmt.Println("  📝 添加增量数据...")
	prepareTestData("group-incremental-delta")

	// 4. 执行增量备份
	fmt.Println("  📦 创建分组增量备份...")
	incrementalReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeChainIncremental,
				Description: "MySQL增量备份",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "group-test-es-incremental",
				BackupType:  types.BackupTypeChainIncremental,
				Description: "ES增量备份",
			},
		},
		Description:      "分组增量备份",
		Atomic:           true,
		CleanupOnFailure: false, // 增量备份失败不清理，保护链完整性
	}

	incrementalTaskID, err := manager.BackupAllAsync(ctx, incrementalReq)
	if err != nil {
		fmt.Printf("❌ 启动分组增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForGroupTask(manager, incrementalTaskID, "分组增量备份")
	if incrementalTask != nil && incrementalTask.Status == types.TaskStatusCompleted {
		fmt.Printf("  ✅ 分组增量备份成功，子任务数: %d\n", len(incrementalTask.SubTaskIDs))
	}
}

// 测试分组恢复
func testGroupRestore(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🔄 测试分组恢复")

	// 1. 先创建一些备份用于恢复测试
	fmt.Println("  📦 创建测试备份...")
	prepareTestData("group-restore-test")

	// 创建分组备份
	backupReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeArchival,
				Description: "分组恢复测试-MySQL备份",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "group-test-restore-index",
				BackupType:  types.BackupTypeArchival,
				Description: "分组恢复测试-ES备份",
			},
		},
		Description: "分组恢复测试备份",
		Atomic:      true,
	}

	backupTaskID, err := manager.BackupAllAsync(ctx, backupReq)
	if err != nil {
		fmt.Printf("❌ 创建测试备份失败: %v\n", err)
		return
	}

	backupTask := waitForGroupTask(manager, backupTaskID, "分组恢复测试备份")
	if backupTask == nil || backupTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 创建测试备份失败")
		return
	}

	// 2. 获取备份ID用于恢复
	var mysqlBackupID, esBackupID string
	for _, subTaskID := range backupTask.SubTaskIDs {
		subTask, _ := manager.GetTask(subTaskID)
		if subTask != nil && subTask.Metadata != nil && subTask.Metadata["backup_record_id"] != nil {
			backupID := subTask.Metadata["backup_record_id"].(string)
			if strings.Contains(subTask.Description, "MySQL") {
				mysqlBackupID = backupID
			} else if strings.Contains(subTask.Description, "ES") {
				esBackupID = backupID
			}
		}
	}

	fmt.Printf("  📋 MySQL备份ID: %s, ES备份ID: %s\n", mysqlBackupID, esBackupID)

	// 3. 创建分组恢复配置
	restoreConfigs := []types.RestoreConfig{
		types.NewRestoreConfig(types.MySQL, "testdb", mysqlBackupID),
		types.NewRestoreConfig(types.Elasticsearch, "group-test-restore-index", esBackupID),
	}

	batchConfig := types.NewBatchRestoreConfig(restoreConfigs, true) // 原子操作
	batchConfig.Description = "分组恢复测试"

	// 4. 执行分组恢复
	fmt.Println("  🔄 启动分组恢复...")
	restoreTaskID, err := manager.RestoreAllAsync(ctx, batchConfig)
	if err != nil {
		fmt.Printf("❌ 启动分组恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForGroupTask(manager, restoreTaskID, "分组恢复")
	if restoreTask != nil && restoreTask.Status == types.TaskStatusCompleted {
		fmt.Printf("  ✅ 分组恢复成功，子任务数: %d\n", len(restoreTask.SubTaskIDs))

		// 验证恢复的数据
		fmt.Println("  🔍 验证恢复的数据...")
		if verifyMySQLData("group-restore-test", "testdb") &&
			verifyESData("group-test-restore-index", "group-restore-test") {
			fmt.Println("  ✅ 分组数据验证成功")
		}
	}
}

// 测试分组任务管理
func testGroupTaskManagement(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📋 测试分组任务管理")

	// 1. 启动一个长时间运行的分组任务
	prepareTestData("task-mgmt-test")

	backupReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeArchival,
				Description: "任务管理测试-MySQL",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "group-test-task-mgmt",
				BackupType:  types.BackupTypeArchival,
				Description: "任务管理测试-ES",
			},
		},
		Description: "分组任务管理测试",
		Atomic:      false,
	}

	fmt.Println("  🚀 启动分组任务...")
	taskID, err := manager.BackupAllAsync(ctx, backupReq)
	if err != nil {
		fmt.Printf("❌ 启动任务失败: %v\n", err)
		return
	}

	// 2. 测试任务详情获取
	fmt.Println("  🔍 测试任务详情获取...")
	task, err := manager.GetTask(taskID)
	if err != nil {
		fmt.Printf("❌ 获取任务详情失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 主任务: %s, 状态: %s, 子任务数: %d\n",
		task.Description, task.Status, len(task.SubTaskIDs))

	// 3. 监控子任务进度
	fmt.Println("  📊 监控子任务进度...")
	for i, subTaskID := range task.SubTaskIDs {
		subTask, _ := manager.GetTask(subTaskID)
		if subTask != nil {
			fmt.Printf("    %d. 子任务: %s, 状态: %s, 进度: %d%%\n",
				i+1, subTask.Description, subTask.Status, int(subTask.Progress))
		}
	}

	// 4. 测试任务列表
	fmt.Println("  📋 测试任务列表...")
	tasks, err := manager.ListTasks()
	if err != nil {
		fmt.Printf("❌ 获取任务列表失败: %v\n", err)
		return
	}

	// 统计分组任务
	groupTaskCount := 0
	for _, t := range tasks {
		if t.Type == types.BackupAllTask || t.Type == types.RestoreAllTask {
			groupTaskCount++
		}
	}

	fmt.Printf("  ✅ 总任务数: %d, 分组任务数: %d\n", len(tasks), groupTaskCount)

	// 5. 等待任务完成
	waitForGroupTask(manager, taskID, "分组任务管理测试")
}

// 测试分组备份列表
func testGroupBackupList(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📋 测试分组备份列表")

	// 1. 列出所有MySQL备份
	fmt.Println("  📋 MySQL备份列表:")
	mysqlArchival, err := manager.ListArchivalBackups(ctx, types.MySQL)
	if err != nil {
		fmt.Printf("❌ 获取MySQL归档备份失败: %v\n", err)
	} else {
		fmt.Printf("  MySQL归档备份数: %d\n", len(mysqlArchival))
		for i, backup := range mysqlArchival {
			if i < 3 { // 只显示前3个
				fmt.Printf("    %d. ID: %s, 时间: %s\n",
					i+1, backup.Record.ID, backup.Record.Timestamp.Format("2006-01-02 15:04:05"))
			}
		}
	}

	mysqlChains, err := manager.ListIncrementalChains(ctx, types.MySQL)
	if err != nil {
		fmt.Printf("❌ 获取MySQL增量链失败: %v\n", err)
	} else {
		fmt.Printf("  MySQL增量链数: %d\n", len(mysqlChains))
	}

	// 2. 列出所有ES备份
	fmt.Println("  📋 ES备份列表:")
	esArchival, err := manager.ListArchivalBackups(ctx, types.Elasticsearch)
	if err != nil {
		fmt.Printf("❌ 获取ES归档备份失败: %v\n", err)
	} else {
		fmt.Printf("  ES归档备份数: %d\n", len(esArchival))
		for i, backup := range esArchival {
			if i < 3 { // 只显示前3个
				fmt.Printf("    %d. ID: %s, 时间: %s\n",
					i+1, backup.Record.ID, backup.Record.Timestamp.Format("2006-01-02 15:04:05"))
			}
		}
	}

	esChains, err := manager.ListIncrementalChains(ctx, types.Elasticsearch)
	if err != nil {
		fmt.Printf("❌ 获取ES增量链失败: %v\n", err)
	} else {
		fmt.Printf("  ES增量链数: %d\n", len(esChains))
	}

	// 3. 统计总备份数
	totalArchival := len(mysqlArchival) + len(esArchival)
	totalChains := len(mysqlChains) + len(esChains)
	fmt.Printf("  📊 跨数据源备份统计: 归档备份 %d 个, 增量链 %d 个\n", totalArchival, totalChains)
}

// 测试原子操作
func testGroupAtomicOperations(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("⚛️ 测试分组原子操作")

	// 1. 测试原子备份（成功场景）
	fmt.Println("  ✅ 测试原子备份成功场景...")
	prepareTestData("atomic-success")

	atomicReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeArchival,
				Description: "原子操作测试-MySQL",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "atomic-test-index",
				BackupType:  types.BackupTypeArchival,
				Description: "原子操作测试-ES",
			},
		},
		Description:      "原子备份成功测试",
		Atomic:           true,
		CleanupOnFailure: true,
	}

	taskID, err := manager.BackupAllAsync(ctx, atomicReq)
	if err != nil {
		fmt.Printf("❌ 启动原子备份失败: %v\n", err)
		return
	}

	task := waitForGroupTask(manager, taskID, "原子备份成功测试")
	if task != nil && task.Status == types.TaskStatusCompleted {
		fmt.Printf("  ✅ 原子备份成功，所有子任务都完成\n")
	}

	// 2. 测试原子恢复
	fmt.Println("  🔄 测试原子恢复...")
	// 注意：这里需要实际的备份ID，在真实场景中会从上面的备份结果中获取
	// 为了演示，我们假设使用之前创建的备份
}

// 测试失败场景
func testGroupFailureScenarios(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("💥 测试分组失败场景")

	// 1. 测试部分备份失败（非原子模式）
	fmt.Println("  ⚠️ 测试部分失败非原子模式...")

	partialFailReq := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "testdb",
				BackupType:  types.BackupTypeArchival,
				Description: "部分失败测试-MySQL",
			},
			{
				SourceType:  types.MySQL,
				SourceName:  "non-existent-db", // 故意使用不存在的数据库
				BackupType:  types.BackupTypeArchival,
				Description: "部分失败测试-MySQL（预期失败）",
			},
		},
		Description:      "部分失败非原子测试",
		Atomic:           false, // 非原子，允许部分成功
		CleanupOnFailure: false,
	}

	taskID, err := manager.BackupAllAsync(ctx, partialFailReq)
	if err != nil {
		fmt.Printf("❌ 启动部分失败测试失败: %v\n", err)
		return
	}

	task := waitForGroupTask(manager, taskID, "部分失败测试")
	if task != nil {
		fmt.Printf("  📊 主任务状态: %s\n", task.Status)

		// 检查子任务结果
		successCount := 0
		failCount := 0
		for _, subTaskID := range task.SubTaskIDs {
			subTask, _ := manager.GetTask(subTaskID)
			if subTask != nil {
				if subTask.Status == types.TaskStatusCompleted {
					successCount++
				} else if subTask.Status == types.TaskStatusFailed {
					failCount++
				}
			}
		}

		fmt.Printf("  📊 子任务结果: 成功 %d 个, 失败 %d 个\n", successCount, failCount)

		if successCount > 0 && failCount > 0 {
			fmt.Println("  ✅ 部分失败场景测试成功：允许部分操作成功")
		}
	}
}

// 综合测试
func runComprehensiveGroupTest(logger *zap.Logger, manager unibackup.BackupManager) {
	fmt.Println("🎯 运行分组功能综合测试")

	ctx := context.Background()
	testGroupArchivalBackup(ctx, manager)
	fmt.Println()
	testGroupIncrementalBackup(ctx, manager)
	fmt.Println()
	testGroupRestore(ctx, manager)
	fmt.Println()
	testGroupTaskManagement(ctx, manager)
	fmt.Println()
	testGroupBackupList(ctx, manager)
}

// 所有测试
func runAllGroupTests(logger *zap.Logger, manager unibackup.BackupManager) {
	fmt.Println("🎉 运行所有分组测试")
	ctx := context.Background()
	runComprehensiveGroupTest(logger, manager)
	fmt.Println()
	testGroupAtomicOperations(ctx, manager)
	fmt.Println()
	testGroupFailureScenarios(ctx, manager)
}

// 等待分组任务完成（包含子任务监控）
func waitForGroupTask(manager unibackup.BackupManager, taskID string, description string) *types.Task {
	fmt.Printf("⏳ 等待分组任务完成: %s\n", description)

	maxWait := 120 // 分组任务需要更长等待时间
	waited := 0

	for waited < maxWait {
		task, err := manager.GetTask(taskID)
		if err != nil {
			fmt.Printf("❌ 获取任务状态失败: %v\n", err)
			return nil
		}

		switch task.Status {
		case types.TaskStatusCompleted:
			fmt.Printf("✅ 分组任务完成: %s\n", description)
			return task
		case types.TaskStatusFailed:
			fmt.Printf("❌ 分组任务失败: %s\n", description)

			// 显示失败的子任务详情
			for i, subTaskID := range task.SubTaskIDs {
				subTask, _ := manager.GetTask(subTaskID)
				if subTask != nil && subTask.Status == types.TaskStatusFailed {
					fmt.Printf("  💥 子任务 %d 失败: %s - %s\n", i+1, subTask.Description, subTask.Error)
				}
			}
			return task
		case types.TaskStatusCancelled:
			fmt.Printf("⭕ 分组任务取消: %s\n", description)
			return task
		case types.TaskStatusRunning, types.TaskStatusPending:
			// 显示整体进度和子任务状态
			fmt.Printf("⏳ 分组任务进行中: %s (进度: %d%%, 子任务: %d)\n",
				description, int(task.Progress), len(task.SubTaskIDs))

			// 显示子任务状态
			for i, subTaskID := range task.SubTaskIDs {
				subTask, _ := manager.GetTask(subTaskID)
				if subTask != nil {
					fmt.Printf("    %d. %s: %s (%d%%)\n",
						i+1, subTask.Description, subTask.Status, int(subTask.Progress))
				}
			}

			time.Sleep(3 * time.Second)
			waited += 3
		default:
			fmt.Printf("⚠️ 未知任务状态: %s\n", task.Status)
			time.Sleep(2 * time.Second)
			waited += 2
		}
	}

	fmt.Printf("⏰ 等待超时，停止等待分组任务: %s\n", description)
	return nil
}

// 准备测试数据（MySQL + ES）
func prepareTestData(testName string) {
	// 准备MySQL测试数据
	prepareMySQLTestData(testName)

	// 准备ES测试数据
	prepareESTestData(testName)
}

// 准备MySQL测试数据
func prepareMySQLTestData(testName string) {
	db, err := getMySQLConnection()
	if err != nil {
		fmt.Printf("❌ 连接MySQL失败: %v\n", err)
		return
	}
	defer db.Close()

	// 确保测试表存在
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS group_test_data (
		id INT PRIMARY KEY AUTO_INCREMENT,
		test_name VARCHAR(100),
		test_value VARCHAR(255),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		fmt.Printf("❌ 创建MySQL测试表失败: %v\n", err)
		return
	}

	// 插入测试数据
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	testValue := fmt.Sprintf("%s-mysql-data-%s", testName, timestamp)

	_, err = db.Exec("INSERT INTO group_test_data (test_name, test_value) VALUES (?, ?)", testName, testValue)
	if err != nil {
		fmt.Printf("❌ 插入MySQL测试数据失败: %v\n", err)
		return
	}

	fmt.Printf("    ✅ MySQL数据: %s = %s\n", testName, testValue)
}

// 准备ES测试数据
func prepareESTestData(testName string) {
	esURL := "http://unibackup-test-elasticsearch:9200"
	indexName := getESIndexName(testName)

	// 创建索引和数据
	testData := map[string]interface{}{
		"test_name":  testName,
		"test_value": fmt.Sprintf("%s-es-data-%s", testName, time.Now().Format("2006-01-02 15:04:05")),
		"created_at": time.Now().Format(time.RFC3339),
	}

	// 创建索引
	mapping := map[string]interface{}{
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				"test_name":  map[string]interface{}{"type": "keyword"},
				"test_value": map[string]interface{}{"type": "text"},
				"created_at": map[string]interface{}{"type": "date"},
			},
		},
	}

	mappingBytes, _ := json.Marshal(mapping)
	req, _ := http.NewRequest("PUT", fmt.Sprintf("%s/%s", esURL, indexName), strings.NewReader(string(mappingBytes)))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err == nil {
		resp.Body.Close()
	}

	// 插入数据
	dataBytes, _ := json.Marshal(testData)
	req, _ = http.NewRequest("POST", fmt.Sprintf("%s/%s/_doc", esURL, indexName), strings.NewReader(string(dataBytes)))
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.Do(req)
	if err == nil {
		resp.Body.Close()

		// 刷新索引
		req, _ = http.NewRequest("POST", fmt.Sprintf("%s/%s/_refresh", esURL, indexName), nil)
		client.Do(req)

		fmt.Printf("    ✅ ES数据: %s = %s\n", testName, testData["test_value"])
	}
}

// 根据测试名称生成ES索引名
func getESIndexName(testName string) string {
	// 根据测试类型生成不同的索引名
	switch {
	case strings.Contains(testName, "archival"):
		return "group-test-es-index"
	case strings.Contains(testName, "incremental"):
		return "group-test-es-incremental"
	case strings.Contains(testName, "restore"):
		return "group-test-restore-index"
	case strings.Contains(testName, "atomic"):
		return "atomic-test-index"
	case strings.Contains(testName, "task-mgmt"):
		return "group-test-task-mgmt"
	default:
		return fmt.Sprintf("group-test-%s", strings.Replace(testName, "_", "-", -1))
	}
}

// 获取MySQL连接
func getMySQLConnection() (*sql.DB, error) {
	dsn := "backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/testdb?parseTime=true"
	return sql.Open("mysql", dsn)
}

// 验证MySQL数据
func verifyMySQLData(testName, dbName string) bool {
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return false
	}
	defer db.Close()

	var testValue string
	err = db.QueryRow("SELECT test_value FROM group_test_data WHERE test_name = ? ORDER BY created_at DESC LIMIT 1", testName).Scan(&testValue)
	if err != nil {
		return false
	}

	fmt.Printf("    ✅ MySQL验证: %s = %s\n", testName, testValue)
	return true
}

// 验证ES数据
func verifyESData(indexName, testName string) bool {
	esURL := "http://unibackup-test-elasticsearch:9200"

	searchURL := fmt.Sprintf("%s/%s/_search", esURL, indexName)
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"test_name": testName,
			},
		},
	}

	queryBytes, _ := json.Marshal(query)
	req, _ := http.NewRequest("POST", searchURL, strings.NewReader(string(queryBytes)))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return false
	}

	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)

	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return false
	}

	total := hits["total"].(map[string]interface{})["value"].(float64)
	if total == 0 {
		return false
	}

	fmt.Printf("    ✅ ES验证: %s 找到 %d 个文档\n", testName, int(total))
	return true
}

func showUsage() {
	fmt.Println("分组备份恢复统一测试套件")
	fmt.Println("==============================")
	fmt.Println("")
	fmt.Println("用法: group-unified-test <test-type>")
	fmt.Println("")
	fmt.Println("测试类型:")
	fmt.Println("  group-archival-backup    分组归档备份测试")
	fmt.Println("  group-incremental-backup 分组增量备份测试")
	fmt.Println("  group-restore            分组恢复测试")
	fmt.Println("  group-task-management    分组任务管理测试")
	fmt.Println("  group-backup-list        分组备份列表测试")
	fmt.Println("  group-atomic-operations  原子操作测试")
	fmt.Println("  group-failure-scenarios  失败场景测试")
	fmt.Println("  comprehensive            综合测试")
	fmt.Println("  all                      所有测试")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  group-unified-test comprehensive")
	fmt.Println("  group-unified-test group-archival-backup")
	fmt.Println("  group-unified-test all")
}
