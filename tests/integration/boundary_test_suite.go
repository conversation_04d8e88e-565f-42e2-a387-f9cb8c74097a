package main

import (
	"context"
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
)

// BoundaryTestSuite 边界条件和极限测试套件
type BoundaryTestSuite struct {
	logger     *zap.Logger
	testDir    string
	backend    interfaces.Backend
	initialMem runtime.MemStats
}

// NewBoundaryTestSuite 创建边界条件测试套件
func NewBoundaryTestSuite(logger *zap.Logger) *BoundaryTestSuite {
	return &BoundaryTestSuite{
		logger: logger,
	}
}

// Setup 初始化测试环境
func (suite *BoundaryTestSuite) Setup() error {
	// 记录初始内存状态
	runtime.GC()
	runtime.ReadMemStats(&suite.initialMem)

	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "boundary-test-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	suite.testDir = testDir

	// 创建LocalBackend实例
	backend, err := local.NewLocalBackend(testDir)
	if err != nil {
		return fmt.Errorf("创建LocalBackend失败: %w", err)
	}
	suite.backend = backend

	suite.logger.Info("边界条件测试环境初始化完成",
		zap.String("test_dir", testDir),
		zap.Uint64("initial_mem_mb", suite.initialMem.Alloc/(1024*1024)))
	return nil
}

// Cleanup 清理测试环境
func (suite *BoundaryTestSuite) Cleanup() error {
	if suite.testDir != "" {
		return os.RemoveAll(suite.testDir)
	}
	return nil
}

// RunAllTests 运行所有边界条件测试
func (suite *BoundaryTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"大数据量测试", suite.testLargeDataVolume},
		{"内存限制测试", suite.testMemoryLimits},
		{"文件数量极限测试", suite.testFileCountLimits},
		{"路径长度极限测试", suite.testPathLengthLimits},
		{"文件名边界测试", suite.testFileNameBoundaries},
		{"并发极限测试", suite.testConcurrencyLimits},
		{"磁盘空间边界测试", suite.testDiskSpaceBoundaries},
		{"长时间运行稳定性测试", suite.testLongRunningStability},
	}

	for _, test := range tests {
		suite.logger.Info("开始边界测试", zap.String("test", test.name))

		// 记录测试前的内存状态
		var beforeMem runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&beforeMem)

		start := time.Now()
		err := test.test()
		duration := time.Since(start)

		// 记录测试后的内存状态
		var afterMem runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&afterMem)

		memDiff := int64(afterMem.Alloc) - int64(beforeMem.Alloc)

		if err != nil {
			return fmt.Errorf("边界测试失败 [%s]: %w", test.name, err)
		}

		suite.logger.Info("边界测试通过",
			zap.String("test", test.name),
			zap.String("duration", duration.String()),
			zap.Int64("mem_diff_mb", memDiff/(1024*1024)))
	}

	return nil
}

// testLargeDataVolume 测试大数据量处理
func (suite *BoundaryTestSuite) testLargeDataVolume() error {
	ctx := context.Background()

	// 测试不同大小的文件
	testSizes := []struct {
		name string
		size int64
	}{
		{"100MB", 100 * 1024 * 1024},
		{"500MB", 500 * 1024 * 1024},
		{"1GB", 1024 * 1024 * 1024},
	}

	for _, testSize := range testSizes {
		suite.logger.Info("开始大数据量测试", zap.String("size", testSize.name))

		testKey := fmt.Sprintf("test/large/%s.bin", testSize.name)

		// 创建大数据流（避免一次性分配大内存）
		dataReader := &LargeDataReader{size: testSize.size}

		start := time.Now()
		written, err := suite.backend.Put(ctx, testKey, dataReader)
		writeDuration := time.Since(start)

		if err != nil {
			// 如果是磁盘空间不足，记录但不失败
			if interfaces.IsInsufficientSpace(err) {
				suite.logger.Warn("磁盘空间不足，跳过大数据量测试",
					zap.String("size", testSize.name),
					zap.Error(err))
				continue
			}
			return fmt.Errorf("大数据量写入失败 [%s]: %w", testSize.name, err)
		}

		if written != testSize.size {
			return fmt.Errorf("写入字节数不一致 [%s]: 期望 %d, 实际 %d",
				testSize.name, testSize.size, written)
		}

		suite.logger.Info("大数据量写入完成",
			zap.String("size", testSize.name),
			zap.String("duration", writeDuration.String()),
			zap.Float64("speed_mbps", float64(written)/writeDuration.Seconds()/(1024*1024)))

		// 测试大文件读取
		start = time.Now()
		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("大数据量读取失败 [%s]: %w", testSize.name, err)
		}
		defer reader.Close()

		// 分块读取以避免内存问题
		totalRead := int64(0)
		buffer := make([]byte, 1024*1024) // 1MB buffer
		for {
			n, err := reader.Read(buffer)
			if err == io.EOF {
				break
			}
			if err != nil {
				return fmt.Errorf("大数据量读取过程中出错 [%s]: %w", testSize.name, err)
			}
			totalRead += int64(n)
		}
		readDuration := time.Since(start)

		if totalRead != testSize.size {
			return fmt.Errorf("读取字节数不一致 [%s]: 期望 %d, 实际 %d",
				testSize.name, testSize.size, totalRead)
		}

		suite.logger.Info("大数据量读取完成",
			zap.String("size", testSize.name),
			zap.String("duration", readDuration.String()),
			zap.Float64("speed_mbps", float64(totalRead)/readDuration.Seconds()/(1024*1024)))

		// 清理大文件
		err = suite.backend.Delete(ctx, testKey)
		if err != nil {
			suite.logger.Warn("清理大文件失败", zap.String("key", testKey), zap.Error(err))
		}
	}

	return nil
}

// LargeDataReader 大数据流读取器，避免一次性分配大内存
type LargeDataReader struct {
	size     int64
	position int64
}

func (r *LargeDataReader) Read(p []byte) (n int, err error) {
	if r.position >= r.size {
		return 0, io.EOF
	}

	remaining := r.size - r.position
	toRead := int64(len(p))
	if toRead > remaining {
		toRead = remaining
	}

	// 填充数据模式
	for i := int64(0); i < toRead; i++ {
		p[i] = byte((r.position + i) % 256)
	}

	r.position += toRead
	return int(toRead), nil
}

// testMemoryLimits 测试内存限制
func (suite *BoundaryTestSuite) testMemoryLimits() error {
	ctx := context.Background()

	// 记录测试开始时的内存
	var startMem runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&startMem)

	// 创建多个中等大小的文件来测试内存使用
	fileCount := 50
	fileSize := int64(10 * 1024 * 1024) // 10MB per file

	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/memory/file-%d.bin", i)
		dataReader := &LargeDataReader{size: fileSize}

		_, err := suite.backend.Put(ctx, testKey, dataReader)
		if err != nil {
			return fmt.Errorf("内存限制测试写入失败 [file %d]: %w", i, err)
		}

		// 每10个文件检查一次内存使用
		if (i+1)%10 == 0 {
			var currentMem runtime.MemStats
			runtime.GC()
			runtime.ReadMemStats(&currentMem)

			memUsed := currentMem.Alloc - startMem.Alloc
			suite.logger.Info("内存使用检查",
				zap.Int("files_created", i+1),
				zap.Int64("mem_used_mb", int64(memUsed/(1024*1024))),
				zap.Int("heap_objects", int(currentMem.HeapObjects)))

			// 如果内存使用过多，警告但继续
			if memUsed > 500*1024*1024 { // 500MB
				suite.logger.Warn("内存使用较高", zap.Int64("mem_used_mb", int64(memUsed/(1024*1024))))
			}
		}
	}

	// 测试读取所有文件
	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/memory/file-%d.bin", i)

		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("内存限制测试读取失败 [file %d]: %w", i, err)
		}

		// 只读取一小部分来验证文件存在
		buffer := make([]byte, 1024)
		_, err = reader.Read(buffer)
		reader.Close()

		if err != nil && err != io.EOF {
			return fmt.Errorf("内存限制测试读取数据失败 [file %d]: %w", i, err)
		}
	}

	// 记录测试结束时的内存
	var endMem runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&endMem)

	totalMemUsed := endMem.Alloc - startMem.Alloc
	suite.logger.Info("内存限制测试完成",
		zap.Int("total_files", fileCount),
		zap.Int64("total_mem_used_mb", int64(totalMemUsed/(1024*1024))))

	return nil
}

// testFileCountLimits 测试文件数量极限
func (suite *BoundaryTestSuite) testFileCountLimits() error {
	ctx := context.Background()

	// 创建大量小文件
	fileCount := 10000
	fileSize := int64(1024) // 1KB per file

	suite.logger.Info("开始文件数量极限测试",
		zap.Int("target_count", fileCount),
		zap.Int64("file_size", fileSize))

	start := time.Now()
	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/count/file-%d.txt", i)
		data := fmt.Sprintf("file-data-%d", i)

		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(data))
		if err != nil {
			return fmt.Errorf("文件数量测试写入失败 [file %d]: %w", i, err)
		}

		// 每1000个文件报告进度
		if (i+1)%1000 == 0 {
			elapsed := time.Since(start)
			rate := float64(i+1) / elapsed.Seconds()
			suite.logger.Info("文件创建进度",
				zap.Int("created", i+1),
				zap.Float64("rate_per_sec", rate),
				zap.String("elapsed", elapsed.String()))
		}
	}

	createDuration := time.Since(start)
	suite.logger.Info("文件创建完成",
		zap.Int("count", fileCount),
		zap.String("duration", createDuration.String()),
		zap.Float64("rate_per_sec", float64(fileCount)/createDuration.Seconds()))

	// 验证所有文件都存在
	start = time.Now()
	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/count/file-%d.txt", i)

		exists, err := suite.backend.Exists(ctx, testKey)
		if err != nil {
			return fmt.Errorf("文件存在性检查失败 [file %d]: %w", i, err)
		}
		if !exists {
			return fmt.Errorf("文件不存在 [file %d]: %s", i, testKey)
		}

		// 每1000个文件报告进度
		if (i+1)%1000 == 0 {
			elapsed := time.Since(start)
			rate := float64(i+1) / elapsed.Seconds()
			suite.logger.Info("文件检查进度",
				zap.Int("checked", i+1),
				zap.Float64("rate_per_sec", rate))
		}
	}

	checkDuration := time.Since(start)
	suite.logger.Info("文件检查完成",
		zap.Int("count", fileCount),
		zap.String("duration", checkDuration.String()),
		zap.Float64("rate_per_sec", float64(fileCount)/checkDuration.Seconds()))

	return nil
}

// testPathLengthLimits 测试路径长度极限
func (suite *BoundaryTestSuite) testPathLengthLimits() error {
	ctx := context.Background()

	// 测试不同长度的路径
	pathTests := []struct {
		name   string
		length int
	}{
		{"短路径", 50},
		{"中等路径", 200},
		{"长路径", 500},
		{"极长路径", 1000},
	}

	for _, test := range pathTests {
		// 构建指定长度的路径
		pathParts := []string{"test", "path-length"}
		currentLength := len(strings.Join(pathParts, "/"))

		// 添加路径段直到达到目标长度
		segmentIndex := 0
		for currentLength < test.length-20 { // 留一些余量给文件名
			segment := fmt.Sprintf("segment-%d", segmentIndex)
			pathParts = append(pathParts, segment)
			currentLength = len(strings.Join(pathParts, "/"))
			segmentIndex++
		}

		// 添加文件名
		pathParts = append(pathParts, "test-file.txt")
		testKey := strings.Join(pathParts, "/")

		actualLength := len(testKey)
		suite.logger.Info("测试路径长度",
			zap.String("name", test.name),
			zap.Int("target_length", test.length),
			zap.Int("actual_length", actualLength),
			zap.String("path", testKey[:min(100, len(testKey))]+"..."))

		// 尝试写入文件
		testData := fmt.Sprintf("path-length-test-data-%s", test.name)
		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))

		if err != nil {
			// 路径过长可能会失败，这是预期的
			suite.logger.Warn("长路径写入失败",
				zap.String("name", test.name),
				zap.Int("length", actualLength),
				zap.Error(err))
			continue
		}

		// 验证文件可以读取
		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("长路径读取失败 [%s]: %w", test.name, err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("长路径读取数据失败 [%s]: %w", test.name, err)
		}

		if string(data) != testData {
			return fmt.Errorf("长路径数据不一致 [%s]", test.name)
		}

		suite.logger.Info("长路径测试通过",
			zap.String("name", test.name),
			zap.Int("length", actualLength))
	}

	return nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// testFileNameBoundaries 测试文件名边界条件
func (suite *BoundaryTestSuite) testFileNameBoundaries() error {
	ctx := context.Background()

	// 测试各种边界文件名
	fileNameTests := []struct {
		name       string
		fileName   string
		shouldWork bool
	}{
		{"单字符", "a", true},
		{"长文件名", strings.Repeat("a", 200), false}, // 可能超过文件系统限制
		{"中文文件名", "测试文件.txt", true},
		{"特殊字符", "file-with_special.chars.txt", true},
		{"数字文件名", "123456789.txt", true},
		{"点开头", ".hidden-file.txt", true},
		{"多个点", "file.with.many.dots.txt", true},
		{"空格文件名", "file with spaces.txt", true},
	}

	for _, test := range fileNameTests {
		testKey := fmt.Sprintf("test/filename/%s", test.fileName)
		testData := fmt.Sprintf("filename-test-data-%s", test.name)

		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))

		if err != nil {
			if test.shouldWork {
				return fmt.Errorf("文件名测试失败 [%s]: %w", test.name, err)
			} else {
				suite.logger.Info("文件名被拒绝（预期）",
					zap.String("name", test.name),
					zap.String("filename", test.fileName),
					zap.Error(err))
				continue
			}
		}

		if !test.shouldWork {
			suite.logger.Warn("文件名应该被拒绝但成功了",
				zap.String("name", test.name),
				zap.String("filename", test.fileName))
		}

		// 验证文件可以读取
		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("文件名测试读取失败 [%s]: %w", test.name, err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("文件名测试读取数据失败 [%s]: %w", test.name, err)
		}

		if string(data) != testData {
			return fmt.Errorf("文件名测试数据不一致 [%s]", test.name)
		}

		suite.logger.Info("文件名测试通过",
			zap.String("name", test.name),
			zap.String("filename", test.fileName))
	}

	return nil
}

// testConcurrencyLimits 测试并发极限
func (suite *BoundaryTestSuite) testConcurrencyLimits() error {
	ctx := context.Background()

	// 测试不同并发级别
	concurrencyLevels := []int{50, 100, 200, 500}

	for _, concurrency := range concurrencyLevels {
		suite.logger.Info("开始并发极限测试", zap.Int("concurrency", concurrency))

		start := time.Now()
		errChan := make(chan error, concurrency)

		for i := 0; i < concurrency; i++ {
			go func(index int) {
				testKey := fmt.Sprintf("test/concurrency/%d/file-%d.txt", concurrency, index)
				testData := fmt.Sprintf("concurrency-test-data-%d-%d", concurrency, index)

				_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
				errChan <- err
			}(i)
		}

		// 收集所有结果
		errorCount := 0
		for i := 0; i < concurrency; i++ {
			if err := <-errChan; err != nil {
				errorCount++
				if errorCount <= 5 { // 只记录前5个错误
					suite.logger.Warn("并发写入失败",
						zap.Int("concurrency", concurrency),
						zap.Error(err))
				}
			}
		}

		duration := time.Since(start)
		successRate := float64(concurrency-errorCount) / float64(concurrency) * 100

		suite.logger.Info("并发极限测试完成",
			zap.Int("concurrency", concurrency),
			zap.String("duration", duration.String()),
			zap.Float64("success_rate", successRate),
			zap.Int("errors", errorCount))

		// 如果成功率太低，记录警告但不失败
		if successRate < 90 {
			suite.logger.Warn("并发成功率较低",
				zap.Int("concurrency", concurrency),
				zap.Float64("success_rate", successRate))
		}
	}

	return nil
}

// testDiskSpaceBoundaries 测试磁盘空间边界
func (suite *BoundaryTestSuite) testDiskSpaceBoundaries() error {
	ctx := context.Background()

	// 由于syscall.Statfs在不同操作系统上的兼容性问题，
	// 我们使用一个固定的测试大小来进行磁盘空间边界测试
	suite.logger.Info("开始磁盘空间边界测试（使用固定测试大小）")

	// 使用一个相对较大但安全的测试大小
	testSize := uint64(1024 * 1024 * 1024) // 1GB

	suite.logger.Info("开始磁盘空间边界测试",
		zap.Int64("test_size_gb", int64(testSize/(1024*1024*1024))))

	testKey := "test/disk-space/large-file.bin"
	dataReader := &LargeDataReader{size: int64(testSize)}

	start := time.Now()
	written, err := suite.backend.Put(ctx, testKey, dataReader)
	duration := time.Since(start)

	if err != nil {
		if interfaces.IsInsufficientSpace(err) {
			suite.logger.Info("磁盘空间不足错误被正确处理", zap.Error(err))
			return nil
		}
		return fmt.Errorf("磁盘空间边界测试失败: %w", err)
	}

	suite.logger.Info("磁盘空间边界测试完成",
		zap.Int64("written_gb", written/(1024*1024*1024)),
		zap.String("duration", duration.String()),
		zap.Float64("speed_mbps", float64(written)/duration.Seconds()/(1024*1024)))

	// 清理大文件
	err = suite.backend.Delete(ctx, testKey)
	if err != nil {
		suite.logger.Warn("清理大文件失败", zap.Error(err))
	}

	return nil
}

// testLongRunningStability 测试长时间运行稳定性
func (suite *BoundaryTestSuite) testLongRunningStability() error {
	ctx := context.Background()

	// 运行5分钟的稳定性测试
	testDuration := 5 * time.Minute
	suite.logger.Info("开始长时间运行稳定性测试", zap.String("duration", testDuration.String()))

	start := time.Now()
	operationCount := 0
	errorCount := 0

	for time.Since(start) < testDuration {
		testKey := fmt.Sprintf("test/stability/file-%d.txt", operationCount)
		testData := fmt.Sprintf("stability-test-data-%d", operationCount)

		// 写入文件
		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
		if err != nil {
			errorCount++
			if errorCount <= 5 {
				suite.logger.Warn("稳定性测试写入失败", zap.Error(err))
			}
		} else {
			// 读取文件
			reader, err := suite.backend.Get(ctx, testKey)
			if err != nil {
				errorCount++
				if errorCount <= 5 {
					suite.logger.Warn("稳定性测试读取失败", zap.Error(err))
				}
			} else {
				io.ReadAll(reader)
				reader.Close()
			}

			// 删除文件
			suite.backend.Delete(ctx, testKey)
		}

		operationCount++

		// 每1000次操作报告一次进度
		if operationCount%1000 == 0 {
			elapsed := time.Since(start)
			rate := float64(operationCount) / elapsed.Seconds()
			errorRate := float64(errorCount) / float64(operationCount) * 100

			suite.logger.Info("稳定性测试进度",
				zap.Int("operations", operationCount),
				zap.String("elapsed", elapsed.String()),
				zap.Float64("rate_per_sec", rate),
				zap.Float64("error_rate", errorRate))
		}

		// 短暂休息以避免过度占用资源
		if operationCount%100 == 0 {
			time.Sleep(time.Millisecond * 10)
		}
	}

	totalDuration := time.Since(start)
	errorRate := float64(errorCount) / float64(operationCount) * 100

	suite.logger.Info("长时间运行稳定性测试完成",
		zap.Int("total_operations", operationCount),
		zap.String("total_duration", totalDuration.String()),
		zap.Float64("avg_rate_per_sec", float64(operationCount)/totalDuration.Seconds()),
		zap.Int("total_errors", errorCount),
		zap.Float64("error_rate", errorRate))

	// 如果错误率过高，返回错误
	if errorRate > 5.0 {
		return fmt.Errorf("稳定性测试错误率过高: %.2f%%", errorRate)
	}

	return nil
}

// runBoundaryTests 运行边界条件测试的主函数
func runBoundaryTests(logger *zap.Logger) error {
	logger.Info("🔧 开始边界条件和极限测试")

	suite := NewBoundaryTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("边界条件测试失败: %w", err)
	}

	logger.Info("✅ 边界条件和极限测试全部通过")
	return nil
}

// 边界条件测试主入口
func main() {
	// 配置日志
	logger, _ := zap.NewDevelopment()

	if len(os.Args) < 2 {
		logger.Error("使用方法: boundary-test <test-type>")
		logger.Info("可用的测试类型:")
		logger.Info("  all                     - 运行所有边界条件测试")
		os.Exit(1)
	}

	testType := os.Args[1]

	logger.Info("🚀 开始边界条件测试", zap.String("type", testType))

	var err error
	switch testType {
	case "all":
		err = runBoundaryTests(logger)
	default:
		logger.Error("未知的测试类型", zap.String("type", testType))
		os.Exit(1)
	}

	if err != nil {
		logger.Error("边界条件测试失败", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("✅ 边界条件测试全部通过", zap.String("type", testType))
}
