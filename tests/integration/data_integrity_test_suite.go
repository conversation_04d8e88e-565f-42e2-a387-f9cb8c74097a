package main

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
)

// DataIntegrityTestSuite 数据完整性测试套件
type DataIntegrityTestSuite struct {
	logger    *zap.Logger
	testDir   string
	backend   interfaces.Backend
	testFiles map[string][]byte // 测试文件及其内容
	checksums map[string]string // 文件校验和
}

// NewDataIntegrityTestSuite 创建数据完整性测试套件
func NewDataIntegrityTestSuite(logger *zap.Logger) *DataIntegrityTestSuite {
	return &DataIntegrityTestSuite{
		logger:    logger,
		testFiles: make(map[string][]byte),
		checksums: make(map[string]string),
	}
}

// Setup 初始化测试环境
func (suite *DataIntegrityTestSuite) Setup() error {
	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "data-integrity-test-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	suite.testDir = testDir

	// 创建LocalBackend实例
	backend, err := local.NewLocalBackend(testDir)
	if err != nil {
		return fmt.Errorf("创建LocalBackend失败: %w", err)
	}
	suite.backend = backend

	// 准备测试数据
	suite.prepareTestData()

	suite.logger.Info("数据完整性测试环境初始化完成", zap.String("test_dir", testDir))
	return nil
}

// Cleanup 清理测试环境
func (suite *DataIntegrityTestSuite) Cleanup() error {
	if suite.testDir != "" {
		return os.RemoveAll(suite.testDir)
	}
	return nil
}

// prepareTestData 准备测试数据
func (suite *DataIntegrityTestSuite) prepareTestData() {
	// 小文本文件
	suite.testFiles["small.txt"] = []byte("Hello, UniBackup! This is a small test file.")

	// 中等大小的JSON文件
	jsonData := `{
		"database": "test_db",
		"tables": ["users", "orders", "products"],
		"backup_time": "2025-07-19T12:00:00Z",
		"metadata": {
			"version": "1.0",
			"checksum": "abc123def456"
		}
	}`
	suite.testFiles["metadata.json"] = []byte(jsonData)

	// 大文件（1MB）
	largeData := make([]byte, 1024*1024)
	for i := range largeData {
		largeData[i] = byte(i % 256)
	}
	suite.testFiles["large.bin"] = largeData

	// 包含特殊字符的文件
	specialData := "特殊字符测试: αβγδε, 中文测试, emoji: 🚀🔒📊"
	suite.testFiles["special_chars.txt"] = []byte(specialData)

	// 空文件
	suite.testFiles["empty.txt"] = []byte{}

	// 计算所有文件的校验和
	for filename, data := range suite.testFiles {
		hash := sha256.Sum256(data)
		suite.checksums[filename] = fmt.Sprintf("%x", hash)
	}
}

// RunAllTests 运行所有数据完整性测试
func (suite *DataIntegrityTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"数据写入完整性测试", suite.testDataWriteIntegrity},
		{"数据读取完整性测试", suite.testDataReadIntegrity},
		{"校验和验证测试", suite.testChecksumVerification},
		{"数据一致性测试", suite.testDataConsistency},
		{"增量数据完整性测试", suite.testIncrementalDataIntegrity},
		{"并发数据完整性测试", suite.testConcurrentDataIntegrity},
		{"大文件完整性测试", suite.testLargeFileIntegrity},
		{"特殊字符数据完整性测试", suite.testSpecialCharacterIntegrity},
		{"空文件处理测试", suite.testEmptyFileHandling},
		{"数据损坏检测测试", suite.testDataCorruptionDetection},
	}

	for _, test := range tests {
		suite.logger.Info("开始数据完整性测试", zap.String("test", test.name))
		if err := test.test(); err != nil {
			return fmt.Errorf("数据完整性测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("数据完整性测试通过", zap.String("test", test.name))
	}

	return nil
}

// testDataWriteIntegrity 测试数据写入完整性
func (suite *DataIntegrityTestSuite) testDataWriteIntegrity() error {
	ctx := context.Background()

	for filename, data := range suite.testFiles {
		key := fmt.Sprintf("test/write-integrity/%s", filename)

		// 写入数据
		written, err := suite.backend.Put(ctx, key, strings.NewReader(string(data)))
		if err != nil {
			return fmt.Errorf("写入文件失败 [%s]: %w", filename, err)
		}

		// 验证写入字节数
		if written != int64(len(data)) {
			return fmt.Errorf("写入字节数不匹配 [%s]: 期望 %d, 实际 %d",
				filename, len(data), written)
		}

		suite.logger.Info("文件写入完整性验证通过",
			zap.String("file", filename),
			zap.Int64("size", written))
	}

	return nil
}

// testDataReadIntegrity 测试数据读取完整性
func (suite *DataIntegrityTestSuite) testDataReadIntegrity() error {
	ctx := context.Background()

	// 先写入所有测试文件
	for filename, data := range suite.testFiles {
		key := fmt.Sprintf("test/read-integrity/%s", filename)
		_, err := suite.backend.Put(ctx, key, strings.NewReader(string(data)))
		if err != nil {
			return fmt.Errorf("预写入文件失败 [%s]: %w", filename, err)
		}
	}

	// 读取并验证完整性
	for filename, expectedData := range suite.testFiles {
		key := fmt.Sprintf("test/read-integrity/%s", filename)

		reader, err := suite.backend.Get(ctx, key)
		if err != nil {
			return fmt.Errorf("读取文件失败 [%s]: %w", filename, err)
		}
		defer reader.Close()

		actualData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取文件内容失败 [%s]: %w", filename, err)
		}

		// 验证数据完整性
		if len(actualData) != len(expectedData) {
			return fmt.Errorf("文件大小不匹配 [%s]: 期望 %d, 实际 %d",
				filename, len(expectedData), len(actualData))
		}

		if string(actualData) != string(expectedData) {
			return fmt.Errorf("文件内容不匹配 [%s]", filename)
		}

		suite.logger.Info("文件读取完整性验证通过",
			zap.String("file", filename),
			zap.Int("size", len(actualData)))
	}

	return nil
}

// testChecksumVerification 测试校验和验证
func (suite *DataIntegrityTestSuite) testChecksumVerification() error {
	ctx := context.Background()

	for filename, data := range suite.testFiles {
		key := fmt.Sprintf("test/checksum/%s", filename)

		// 写入文件
		_, err := suite.backend.Put(ctx, key, strings.NewReader(string(data)))
		if err != nil {
			return fmt.Errorf("写入文件失败 [%s]: %w", filename, err)
		}

		// 读取文件并计算校验和
		reader, err := suite.backend.Get(ctx, key)
		if err != nil {
			return fmt.Errorf("读取文件失败 [%s]: %w", filename, err)
		}
		defer reader.Close()

		hash := sha256.New()
		_, err = io.Copy(hash, reader)
		if err != nil {
			return fmt.Errorf("计算校验和失败 [%s]: %w", filename, err)
		}

		actualChecksum := fmt.Sprintf("%x", hash.Sum(nil))
		expectedChecksum := suite.checksums[filename]

		if actualChecksum != expectedChecksum {
			return fmt.Errorf("校验和不匹配 [%s]: 期望 %s, 实际 %s",
				filename, expectedChecksum, actualChecksum)
		}

		suite.logger.Info("校验和验证通过",
			zap.String("file", filename),
			zap.String("checksum", actualChecksum[:16]+"..."))
	}

	return nil
}

// testDataConsistency 测试数据一致性
func (suite *DataIntegrityTestSuite) testDataConsistency() error {
	ctx := context.Background()
	testKey := "test/consistency/data.txt"
	testData := "数据一致性测试内容"

	// 多次写入相同数据
	for i := 0; i < 10; i++ {
		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
		if err != nil {
			return fmt.Errorf("第%d次写入失败: %w", i+1, err)
		}

		// 立即读取验证
		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("第%d次读取失败: %w", i+1, err)
		}
		defer reader.Close()

		actualData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("第%d次读取内容失败: %w", i+1, err)
		}

		if string(actualData) != testData {
			return fmt.Errorf("第%d次数据不一致", i+1)
		}
	}

	suite.logger.Info("数据一致性测试通过", zap.Int("iterations", 10))
	return nil
}

// testIncrementalDataIntegrity 测试增量数据完整性
func (suite *DataIntegrityTestSuite) testIncrementalDataIntegrity() error {
	ctx := context.Background()
	baseKey := "test/incremental/base.txt"

	// 基础数据
	baseData := "基础数据内容"
	_, err := suite.backend.Put(ctx, baseKey, strings.NewReader(baseData))
	if err != nil {
		return fmt.Errorf("写入基础数据失败: %w", err)
	}

	// 模拟增量更新
	increments := []string{
		"基础数据内容\n增量1",
		"基础数据内容\n增量1\n增量2",
		"基础数据内容\n增量1\n增量2\n增量3",
	}

	for i, incrementData := range increments {
		incrementKey := fmt.Sprintf("test/incremental/increment-%d.txt", i+1)

		// 写入增量数据
		_, err := suite.backend.Put(ctx, incrementKey, strings.NewReader(incrementData))
		if err != nil {
			return fmt.Errorf("写入增量数据%d失败: %w", i+1, err)
		}

		// 验证增量数据完整性
		reader, err := suite.backend.Get(ctx, incrementKey)
		if err != nil {
			return fmt.Errorf("读取增量数据%d失败: %w", i+1, err)
		}
		defer reader.Close()

		actualData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取增量内容%d失败: %w", i+1, err)
		}

		if string(actualData) != incrementData {
			return fmt.Errorf("增量数据%d不一致", i+1)
		}

		suite.logger.Info("增量数据完整性验证通过", zap.Int("increment", i+1))
	}

	return nil
}

// testConcurrentDataIntegrity 测试并发数据完整性
func (suite *DataIntegrityTestSuite) testConcurrentDataIntegrity() error {
	ctx := context.Background()
	concurrency := 20

	errChan := make(chan error, concurrency)

	for i := 0; i < concurrency; i++ {
		go func(goroutineID int) {
			key := fmt.Sprintf("test/concurrent/file-%d.txt", goroutineID)
			data := fmt.Sprintf("并发测试数据-%d", goroutineID)

			// 写入数据
			_, err := suite.backend.Put(ctx, key, strings.NewReader(data))
			if err != nil {
				errChan <- fmt.Errorf("并发写入失败 [goroutine %d]: %w", goroutineID, err)
				return
			}

			// 立即读取验证
			reader, err := suite.backend.Get(ctx, key)
			if err != nil {
				errChan <- fmt.Errorf("并发读取失败 [goroutine %d]: %w", goroutineID, err)
				return
			}
			defer reader.Close()

			actualData, err := io.ReadAll(reader)
			if err != nil {
				errChan <- fmt.Errorf("并发读取内容失败 [goroutine %d]: %w", goroutineID, err)
				return
			}

			if string(actualData) != data {
				errChan <- fmt.Errorf("并发数据不一致 [goroutine %d]", goroutineID)
				return
			}

			errChan <- nil
		}(i)
	}

	// 检查所有并发操作结果
	for i := 0; i < concurrency; i++ {
		if err := <-errChan; err != nil {
			return fmt.Errorf("并发数据完整性测试失败: %w", err)
		}
	}

	suite.logger.Info("并发数据完整性测试通过", zap.Int("concurrency", concurrency))
	return nil
}

// testLargeFileIntegrity 测试大文件完整性
func (suite *DataIntegrityTestSuite) testLargeFileIntegrity() error {
	ctx := context.Background()

	// 创建10MB的测试数据
	largeData := make([]byte, 10*1024*1024)
	for i := range largeData {
		largeData[i] = byte(i % 256)
	}

	// 计算原始数据的MD5和SHA256
	md5Hash := md5.Sum(largeData)
	sha256Hash := sha256.Sum256(largeData)

	key := "test/large-file/10mb.bin"

	// 写入大文件
	start := time.Now()
	written, err := suite.backend.Put(ctx, key, strings.NewReader(string(largeData)))
	writeTime := time.Since(start)

	if err != nil {
		return fmt.Errorf("写入大文件失败: %w", err)
	}

	if written != int64(len(largeData)) {
		return fmt.Errorf("大文件写入字节数不匹配: 期望 %d, 实际 %d",
			len(largeData), written)
	}

	// 读取大文件并验证完整性
	start = time.Now()
	reader, err := suite.backend.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("读取大文件失败: %w", err)
	}
	defer reader.Close()

	// 分块读取并计算校验和
	md5Hasher := md5.New()
	sha256Hasher := sha256.New()

	buffer := make([]byte, 64*1024) // 64KB buffer
	totalRead := int64(0)

	for {
		n, err := reader.Read(buffer)
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("读取大文件内容失败: %w", err)
		}

		md5Hasher.Write(buffer[:n])
		sha256Hasher.Write(buffer[:n])
		totalRead += int64(n)
	}

	readTime := time.Since(start)

	// 验证读取字节数
	if totalRead != int64(len(largeData)) {
		return fmt.Errorf("大文件读取字节数不匹配: 期望 %d, 实际 %d",
			len(largeData), totalRead)
	}

	// 验证校验和
	actualMD5 := md5Hasher.Sum(nil)
	actualSHA256 := sha256Hasher.Sum(nil)

	if string(actualMD5) != string(md5Hash[:]) {
		return fmt.Errorf("大文件MD5校验和不匹配")
	}

	if string(actualSHA256) != string(sha256Hash[:]) {
		return fmt.Errorf("大文件SHA256校验和不匹配")
	}

	suite.logger.Info("大文件完整性测试通过",
		zap.Int("size_mb", len(largeData)/(1024*1024)),
		zap.String("write_time", writeTime.String()),
		zap.String("read_time", readTime.String()),
		zap.Float64("write_speed_mbps", float64(len(largeData))/writeTime.Seconds()/(1024*1024)),
		zap.Float64("read_speed_mbps", float64(len(largeData))/readTime.Seconds()/(1024*1024)))

	return nil
}

// testSpecialCharacterIntegrity 测试特殊字符数据完整性
func (suite *DataIntegrityTestSuite) testSpecialCharacterIntegrity() error {
	ctx := context.Background()

	specialData := suite.testFiles["special_chars.txt"]
	key := "test/special/chars.txt"

	// 写入特殊字符数据
	_, err := suite.backend.Put(ctx, key, strings.NewReader(string(specialData)))
	if err != nil {
		return fmt.Errorf("写入特殊字符数据失败: %w", err)
	}

	// 读取并验证
	reader, err := suite.backend.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("读取特殊字符数据失败: %w", err)
	}
	defer reader.Close()

	actualData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取特殊字符内容失败: %w", err)
	}

	if string(actualData) != string(specialData) {
		return fmt.Errorf("特殊字符数据不一致")
	}

	suite.logger.Info("特殊字符数据完整性测试通过",
		zap.Int("original_length", len(specialData)),
		zap.Int("read_length", len(actualData)))

	return nil
}

// testEmptyFileHandling 测试空文件处理
func (suite *DataIntegrityTestSuite) testEmptyFileHandling() error {
	ctx := context.Background()
	key := "test/empty/file.txt"

	// 写入空文件
	written, err := suite.backend.Put(ctx, key, strings.NewReader(""))
	if err != nil {
		return fmt.Errorf("写入空文件失败: %w", err)
	}

	if written != 0 {
		return fmt.Errorf("空文件写入字节数应为0, 实际为 %d", written)
	}

	// 读取空文件
	reader, err := suite.backend.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("读取空文件失败: %w", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取空文件内容失败: %w", err)
	}

	if len(data) != 0 {
		return fmt.Errorf("空文件读取应返回0字节, 实际返回 %d 字节", len(data))
	}

	suite.logger.Info("空文件处理测试通过")
	return nil
}

// testDataCorruptionDetection 测试数据损坏检测
func (suite *DataIntegrityTestSuite) testDataCorruptionDetection() error {
	ctx := context.Background()
	key := "test/corruption/file.txt"
	originalData := "这是用于损坏检测的测试数据"

	// 写入原始数据
	_, err := suite.backend.Put(ctx, key, strings.NewReader(originalData))
	if err != nil {
		return fmt.Errorf("写入原始数据失败: %w", err)
	}

	// 计算原始数据校验和
	originalHash := sha256.Sum256([]byte(originalData))

	// 模拟直接修改文件系统中的文件（如果可能）
	filePath := filepath.Join(suite.testDir, key)
	if _, err := os.Stat(filePath); err == nil {
		// 文件存在，尝试修改它来模拟损坏
		corruptedData := "这是被损坏的测试数据"
		err = os.WriteFile(filePath, []byte(corruptedData), 0644)
		if err != nil {
			suite.logger.Warn("无法模拟文件损坏", zap.Error(err))
			return nil // 跳过这个测试，不算失败
		}

		// 读取被损坏的数据
		reader, err := suite.backend.Get(ctx, key)
		if err != nil {
			return fmt.Errorf("读取损坏文件失败: %w", err)
		}
		defer reader.Close()

		actualData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取损坏文件内容失败: %w", err)
		}

		// 计算实际数据校验和
		actualHash := sha256.Sum256(actualData)

		// 验证校验和不匹配（检测到损坏）
		if string(actualHash[:]) == string(originalHash[:]) {
			return fmt.Errorf("未能检测到数据损坏")
		}

		suite.logger.Info("数据损坏检测测试通过",
			zap.String("original_checksum", fmt.Sprintf("%x", originalHash)[:16]+"..."),
			zap.String("actual_checksum", fmt.Sprintf("%x", actualHash)[:16]+"..."))
	} else {
		suite.logger.Info("跳过数据损坏检测测试（无法访问底层文件）")
	}

	return nil
}

// 数据完整性测试主入口
func main() {
	// 配置日志
	logger, _ := zap.NewDevelopment()

	if len(os.Args) < 2 {
		logger.Error("使用方法: data-integrity-test <test-type>", zap.String("error", "未指定测试类型"))
		logger.Info("可用的测试类型:", zap.String("available_tests", "all"))
		logger.Info("  all                     - 运行所有数据完整性测试", zap.String("all", "all"))
		os.Exit(1)
	}

	testType := os.Args[1]

	logger.Info("🚀 开始数据完整性测试", zap.String("type", testType))

	var err error
	switch testType {
	case "all":
		err = runDataIntegrityTests(logger)
	default:
		logger.Error("未知的测试类型", zap.String("type", testType))
		os.Exit(1)
	}

	if err != nil {
		logger.Error("数据完整性测试失败", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("✅ 数据完整性测试全部通过", zap.String("type", testType))
}

// runDataIntegrityTests 运行数据完整性测试的主函数
func runDataIntegrityTests(logger *zap.Logger) error {
	logger.Info("🔍 开始数据完整性测试")

	suite := NewDataIntegrityTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("数据完整性测试失败: %w", err)
	}

	logger.Info("✅ 数据完整性测试全部通过")
	return nil
}
