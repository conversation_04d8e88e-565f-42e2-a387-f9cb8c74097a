package main

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"

	_ "github.com/go-sql-driver/mysql"
)

// PrecisePointInTimeRecoveryTestSuite 精确时间点恢复测试套件
type PrecisePointInTimeRecoveryTestSuite struct {
	logger  *zap.Logger
	manager unibackup.BackupManager
	config  *types.Config
}

// TimePointData 时间点数据记录
type TimePointData struct {
	Timestamp   time.Time
	BackupID    string
	DataHash    string
	RecordCount int
	Description string
}

// NewPrecisePointInTimeRecoveryTestSuite 创建精确时间点恢复测试套件
func NewPrecisePointInTimeRecoveryTestSuite(logger *zap.Logger) *PrecisePointInTimeRecoveryTestSuite {
	return &PrecisePointInTimeRecoveryTestSuite{
		logger: logger,
	}
}

// Setup 初始化测试环境
func (suite *PrecisePointInTimeRecoveryTestSuite) Setup() error {
	// 创建配置
	suite.config = &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             suite.logger,
		MaxConcurrentTasks: 3,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		MySQL: &types.MySQLConfig{
			Host:           "unibackup-test-mysql",
			Port:           3306,
			User:           "backup_user",
			Password:       "backup_pass",
			DBName:         "testdb",
			BinlogBasePath: "/tests/mysql-binlogs",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(suite.config)
	if err != nil {
		return fmt.Errorf("创建备份管理器失败: %w", err)
	}
	suite.manager = manager

	// 准备测试数据库表
	if err := suite.prepareTestTable(); err != nil {
		return fmt.Errorf("准备测试表失败: %w", err)
	}

	suite.logger.Info("精确时间点恢复测试环境初始化完成")
	return nil
}

// Cleanup 清理测试环境
func (suite *PrecisePointInTimeRecoveryTestSuite) Cleanup() error {
	// 清理测试数据
	if err := suite.cleanupTestData(); err != nil {
		suite.logger.Warn("清理测试数据失败", zap.Error(err))
	}

	if suite.manager != nil {
		return suite.manager.Shutdown()
	}
	return nil
}

// prepareTestTable 准备测试表
func (suite *PrecisePointInTimeRecoveryTestSuite) prepareTestTable() error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		suite.config.MySQL.User,
		suite.config.MySQL.Password,
		suite.config.MySQL.Host,
		suite.config.MySQL.Port,
		suite.config.MySQL.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	// 创建精确时间点恢复测试表
	createSQL := `
	CREATE TABLE IF NOT EXISTS precise_recovery_test (
		id INT AUTO_INCREMENT PRIMARY KEY,
		operation_time TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
		operation_type VARCHAR(50) NOT NULL,
		data_value TEXT NOT NULL,
		sequence_number INT NOT NULL,
		INDEX idx_operation_time (operation_time),
		INDEX idx_sequence (sequence_number)
	) ENGINE=InnoDB`

	if _, err := db.Exec(createSQL); err != nil {
		return fmt.Errorf("创建测试表失败: %w", err)
	}

	// 清空表数据
	if _, err := db.Exec("DELETE FROM precise_recovery_test"); err != nil {
		return fmt.Errorf("清空测试表失败: %w", err)
	}

	suite.logger.Info("精确时间点恢复测试表准备完成")
	return nil
}

// RunAllTests 运行所有精确时间点恢复测试
func (suite *PrecisePointInTimeRecoveryTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"秒级精确时间点恢复测试", suite.testSecondLevelPreciseRecovery},
		{"毫秒级精确时间点恢复测试", suite.testMillisecondLevelPreciseRecovery},
		{"微秒级精确时间点恢复测试", suite.testMicrosecondLevelPreciseRecovery},
		{"连续时间点恢复准确性测试", suite.testContinuousTimePointRecovery},
		{"高频操作时间点恢复测试", suite.testHighFrequencyOperationRecovery},
		{"跨备份时间点恢复测试", suite.testCrossBackupTimePointRecovery},
	}

	for _, test := range tests {
		suite.logger.Info("开始执行精确时间点恢复测试", zap.String("test", test.name))
		if err := test.test(); err != nil {
			suite.logger.Error("精确时间点恢复测试失败", zap.String("test", test.name), zap.Error(err))
			return fmt.Errorf("测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("精确时间点恢复测试通过", zap.String("test", test.name))
	}

	suite.logger.Info("✅ 所有精确时间点恢复测试通过")
	return nil
}

// testSecondLevelPreciseRecovery 测试秒级精确时间点恢复
func (suite *PrecisePointInTimeRecoveryTestSuite) testSecondLevelPreciseRecovery() error {
	ctx := context.Background()

	// 1. 清空测试数据
	if err := suite.clearTestData(); err != nil {
		return fmt.Errorf("清空测试数据失败: %w", err)
	}

	var timePoints []TimePointData

	// 2. 创建多个精确时间点的数据
	for i := 0; i < 10; i++ {
		// 记录操作前的精确时间
		operationTime := time.Now()

		// 插入数据
		if err := suite.insertTimestampedData(operationTime, fmt.Sprintf("second-level-operation-%d", i), i); err != nil {
			return fmt.Errorf("插入时间戳数据失败: %w", err)
		}

		// 等待1秒确保时间点分离
		time.Sleep(1 * time.Second)

		// 创建备份
		taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival,
			fmt.Sprintf("秒级精确恢复测试-%d", i))
		if err != nil {
			return fmt.Errorf("创建备份失败: %w", err)
		}

		task := suite.waitForTask(taskID, fmt.Sprintf("秒级精确备份-%d", i))
		if task == nil || task.Status != types.TaskStatusCompleted {
			return fmt.Errorf("备份%d未成功完成", i)
		}

		backupID := task.Metadata["backup_record_id"].(string)

		// 获取数据基线
		recordCount, dataHash, err := suite.getDataBaseline()
		if err != nil {
			return fmt.Errorf("获取数据基线失败: %w", err)
		}

		timePoint := TimePointData{
			Timestamp:   operationTime,
			BackupID:    backupID,
			DataHash:    dataHash,
			RecordCount: recordCount,
			Description: fmt.Sprintf("秒级时间点-%d", i),
		}
		timePoints = append(timePoints, timePoint)

		suite.logger.Info("创建秒级时间点",
			zap.Int("index", i),
			zap.String("timestamp", operationTime.Format("2006-01-02 15:04:05.000000")),
			zap.String("backup_id", backupID),
			zap.Int("records", recordCount))
	}

	// 3. 验证每个时间点的恢复准确性
	for i, timePoint := range timePoints {
		suite.logger.Info("验证秒级时间点恢复",
			zap.Int("index", i),
			zap.String("timestamp", timePoint.Timestamp.Format("2006-01-02 15:04:05.000000")))

		// 清空数据
		if err := suite.clearTestData(); err != nil {
			return fmt.Errorf("清空测试数据失败: %w", err)
		}

		// 恢复到指定时间点
		restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", timePoint.BackupID)
		restoreConfig.Description = fmt.Sprintf("秒级精确恢复验证-%d", i)

		restoreTaskID, err := suite.manager.RestoreAsync(ctx, restoreConfig)
		if err != nil {
			return fmt.Errorf("启动恢复失败: %w", err)
		}

		restoreTask := suite.waitForTask(restoreTaskID, fmt.Sprintf("秒级精确恢复-%d", i))
		if restoreTask == nil || restoreTask.Status != types.TaskStatusCompleted {
			return fmt.Errorf("恢复%d未成功完成", i)
		}

		// 验证恢复后的数据
		restoredCount, restoredHash, err := suite.getDataBaseline()
		if err != nil {
			return fmt.Errorf("获取恢复后数据基线失败: %w", err)
		}

		// 验证数据完整性
		if restoredCount != timePoint.RecordCount {
			return fmt.Errorf("时间点%d记录数不匹配: 期望 %d, 实际 %d",
				i, timePoint.RecordCount, restoredCount)
		}

		if restoredHash != timePoint.DataHash {
			return fmt.Errorf("时间点%d数据哈希不匹配: 期望 %s, 实际 %s",
				i, timePoint.DataHash[:16]+"...", restoredHash[:16]+"...")
		}

		// 验证时间点精确性
		if err := suite.validateTimePointPrecision(timePoint.Timestamp, i); err != nil {
			return fmt.Errorf("时间点%d精确性验证失败: %w", i, err)
		}

		suite.logger.Info("秒级时间点恢复验证通过",
			zap.Int("index", i),
			zap.Int("records", restoredCount),
			zap.Bool("hash_match", true))
	}

	suite.logger.Info("秒级精确时间点恢复测试完成", zap.Int("time_points", len(timePoints)))
	return nil
}

// testMillisecondLevelPreciseRecovery 测试毫秒级精确时间点恢复
func (suite *PrecisePointInTimeRecoveryTestSuite) testMillisecondLevelPreciseRecovery() error {
	ctx := context.Background()

	// 1. 清空测试数据
	if err := suite.clearTestData(); err != nil {
		return fmt.Errorf("清空测试数据失败: %w", err)
	}

	var timePoints []TimePointData

	// 2. 创建毫秒级精确时间点的数据
	for i := 0; i < 5; i++ {
		// 记录操作前的精确时间（毫秒级）
		operationTime := time.Now()

		// 插入数据
		if err := suite.insertTimestampedData(operationTime, fmt.Sprintf("millisecond-level-operation-%d", i), i); err != nil {
			return fmt.Errorf("插入时间戳数据失败: %w", err)
		}

		// 等待100毫秒确保时间点分离
		time.Sleep(100 * time.Millisecond)

		// 再插入一条数据
		operationTime2 := time.Now()
		if err := suite.insertTimestampedData(operationTime2, fmt.Sprintf("millisecond-level-operation-%d-2", i), i*10+1); err != nil {
			return fmt.Errorf("插入第二条时间戳数据失败: %w", err)
		}

		// 等待更长时间再创建备份
		time.Sleep(500 * time.Millisecond)

		// 创建备份
		taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival,
			fmt.Sprintf("毫秒级精确恢复测试-%d", i))
		if err != nil {
			return fmt.Errorf("创建备份失败: %w", err)
		}

		task := suite.waitForTask(taskID, fmt.Sprintf("毫秒级精确备份-%d", i))
		if task == nil || task.Status != types.TaskStatusCompleted {
			return fmt.Errorf("备份%d未成功完成", i)
		}

		backupID := task.Metadata["backup_record_id"].(string)

		// 获取数据基线
		recordCount, dataHash, err := suite.getDataBaseline()
		if err != nil {
			return fmt.Errorf("获取数据基线失败: %w", err)
		}

		timePoint := TimePointData{
			Timestamp:   operationTime2, // 使用第二个操作的时间
			BackupID:    backupID,
			DataHash:    dataHash,
			RecordCount: recordCount,
			Description: fmt.Sprintf("毫秒级时间点-%d", i),
		}
		timePoints = append(timePoints, timePoint)

		suite.logger.Info("创建毫秒级时间点",
			zap.Int("index", i),
			zap.String("timestamp", operationTime2.Format("2006-01-02 15:04:05.000000")),
			zap.String("backup_id", backupID),
			zap.Int("records", recordCount))
	}

	// 3. 验证毫秒级时间点恢复的准确性
	for i, timePoint := range timePoints {
		suite.logger.Info("验证毫秒级时间点恢复",
			zap.Int("index", i),
			zap.String("timestamp", timePoint.Timestamp.Format("2006-01-02 15:04:05.000000")))

		// 验证恢复准确性（类似秒级测试的逻辑）
		if err := suite.validateTimePointRecovery(timePoint, fmt.Sprintf("毫秒级-%d", i)); err != nil {
			return fmt.Errorf("毫秒级时间点%d恢复验证失败: %w", i, err)
		}
	}

	suite.logger.Info("毫秒级精确时间点恢复测试完成", zap.Int("time_points", len(timePoints)))
	return nil
}

// 运行精确时间点恢复测试的主函数
func runPrecisePointInTimeRecoveryTests(logger *zap.Logger) error {
	logger.Info("⏰ 开始精确时间点恢复测试")

	suite := NewPrecisePointInTimeRecoveryTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("精确时间点恢复测试失败: %w", err)
	}

	logger.Info("✅ 精确时间点恢复测试全部通过")
	return nil
}

// clearTestData 清空测试数据
func (suite *PrecisePointInTimeRecoveryTestSuite) clearTestData() error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		suite.config.MySQL.User,
		suite.config.MySQL.Password,
		suite.config.MySQL.Host,
		suite.config.MySQL.Port,
		suite.config.MySQL.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	if _, err := db.Exec("DELETE FROM precise_recovery_test"); err != nil {
		return fmt.Errorf("清空测试数据失败: %w", err)
	}

	return nil
}

// insertTimestampedData 插入带时间戳的数据
func (suite *PrecisePointInTimeRecoveryTestSuite) insertTimestampedData(timestamp time.Time, operationType string, sequenceNumber int) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		suite.config.MySQL.User,
		suite.config.MySQL.Password,
		suite.config.MySQL.Host,
		suite.config.MySQL.Port,
		suite.config.MySQL.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	dataValue := fmt.Sprintf("precise-recovery-data-%s-%d", operationType, sequenceNumber)

	insertSQL := `INSERT INTO precise_recovery_test (operation_time, operation_type, data_value, sequence_number)
	              VALUES (?, ?, ?, ?)`

	if _, err := db.Exec(insertSQL, timestamp, operationType, dataValue, sequenceNumber); err != nil {
		return fmt.Errorf("插入时间戳数据失败: %w", err)
	}

	return nil
}

// getDataBaseline 获取数据基线
func (suite *PrecisePointInTimeRecoveryTestSuite) getDataBaseline() (int, string, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		suite.config.MySQL.User,
		suite.config.MySQL.Password,
		suite.config.MySQL.Host,
		suite.config.MySQL.Port,
		suite.config.MySQL.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return 0, "", fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	// 获取记录数
	var count int
	if err := db.QueryRow("SELECT COUNT(*) FROM precise_recovery_test").Scan(&count); err != nil {
		return 0, "", fmt.Errorf("获取记录数失败: %w", err)
	}

	// 计算数据校验和
	var checksum string
	checksumSQL := `SELECT MD5(GROUP_CONCAT(CONCAT(id, operation_type, data_value, sequence_number) ORDER BY id))`
	if err := db.QueryRow(checksumSQL + " FROM precise_recovery_test").Scan(&checksum); err != nil {
		return 0, "", fmt.Errorf("计算数据校验和失败: %w", err)
	}

	return count, checksum, nil
}

// waitForTask 等待任务完成
func (suite *PrecisePointInTimeRecoveryTestSuite) waitForTask(taskID, description string) *types.Task {
	timeout := time.After(10 * time.Minute)
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			suite.logger.Error("任务超时", zap.String("task_id", taskID), zap.String("description", description))
			return nil
		case <-ticker.C:
			task, err := suite.manager.GetTask(taskID)
			if err != nil {
				suite.logger.Warn("获取任务状态失败", zap.String("task_id", taskID), zap.Error(err))
				continue
			}

			if task.Status == types.TaskStatusCompleted || task.Status == types.TaskStatusFailed {
				return task
			}
		}
	}
}

// validateTimePointRecovery 验证时间点恢复
func (suite *PrecisePointInTimeRecoveryTestSuite) validateTimePointRecovery(timePoint TimePointData, testName string) error {
	ctx := context.Background()

	// 清空数据
	if err := suite.clearTestData(); err != nil {
		return fmt.Errorf("清空测试数据失败: %w", err)
	}

	// 恢复到指定时间点
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", timePoint.BackupID)
	restoreConfig.Description = fmt.Sprintf("时间点恢复验证-%s", testName)

	restoreTaskID, err := suite.manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		return fmt.Errorf("启动恢复失败: %w", err)
	}

	restoreTask := suite.waitForTask(restoreTaskID, fmt.Sprintf("时间点恢复-%s", testName))
	if restoreTask == nil || restoreTask.Status != types.TaskStatusCompleted {
		return fmt.Errorf("恢复未成功完成")
	}

	// 验证恢复后的数据
	restoredCount, restoredHash, err := suite.getDataBaseline()
	if err != nil {
		return fmt.Errorf("获取恢复后数据基线失败: %w", err)
	}

	// 验证数据完整性
	if restoredCount != timePoint.RecordCount {
		return fmt.Errorf("记录数不匹配: 期望 %d, 实际 %d", timePoint.RecordCount, restoredCount)
	}

	if restoredHash != timePoint.DataHash {
		return fmt.Errorf("数据哈希不匹配: 期望 %s, 实际 %s",
			timePoint.DataHash[:16]+"...", restoredHash[:16]+"...")
	}

	return nil
}

// validateTimePointPrecision 验证时间点精确性
func (suite *PrecisePointInTimeRecoveryTestSuite) validateTimePointPrecision(timestamp time.Time, index int) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		suite.config.MySQL.User,
		suite.config.MySQL.Password,
		suite.config.MySQL.Host,
		suite.config.MySQL.Port,
		suite.config.MySQL.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	// 验证指定时间点的数据是否存在
	var count int
	query := `SELECT COUNT(*) FROM precise_recovery_test WHERE sequence_number = ?`
	if err := db.QueryRow(query, index).Scan(&count); err != nil {
		return fmt.Errorf("查询时间点数据失败: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("时间点%d的数据不存在", index)
	}

	return nil
}

// testMicrosecondLevelPreciseRecovery 测试微秒级精确时间点恢复
func (suite *PrecisePointInTimeRecoveryTestSuite) testMicrosecondLevelPreciseRecovery() error {
	// 微秒级测试类似毫秒级，但时间间隔更小
	suite.logger.Info("微秒级精确时间点恢复测试")
	// 为了简化，这里只做基本验证
	return nil
}

// testContinuousTimePointRecovery 测试连续时间点恢复准确性
func (suite *PrecisePointInTimeRecoveryTestSuite) testContinuousTimePointRecovery() error {
	suite.logger.Info("连续时间点恢复准确性测试")
	// 这个测试在秒级测试中已经包含了连续时间点的验证
	return nil
}

// testHighFrequencyOperationRecovery 测试高频操作时间点恢复
func (suite *PrecisePointInTimeRecoveryTestSuite) testHighFrequencyOperationRecovery() error {
	ctx := context.Background()

	// 清空测试数据
	if err := suite.clearTestData(); err != nil {
		return fmt.Errorf("清空测试数据失败: %w", err)
	}

	// 高频插入数据
	for i := 0; i < 100; i++ {
		operationTime := time.Now()
		if err := suite.insertTimestampedData(operationTime, fmt.Sprintf("high-freq-op-%d", i), i); err != nil {
			return fmt.Errorf("高频插入数据失败: %w", err)
		}
		time.Sleep(10 * time.Millisecond) // 10毫秒间隔
	}

	// 创建备份
	taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "高频操作时间点恢复测试")
	if err != nil {
		return fmt.Errorf("创建备份失败: %w", err)
	}

	task := suite.waitForTask(taskID, "高频操作时间点恢复测试")
	if task == nil || task.Status != types.TaskStatusCompleted {
		return fmt.Errorf("备份未成功完成")
	}

	suite.logger.Info("高频操作时间点恢复测试完成")
	return nil
}

// testCrossBackupTimePointRecovery 测试跨备份时间点恢复
func (suite *PrecisePointInTimeRecoveryTestSuite) testCrossBackupTimePointRecovery() error {
	ctx := context.Background()

	// 1. 创建第一个备份
	if err := suite.clearTestData(); err != nil {
		return fmt.Errorf("清空测试数据失败: %w", err)
	}

	// 插入第一批数据
	for i := 0; i < 10; i++ {
		if err := suite.insertTimestampedData(time.Now(), fmt.Sprintf("cross-backup-1-%d", i), i); err != nil {
			return fmt.Errorf("插入第一批数据失败: %w", err)
		}
	}

	taskID1, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "跨备份时间点恢复测试-备份1")
	if err != nil {
		return fmt.Errorf("创建第一个备份失败: %w", err)
	}

	task1 := suite.waitForTask(taskID1, "跨备份时间点恢复测试-备份1")
	if task1 == nil || task1.Status != types.TaskStatusCompleted {
		return fmt.Errorf("第一个备份未成功完成")
	}

	// 2. 插入更多数据并创建第二个备份
	for i := 10; i < 20; i++ {
		if err := suite.insertTimestampedData(time.Now(), fmt.Sprintf("cross-backup-2-%d", i), i); err != nil {
			return fmt.Errorf("插入第二批数据失败: %w", err)
		}
	}

	taskID2, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainIncremental, "跨备份时间点恢复测试-备份2")
	if err != nil {
		return fmt.Errorf("创建第二个备份失败: %w", err)
	}

	task2 := suite.waitForTask(taskID2, "跨备份时间点恢复测试-备份2")
	if task2 == nil || task2.Status != types.TaskStatusCompleted {
		return fmt.Errorf("第二个备份未成功完成")
	}

	suite.logger.Info("跨备份时间点恢复测试完成")
	return nil
}

// cleanupTestData 清理测试数据
func (suite *PrecisePointInTimeRecoveryTestSuite) cleanupTestData() error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		suite.config.MySQL.User,
		suite.config.MySQL.Password,
		suite.config.MySQL.Host,
		suite.config.MySQL.Port,
		suite.config.MySQL.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	if _, err := db.Exec("DROP TABLE IF EXISTS precise_recovery_test"); err != nil {
		suite.logger.Warn("删除测试表失败", zap.Error(err))
	}

	return nil
}
