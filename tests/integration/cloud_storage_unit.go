package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"
)

// CloudStorageUnitTestSuite 云存储单元测试套件
type CloudStorageUnitTestSuite struct {
	logger *zap.Logger
	config *types.Config
}

// NewCloudStorageUnitTestSuite 创建云存储单元测试套件
func NewCloudStorageUnitTestSuite(logger *zap.Logger, config *types.Config) *CloudStorageUnitTestSuite {
	return &CloudStorageUnitTestSuite{
		logger: logger,
		config: config,
	}
}

// RunAllTests 运行所有云存储单元测试
func (suite *CloudStorageUnitTestSuite) RunAllTests() error {
	suite.logger.Info("🚀 开始云存储单元测试")

	tests := []struct {
		name string
		test func() error
	}{
		{"Backend接口实现测试", suite.testBackendInterface},
		{"Put方法测试", suite.testPutMethod},
		{"Get方法测试", suite.testGetMethod},
		{"NewWriter方法测试", suite.testNewWriterMethod},
		{"List方法测试", suite.testListMethod},
		{"Delete方法测试", suite.testDeleteMethod},
		{"Exists方法测试", suite.testExistsMethod},
		{"HealthCheck方法测试", suite.testHealthCheckMethod},
		{"错误处理测试", suite.testErrorHandling},
		{"并发安全测试", suite.testConcurrencySafety},
		{"配置验证测试", suite.testConfigValidation},
		{"大文件处理测试", suite.testLargeFileHandling},
		{"特殊字符处理测试", suite.testSpecialCharacters},
	}

	for _, test := range tests {
		suite.logger.Info("执行测试", zap.String("name", test.name))
		if err := test.test(); err != nil {
			suite.logger.Error("测试失败", zap.String("name", test.name), zap.Error(err))
			return fmt.Errorf("测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("测试通过", zap.String("name", test.name))
	}

	suite.logger.Info("✅ 所有云存储单元测试通过")
	return nil
}

// testBackendInterface 测试Backend接口实现
func (suite *CloudStorageUnitTestSuite) testBackendInterface() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	// 验证Backend接口的所有方法都可以调用
	ctx := context.Background()

	// 测试HealthCheck
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("HealthCheck失败: %w", err)
	}

	return nil
}

// testPutMethod 测试Put方法
func (suite *CloudStorageUnitTestSuite) testPutMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("test-put-%d", time.Now().Unix())
	testData := "Hello, Cloud Storage!"
	reader := strings.NewReader(testData)

	// 测试Put方法
	bytesWritten, err := backend.Put(ctx, testKey, reader)
	if err != nil {
		return fmt.Errorf("Put方法失败: %w", err)
	}

	if bytesWritten != int64(len(testData)) {
		return fmt.Errorf("字节数不匹配: 期望 %d, 实际 %d", len(testData), bytesWritten)
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testGetMethod 测试Get方法
func (suite *CloudStorageUnitTestSuite) testGetMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("test-get-%d", time.Now().Unix())
	testData := "Hello, Get Method!"

	// 先Put数据
	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("Put数据失败: %w", err)
	}

	// 测试Get方法
	reader, err := backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("Get方法失败: %w", err)
	}
	defer reader.Close()

	// 验证数据
	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取数据失败: %w", err)
	}

	if string(data) != testData {
		return fmt.Errorf("数据不匹配: 期望 %s, 实际 %s", testData, string(data))
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testNewWriterMethod 测试NewWriter方法
func (suite *CloudStorageUnitTestSuite) testNewWriterMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("test-writer-%d", time.Now().Unix())
	testData := "Hello, NewWriter Method!"

	// 测试NewWriter方法
	writer, err := backend.NewWriter(ctx, testKey)
	if err != nil {
		return fmt.Errorf("NewWriter方法失败: %w", err)
	}

	// 写入数据
	_, err = writer.Write([]byte(testData))
	if err != nil {
		return fmt.Errorf("写入数据失败: %w", err)
	}

	// 关闭writer
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("关闭writer失败: %w", err)
	}

	// 验证数据是否正确写入
	reader, err := backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("获取写入的数据失败: %w", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取数据失败: %w", err)
	}

	if string(data) != testData {
		return fmt.Errorf("数据不匹配: 期望 %s, 实际 %s", testData, string(data))
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testListMethod 测试List方法
func (suite *CloudStorageUnitTestSuite) testListMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	prefix := fmt.Sprintf("test-list-%d", time.Now().Unix())

	// 创建测试文件
	testFiles := []string{
		prefix + "/file1.txt",
		prefix + "/file2.txt",
		prefix + "/subdir/file3.txt",
	}

	for _, file := range testFiles {
		_, err = backend.Put(ctx, file, strings.NewReader("test data"))
		if err != nil {
			return fmt.Errorf("创建测试文件失败 %s: %w", file, err)
		}
	}

	// 测试List方法
	objects, err := backend.List(ctx, prefix)
	if err != nil {
		return fmt.Errorf("List方法失败: %w", err)
	}

	if len(objects) != len(testFiles) {
		return fmt.Errorf("文件数量不匹配: 期望 %d, 实际 %d", len(testFiles), len(objects))
	}

	// 清理测试数据
	for _, file := range testFiles {
		_ = backend.Delete(ctx, file)
	}

	return nil
}

// testDeleteMethod 测试Delete方法
func (suite *CloudStorageUnitTestSuite) testDeleteMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("test-delete-%d", time.Now().Unix())

	// 先创建文件
	_, err = backend.Put(ctx, testKey, strings.NewReader("test data"))
	if err != nil {
		return fmt.Errorf("创建测试文件失败: %w", err)
	}

	// 验证文件存在
	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("检查文件存在性失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("文件应该存在")
	}

	// 测试Delete方法
	err = backend.Delete(ctx, testKey)
	if err != nil {
		return fmt.Errorf("Delete方法失败: %w", err)
	}

	// 验证文件已删除
	exists, err = backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("检查文件存在性失败: %w", err)
	}
	if exists {
		return fmt.Errorf("文件应该已被删除")
	}

	return nil
}

// testExistsMethod 测试Exists方法
func (suite *CloudStorageUnitTestSuite) testExistsMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("test-exists-%d", time.Now().Unix())

	// 测试不存在的文件
	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("Exists方法失败: %w", err)
	}
	if exists {
		return fmt.Errorf("文件不应该存在")
	}

	// 创建文件
	_, err = backend.Put(ctx, testKey, strings.NewReader("test data"))
	if err != nil {
		return fmt.Errorf("创建测试文件失败: %w", err)
	}

	// 测试存在的文件
	exists, err = backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("Exists方法失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("文件应该存在")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testHealthCheckMethod 测试HealthCheck方法
func (suite *CloudStorageUnitTestSuite) testHealthCheckMethod() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()

	// 测试HealthCheck方法
	err = backend.HealthCheck(ctx)
	if err != nil {
		return fmt.Errorf("HealthCheck方法失败: %w", err)
	}

	return nil
}

// testErrorHandling 测试错误处理
func (suite *CloudStorageUnitTestSuite) testErrorHandling() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	nonExistentKey := "non-existent-file"

	// 测试获取不存在的文件
	_, err = backend.Get(ctx, nonExistentKey)
	if err == nil {
		return fmt.Errorf("获取不存在的文件应该返回错误")
	}

	// 测试删除不存在的文件（某些存储可能不返回错误）
	err = backend.Delete(ctx, nonExistentKey)
	// 不检查错误，因为某些存储删除不存在的文件不会报错

	return nil
}

// testConcurrencySafety 测试并发安全性
func (suite *CloudStorageUnitTestSuite) testConcurrencySafety() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	prefix := fmt.Sprintf("test-concurrent-%d", time.Now().Unix())

	// 并发写入测试
	errChan := make(chan error, 10)
	for i := 0; i < 10; i++ {
		go func(index int) {
			key := fmt.Sprintf("%s/file-%d", prefix, index)
			data := fmt.Sprintf("concurrent data %d", index)
			_, err := backend.Put(ctx, key, strings.NewReader(data))
			errChan <- err
		}(i)
	}

	// 检查并发写入结果
	for i := 0; i < 10; i++ {
		if err := <-errChan; err != nil {
			return fmt.Errorf("并发写入失败: %w", err)
		}
	}

	// 清理测试数据
	objects, _ := backend.List(ctx, prefix)
	for _, obj := range objects {
		_ = backend.Delete(ctx, obj.Key)
	}

	return nil
}

// testConfigValidation 测试配置验证
func (suite *CloudStorageUnitTestSuite) testConfigValidation() error {
	// 测试有效配置
	validConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-bucket",
			Region:    "us-east-1",
			AccessKey: "test-key",
			SecretKey: "test-secret",
			Endpoint:  "http://localhost:9000",
		},
	}

	backend, err := storage.NewBackend(validConfig, suite.logger)
	if err != nil {
		return fmt.Errorf("有效配置应该成功创建Backend: %w", err)
	}
	defer func() {
		if closer, ok := backend.(io.Closer); ok {
			closer.Close()
		}
	}()

	// 测试无效配置 - 缺少必需字段
	invalidConfigs := []*types.Config{
		{
			BackupRoot: "/tests/data/backup",
			CloudStorage: &types.CloudStorageConfig{
				Enabled: true,
				Type:    "s3",
				// 缺少 Bucket
			},
		},
		{
			BackupRoot: "/tests/data/backup",
			CloudStorage: &types.CloudStorageConfig{
				Enabled: true,
				Type:    "invalid-type",
				Bucket:  "test-bucket",
			},
		},
	}

	for i, config := range invalidConfigs {
		_, err := storage.NewBackend(config, suite.logger)
		if err == nil {
			return fmt.Errorf("无效配置 %d 应该返回错误", i)
		}
	}

	return nil
}

// testLargeFileHandling 测试大文件处理
func (suite *CloudStorageUnitTestSuite) testLargeFileHandling() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("test-large-file-%d", time.Now().Unix())

	// 创建1MB的测试数据
	largeData := make([]byte, 1024*1024)
	for i := range largeData {
		largeData[i] = byte(i % 256)
	}

	// 测试Put方法处理大文件
	bytesWritten, err := backend.Put(ctx, testKey, bytes.NewReader(largeData))
	if err != nil {
		return fmt.Errorf("Put大文件失败: %w", err)
	}

	if bytesWritten != int64(len(largeData)) {
		return fmt.Errorf("大文件字节数不匹配: 期望 %d, 实际 %d", len(largeData), bytesWritten)
	}

	// 测试Get方法读取大文件
	reader, err := backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("Get大文件失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取大文件数据失败: %w", err)
	}

	if !bytes.Equal(largeData, retrievedData) {
		return fmt.Errorf("大文件数据不匹配")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testSpecialCharacters 测试特殊字符处理
func (suite *CloudStorageUnitTestSuite) testSpecialCharacters() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建Backend失败: %w", err)
	}

	ctx := context.Background()

	// 测试包含特殊字符的键名
	specialKeys := []string{
		fmt.Sprintf("test-special-%d/file with spaces.txt", time.Now().Unix()),
		fmt.Sprintf("test-special-%d/文件中文名.txt", time.Now().Unix()),
		fmt.Sprintf("test-special-%d/file-with-dashes_and_underscores.txt", time.Now().Unix()),
		fmt.Sprintf("test-special-%d/deep/nested/directory/file.txt", time.Now().Unix()),
	}

	testData := "Special characters test data"

	for _, key := range specialKeys {
		// 测试Put
		_, err := backend.Put(ctx, key, strings.NewReader(testData))
		if err != nil {
			return fmt.Errorf("Put特殊字符键名失败 [%s]: %w", key, err)
		}

		// 测试Exists
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			return fmt.Errorf("Exists特殊字符键名失败 [%s]: %w", key, err)
		}
		if !exists {
			return fmt.Errorf("特殊字符键名文件应该存在 [%s]", key)
		}

		// 测试Get
		reader, err := backend.Get(ctx, key)
		if err != nil {
			return fmt.Errorf("Get特殊字符键名失败 [%s]: %w", key, err)
		}

		data, err := io.ReadAll(reader)
		reader.Close()
		if err != nil {
			return fmt.Errorf("读取特殊字符键名数据失败 [%s]: %w", key, err)
		}

		if string(data) != testData {
			return fmt.Errorf("特殊字符键名数据不匹配 [%s]", key)
		}

		// 清理测试数据
		_ = backend.Delete(ctx, key)
	}

	return nil
}

// runCloudStorageUnitTests 运行云存储单元测试的入口函数
func runCloudStorageUnitTests(logger *zap.Logger, configPath string) error {
	// 加载配置
	config, err := loadTestConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载测试配置失败: %w", err)
	}

	// 创建测试套件
	suite := NewCloudStorageUnitTestSuite(logger, config)

	// 运行所有测试
	return suite.RunAllTests()
}
