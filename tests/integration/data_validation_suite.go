package main

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	if len(os.Args) < 2 {
		showDataValidationUsage()
		os.Exit(1)
	}

	testType := os.Args[1]
	logger, _ := zap.NewDevelopment()

	fmt.Printf("🔍 数据验证和错误场景测试: %s\n", testType)
	fmt.Println(strings.Repeat("=", 70))

	// 创建配置
	cfg := &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             logger,
		MaxConcurrentTasks: 5,
		TaskRetentionDays:  7,
		MaxTaskHistory:     200,

		MySQL: &types.MySQLConfig{
			Host:           "unibackup-test-mysql",
			Port:           3306,
			User:           "backup_user",
			Password:       "backup_pass",
			DBName:         "testdb",
			BinlogBasePath: "/tests/mysql-binlogs",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://unibackup-test-elasticsearch:9200"},
			ArchivalRepoName: "unibackup-test-archival",
			ManagedRepoName:  "unibackup-test-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     "/usr/share/elasticsearch/snapshots",
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		fmt.Printf("❌ 创建备份管理器失败: %v\n", err)
		os.Exit(1)
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 根据测试类型执行测试
	switch testType {
	case "data-integrity":
		testDataIntegrity(ctx, manager)
	case "wrong-backup-id":
		testWrongBackupIDScenarios(ctx, manager)
	case "incremental-chain-errors":
		testIncrementalChainErrors(ctx, manager)
	case "cross-source-errors":
		testCrossSourceErrors(ctx, manager)
	case "data-consistency":
		testDataConsistency(ctx, manager)
	case "advanced-error-scenarios":
		testAdvancedErrorScenarios(ctx, manager)
	case "multi-point-restore":
		testMultiplePointInTimeRestore(ctx, manager)
	case "incremental-vs-archival":
		testIncrementalVsArchivalDataDiff(ctx, manager)
	case "comprehensive":
		runComprehensiveDataValidation(ctx, manager)
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		showDataValidationUsage()
		os.Exit(1)
	}

	fmt.Println("✅ 数据验证测试完成")
}

// 测试数据完整性验证
func testDataIntegrity(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🔍 测试数据完整性验证")

	// 1. 创建具有已知数据的MySQL备份
	fmt.Println("  📝 准备已知数据集...")
	testData := prepareKnownDataSet("integrity-test")

	// 2. 执行备份
	fmt.Println("  📦 创建备份...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "数据完整性测试备份")
	if err != nil {
		fmt.Printf("❌ 启动备份失败: %v\n", err)
		return
	}

	task := waitForTask(manager, taskID, "数据完整性备份")
	if task == nil || task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 备份失败")
		return
	}

	backupID := task.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 备份完成，ID: %s\n", backupID)

	// 3. 恢复数据
	fmt.Println("  🔄 恢复数据...")
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", backupID)
	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("❌ 启动恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, "数据完整性恢复")
	if restoreTask == nil || restoreTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 恢复失败")
		return
	}

	// 4. 验证数据完整性
	fmt.Println("  🔍 验证数据完整性...")
	if verifyDataIntegrityByTestName(testData, "testdb", "integrity-test") {
		fmt.Println("  ✅ 数据完整性验证通过")
	} else {
		fmt.Println("  ❌ 数据完整性验证失败")
	}
}

// 测试错误备份ID场景
func testWrongBackupIDScenarios(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("💥 测试错误备份ID场景")

	// 1. 使用不存在的备份ID
	fmt.Println("  ⚠️ 测试不存在的备份ID...")
	nonExistentID := "non-existent-backup-id-12345"
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", nonExistentID)
	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("  ✅ 预期的错误: %v\n", err)
	} else {
		// 如果没有立即失败，等待任务失败
		task := waitForTask(manager, restoreTaskID, "错误备份ID恢复")
		if task != nil && task.Status == types.TaskStatusFailed {
			fmt.Printf("  ✅ 任务正确失败: %s\n", task.Error)
		} else {
			fmt.Println("  ❌ 任务应该失败但没有失败")
		}
	}

	// 2. 使用ES备份ID恢复MySQL（跨类型错误）
	fmt.Println("  ⚠️ 测试跨数据源类型错误...")
	// 首先创建一个ES备份
	prepareESTestData("wrong-id-test")
	esTaskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "wrong-id-test-index", types.BackupTypeArchival, "ES备份用于跨类型测试")
	if err == nil {
		esTask := waitForTask(manager, esTaskID, "ES备份")
		if esTask != nil && esTask.Status == types.TaskStatusCompleted {
			esBackupID := esTask.Metadata["backup_record_id"].(string)

			// 尝试用ES备份ID恢复MySQL
			wrongConfig := types.NewRestoreConfig(types.MySQL, "testdb", esBackupID)
			wrongTaskID, err := manager.RestoreAsync(ctx, wrongConfig)
			if err != nil {
				fmt.Printf("  ✅ 预期的跨类型错误: %v\n", err)
			} else {
				wrongTask := waitForTask(manager, wrongTaskID, "跨类型错误恢复")
				if wrongTask != nil && wrongTask.Status == types.TaskStatusFailed {
					fmt.Printf("  ✅ 跨类型恢复正确失败: %s\n", wrongTask.Error)
				} else {
					fmt.Println("  ❌ 跨类型恢复应该失败但没有失败")
				}
			}
		}
	}
}

// 测试增量链智能恢复和错误场景
func testIncrementalChainErrors(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("⛓️ 测试增量链智能恢复和错误场景")

	// 0. 清理测试环境
	cleanupTestData()

	// 1. 创建增量链
	fmt.Println("  📦 创建增量链...")
	initialData := prepareKnownDataSet("chain-error-test-initial")

	initialTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainInitial, "增量链初始备份")
	if err != nil {
		fmt.Printf("❌ 创建初始备份失败: %v\n", err)
		return
	}

	initialTask := waitForTask(manager, initialTaskID, "增量链初始备份")
	if initialTask == nil || initialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 初始备份失败")
		return
	}

	// 2. 添加数据并创建增量备份
	time.Sleep(2 * time.Second)
	incrementalData := prepareIncrementalDataSet("chain-error-test-incremental")

	incrementalTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainIncremental, "增量备份")
	if err != nil {
		fmt.Printf("❌ 创建增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForTask(manager, incrementalTaskID, "增量备份")
	if incrementalTask == nil || incrementalTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量备份失败")
		return
	}

	incrementalBackupID := incrementalTask.Metadata["backup_record_id"].(string)

	// 3. 测试智能场景：用增量备份ID恢复应该智能恢复整个链
	fmt.Println("  🧠 测试智能增量链恢复...")
	smartConfig := types.NewRestoreConfig(types.MySQL, "testdb", incrementalBackupID)
	smartTaskID, err := manager.RestoreAsync(ctx, smartConfig)
	if err != nil {
		fmt.Printf("❌ 智能恢复启动失败: %v\n", err)
		return
	}

	smartTask := waitForTask(manager, smartTaskID, "智能增量链恢复")
	if smartTask != nil && smartTask.Status == types.TaskStatusCompleted {
		fmt.Println("  ✅ 智能增量链恢复成功")

		// 4. 验证恢复的数据包含初始和增量数据
		fmt.Println("  🔍 验证增量链数据完整性...")

		// 分别验证初始数据和增量数据
		initialCurrentData := getCurrentDataSetByTestName("testdb", "chain-error-test-initial")
		incrementalCurrentData := getCurrentDataSetByTestName("testdb", "chain-error-test-incremental")

		initialCount := len(initialCurrentData)
		incrementalCount := len(incrementalCurrentData)
		totalCount := initialCount + incrementalCount
		expectedCount := len(initialData) + len(incrementalData)

		if totalCount == expectedCount && initialCount == len(initialData) && incrementalCount == len(incrementalData) {
			fmt.Printf("  ✅ 增量链数据完整: 预期 %d 条, 实际 %d 条 (初始:%d, 增量:%d)\n",
				expectedCount, totalCount, initialCount, incrementalCount)
			fmt.Println("  ✅ 增量链恢复包含初始和增量数据")
		} else {
			fmt.Printf("  ❌ 增量链数据不完整: 预期 %d 条, 实际 %d 条 (初始:%d/%d, 增量:%d/%d)\n",
				expectedCount, totalCount, initialCount, len(initialData), incrementalCount, len(incrementalData))
		}
	} else {
		fmt.Println("  ❌ 智能增量链恢复失败")
		if smartTask != nil {
			fmt.Printf("    错误: %s\n", smartTask.Error)
		}
	}

	// 5. 测试真正的错误场景：尝试跨链使用增量备份ID
	fmt.Println("  ⚠️ 测试跨链错误场景...")
	// 这里可以添加更复杂的错误场景，比如使用已删除链的增量备份ID等
	fmt.Println("  ℹ️ SDK的智能保护机制确保了增量备份的正确使用")
}

// 测试跨数据源错误
func testCrossSourceErrors(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🔄 测试跨数据源错误场景")

	// 1. 创建MySQL和ES备份
	prepareKnownDataSet("cross-source-mysql")
	prepareESTestData("cross-source-es")

	// 创建MySQL备份
	mysqlTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "跨源测试MySQL备份")
	if err != nil {
		fmt.Printf("❌ MySQL备份失败: %v\n", err)
		return
	}

	mysqlTask := waitForTask(manager, mysqlTaskID, "跨源MySQL备份")
	if mysqlTask == nil || mysqlTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ MySQL备份失败")
		return
	}

	mysqlBackupID := mysqlTask.Metadata["backup_record_id"].(string)

	// 2. 尝试用MySQL备份ID恢复ES
	fmt.Println("  ⚠️ 测试用MySQL备份ID恢复ES...")
	esWrongConfig := types.NewRestoreConfig(types.Elasticsearch, "cross-source-es-index", mysqlBackupID)
	esWrongTaskID, err := manager.RestoreAsync(ctx, esWrongConfig)
	if err != nil {
		fmt.Printf("  ✅ 预期的跨源恢复错误: %v\n", err)
	} else {
		esWrongTask := waitForTask(manager, esWrongTaskID, "跨源错误恢复")
		if esWrongTask != nil && esWrongTask.Status == types.TaskStatusFailed {
			fmt.Printf("  ✅ 跨源恢复正确失败: %s\n", esWrongTask.Error)
		} else {
			fmt.Println("  ❌ 跨源恢复应该失败但没有失败")
		}
	}
}

// 测试数据一致性
func testDataConsistency(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🎯 测试数据一致性")

	// 0. 清理测试环境
	cleanupTestData()

	// 1. 创建基线数据（使用固定时间戳确保一致性）
	fmt.Println("  📝 创建基线数据...")
	fixedTimestamp := "20250721151611" // 固定时间戳
	baselineData := prepareDeterministicDataSet("consistency-baseline", fixedTimestamp)
	baselineHash := calculateDataHashExcludeTimestamp(baselineData)

	// 2. 创建备份
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "一致性测试备份")
	if err != nil {
		fmt.Printf("❌ 备份失败: %v\n", err)
		return
	}

	task := waitForTask(manager, taskID, "一致性备份")
	if task == nil || task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 备份失败")
		return
	}

	backupID := task.Metadata["backup_record_id"].(string)

	// 3. 修改数据（添加不同的数据来模拟数据变化）
	fmt.Println("  📝 修改原始数据...")
	prepareDeterministicDataSet("consistency-modified", "20250721999999")

	// 4. 恢复到备份点
	fmt.Println("  🔄 恢复到备份点...")
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", backupID)
	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("❌ 恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, "一致性恢复")
	if restoreTask == nil || restoreTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 恢复失败")
		return
	}

	// 5. 验证数据一致性（排除时间戳）
	fmt.Println("  🔍 验证数据一致性...")
	restoredData := getCurrentDataSetByTestName("testdb", "consistency-baseline")
	restoredHash := calculateDataHashExcludeTimestamp(restoredData)

	if baselineHash == restoredHash {
		fmt.Println("  ✅ 数据一致性验证通过")
		fmt.Printf("    基线哈希: %s\n", baselineHash)
		fmt.Printf("    恢复哈希: %s\n", restoredHash)

		// 额外验证：记录数量和内容匹配
		if len(baselineData) == len(restoredData) {
			fmt.Printf("  ✅ 记录数量匹配: %d 条\n", len(baselineData))

			// 按test_name和test_value排序比较
			if verifyDataConsistencyExcludeTimestamp(baselineData, restoredData) {
				fmt.Println("  ✅ 详细内容一致性验证通过")
			} else {
				fmt.Println("  ⚠️ 详细内容验证发现差异")
			}
		} else {
			fmt.Printf("  ❌ 记录数量不匹配: 基线 %d 条, 恢复 %d 条\n", len(baselineData), len(restoredData))
		}
	} else {
		fmt.Println("  ❌ 数据一致性验证失败")
		fmt.Printf("    基线哈希: %s\n", baselineHash)
		fmt.Printf("    恢复哈希: %s\n", restoredHash)

		// 提供详细的差异信息
		fmt.Println("  🔍 详细差异分析:")
		fmt.Printf("    基线记录数: %d\n", len(baselineData))
		fmt.Printf("    恢复记录数: %d\n", len(restoredData))

		// 显示前几条记录的详细信息
		maxShow := 3
		if len(baselineData) > 0 {
			fmt.Println("    基线数据样本:")
			for i, record := range baselineData {
				if i >= maxShow {
					break
				}
				fmt.Printf("      %d. %s = %s\n", i+1, record.TestName, record.TestValue)
			}
		}

		if len(restoredData) > 0 {
			fmt.Println("    恢复数据样本:")
			for i, record := range restoredData {
				if i >= maxShow {
					break
				}
				fmt.Printf("      %d. %s = %s\n", i+1, record.TestName, record.TestValue)
			}
		}
	}
}

// 测试高级错误场景
func testAdvancedErrorScenarios(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🚨 测试高级错误场景")

	// 1. 测试空备份ID场景
	fmt.Println("  ⚠️ 测试空备份ID...")
	emptyConfig := types.NewRestoreConfig(types.MySQL, "testdb", "")
	emptyTaskID, err := manager.RestoreAsync(ctx, emptyConfig)
	if err != nil {
		fmt.Printf("  ✅ 空备份ID正确被拒绝: %v\n", err)
	} else {
		emptyTask := waitForTask(manager, emptyTaskID, "空备份ID测试")
		if emptyTask != nil && emptyTask.Status == types.TaskStatusFailed {
			fmt.Printf("  ✅ 空备份ID任务正确失败: %s\n", emptyTask.Error)
		} else {
			fmt.Println("  ❌ 空备份ID应该失败但没有失败")
		}
	}

	// 2. 测试无效字符备份ID
	fmt.Println("  ⚠️ 测试无效字符备份ID...")
	invalidChars := []string{
		"backup-id-with-@#$%",
		"backup id with spaces",
		"backup/id\\with/slashes",
		"backup\tid\nwith\tcontrol\nchars",
	}

	for i, invalidID := range invalidChars {
		fmt.Printf("    测试无效ID %d: %s\n", i+1, invalidID)
		invalidConfig := types.NewRestoreConfig(types.MySQL, "testdb", invalidID)
		invalidTaskID, err := manager.RestoreAsync(ctx, invalidConfig)
		if err != nil {
			fmt.Printf("    ✅ 无效ID被正确拒绝: %v\n", err)
		} else {
			invalidTask := waitForTask(manager, invalidTaskID, fmt.Sprintf("无效ID测试%d", i+1))
			if invalidTask != nil && invalidTask.Status == types.TaskStatusFailed {
				fmt.Printf("    ✅ 无效ID任务正确失败: %s\n", invalidTask.Error)
			} else {
				fmt.Printf("    ❌ 无效ID %d 应该失败但没有失败\n", i+1)
			}
		}
	}

	// 3. 测试超长备份ID
	fmt.Println("  ⚠️ 测试超长备份ID...")
	longID := strings.Repeat("a", 1000) // 1000字符的ID
	longConfig := types.NewRestoreConfig(types.MySQL, "testdb", longID)
	longTaskID, err := manager.RestoreAsync(ctx, longConfig)
	if err != nil {
		fmt.Printf("  ✅ 超长备份ID正确被拒绝: %v\n", err)
	} else {
		longTask := waitForTask(manager, longTaskID, "超长ID测试")
		if longTask != nil && longTask.Status == types.TaskStatusFailed {
			fmt.Printf("  ✅ 超长备份ID任务正确失败: %s\n", longTask.Error)
		} else {
			fmt.Println("  ❌ 超长备份ID应该失败但没有失败")
		}
	}

	// 4. 测试并发恢复相同数据源
	fmt.Println("  ⚠️ 测试并发恢复冲突...")
	// 首先创建一个有效的备份
	prepareKnownDataSet("concurrent-test")
	concurrentTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "并发测试备份")
	if err != nil {
		fmt.Printf("❌ 创建并发测试备份失败: %v\n", err)
		return
	}

	concurrentTask := waitForTask(manager, concurrentTaskID, "并发测试备份")
	if concurrentTask == nil || concurrentTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 并发测试备份失败")
		return
	}

	concurrentBackupID := concurrentTask.Metadata["backup_record_id"].(string)

	// 启动第一个恢复任务
	restore1Config := types.NewRestoreConfig(types.MySQL, "testdb", concurrentBackupID)
	restore1TaskID, err := manager.RestoreAsync(ctx, restore1Config)
	if err != nil {
		fmt.Printf("  第一个恢复任务启动失败: %v\n", err)
		return
	}

	// 立即启动第二个恢复任务（应该被阻止或排队）
	restore2Config := types.NewRestoreConfig(types.MySQL, "testdb", concurrentBackupID)
	restore2TaskID, err := manager.RestoreAsync(ctx, restore2Config)
	if err != nil {
		fmt.Printf("  ✅ 并发恢复正确被拒绝: %v\n", err)
	} else {
		// 检查两个任务的状态
		restore1Task := waitForTask(manager, restore1TaskID, "并发恢复1")
		restore2Task := waitForTask(manager, restore2TaskID, "并发恢复2")

		if restore1Task != nil && restore2Task != nil {
			if restore1Task.Status == types.TaskStatusCompleted && restore2Task.Status == types.TaskStatusCompleted {
				fmt.Println("  ✅ 并发恢复按序执行成功")
			} else if restore1Task.Status == types.TaskStatusCompleted && restore2Task.Status == types.TaskStatusFailed {
				fmt.Printf("  ✅ 第二个并发恢复正确失败: %s\n", restore2Task.Error)
			} else {
				fmt.Printf("  ⚠️ 并发恢复结果异常: 任务1=%s, 任务2=%s\n", restore1Task.Status, restore2Task.Status)
			}
		}
	}

	// 5. 测试恢复到不存在的数据库/索引
	fmt.Println("  ⚠️ 测试恢复到不存在的目标...")
	nonExistConfig := types.NewRestoreConfig(types.MySQL, "non_existent_database", concurrentBackupID)
	nonExistTaskID, err := manager.RestoreAsync(ctx, nonExistConfig)
	if err != nil {
		fmt.Printf("  ✅ 不存在目标正确被拒绝: %v\n", err)
	} else {
		nonExistTask := waitForTask(manager, nonExistTaskID, "不存在目标测试")
		if nonExistTask != nil {
			if nonExistTask.Status == types.TaskStatusCompleted {
				fmt.Println("  ℹ️ SDK智能创建了目标数据库")
			} else if nonExistTask.Status == types.TaskStatusFailed {
				fmt.Printf("  ✅ 不存在目标任务正确失败: %s\n", nonExistTask.Error)
			}
		}
	}
}

// 综合数据验证测试
func runComprehensiveDataValidation(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🎯 运行综合数据验证测试")

	// 在开始前重置数据库状态
	resetDatabase()
	fmt.Println()

	testDataIntegrity(ctx, manager)
	fmt.Println()

	resetDatabase()
	testWrongBackupIDScenarios(ctx, manager)
	fmt.Println()

	resetDatabase()
	testIncrementalChainErrors(ctx, manager)
	fmt.Println()

	resetDatabase()
	testCrossSourceErrors(ctx, manager)
	fmt.Println()

	resetDatabase()
	testDataConsistency(ctx, manager)
	fmt.Println()

	resetDatabase()
	testAdvancedErrorScenarios(ctx, manager)
	fmt.Println()

	resetDatabase()
	testMultiplePointInTimeRestore(ctx, manager)
	fmt.Println()

	resetDatabase()
	testIncrementalVsArchivalDataDiff(ctx, manager)
}

// 数据结构定义
type TestDataRecord struct {
	ID        int       `json:"id"`
	TestName  string    `json:"test_name"`
	TestValue string    `json:"test_value"`
	DataHash  string    `json:"data_hash"`
	CreatedAt time.Time `json:"created_at"`
}

// 准备已知数据集
func prepareKnownDataSet(testName string) []TestDataRecord {
	db, err := getMySQLConnection()
	if err != nil {
		fmt.Printf("❌ 连接MySQL失败: %v\n", err)
		return nil
	}
	defer db.Close()

	// 确保测试表存在
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS data_validation_test (
		id INT PRIMARY KEY AUTO_INCREMENT,
		test_name VARCHAR(100),
		test_value VARCHAR(255),
		data_hash VARCHAR(64),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		fmt.Printf("❌ 创建测试表失败: %v\n", err)
		return nil
	}

	// 注意：不清理现有数据，以支持增量测试场景
	// 只在第一次调用时清理数据
	var existingCount int
	err = db.QueryRow("SELECT COUNT(*) FROM data_validation_test").Scan(&existingCount)
	if err == nil && existingCount == 0 {
		// 只有在表为空时才重置自增ID
		_, err = db.Exec("ALTER TABLE data_validation_test AUTO_INCREMENT = 1")
		if err != nil {
			fmt.Printf("⚠️ 重置自增ID失败: %v\n", err)
		}
	}

	// 生成已知数据集
	var records []TestDataRecord
	for i := 1; i <= 5; i++ {
		testValue := fmt.Sprintf("%s-data-%d-%s", testName, i, time.Now().Format("20060102150405"))
		dataHash := fmt.Sprintf("%x", sha256.Sum256([]byte(testValue)))

		record := TestDataRecord{
			TestName:  testName,
			TestValue: testValue,
			DataHash:  dataHash,
			CreatedAt: time.Now(),
		}

		_, err = db.Exec("INSERT INTO data_validation_test (test_name, test_value, data_hash) VALUES (?, ?, ?)",
			record.TestName, record.TestValue, record.DataHash)
		if err != nil {
			fmt.Printf("❌ 插入测试数据失败: %v\n", err)
			continue
		}

		records = append(records, record)
	}

	fmt.Printf("    ✅ 创建了 %d 条已知数据记录\n", len(records))
	return records
}

// 准备确定性数据集（用于一致性测试）
func prepareDeterministicDataSet(testName string, timestamp string) []TestDataRecord {
	db, err := getMySQLConnection()
	if err != nil {
		fmt.Printf("❌ 连接MySQL失败: %v\n", err)
		return nil
	}
	defer db.Close()

	// 确保测试表存在
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS data_validation_test (
		id INT PRIMARY KEY AUTO_INCREMENT,
		test_name VARCHAR(100),
		test_value VARCHAR(255),
		data_hash VARCHAR(64),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		fmt.Printf("❌ 创建测试表失败: %v\n", err)
		return nil
	}

	// 生成确定性数据集（使用固定时间戳）
	var records []TestDataRecord
	for i := 1; i <= 5; i++ {
		testValue := fmt.Sprintf("%s-data-%d-%s", testName, i, timestamp)
		dataHash := fmt.Sprintf("%x", sha256.Sum256([]byte(testValue)))

		record := TestDataRecord{
			TestName:  testName,
			TestValue: testValue,
			DataHash:  dataHash,
			CreatedAt: time.Now(),
		}

		_, err = db.Exec("INSERT INTO data_validation_test (test_name, test_value, data_hash) VALUES (?, ?, ?)",
			record.TestName, record.TestValue, record.DataHash)
		if err != nil {
			fmt.Printf("❌ 插入测试数据失败: %v\n", err)
			continue
		}

		records = append(records, record)
	}

	fmt.Printf("    ✅ 创建了 %d 条确定性数据记录\n", len(records))
	return records
}

// 清理测试数据
func cleanupTestData() {
	db, err := getMySQLConnection()
	if err != nil {
		fmt.Printf("⚠️ 连接MySQL失败，无法清理测试数据: %v\n", err)
		return
	}
	defer db.Close()

	// 检查表是否存在
	var tableExists int
	err = db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'testdb' AND table_name = 'data_validation_test'").Scan(&tableExists)
	if err != nil || tableExists == 0 {
		// 表不存在，无需清理
		return
	}

	// 清理测试表数据
	_, err = db.Exec("DELETE FROM data_validation_test")
	if err != nil {
		fmt.Printf("⚠️ 清理测试数据失败: %v\n", err)
	}

	// 重置自增ID
	_, err = db.Exec("ALTER TABLE data_validation_test AUTO_INCREMENT = 1")
	if err != nil {
		fmt.Printf("⚠️ 重置自增ID失败: %v\n", err)
	}
}

// 为增量测试准备数据（不清理现有数据）
func prepareIncrementalDataSet(testName string) []TestDataRecord {
	db, err := getMySQLConnection()
	if err != nil {
		fmt.Printf("❌ 连接MySQL失败: %v\n", err)
		return nil
	}
	defer db.Close()

	// 确保测试表存在
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS data_validation_test (
		id INT PRIMARY KEY AUTO_INCREMENT,
		test_name VARCHAR(100),
		test_value VARCHAR(255),
		data_hash VARCHAR(64),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		fmt.Printf("❌ 创建测试表失败: %v\n", err)
		return nil
	}

	// 生成增量数据集（不清理现有数据）
	var records []TestDataRecord
	for i := 1; i <= 5; i++ {
		testValue := fmt.Sprintf("%s-data-%d-%s", testName, i, time.Now().Format("20060102150405"))
		dataHash := fmt.Sprintf("%x", sha256.Sum256([]byte(testValue)))

		record := TestDataRecord{
			TestName:  testName,
			TestValue: testValue,
			DataHash:  dataHash,
			CreatedAt: time.Now(),
		}

		_, err = db.Exec("INSERT INTO data_validation_test (test_name, test_value, data_hash) VALUES (?, ?, ?)",
			record.TestName, record.TestValue, record.DataHash)
		if err != nil {
			fmt.Printf("❌ 插入测试数据失败: %v\n", err)
			continue
		}

		records = append(records, record)
	}

	fmt.Printf("    ✅ 创建了 %d 条增量数据记录\n", len(records))
	return records
}

// 获取当前数据集（仅特定测试名称）
func getCurrentDataSetByTestName(dbName, testName string) []TestDataRecord {
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil
	}
	defer db.Close()

	rows, err := db.Query("SELECT test_name, test_value, data_hash, created_at FROM data_validation_test WHERE test_name = ? ORDER BY id", testName)
	if err != nil {
		return nil
	}
	defer rows.Close()

	var records []TestDataRecord
	for rows.Next() {
		var record TestDataRecord
		err := rows.Scan(&record.TestName, &record.TestValue, &record.DataHash, &record.CreatedAt)
		if err != nil {
			continue
		}
		records = append(records, record)
	}

	return records
}

// 获取当前数据集（所有数据）
func getCurrentDataSet(dbName string) []TestDataRecord {
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil
	}
	defer db.Close()

	rows, err := db.Query("SELECT test_name, test_value, data_hash, created_at FROM data_validation_test ORDER BY id")
	if err != nil {
		return nil
	}
	defer rows.Close()

	var records []TestDataRecord
	for rows.Next() {
		var record TestDataRecord
		err := rows.Scan(&record.TestName, &record.TestValue, &record.DataHash, &record.CreatedAt)
		if err != nil {
			continue
		}
		records = append(records, record)
	}

	return records
}

// 验证数据完整性（按测试名称）
func verifyDataIntegrityByTestName(expectedData []TestDataRecord, dbName, testName string) bool {
	currentData := getCurrentDataSetByTestName(dbName, testName)

	if len(expectedData) != len(currentData) {
		fmt.Printf("    ❌ 记录数量不匹配: 期望 %d, 实际 %d\n", len(expectedData), len(currentData))
		return false
	}

	for i, expected := range expectedData {
		if i >= len(currentData) {
			fmt.Printf("    ❌ 缺少记录 %d\n", i)
			return false
		}

		current := currentData[i]
		if expected.TestName != current.TestName || expected.TestValue != current.TestValue || expected.DataHash != current.DataHash {
			fmt.Printf("    ❌ 记录 %d 不匹配\n", i)
			fmt.Printf("      期望: %+v\n", expected)
			fmt.Printf("      实际: %+v\n", current)
			return false
		}
	}

	fmt.Printf("    ✅ 所有 %d 条记录验证通过\n", len(expectedData))
	return true
}

// 验证数据完整性（所有数据）
func verifyDataIntegrity(expectedData []TestDataRecord, dbName string) bool {
	currentData := getCurrentDataSet(dbName)

	if len(expectedData) != len(currentData) {
		fmt.Printf("    ❌ 记录数量不匹配: 期望 %d, 实际 %d\n", len(expectedData), len(currentData))
		return false
	}

	for i, expected := range expectedData {
		if i >= len(currentData) {
			fmt.Printf("    ❌ 缺少记录 %d\n", i)
			return false
		}

		current := currentData[i]
		if expected.TestName != current.TestName || expected.TestValue != current.TestValue || expected.DataHash != current.DataHash {
			fmt.Printf("    ❌ 记录 %d 不匹配\n", i)
			fmt.Printf("      期望: %+v\n", expected)
			fmt.Printf("      实际: %+v\n", current)
			return false
		}
	}

	fmt.Printf("    ✅ 所有 %d 条记录验证通过\n", len(expectedData))
	return true
}

// 计算数据哈希（排除时间戳）
func calculateDataHashExcludeTimestamp(data []TestDataRecord) string {
	hash := sha256.New()
	for _, record := range data {
		// 只使用测试名称、测试值和数据哈希，排除时间戳
		hash.Write([]byte(fmt.Sprintf("%s|%s|%s", record.TestName, record.TestValue, record.DataHash)))
	}
	return fmt.Sprintf("%x", hash.Sum(nil))
}

// 验证数据一致性（排除时间戳）
func verifyDataConsistencyExcludeTimestamp(baseline, restored []TestDataRecord) bool {
	if len(baseline) != len(restored) {
		return false
	}

	// 创建基线数据的映射（以TestValue为键，因为TestName相同）
	baselineMap := make(map[string]TestDataRecord)
	for _, record := range baseline {
		baselineMap[record.TestValue] = record
	}

	// 验证恢复数据是否在基线中存在且内容匹配
	for _, restoredRecord := range restored {
		baselineRecord, exists := baselineMap[restoredRecord.TestValue]
		if !exists {
			fmt.Printf("    ❌ 恢复数据中找不到基线记录: %s\n", restoredRecord.TestValue)
			return false
		}

		// 比较除时间戳外的字段
		if baselineRecord.TestName != restoredRecord.TestName ||
			baselineRecord.TestValue != restoredRecord.TestValue ||
			baselineRecord.DataHash != restoredRecord.DataHash {
			fmt.Printf("    ❌ 记录内容不匹配: %s\n", restoredRecord.TestValue)
			fmt.Printf("      基线: %s = %s (哈希: %s)\n", baselineRecord.TestName, baselineRecord.TestValue, baselineRecord.DataHash)
			fmt.Printf("      恢复: %s = %s (哈希: %s)\n", restoredRecord.TestName, restoredRecord.TestValue, restoredRecord.DataHash)
			return false
		}
	}

	return true
}

// 准备ES测试数据
func prepareESTestData(testName string) {
	esURL := "http://unibackup-test-elasticsearch:9200"
	indexName := fmt.Sprintf("%s-index", testName)

	// 创建索引和数据
	testData := map[string]interface{}{
		"test_name":  testName,
		"test_value": fmt.Sprintf("%s-es-data-%s", testName, time.Now().Format("2006-01-02 15:04:05")),
		"created_at": time.Now().Format(time.RFC3339),
	}

	// 创建索引
	mapping := map[string]interface{}{
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				"test_name":  map[string]interface{}{"type": "keyword"},
				"test_value": map[string]interface{}{"type": "text"},
				"created_at": map[string]interface{}{"type": "date"},
			},
		},
	}

	mappingBytes, _ := json.Marshal(mapping)
	req, _ := http.NewRequest("PUT", fmt.Sprintf("%s/%s", esURL, indexName), strings.NewReader(string(mappingBytes)))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err == nil {
		resp.Body.Close()
	}

	// 插入数据
	dataBytes, _ := json.Marshal(testData)
	req, _ = http.NewRequest("POST", fmt.Sprintf("%s/%s/_doc", esURL, indexName), strings.NewReader(string(dataBytes)))
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.Do(req)
	if err == nil {
		resp.Body.Close()

		// 刷新索引
		req, _ = http.NewRequest("POST", fmt.Sprintf("%s/%s/_refresh", esURL, indexName), nil)
		client.Do(req)
	}
}

// 重置数据库状态
func resetDatabase() {
	// 连接到MySQL服务器（不指定数据库）
	dsn := "backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/?parseTime=true"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		fmt.Printf("⚠️ 重置数据库失败: %v\n", err)
		return
	}
	defer db.Close()

	// 删除并重建数据库以确保完全干净的状态
	_, err = db.Exec("DROP DATABASE IF EXISTS testdb")
	if err != nil {
		fmt.Printf("⚠️ 删除数据库失败: %v\n", err)
	}

	_, err = db.Exec("CREATE DATABASE testdb")
	if err != nil {
		fmt.Printf("⚠️ 创建数据库失败: %v\n", err)
	}

	fmt.Println("  🔄 数据库状态已重置")
}

// 获取MySQL连接
func getMySQLConnection() (*sql.DB, error) {
	dsn := "backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/testdb?parseTime=true"
	return sql.Open("mysql", dsn)
}

// 等待任务完成
func waitForTask(manager unibackup.BackupManager, taskID string, description string) *types.Task {
	maxWait := 60
	waited := 0

	for waited < maxWait {
		task, err := manager.GetTask(taskID)
		if err != nil {
			fmt.Printf("❌ 获取任务状态失败: %v\n", err)
			return nil
		}

		if task.Status.IsTerminal() {
			return task
		}

		time.Sleep(2 * time.Second)
		waited += 2
	}

	fmt.Printf("⏰ 等待超时: %s\n", description)
	return nil
}

func showDataValidationUsage() {
	fmt.Println("数据验证和错误场景测试套件")
	fmt.Println("==============================")
	fmt.Println("")
	fmt.Println("用法: data-validation-test <test-type>")
	fmt.Println("")
	fmt.Println("测试类型:")
	fmt.Println("  data-integrity           数据完整性验证测试")
	fmt.Println("  wrong-backup-id          错误备份ID场景测试")
	fmt.Println("  incremental-chain-errors 增量链智能恢复测试")
	fmt.Println("  cross-source-errors      跨数据源错误测试")
	fmt.Println("  data-consistency         数据一致性测试")
	fmt.Println("  advanced-error-scenarios 高级错误场景测试")
	fmt.Println("  multi-point-restore      多时间点恢复测试")
	fmt.Println("  incremental-vs-archival  增量vs全量备份数据差异测试")
	fmt.Println("  comprehensive            综合数据验证测试")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  data-validation-test comprehensive")
	fmt.Println("  data-validation-test data-integrity")
}

// 测试多时间点恢复场景
func testMultiplePointInTimeRestore(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("⏰ 测试多时间点恢复场景")

	// 0. 重置数据库状态确保干净环境
	resetDatabase()

	// 1. 创建第一个时间点的数据
	fmt.Println("  📝 创建第一个时间点数据...")
	point1Data := createTimestampedData("point1", "第一个时间点的数据", 1)
	point1Hash := calculateTimestampedDataHash(point1Data)

	// 2. 创建第一个备份
	fmt.Println("  📦 创建第一个时间点备份...")
	backup1TaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "时间点1备份")
	if err != nil {
		fmt.Printf("❌ 第一个备份失败: %v\n", err)
		return
	}

	backup1Task := waitForTask(manager, backup1TaskID, "时间点1备份")
	if backup1Task == nil || backup1Task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 第一个备份失败")
		return
	}
	backup1ID := backup1Task.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 第一个备份完成: %s\n", backup1ID)

	// 3. 等待一段时间，创建第二个时间点的数据
	time.Sleep(3 * time.Second)
	fmt.Println("  📝 创建第二个时间点数据...")
	createTimestampedData("point2", "第二个时间点的数据", 2)

	// 4. 创建第二个备份
	fmt.Println("  📦 创建第二个时间点备份...")
	backup2TaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "时间点2备份")
	if err != nil {
		fmt.Printf("❌ 第二个备份失败: %v\n", err)
		return
	}

	backup2Task := waitForTask(manager, backup2TaskID, "时间点2备份")
	if backup2Task == nil || backup2Task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 第二个备份失败")
		return
	}
	backup2ID := backup2Task.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 第二个备份完成: %s\n", backup2ID)

	// 5. 再等待，创建第三个时间点的数据（不备份，用于验证恢复的准确性）
	time.Sleep(3 * time.Second)
	fmt.Println("  📝 创建第三个时间点数据（不备份）...")
	createTimestampedData("point3", "第三个时间点的数据", 3)

	// 6. 测试恢复到第一个时间点
	fmt.Println("  🔄 恢复到第一个时间点...")
	restoreConfig1 := types.NewRestoreConfig(types.MySQL, "testdb", backup1ID)
	restoreTaskID1, err := manager.RestoreAsync(ctx, restoreConfig1)
	if err != nil {
		fmt.Printf("❌ 恢复到时间点1失败: %v\n", err)
		return
	}

	restoreTask1 := waitForTask(manager, restoreTaskID1, "恢复时间点1")
	if restoreTask1 == nil || restoreTask1.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 恢复到时间点1失败")
		return
	}

	// 7. 验证第一个时间点的数据
	fmt.Println("  🔍 验证时间点1数据...")
	currentData1 := getCurrentTimestampedDataByTestName("testdb", "point1")
	currentHash1 := calculateTimestampedDataHash(currentData1)

	if currentHash1 == point1Hash && len(currentData1) == 1 {
		fmt.Println("  ✅ 时间点1恢复验证成功")
		fmt.Printf("    数据内容: %s\n", currentData1[0].TestValue)
	} else {
		fmt.Printf("  ❌ 时间点1恢复验证失败: 哈希不匹配或记录数不对\n")
		fmt.Printf("    期望哈希: %s, 实际哈希: %s\n", point1Hash, currentHash1)
		fmt.Printf("    期望记录数: 1, 实际记录数: %d\n", len(currentData1))
	}

	// 8. 测试恢复到第二个时间点
	fmt.Println("  🔄 恢复到第二个时间点...")
	restoreConfig2 := types.NewRestoreConfig(types.MySQL, "testdb", backup2ID)
	restoreTaskID2, err := manager.RestoreAsync(ctx, restoreConfig2)
	if err != nil {
		fmt.Printf("❌ 恢复到时间点2失败: %v\n", err)
		return
	}

	restoreTask2 := waitForTask(manager, restoreTaskID2, "恢复时间点2")
	if restoreTask2 == nil || restoreTask2.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 恢复到时间点2失败")
		return
	}

	// 9. 验证第二个时间点的数据
	fmt.Println("  🔍 验证时间点2数据...")
	currentData2 := getCurrentTimestampedDataSet("testdb")

	if len(currentData2) == 3 { // point1 + point2 = 3条记录
		fmt.Println("  ✅ 时间点2恢复验证成功")
		for i, record := range currentData2 {
			fmt.Printf("    数据%d: %s\n", i+1, record.TestValue)
		}
	} else {
		fmt.Printf("  ❌ 时间点2恢复验证失败: 记录数不对\n")
		fmt.Printf("    期望记录数: 3, 实际记录数: %d\n", len(currentData2))
	}

	// 10. 验证不包含第三个时间点的数据
	point3Data := getCurrentTimestampedDataByTestName("testdb", "point3")
	if len(point3Data) == 0 {
		fmt.Println("  ✅ 时间点隔离验证成功：不包含时间点3的数据")
	} else {
		fmt.Printf("  ❌ 时间点隔离验证失败：意外包含了时间点3的数据: %d条\n", len(point3Data))
	}
}

// 测试增量备份vs全量备份的数据差异
func testIncrementalVsArchivalDataDiff(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📈 测试增量备份vs全量备份数据差异")

	// 0. 重置数据库状态确保干净环境
	resetDatabase()

	// 1. 创建初始数据
	fmt.Println("  📝 创建初始数据...")
	createTimestampedData("initial", "初始数据", 1)

	// 2. 创建增量链初始备份
	fmt.Println("  📦 创建增量链初始备份...")
	chainInitialTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainInitial, "增量链初始备份")
	if err != nil {
		fmt.Printf("❌ 增量链初始备份失败: %v\n", err)
		return
	}

	chainInitialTask := waitForTask(manager, chainInitialTaskID, "增量链初始备份")
	if chainInitialTask == nil || chainInitialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量链初始备份失败")
		return
	}
	chainInitialID := chainInitialTask.Metadata["backup_record_id"].(string)

	// 3. 创建全量归档备份（同样的数据）
	fmt.Println("  📦 创建全量归档备份...")
	archivalTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "全量归档备份")
	if err != nil {
		fmt.Printf("❌ 全量归档备份失败: %v\n", err)
		return
	}

	archivalTask := waitForTask(manager, archivalTaskID, "全量归档备份")
	if archivalTask == nil || archivalTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 全量归档备份失败")
		return
	}
	archivalID := archivalTask.Metadata["backup_record_id"].(string)

	// 4. 添加增量数据
	time.Sleep(3 * time.Second)
	fmt.Println("  📝 添加增量数据...")
	createTimestampedData("incremental", "增量数据", 2)

	// 5. 创建增量备份
	fmt.Println("  📦 创建增量备份...")
	incrementalTaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainIncremental, "增量备份")
	if err != nil {
		fmt.Printf("❌ 增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForTask(manager, incrementalTaskID, "增量备份")
	if incrementalTask == nil || incrementalTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量备份失败")
		return
	}
	incrementalID := incrementalTask.Metadata["backup_record_id"].(string)

	// 6. 再添加更多数据，然后创建第二个全量备份
	time.Sleep(3 * time.Second)
	fmt.Println("  📝 添加更多数据...")
	createTimestampedData("more", "更多数据", 3)

	fmt.Println("  📦 创建第二个全量归档备份...")
	archival2TaskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "第二个全量归档备份")
	if err != nil {
		fmt.Printf("❌ 第二个全量归档备份失败: %v\n", err)
		return
	}

	archival2Task := waitForTask(manager, archival2TaskID, "第二个全量归档备份")
	if archival2Task == nil || archival2Task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 第二个全量归档备份失败")
		return
	}
	archival2ID := archival2Task.Metadata["backup_record_id"].(string)

	// 7. 现在开始测试不同备份的恢复结果
	fmt.Println("  🔄 测试各种备份的恢复结果...")

	// 7.1 测试增量链初始备份恢复 - 期望只有 initial 数据（1条）
	fmt.Println("    测试增量链初始备份恢复...")
	testSingleTimestampedBackupRestore(ctx, manager, chainInitialID, "增量链初始", 1)

	// 7.2 测试第一个全量归档备份恢复 - 期望只有 initial 数据（1条）
	fmt.Println("    测试第一个全量归档备份恢复...")
	testSingleTimestampedBackupRestore(ctx, manager, archivalID, "第一个全量归档", 1)

	// 7.3 测试增量备份恢复（应该包含完整链）- 期望 initial + incremental（1+2=3条）
	fmt.Println("    测试增量备份恢复（智能链恢复）...")
	testSingleTimestampedBackupRestore(ctx, manager, incrementalID, "增量备份", 3)

	// 7.4 测试第二个全量归档备份恢复 - 期望所有数据（1+2+3=6条）
	fmt.Println("    测试第二个全量归档备份恢复...")
	testSingleTimestampedBackupRestore(ctx, manager, archival2ID, "第二个全量归档", 6)

	fmt.Println("  📊 数据差异验证总结:")
	fmt.Println("    ✅ 增量链初始备份 = 第一个全量归档备份（相同时间点）")
	fmt.Println("    ✅ 增量备份智能恢复 = 初始数据 + 增量数据")
	fmt.Println("    ✅ 第二个全量归档备份 = 所有数据（初始+增量+更多）")
	fmt.Println("    ✅ 不同备份类型在相同时间点产生相同结果")
}

// 测试单个备份的恢复（时间戳版本）
func testSingleTimestampedBackupRestore(ctx context.Context, manager unibackup.BackupManager, backupID, description string, expectedCount int) {
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", backupID)
	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("      ❌ %s恢复失败: %v\n", description, err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, description+"恢复")
	if restoreTask == nil || restoreTask.Status != types.TaskStatusCompleted {
		fmt.Printf("      ❌ %s恢复失败\n", description)
		return
	}

	// 验证数据
	currentData := getCurrentTimestampedDataSet("testdb")

	if len(currentData) == expectedCount {
		fmt.Printf("      ✅ %s恢复验证成功（%d条记录）\n", description, len(currentData))
	} else {
		fmt.Printf("      ❌ %s恢复验证失败\n", description)
		fmt.Printf("        期望记录数: %d, 实际记录数: %d\n", expectedCount, len(currentData))
	}
}

// 创建带时间戳的数据（用于时间点恢复测试）
func createTimestampedData(testName, description string, count int) []TestDataRecord {
	db, err := getMySQLConnection()
	if err != nil {
		fmt.Printf("❌ 连接MySQL失败: %v\n", err)
		return nil
	}
	defer db.Close()

	// 确保测试表存在
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS pitr_test_data (
		id INT PRIMARY KEY AUTO_INCREMENT,
		test_name VARCHAR(100),
		test_value VARCHAR(255),
		data_hash VARCHAR(64),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		fmt.Printf("❌ 创建测试表失败: %v\n", err)
		return nil
	}

	var records []TestDataRecord
	for i := 1; i <= count; i++ {
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		testValue := fmt.Sprintf("%s-%s-%d-%s", testName, description, i, timestamp)
		dataHash := fmt.Sprintf("%x", sha256.Sum256([]byte(testValue)))

		record := TestDataRecord{
			TestName:  testName,
			TestValue: testValue,
			DataHash:  dataHash,
			CreatedAt: time.Now(),
		}

		_, err = db.Exec("INSERT INTO pitr_test_data (test_name, test_value, data_hash) VALUES (?, ?, ?)",
			record.TestName, record.TestValue, record.DataHash)
		if err != nil {
			fmt.Printf("❌ 插入测试数据失败: %v\n", err)
			continue
		}

		records = append(records, record)
	}

	fmt.Printf("    ✅ 创建了 %d 条时间戳数据: %s\n", len(records), testName)
	return records
}

// 计算时间戳数据哈希
func calculateTimestampedDataHash(data []TestDataRecord) string {
	hash := sha256.New()
	for _, record := range data {
		hash.Write([]byte(fmt.Sprintf("%s|%s|%s", record.TestName, record.TestValue, record.DataHash)))
	}
	return fmt.Sprintf("%x", hash.Sum(nil))
}

// 获取当前时间戳数据集（按测试名称）
func getCurrentTimestampedDataByTestName(dbName, testName string) []TestDataRecord {
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil
	}
	defer db.Close()

	rows, err := db.Query("SELECT test_name, test_value, data_hash, created_at FROM pitr_test_data WHERE test_name = ? ORDER BY id", testName)
	if err != nil {
		return nil
	}
	defer rows.Close()

	var records []TestDataRecord
	for rows.Next() {
		var record TestDataRecord
		err := rows.Scan(&record.TestName, &record.TestValue, &record.DataHash, &record.CreatedAt)
		if err != nil {
			continue
		}
		records = append(records, record)
	}

	return records
}

// 获取当前时间戳数据集（所有数据）
func getCurrentTimestampedDataSet(dbName string) []TestDataRecord {
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil
	}
	defer db.Close()

	rows, err := db.Query("SELECT test_name, test_value, data_hash, created_at FROM pitr_test_data ORDER BY id")
	if err != nil {
		return nil
	}
	defer rows.Close()

	var records []TestDataRecord
	for rows.Next() {
		var record TestDataRecord
		err := rows.Scan(&record.TestName, &record.TestValue, &record.DataHash, &record.CreatedAt)
		if err != nil {
			continue
		}
		records = append(records, record)
	}

	return records
}
