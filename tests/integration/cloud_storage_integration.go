package main

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// CloudStorageIntegrationTestSuite 云存储集成测试套件
type CloudStorageIntegrationTestSuite struct {
	logger *zap.Logger
	config *types.Config
}

// NewCloudStorageIntegrationTestSuite 创建云存储集成测试套件
func NewCloudStorageIntegrationTestSuite(logger *zap.Logger, config *types.Config) *CloudStorageIntegrationTestSuite {
	return &CloudStorageIntegrationTestSuite{
		logger: logger,
		config: config,
	}
}

// RunAllTests 运行所有云存储集成测试
func (suite *CloudStorageIntegrationTestSuite) RunAllTests() error {
	suite.logger.Info("🚀 开始云存储集成测试")

	tests := []struct {
		name string
		test func() error
	}{
		{"工厂函数测试", suite.testBackendFactory},
		{"配置切换测试", suite.testConfigSwitching},
		{"备份管理器集成测试", suite.testBackupManagerIntegration},
		{"端到端备份流程测试", suite.testEndToEndBackupFlow},
		{"端到端恢复流程测试", suite.testEndToEndRestoreFlow},
		{"多云存储类型测试", suite.testMultipleCloudTypes},
		{"故障恢复测试", suite.testFailureRecovery},
	}

	for _, test := range tests {
		suite.logger.Info("执行集成测试", zap.String("name", test.name))
		if err := test.test(); err != nil {
			suite.logger.Error("集成测试失败", zap.String("name", test.name), zap.Error(err))
			return fmt.Errorf("集成测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("集成测试通过", zap.String("name", test.name))
	}

	suite.logger.Info("✅ 所有云存储集成测试通过")
	return nil
}

// testBackendFactory 测试工厂函数
func (suite *CloudStorageIntegrationTestSuite) testBackendFactory() error {
	// 测试云存储后端创建
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("工厂函数创建云存储后端失败: %w", err)
	}

	// 验证后端类型
	ctx := context.Background()
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("云存储后端健康检查失败: %w", err)
	}

	// 测试基本功能
	testKey := fmt.Sprintf("factory-test-%d", time.Now().Unix())
	testData := "Factory function test data"

	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("工厂创建的后端Put操作失败: %w", err)
	}

	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("工厂创建的后端Exists操作失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("工厂创建的后端文件应该存在")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testConfigSwitching 测试配置切换
func (suite *CloudStorageIntegrationTestSuite) testConfigSwitching() error {
	// 测试本地存储配置
	localConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		Logger:     suite.logger,
	}

	localBackend, err := storage.NewBackend(localConfig, suite.logger)
	if err != nil {
		return fmt.Errorf("创建本地存储后端失败: %w", err)
	}

	// 测试云存储配置
	cloudBackend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建云存储后端失败: %w", err)
	}

	ctx := context.Background()
	testKey := fmt.Sprintf("config-switch-test-%d", time.Now().Unix())
	testData := "Config switching test data"

	// 测试本地存储操作
	_, err = localBackend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("本地存储Put操作失败: %w", err)
	}

	// 测试云存储操作
	_, err = cloudBackend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("云存储Put操作失败: %w", err)
	}

	// 验证两个后端的数据隔离
	localExists, err := localBackend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("本地存储Exists操作失败: %w", err)
	}

	cloudExists, err := cloudBackend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("云存储Exists操作失败: %w", err)
	}

	if !localExists || !cloudExists {
		return fmt.Errorf("配置切换后数据应该在各自的存储中存在")
	}

	// 清理测试数据
	_ = localBackend.Delete(ctx, testKey)
	_ = cloudBackend.Delete(ctx, testKey)

	return nil
}

// testBackupManagerIntegration 测试备份管理器集成
func (suite *CloudStorageIntegrationTestSuite) testBackupManagerIntegration() error {
	// 创建备份管理器
	manager, err := unibackup.NewManager(suite.config)
	if err != nil {
		return fmt.Errorf("创建备份管理器失败: %w", err)
	}

	// 测试管理器的基本功能
	ctx := context.Background()

	// 获取任务状态（应该为空）
	tasks, err := manager.ListAllBackups(ctx, types.BackupFilter{})
	if err != nil {
		return fmt.Errorf("获取任务列表失败: %w", err)
	}

	suite.logger.Info("当前任务数量", zap.Int("count", len(tasks.Tasks)))

	return nil
}

// testEndToEndBackupFlow 测试端到端备份流程
func (suite *CloudStorageIntegrationTestSuite) testEndToEndBackupFlow() error {
	// 由于需要真实的数据库连接，这里只测试存储层的备份流程
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建存储后端失败: %w", err)
	}

	ctx := context.Background()
	backupKey := fmt.Sprintf("backup-test-%d/data.sql.gz", time.Now().Unix())

	// 模拟备份数据
	backupData := `-- MySQL dump
CREATE TABLE test_table (
    id INT PRIMARY KEY,
    name VARCHAR(100)
);
INSERT INTO test_table VALUES (1, 'test data');`

	// 使用NewWriter模拟Provider的写入方式
	writer, err := backend.NewWriter(ctx, backupKey)
	if err != nil {
		return fmt.Errorf("创建备份写入器失败: %w", err)
	}

	_, err = writer.Write([]byte(backupData))
	if err != nil {
		writer.Close()
		return fmt.Errorf("写入备份数据失败: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return fmt.Errorf("关闭备份写入器失败: %w", err)
	}

	// 验证备份文件存在
	exists, err := backend.Exists(ctx, backupKey)
	if err != nil {
		return fmt.Errorf("检查备份文件存在性失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("备份文件应该存在")
	}

	// 验证备份数据完整性
	reader, err := backend.Get(ctx, backupKey)
	if err != nil {
		return fmt.Errorf("读取备份文件失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取备份数据失败: %w", err)
	}

	if string(retrievedData) != backupData {
		return fmt.Errorf("备份数据不匹配")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, backupKey)

	return nil
}

// testEndToEndRestoreFlow 测试端到端恢复流程
func (suite *CloudStorageIntegrationTestSuite) testEndToEndRestoreFlow() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建存储后端失败: %w", err)
	}

	ctx := context.Background()
	restoreKey := fmt.Sprintf("restore-test-%d/data.sql", time.Now().Unix())

	// 准备恢复数据
	restoreData := `-- Restore data
USE testdb;
CREATE TABLE restore_test (id INT, data TEXT);
INSERT INTO restore_test VALUES (1, 'restored data');`

	// 先上传恢复数据
	_, err = backend.Put(ctx, restoreKey, strings.NewReader(restoreData))
	if err != nil {
		return fmt.Errorf("上传恢复数据失败: %w", err)
	}

	// 模拟恢复流程 - 读取数据
	reader, err := backend.Get(ctx, restoreKey)
	if err != nil {
		return fmt.Errorf("获取恢复数据失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取恢复数据失败: %w", err)
	}

	if string(retrievedData) != restoreData {
		return fmt.Errorf("恢复数据不匹配")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, restoreKey)

	return nil
}

// testMultipleCloudTypes 测试多种云存储类型
func (suite *CloudStorageIntegrationTestSuite) testMultipleCloudTypes() error {
	// 测试不同的云存储配置（都使用MinIO模拟）
	cloudTypes := []struct {
		name   string
		config *types.CloudStorageConfig
	}{
		{
			name: "S3",
			config: &types.CloudStorageConfig{
				Enabled:   true,
				Type:      "s3",
				Bucket:    "test-backup-bucket",
				Region:    "us-east-1",
				AccessKey: "minioadmin",
				SecretKey: "minioadmin123",
				Endpoint:  "http://unibackup-test-minio:9000",
			},
		},
		{
			name: "GCS模拟",
			config: &types.CloudStorageConfig{
				Enabled:   true,
				Type:      "s3", // 使用S3兼容模式模拟GCS
				Bucket:    "test-gcs-bucket",
				Region:    "us-east-1",
				AccessKey: "minioadmin",
				SecretKey: "minioadmin123",
				Endpoint:  "http://unibackup-test-minio:9000",
			},
		},
	}

	for _, cloudType := range cloudTypes {
		suite.logger.Info("测试云存储类型", zap.String("type", cloudType.name))

		config := &types.Config{
			BackupRoot:   "/tests/data/backup",
			CloudStorage: cloudType.config,
		}

		backend, err := storage.NewBackend(config, suite.logger)
		if err != nil {
			return fmt.Errorf("创建%s后端失败: %w", cloudType.name, err)
		}

		ctx := context.Background()
		testKey := fmt.Sprintf("multi-type-test-%s-%d", strings.ToLower(cloudType.name), time.Now().Unix())
		testData := fmt.Sprintf("Multi-type test data for %s", cloudType.name)

		// 测试基本操作
		_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
		if err != nil {
			return fmt.Errorf("%s Put操作失败: %w", cloudType.name, err)
		}

		exists, err := backend.Exists(ctx, testKey)
		if err != nil {
			return fmt.Errorf("%s Exists操作失败: %w", cloudType.name, err)
		}
		if !exists {
			return fmt.Errorf("%s 文件应该存在", cloudType.name)
		}

		// 清理测试数据
		_ = backend.Delete(ctx, testKey)
	}

	return nil
}

// testFailureRecovery 测试故障恢复
func (suite *CloudStorageIntegrationTestSuite) testFailureRecovery() error {
	backend, err := storage.NewBackend(suite.config, suite.logger)
	if err != nil {
		return fmt.Errorf("创建存储后端失败: %w", err)
	}

	ctx := context.Background()

	// 测试网络超时恢复（使用短超时）
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Millisecond*100)
	defer cancel()

	testKey := fmt.Sprintf("failure-recovery-test-%d", time.Now().Unix())
	testData := "Failure recovery test data"

	// 这个操作可能因为超时而失败，但不应该崩溃
	_, err = backend.Put(timeoutCtx, testKey, strings.NewReader(testData))
	if err != nil {
		suite.logger.Info("预期的超时错误", zap.Error(err))
	}

	// 使用正常的上下文重试
	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("重试Put操作失败: %w", err)
	}

	// 验证数据存在
	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("故障恢复后Exists操作失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("故障恢复后文件应该存在")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// runCloudStorageIntegrationTests 运行云存储集成测试的入口函数
func runCloudStorageIntegrationTests(logger *zap.Logger, configPath string) error {
	// 加载配置
	config, err := loadTestConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载测试配置失败: %w", err)
	}
	suite := NewCloudStorageIntegrationTestSuite(logger, config)

	// 运行所有测试
	return suite.RunAllTests()
}
