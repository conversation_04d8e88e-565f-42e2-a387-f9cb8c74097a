package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"
)

// CloudStorageConnectivityTestSuite 云存储连通性测试套件
type CloudStorageConnectivityTestSuite struct {
	logger *zap.Logger
}

// NewCloudStorageConnectivityTestSuite 创建云存储连通性测试套件
func NewCloudStorageConnectivityTestSuite(logger *zap.Logger) *CloudStorageConnectivityTestSuite {
	return &CloudStorageConnectivityTestSuite{
		logger: logger,
	}
}

// RunAllTests 运行所有连通性测试
func (suite *CloudStorageConnectivityTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"S3连通性测试", suite.testS3Connectivity},
		{"GCS连通性测试", suite.testGCSConnectivity},
		{"Azure连通性测试", suite.testAzureConnectivity},
		{"无效配置测试", suite.testInvalidConfigurations},
		{"超时测试", suite.testTimeoutScenarios},
	}

	for _, test := range tests {
		suite.logger.Info("开始测试", zap.String("test", test.name))
		if err := test.test(); err != nil {
			return fmt.Errorf("测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("测试通过", zap.String("test", test.name))
	}

	return nil
}

// testS3Connectivity 测试S3连通性
func (suite *CloudStorageConnectivityTestSuite) testS3Connectivity() error {
	// 使用测试环境的MinIO配置
	s3Config := &types.CloudStorageConfig{
		Enabled:   true,
		Type:      "s3",
		Bucket:    "unibackup-test-bucket",
		Region:    "us-east-1",
		AccessKey: "minioadmin",
		SecretKey: "minioadmin123",
		Endpoint:  "http://unibackup-test-minio:9000",
	}

	return suite.runConnectivityTest("S3", s3Config)
}

// testGCSConnectivity 测试GCS连通性
func (suite *CloudStorageConnectivityTestSuite) testGCSConnectivity() error {
	// 检查是否有GCS测试环境
	if os.Getenv("GCS_TEST_ENABLED") != "true" {
		suite.logger.Info("跳过GCS测试 - 未启用测试环境")
		return nil
	}

	gcsConfig := &types.CloudStorageConfig{
		Enabled:         true,
		Type:            "gcs",
		Bucket:          os.Getenv("GCS_TEST_BUCKET"),
		ProjectID:       os.Getenv("GCS_TEST_PROJECT_ID"),
		CredentialsFile: os.Getenv("GCS_TEST_CREDENTIALS_FILE"),
	}

	if gcsConfig.Bucket == "" || gcsConfig.ProjectID == "" {
		suite.logger.Info("跳过GCS测试 - 缺少必要的环境变量")
		return nil
	}

	return suite.runConnectivityTest("GCS", gcsConfig)
}

// testAzureConnectivity 测试Azure连通性
func (suite *CloudStorageConnectivityTestSuite) testAzureConnectivity() error {
	// 检查是否有Azure测试环境
	if os.Getenv("AZURE_TEST_ENABLED") != "true" {
		suite.logger.Info("跳过Azure测试 - 未启用测试环境")
		return nil
	}

	azureConfig := &types.CloudStorageConfig{
		Enabled:     true,
		Type:        "azure",
		Container:   os.Getenv("AZURE_TEST_CONTAINER"),
		AccountName: os.Getenv("AZURE_TEST_ACCOUNT_NAME"),
		AccountKey:  os.Getenv("AZURE_TEST_ACCOUNT_KEY"),
	}

	if azureConfig.Container == "" || azureConfig.AccountName == "" {
		suite.logger.Info("跳过Azure测试 - 缺少必要的环境变量")
		return nil
	}

	return suite.runConnectivityTest("Azure", azureConfig)
}

// testInvalidConfigurations 测试无效配置
func (suite *CloudStorageConnectivityTestSuite) testInvalidConfigurations() error {
	invalidConfigs := []struct {
		name   string
		config *types.CloudStorageConfig
	}{
		{
			name: "空配置",
			config: nil,
		},
		{
			name: "缺少类型",
			config: &types.CloudStorageConfig{
				Enabled: true,
				Bucket:  "test-bucket",
			},
		},
		{
			name: "S3缺少Bucket",
			config: &types.CloudStorageConfig{
				Enabled: true,
				Type:    "s3",
				Region:  "us-east-1",
			},
		},
		{
			name: "S3缺少Region",
			config: &types.CloudStorageConfig{
				Enabled: true,
				Type:    "s3",
				Bucket:  "test-bucket",
			},
		},
		{
			name: "不支持的存储类型",
			config: &types.CloudStorageConfig{
				Enabled: true,
				Type:    "unsupported",
				Bucket:  "test-bucket",
			},
		},
	}

	// 创建临时目录用于本地存储模式
	testDir := "/tmp/unibackup-connectivity-test"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	defer os.RemoveAll(testDir)

	// 创建临时manager用于测试
	tempConfig := &types.Config{
		BackupRoot: testDir,
		Logger:     suite.logger,
	}

	manager, err := unibackup.NewManager(tempConfig)
	if err != nil {
		return fmt.Errorf("创建manager失败: %w", err)
	}
	defer manager.Shutdown()

	for _, testCase := range invalidConfigs {
		suite.logger.Info("测试无效配置", zap.String("case", testCase.name))
		
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		result, err := manager.TestCloudStorageConnectivity(ctx, testCase.config)
		cancel()

		// 无效配置应该返回失败结果，但不应该返回error
		if err != nil {
			return fmt.Errorf("无效配置测试 [%s] 不应该返回error: %w", testCase.name, err)
		}

		if result.Success {
			return fmt.Errorf("无效配置 [%s] 不应该测试成功", testCase.name)
		}

		suite.logger.Info("无效配置测试通过", 
			zap.String("case", testCase.name),
			zap.String("error", result.Error))
	}

	return nil
}

// testTimeoutScenarios 测试超时场景
func (suite *CloudStorageConnectivityTestSuite) testTimeoutScenarios() error {
	// 使用一个不存在的端点来模拟超时
	timeoutConfig := &types.CloudStorageConfig{
		Enabled:   true,
		Type:      "s3",
		Bucket:    "test-bucket",
		Region:    "us-east-1",
		AccessKey: "test-key",
		SecretKey: "test-secret",
		Endpoint:  "http://non-existent-endpoint:9000",
	}

	// 创建临时目录用于本地存储模式
	testDir := "/tmp/unibackup-connectivity-test"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	defer os.RemoveAll(testDir)

	// 创建临时manager用于测试
	tempConfig := &types.Config{
		BackupRoot: testDir,
		Logger:     suite.logger,
	}

	manager, err := unibackup.NewManager(tempConfig)
	if err != nil {
		return fmt.Errorf("创建manager失败: %w", err)
	}
	defer manager.Shutdown()

	// 使用短超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := manager.TestCloudStorageConnectivity(ctx, timeoutConfig)
	if err != nil {
		return fmt.Errorf("超时测试不应该返回error: %w", err)
	}

	if result.Success {
		return fmt.Errorf("超时测试不应该成功")
	}

	suite.logger.Info("超时测试通过", zap.String("error", result.Error))
	return nil
}

// runConnectivityTest 运行连通性测试的通用方法
func (suite *CloudStorageConnectivityTestSuite) runConnectivityTest(storageType string, config *types.CloudStorageConfig) error {
	// 创建临时manager用于测试
	// 注意：云存储模式下不需要创建本地目录，BackupRoot只用于路径生成
	tempConfig := &types.Config{
		BackupRoot:   "/tmp/unibackup-connectivity-test", // 云存储模式下这个路径不需要实际存在
		CloudStorage: config,                             // 设置云存储配置
		Logger:       suite.logger,
	}

	manager, err := unibackup.NewManager(tempConfig)
	if err != nil {
		return fmt.Errorf("创建manager失败: %w", err)
	}
	defer manager.Shutdown()

	// 执行连通性测试
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := manager.TestCloudStorageConnectivity(ctx, config)
	if err != nil {
		return fmt.Errorf("%s连通性测试失败: %w", storageType, err)
	}

	// 验证测试结果
	if !result.Success {
		return fmt.Errorf("%s连通性测试失败: %s", storageType, result.Error)
	}

	// 验证测试步骤
	expectedSteps := []string{"配置验证", "Backend创建", "健康检查", "读写操作测试"}
	if len(result.Steps) != len(expectedSteps) {
		return fmt.Errorf("%s测试步骤数量不正确: 期望%d, 实际%d", storageType, len(expectedSteps), len(result.Steps))
	}

	for i, step := range result.Steps {
		if step.Name != expectedSteps[i] {
			return fmt.Errorf("%s测试步骤名称不正确: 期望%s, 实际%s", storageType, expectedSteps[i], step.Name)
		}
		if !step.Success {
			return fmt.Errorf("%s测试步骤失败 [%s]: %s", storageType, step.Name, step.Error)
		}
	}

	suite.logger.Info("连通性测试成功",
		zap.String("storage_type", storageType),
		zap.Duration("duration", result.Duration),
		zap.Int("steps", len(result.Steps)))

	return nil
}

// main 函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: cloud-storage-connectivity-test <test-type>")
		fmt.Println("测试类型:")
		fmt.Println("  all        - 运行所有连通性测试")
		fmt.Println("  s3         - 运行S3连通性测试")
		fmt.Println("  gcs        - 运行GCS连通性测试")
		fmt.Println("  azure      - 运行Azure连通性测试")
		fmt.Println("  invalid    - 运行无效配置测试")
		fmt.Println("  timeout    - 运行超时测试")
		os.Exit(1)
	}

	testType := os.Args[1]
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	fmt.Printf("🧪 开始云存储连通性测试: %s\n", testType)
	fmt.Println("============================================================")

	suite := NewCloudStorageConnectivityTestSuite(logger)

	var err error
	switch testType {
	case "all":
		err = suite.RunAllTests()
	case "s3":
		err = suite.testS3Connectivity()
	case "gcs":
		err = suite.testGCSConnectivity()
	case "azure":
		err = suite.testAzureConnectivity()
	case "invalid":
		err = suite.testInvalidConfigurations()
	case "timeout":
		err = suite.testTimeoutScenarios()
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		os.Exit(1)
	}

	if err != nil {
		logger.Error("❌ 云存储连通性测试失败", zap.Error(err))
		os.Exit(1)
	}

	fmt.Println("🎉 云存储连通性测试全部通过")
}
