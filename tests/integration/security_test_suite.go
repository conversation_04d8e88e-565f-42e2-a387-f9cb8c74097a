package main

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
	"go.uber.org/zap"
)

// SecurityTestSuite 安全性测试套件
type SecurityTestSuite struct {
	logger   *zap.Logger
	testDir  string
	backend  interfaces.Backend
	testData []byte
}

// NewSecurityTestSuite 创建安全性测试套件
func NewSecurityTestSuite(logger *zap.Logger) *SecurityTestSuite {
	return &SecurityTestSuite{
		logger:   logger,
		testData: []byte("敏感测试数据 - 不应被泄露"),
	}
}

// Setup 初始化测试环境
func (suite *SecurityTestSuite) Setup() error {
	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "security-test-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	suite.testDir = testDir

	// 创建LocalBackend实例
	backend, err := local.NewLocalBackend(testDir)
	if err != nil {
		return fmt.Errorf("创建LocalBackend失败: %w", err)
	}
	suite.backend = backend

	suite.logger.Info("安全性测试环境初始化完成", zap.String("test_dir", testDir))
	return nil
}

// Cleanup 清理测试环境
func (suite *SecurityTestSuite) Cleanup() error {
	if suite.testDir != "" {
		return os.RemoveAll(suite.testDir)
	}
	return nil
}

// RunAllTests 运行所有安全性测试
func (suite *SecurityTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"路径遍历攻击防护测试", suite.testPathTraversalProtection},
		{"文件权限安全测试", suite.testFilePermissionSecurity},
		{"敏感数据泄露防护测试", suite.testSensitiveDataLeakageProtection},
		{"访问控制测试", suite.testAccessControl},
		{"输入验证安全测试", suite.testInputValidationSecurity},
		{"并发访问安全测试", suite.testConcurrentAccessSecurity},
		{"临时文件安全测试", suite.testTemporaryFileSecurity},
		{"错误信息安全测试", suite.testErrorMessageSecurity},
	}

	for _, test := range tests {
		suite.logger.Info("开始安全测试", zap.String("test", test.name))
		if err := test.test(); err != nil {
			return fmt.Errorf("安全测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("安全测试通过", zap.String("test", test.name))
	}

	return nil
}

// testPathTraversalProtection 测试路径遍历攻击防护
func (suite *SecurityTestSuite) testPathTraversalProtection() error {
	ctx := context.Background()

	// 测试各种路径遍历攻击模式（真正的安全威胁）
	maliciousKeys := []string{
		"../../../etc/passwd",           // 经典路径遍历
		"..\\..\\..\\windows\\system32", // Windows路径遍历
		"/etc/passwd",                   // 绝对路径
		"test/../../../etc/passwd",      // 混合路径遍历
		"test/./../../etc/passwd",       // 包含当前目录的路径遍历
		"",                              // 空路径
		"..",                            // 父目录
		"test/../parent/../dir.txt",     // 父目录引用
	}

	// 可能被允许但需要记录的路径（不一定是安全威胁）
	questionablePaths := []string{
		"test//double//slash.txt",  // 双斜杠（通常被规范化）
		"test/./current/./dir.txt", // 当前目录引用（通常被规范化）
	}

	// 特殊路径（可能有不同的处理方式）
	specialPaths := []string{
		".", // 当前目录
	}

	for _, maliciousKey := range maliciousKeys {
		// 尝试写入恶意路径
		_, err := suite.backend.Put(ctx, maliciousKey, strings.NewReader("malicious data"))
		if err == nil {
			return fmt.Errorf("恶意路径 '%s' 应该被拒绝，但写入成功", maliciousKey)
		}

		// 验证返回的是InvalidKey错误
		if !interfaces.IsInvalidKey(err) {
			suite.logger.Warn("恶意路径被拒绝但错误类型不正确",
				zap.String("key", maliciousKey),
				zap.Error(err))
		}

		// 尝试读取恶意路径
		_, err = suite.backend.Get(ctx, maliciousKey)
		if err == nil {
			return fmt.Errorf("恶意路径 '%s' 的读取应该被拒绝", maliciousKey)
		}
	}

	// 测试可能被允许但需要记录的路径
	for _, questionablePath := range questionablePaths {
		// 尝试写入可疑路径
		_, err := suite.backend.Put(ctx, questionablePath, strings.NewReader("questionable data"))
		if err == nil {
			suite.logger.Info("可疑路径被允许（可能被规范化）",
				zap.String("key", questionablePath))
		} else {
			suite.logger.Info("可疑路径被拒绝",
				zap.String("key", questionablePath),
				zap.Error(err))
		}
	}

	// 单独处理特殊路径，因为它们可能有不同的行为
	for _, specialPath := range specialPaths {
		// 尝试写入特殊路径
		_, err := suite.backend.Put(ctx, specialPath, strings.NewReader("special data"))
		if err == nil {
			suite.logger.Warn("特殊路径写入成功，可能存在安全风险",
				zap.String("key", specialPath))
		} else {
			suite.logger.Info("特殊路径写入被正确拒绝",
				zap.String("key", specialPath),
				zap.Error(err))
		}

		// 尝试读取特殊路径
		_, err = suite.backend.Get(ctx, specialPath)
		if err == nil {
			suite.logger.Warn("特殊路径读取成功，可能存在安全风险",
				zap.String("key", specialPath))
		} else {
			suite.logger.Info("特殊路径读取被正确拒绝",
				zap.String("key", specialPath),
				zap.Error(err))
		}
	}

	// 测试包含特殊字符的路径（这些可能会导致系统级错误而不是InvalidKey错误）
	specialCharKeys := []string{
		"test/file\x00.txt", // 空字节注入
		"test/file\n.txt",   // 换行符注入
		"test/file\r.txt",   // 回车符注入
		"test/file\t.txt",   // 制表符注入
	}

	for _, specialKey := range specialCharKeys {
		// 尝试写入包含特殊字符的路径
		_, err := suite.backend.Put(ctx, specialKey, strings.NewReader("special char data"))
		if err == nil {
			// 如果写入成功，记录警告但不失败测试
			// 因为某些文件系统可能允许这些字符
			suite.logger.Warn("Backend允许特殊字符路径",
				zap.String("key", fmt.Sprintf("%q", specialKey)))
		} else {
			// 如果被拒绝，记录信息
			suite.logger.Info("Backend正确拒绝特殊字符路径",
				zap.String("key", fmt.Sprintf("%q", specialKey)),
				zap.Error(err))
		}
	}

	// 验证正常路径仍然可用
	validKey := "test/valid/path.txt"
	_, err := suite.backend.Put(ctx, validKey, strings.NewReader(string(suite.testData)))
	if err != nil {
		return fmt.Errorf("正常路径写入失败: %w", err)
	}

	reader, err := suite.backend.Get(ctx, validKey)
	if err != nil {
		return fmt.Errorf("正常路径读取失败: %w", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取正常路径数据失败: %w", err)
	}
	if string(data) != string(suite.testData) {
		return fmt.Errorf("正常路径数据不一致")
	}

	return nil
}

// testFilePermissionSecurity 测试文件权限安全
func (suite *SecurityTestSuite) testFilePermissionSecurity() error {
	ctx := context.Background()
	testKey := "test/permission-test.txt"

	// 写入测试文件
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(string(suite.testData)))
	if err != nil {
		return fmt.Errorf("写入测试文件失败: %w", err)
	}

	// 检查文件权限
	filePath := filepath.Join(suite.testDir, testKey)
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 验证文件权限不会过于宽松
	mode := fileInfo.Mode()
	if mode&0077 != 0 {
		suite.logger.Warn("文件权限可能过于宽松",
			zap.String("file", filePath),
			zap.String("mode", mode.String()))
	}

	// 检查目录权限
	dirPath := filepath.Dir(filePath)
	dirInfo, err := os.Stat(dirPath)
	if err != nil {
		return fmt.Errorf("获取目录信息失败: %w", err)
	}

	dirMode := dirInfo.Mode()
	if dirMode&0077 != 0 {
		suite.logger.Warn("目录权限可能过于宽松",
			zap.String("dir", dirPath),
			zap.String("mode", dirMode.String()))
	}

	// 测试权限修改后的行为
	originalMode := mode
	err = os.Chmod(filePath, 0000) // 移除所有权限
	if err != nil {
		return fmt.Errorf("修改文件权限失败: %w", err)
	}

	// 尝试读取无权限文件
	_, err = suite.backend.Get(ctx, testKey)
	if err == nil {
		// 恢复权限后再次测试
		os.Chmod(filePath, originalMode)
		return fmt.Errorf("读取无权限文件应该失败")
	}

	// 验证返回权限错误
	if !interfaces.IsPermissionDenied(err) {
		suite.logger.Warn("权限错误类型不正确", zap.Error(err))
	}

	// 恢复文件权限
	err = os.Chmod(filePath, originalMode)
	if err != nil {
		return fmt.Errorf("恢复文件权限失败: %w", err)
	}

	// 验证权限恢复后可以正常访问
	reader, err := suite.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("权限恢复后读取失败: %w", err)
	}
	defer reader.Close()

	return nil
}

// testSensitiveDataLeakageProtection 测试敏感数据泄露防护
func (suite *SecurityTestSuite) testSensitiveDataLeakageProtection() error {
	ctx := context.Background()

	// 创建包含敏感信息的测试数据
	sensitiveData := []string{
		"password=secret123",
		"api_key=sk-1234567890abcdef",
		"database_url=mysql://user:pass@host:3306/db",
		"private_key=-----BEGIN PRIVATE KEY-----",
		"access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
		"credit_card=4111-1111-1111-1111",
		"ssn=***********",
	}

	for i, data := range sensitiveData {
		testKey := fmt.Sprintf("test/sensitive/data-%d.txt", i)

		// 写入敏感数据
		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(data))
		if err != nil {
			return fmt.Errorf("写入敏感数据失败: %w", err)
		}

		// 验证数据可以正确读取（功能正常）
		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("读取敏感数据失败: %w", err)
		}
		defer reader.Close()

		readData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取敏感数据内容失败: %w", err)
		}

		if string(readData) != data {
			return fmt.Errorf("敏感数据不一致")
		}

		// 检查文件系统中的文件权限
		filePath := filepath.Join(suite.testDir, testKey)
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			return fmt.Errorf("获取敏感文件信息失败: %w", err)
		}

		// 敏感文件应该有严格的权限控制
		mode := fileInfo.Mode()
		if mode&0077 != 0 {
			suite.logger.Warn("敏感文件权限过于宽松",
				zap.String("file", filePath),
				zap.String("mode", mode.String()))
		}
	}

	return nil
}

// testAccessControl 测试访问控制
func (suite *SecurityTestSuite) testAccessControl() error {
	ctx := context.Background()

	// 测试同时访问多个文件的权限控制
	fileCount := 10
	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/access-control/file-%d.txt", i)
		testData := fmt.Sprintf("access-control-data-%d", i)

		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
		if err != nil {
			return fmt.Errorf("写入访问控制测试文件失败: %w", err)
		}
	}

	// 验证所有文件都可以正常访问
	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/access-control/file-%d.txt", i)

		exists, err := suite.backend.Exists(ctx, testKey)
		if err != nil {
			return fmt.Errorf("检查文件存在性失败: %w", err)
		}
		if !exists {
			return fmt.Errorf("访问控制测试文件不存在: %s", testKey)
		}

		reader, err := suite.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("读取访问控制测试文件失败: %w", err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取访问控制文件数据失败: %w", err)
		}

		expectedData := fmt.Sprintf("access-control-data-%d", i)
		if string(data) != expectedData {
			return fmt.Errorf("访问控制文件数据不一致")
		}
	}

	return nil
}

// testInputValidationSecurity 测试输入验证安全
func (suite *SecurityTestSuite) testInputValidationSecurity() error {
	ctx := context.Background()

	// 测试各种恶意输入
	maliciousInputs := []struct {
		key  string
		data string
		desc string
	}{
		{"test/sql-injection.txt", "'; DROP TABLE users; --", "SQL注入尝试"},
		{"test/script-injection.txt", "<script>alert('xss')</script>", "脚本注入尝试"},
		{"test/command-injection.txt", "; rm -rf /", "命令注入尝试"},
		{"test/buffer-overflow.txt", strings.Repeat("A", 10000), "缓冲区溢出尝试"},
		{"test/unicode-attack.txt", "test\u202e\u0041\u0042", "Unicode攻击尝试"},
		{"test/format-string.txt", "%s%s%s%s%s%s%s%s", "格式字符串攻击尝试"},
	}

	for _, input := range maliciousInputs {
		// 尝试写入恶意数据
		_, err := suite.backend.Put(ctx, input.key, strings.NewReader(input.data))
		if err != nil {
			// 如果被拒绝，记录日志但继续测试
			suite.logger.Info("恶意输入被拒绝",
				zap.String("desc", input.desc),
				zap.String("key", input.key),
				zap.Error(err))
			continue
		}

		// 如果写入成功，验证数据完整性
		reader, err := suite.backend.Get(ctx, input.key)
		if err != nil {
			return fmt.Errorf("读取恶意输入数据失败 [%s]: %w", input.desc, err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取恶意输入内容失败 [%s]: %w", input.desc, err)
		}

		// 验证数据没有被意外修改或执行
		if string(data) != input.data {
			return fmt.Errorf("恶意输入数据被意外修改 [%s]", input.desc)
		}

		suite.logger.Info("恶意输入被安全处理",
			zap.String("desc", input.desc),
			zap.String("key", input.key))
	}

	return nil
}

// testConcurrentAccessSecurity 测试并发访问安全
func (suite *SecurityTestSuite) testConcurrentAccessSecurity() error {
	ctx := context.Background()
	concurrency := 20
	testKey := "test/concurrent-security.txt"

	// 并发写入同一个文件
	errChan := make(chan error, concurrency)
	for i := 0; i < concurrency; i++ {
		go func(index int) {
			data := fmt.Sprintf("concurrent-security-data-%d", index)
			key := fmt.Sprintf("%s-%d", testKey, index)

			// 多次尝试写入以测试竞争条件
			for attempt := 0; attempt < 3; attempt++ {
				_, err := suite.backend.Put(ctx, key, strings.NewReader(data))
				if err != nil {
					errChan <- fmt.Errorf("并发写入失败 [goroutine %d, attempt %d]: %w", index, attempt, err)
					return
				}
				time.Sleep(time.Millisecond) // 短暂延迟增加竞争概率
			}
			errChan <- nil
		}(i)
	}

	// 检查所有并发操作的结果
	for i := 0; i < concurrency; i++ {
		if err := <-errChan; err != nil {
			return fmt.Errorf("并发访问安全测试失败: %w", err)
		}
	}

	// 验证所有文件都正确写入
	for i := 0; i < concurrency; i++ {
		key := fmt.Sprintf("%s-%d", testKey, i)
		exists, err := suite.backend.Exists(ctx, key)
		if err != nil {
			return fmt.Errorf("检查并发文件存在性失败: %w", err)
		}
		if !exists {
			return fmt.Errorf("并发写入的文件不存在: %s", key)
		}
	}

	return nil
}

// testTemporaryFileSecurity 测试临时文件安全
func (suite *SecurityTestSuite) testTemporaryFileSecurity() error {
	ctx := context.Background()
	testKey := "test/temp-file-security.txt"
	largeData := make([]byte, 5*1024*1024) // 5MB数据
	for i := range largeData {
		largeData[i] = byte(i % 256)
	}

	// 写入大文件，可能会创建临时文件
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(string(largeData)))
	if err != nil {
		return fmt.Errorf("写入大文件失败: %w", err)
	}

	// 检查是否有临时文件残留
	err = filepath.Walk(suite.testDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查可疑的临时文件模式
		name := info.Name()
		if strings.Contains(name, "tmp") ||
			strings.Contains(name, "temp") ||
			strings.HasPrefix(name, ".") && strings.HasSuffix(name, ".tmp") {
			suite.logger.Warn("发现可疑的临时文件", zap.String("path", path))
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("检查临时文件失败: %w", err)
	}

	// 验证文件完整性
	reader, err := suite.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("读取大文件失败: %w", err)
	}
	defer reader.Close()

	readData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取大文件数据失败: %w", err)
	}

	if len(readData) != len(largeData) {
		return fmt.Errorf("大文件数据长度不一致: 期望 %d, 实际 %d", len(largeData), len(readData))
	}

	return nil
}

// testErrorMessageSecurity 测试错误信息安全
func (suite *SecurityTestSuite) testErrorMessageSecurity() error {
	ctx := context.Background()

	// 测试各种错误场景，确保错误信息不泄露敏感信息
	errorTests := []struct {
		operation string
		key       string
		expectErr bool
	}{
		{"Get", "non-existent-file.txt", true},
		{"Get", "../../../etc/passwd", true},
		{"Put", "", true},
		{"Delete", "non-existent-file.txt", false}, // Delete通常不报错
		{"Exists", "../../../etc/passwd", true},
	}

	for _, test := range errorTests {
		var err error

		switch test.operation {
		case "Get":
			_, err = suite.backend.Get(ctx, test.key)
		case "Put":
			_, err = suite.backend.Put(ctx, test.key, strings.NewReader("test"))
		case "Delete":
			err = suite.backend.Delete(ctx, test.key)
		case "Exists":
			_, err = suite.backend.Exists(ctx, test.key)
		}

		if test.expectErr && err == nil {
			return fmt.Errorf("操作 %s 应该返回错误，但成功了", test.operation)
		}

		if err != nil {
			errorMsg := err.Error()

			// 检查错误信息是否泄露敏感路径信息
			sensitivePatterns := []string{
				suite.testDir,           // 不应该暴露完整的测试目录路径
				"/etc/passwd",           // 不应该暴露系统路径
				"C:\\Windows\\System32", // 不应该暴露Windows系统路径
			}

			for _, pattern := range sensitivePatterns {
				if strings.Contains(errorMsg, pattern) {
					suite.logger.Warn("错误信息可能泄露敏感路径",
						zap.String("operation", test.operation),
						zap.String("key", test.key),
						zap.String("error", errorMsg),
						zap.String("sensitive_pattern", pattern))
				}
			}

			suite.logger.Info("错误信息检查",
				zap.String("operation", test.operation),
				zap.String("key", test.key),
				zap.String("error", errorMsg))
		}
	}

	return nil
}

// runSecurityTests 运行安全性测试的主函数
func runSecurityTests(logger *zap.Logger) error {
	logger.Info("🔒 开始安全性测试")

	suite := NewSecurityTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("安全性测试失败: %w", err)
	}

	logger.Info("✅ 安全性测试全部通过")
	return nil
}

// 安全性测试主入口
func main() {
	// 配置日志
	logger, _ := zap.NewDevelopment()

	if len(os.Args) < 2 {
		logger.Error("使用方法: security-test <test-type>")
		logger.Info("可用的测试类型:")
		logger.Info("  all                     - 运行所有安全性测试")
		os.Exit(1)
	}

	testType := os.Args[1]

	logger.Info("🚀 开始安全性测试", zap.String("type", testType))

	var err error
	switch testType {
	case "all":
		err = runSecurityTests(logger)
	default:
		logger.Error("未知的测试类型", zap.String("type", testType))
		os.Exit(1)
	}

	if err != nil {
		logger.Error("安全性测试失败", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("✅ 安全性测试全部通过", zap.String("type", testType))
}
