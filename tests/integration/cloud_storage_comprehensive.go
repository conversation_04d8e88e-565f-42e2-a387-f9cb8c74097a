package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	// 导入所有云存储驱动
	"go.uber.org/zap"
	_ "gocloud.dev/blob/azureblob"
	_ "gocloud.dev/blob/gcsblob"
	_ "gocloud.dev/blob/s3blob"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// TestResult 测试结果
type TestResult struct {
	TestName string        `json:"test_name"`
	Category string        `json:"category"`
	Success  bool          `json:"success"`
	Duration time.Duration `json:"duration"`
	Error    string        `json:"error,omitempty"`
}

// CloudStorageComprehensiveTest 云存储全面验证测试
type CloudStorageComprehensiveTest struct {
	logger     *zap.Logger
	config     *types.Config
	backend    interfaces.Backend
	testDataID string
	results    []TestResult
}

func main() {
	// 检查参数
	if len(os.Args) > 1 && (os.Args[1] == "-h" || os.Args[1] == "--help") {
		showUsage()
		os.Exit(0)
	}

	// 确定配置文件路径
	configPath := "/tests/config/test_config_cloud_storage_comprehensive.json"
	if len(os.Args) >= 2 {
		configPath = os.Args[1]
	}

	// 创建日志记录器
	logger, _ := zap.NewDevelopment()

	fmt.Printf("🚀 UniBackup 云存储功能全面正确性验证测试\n")
	fmt.Printf("📁 配置文件: %s\n", configPath)
	fmt.Println(strings.Repeat("=", 80))

	startTime := time.Now()

	// 加载并验证配置
	config, err := loadTestConfig(configPath)
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 验证测试环境
	if err := validateCloudStorageEnvironment(config); err != nil {
		fmt.Printf("❌ 测试环境验证失败: %v\n", err)
		fmt.Println("\n💡 请检查:")
		fmt.Println("   1. 云存储配置是否正确")
		fmt.Println("   2. 认证信息是否已设置")
		fmt.Println("   3. 网络连接是否正常")
		os.Exit(1)
	}

	// 运行云存储全面验证测试
	err = runCloudStorageComprehensiveTest(logger, config)
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("❌ 云存储功能验证测试失败: %v\n", err)
		fmt.Printf("⏱️  测试耗时: %v\n", duration)
		os.Exit(1)
	}

	fmt.Println(strings.Repeat("=", 80))
	fmt.Printf("✅ 云存储功能全面正确性验证测试成功完成\n")
	fmt.Printf("⏱️  测试耗时: %v\n", duration)
}

// runCloudStorageComprehensiveTest 运行云存储全面验证测试
func runCloudStorageComprehensiveTest(logger *zap.Logger, config *types.Config) error {
	// 创建测试实例
	test, err := NewCloudStorageComprehensiveTest(logger, config)
	if err != nil {
		return fmt.Errorf("创建测试实例失败: %w", err)
	}

	// 运行所有测试
	return test.RunAllTests()
}

// NewCloudStorageComprehensiveTest 创建云存储全面验证测试
func NewCloudStorageComprehensiveTest(logger *zap.Logger, config *types.Config) (*CloudStorageComprehensiveTest, error) {
	backend, err := storage.NewBackend(config, logger)
	if err != nil {
		return nil, fmt.Errorf("创建存储后端失败: %w", err)
	}

	return &CloudStorageComprehensiveTest{
		logger:     logger,
		config:     config,
		backend:    backend,
		testDataID: fmt.Sprintf("comprehensive-test-%d", time.Now().Unix()),
		results:    make([]TestResult, 0),
	}, nil
}

// RunAllTests 运行所有测试
func (test *CloudStorageComprehensiveTest) RunAllTests() error {
	test.logger.Info("🚀 开始云存储功能全面正确性验证测试")

	// 第一阶段：云存储实现正确性检查
	test.logger.Info("📋 第一阶段：云存储实现正确性检查")
	test.runStorageImplementationTests()

	// 第二阶段：数据完整性测试场景
	test.logger.Info("📋 第二阶段：数据完整性测试场景")
	test.runDataIntegrityTests()

	// 第三阶段：云存储特定验证
	test.logger.Info("📋 第三阶段：云存储特定验证")
	test.runCloudStorageSpecificTests()

	// 生成测试报告
	return test.generateTestReport()
}

// runStorageImplementationTests 运行存储实现正确性测试
func (test *CloudStorageComprehensiveTest) runStorageImplementationTests() {
	tests := []struct {
		name string
		fn   func() error
	}{
		{"存储完整性验证", test.testStorageCompleteness},
		{"备份数据文件存储", test.testBackupDataStorage},
		{"元数据文件存储", test.testMetadataStorage},
		{"备份记录存储", test.testBackupRecordsStorage},
		{"任务记录存储", test.testTaskRecordsStorage},
		{"状态文件存储", test.testStatusFilesStorage},
	}

	test.runTestGroup("存储实现正确性", tests)
}

// runDataIntegrityTests 运行数据完整性测试
func (test *CloudStorageComprehensiveTest) runDataIntegrityTests() {
	tests := []struct {
		name string
		fn   func() error
	}{
		{"全量备份和恢复测试", test.testFullBackupRestore},
		{"增量备份和恢复测试", test.testIncrementalBackupRestore},
		{"时间点恢复验证测试", test.testPointInTimeRecovery},
	}

	test.runTestGroup("数据完整性", tests)
}

// runCloudStorageSpecificTests 运行云存储特定测试
func (test *CloudStorageComprehensiveTest) runCloudStorageSpecificTests() {
	tests := []struct {
		name string
		fn   func() error
	}{
		{"多云存储类型测试", test.testMultiCloudStorage},
		{"网络中断恢复测试", test.testNetworkInterruption},
		{"并发操作测试", test.testConcurrentOperations},
		{"大数据量测试", test.testLargeDataVolume},
	}

	test.runTestGroup("云存储特定功能", tests)
}

// runTestGroup 运行测试组
func (test *CloudStorageComprehensiveTest) runTestGroup(category string, tests []struct {
	name string
	fn   func() error
}) {
	for _, testCase := range tests {
		test.logger.Info("执行测试", zap.String("category", category), zap.String("test", testCase.name))
		start := time.Now()

		err := testCase.fn()
		duration := time.Since(start)

		result := TestResult{
			TestName: testCase.name,
			Category: category,
			Success:  err == nil,
			Duration: duration,
		}

		if err != nil {
			result.Error = err.Error()
			test.logger.Error("测试失败", zap.String("category", category), zap.String("test", testCase.name), zap.Error(err))
		} else {
			test.logger.Info("测试通过", zap.String("category", category), zap.String("test", testCase.name), zap.String("duration", duration.String()))
		}

		test.results = append(test.results, result)
	}
}

// testStorageCompleteness 测试存储完整性
func (test *CloudStorageComprehensiveTest) testStorageCompleteness() error {
	test.logger.Info("验证云存储目录结构完整性")

	ctx := context.Background()
	expectedDirs := []string{"mysql", "elasticsearch", "tasks", "metadata"}

	for _, dir := range expectedDirs {
		testKey := fmt.Sprintf("%s/test-completeness-%s.txt", dir, test.testDataID)
		testData := []byte(fmt.Sprintf("completeness test data for %s", dir))

		// 写入测试文件
		if _, err := test.backend.Put(ctx, testKey, bytes.NewReader(testData)); err != nil {
			return fmt.Errorf("无法在目录 %s 中创建测试文件: %w", dir, err)
		}

		// 验证文件存在
		if exists, err := test.backend.Exists(ctx, testKey); err != nil {
			return fmt.Errorf("检查文件存在性失败 %s: %w", testKey, err)
		} else if !exists {
			return fmt.Errorf("文件不存在: %s", testKey)
		}

		// 读取并验证内容
		reader, err := test.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("读取测试文件失败 %s: %w", testKey, err)
		}
		defer reader.Close()

		retrievedData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取文件内容失败 %s: %w", testKey, err)
		}

		if string(retrievedData) != string(testData) {
			return fmt.Errorf("文件内容不一致 %s", testKey)
		}

		// 清理测试文件
		if err := test.backend.Delete(ctx, testKey); err != nil {
			test.logger.Warn("清理测试文件失败", zap.String("key", testKey), zap.Error(err))
		}
	}

	test.logger.Info("存储完整性验证通过")
	return nil
}

// testBackupDataStorage 测试备份数据文件存储
func (test *CloudStorageComprehensiveTest) testBackupDataStorage() error {
	test.logger.Info("验证备份数据文件存储")
	ctx := context.Background()

	mysqlBackupData := []byte(`-- MySQL dump test data
CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(100));
INSERT INTO test_table (id, name) VALUES (1, 'test data');`)

	mysqlKey := fmt.Sprintf("mysql/backup-%s.sql", test.testDataID)
	if _, err := test.backend.Put(ctx, mysqlKey, bytes.NewReader(mysqlBackupData)); err != nil {
		return fmt.Errorf("存储MySQL备份数据失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, mysqlKey)
	if err != nil {
		return fmt.Errorf("获取MySQL备份数据失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取MySQL备份数据失败: %w", err)
	}

	if string(retrievedData) != string(mysqlBackupData) {
		return fmt.Errorf("MySQL备份数据不一致")
	}

	test.backend.Delete(ctx, mysqlKey)
	test.logger.Info("备份数据文件存储验证通过")
	return nil
}

// testMetadataStorage 测试元数据文件存储
func (test *CloudStorageComprehensiveTest) testMetadataStorage() error {
	test.logger.Info("验证元数据文件存储")
	ctx := context.Background()

	metadata := map[string]interface{}{
		"backup_id":   test.testDataID,
		"created_at":  time.Now().Format(time.RFC3339),
		"backup_type": "full",
		"source_type": "mysql",
		"size_bytes":  12345,
	}

	metaData, _ := json.Marshal(metadata)
	metaKey := fmt.Sprintf("metadata/backup-%s.json", test.testDataID)

	if _, err := test.backend.Put(ctx, metaKey, bytes.NewReader(metaData)); err != nil {
		return fmt.Errorf("存储元数据失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, metaKey)
	if err != nil {
		return fmt.Errorf("获取元数据失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取元数据失败: %w", err)
	}

	var retrievedMeta map[string]interface{}
	if err := json.Unmarshal(retrievedData, &retrievedMeta); err != nil {
		return fmt.Errorf("解析元数据失败: %w", err)
	}

	if retrievedMeta["backup_id"] != metadata["backup_id"] {
		return fmt.Errorf("元数据不一致")
	}

	test.backend.Delete(ctx, metaKey)
	test.logger.Info("元数据文件存储验证通过")
	return nil
}

// testBackupRecordsStorage 测试备份记录存储
func (test *CloudStorageComprehensiveTest) testBackupRecordsStorage() error {
	ctx := context.Background()
	backupRecord := map[string]interface{}{
		"id":     test.testDataID,
		"type":   "mysql_full",
		"status": "completed",
	}

	recordData, _ := json.Marshal(backupRecord)
	recordKey := fmt.Sprintf("backup_records/%s.json", test.testDataID)

	if _, err := test.backend.Put(ctx, recordKey, bytes.NewReader(recordData)); err != nil {
		return fmt.Errorf("存储备份记录失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, recordKey)
	if err != nil {
		return fmt.Errorf("获取备份记录失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取备份记录失败: %w", err)
	}

	var retrievedRecord map[string]interface{}
	if err := json.Unmarshal(retrievedData, &retrievedRecord); err != nil {
		return fmt.Errorf("解析备份记录失败: %w", err)
	}

	if retrievedRecord["id"] != backupRecord["id"] {
		return fmt.Errorf("备份记录不一致")
	}

	test.backend.Delete(ctx, recordKey)
	return nil
}

// testTaskRecordsStorage 测试任务记录存储
func (test *CloudStorageComprehensiveTest) testTaskRecordsStorage() error {
	ctx := context.Background()
	taskRecord := map[string]interface{}{
		"task_id": fmt.Sprintf("task-%s", test.testDataID),
		"type":    "backup",
		"status":  "running",
	}

	taskData, _ := json.Marshal(taskRecord)
	taskKey := fmt.Sprintf("tasks/task-%s.json", test.testDataID)

	if _, err := test.backend.Put(ctx, taskKey, bytes.NewReader(taskData)); err != nil {
		return fmt.Errorf("存储任务记录失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, taskKey)
	if err != nil {
		return fmt.Errorf("获取任务记录失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取任务记录失败: %w", err)
	}

	var retrievedTask map[string]interface{}
	if err := json.Unmarshal(retrievedData, &retrievedTask); err != nil {
		return fmt.Errorf("解析任务记录失败: %w", err)
	}

	if retrievedTask["task_id"] != taskRecord["task_id"] {
		return fmt.Errorf("任务记录不一致")
	}

	test.backend.Delete(ctx, taskKey)
	return nil
}

// testStatusFilesStorage 测试状态文件存储
func (test *CloudStorageComprehensiveTest) testStatusFilesStorage() error {
	ctx := context.Background()
	statusData := map[string]interface{}{
		"system_status": "healthy",
		"active_tasks":  2,
	}

	statusJSON, _ := json.Marshal(statusData)
	statusKey := fmt.Sprintf("system/status-%s.json", test.testDataID)

	if _, err := test.backend.Put(ctx, statusKey, bytes.NewReader(statusJSON)); err != nil {
		return fmt.Errorf("存储状态文件失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, statusKey)
	if err != nil {
		return fmt.Errorf("获取状态文件失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取状态文件失败: %w", err)
	}

	var retrievedStatus map[string]interface{}
	if err := json.Unmarshal(retrievedData, &retrievedStatus); err != nil {
		return fmt.Errorf("解析状态文件失败: %w", err)
	}

	if retrievedStatus["system_status"] != statusData["system_status"] {
		return fmt.Errorf("状态文件不一致")
	}

	test.backend.Delete(ctx, statusKey)
	return nil
}

// testFullBackupRestore 测试全量备份和恢复
func (test *CloudStorageComprehensiveTest) testFullBackupRestore() error {
	test.logger.Info("执行全量备份和恢复测试")
	ctx := context.Background()

	testData := map[string]interface{}{
		"test_id": test.testDataID,
		"records": []map[string]interface{}{
			{"id": 1, "name": "Alice"},
			{"id": 2, "name": "Bob"},
		},
	}

	backupData, _ := json.Marshal(testData)
	backupKey := fmt.Sprintf("full-backup-test/%s/backup.json", test.testDataID)

	if _, err := test.backend.Put(ctx, backupKey, bytes.NewReader(backupData)); err != nil {
		return fmt.Errorf("模拟全量备份失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, backupKey)
	if err != nil {
		return fmt.Errorf("模拟全量恢复失败: %w", err)
	}
	defer reader.Close()

	restoredData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取恢复数据失败: %w", err)
	}

	var restoredMap map[string]interface{}
	if err := json.Unmarshal(restoredData, &restoredMap); err != nil {
		return fmt.Errorf("解析恢复数据失败: %w", err)
	}

	if restoredMap["test_id"] != testData["test_id"] {
		return fmt.Errorf("数据完整性验证失败")
	}

	test.backend.Delete(ctx, backupKey)
	test.logger.Info("全量备份和恢复测试通过")
	return nil
}

// testIncrementalBackupRestore 测试增量备份和恢复
func (test *CloudStorageComprehensiveTest) testIncrementalBackupRestore() error {
	test.logger.Info("执行增量备份和恢复测试")
	ctx := context.Background()

	baseData := map[string]interface{}{
		"test_id": test.testDataID,
		"records": []map[string]interface{}{
			{"id": 1, "name": "Alice"},
			{"id": 2, "name": "Bob"},
		},
	}

	incrementalData := map[string]interface{}{
		"test_id": test.testDataID,
		"records": []map[string]interface{}{
			{"id": 3, "name": "Charlie"},
		},
	}

	baseBackupData, _ := json.Marshal(baseData)
	baseKey := fmt.Sprintf("incremental-backup-test/%s/base.json", test.testDataID)
	if _, err := test.backend.Put(ctx, baseKey, bytes.NewReader(baseBackupData)); err != nil {
		return fmt.Errorf("存储基础数据失败: %w", err)
	}

	incrementalBackupData, _ := json.Marshal(incrementalData)
	incrementalKey := fmt.Sprintf("incremental-backup-test/%s/incremental.json", test.testDataID)
	if _, err := test.backend.Put(ctx, incrementalKey, bytes.NewReader(incrementalBackupData)); err != nil {
		return fmt.Errorf("存储增量数据失败: %w", err)
	}

	// 验证两个文件都存在
	if exists, _ := test.backend.Exists(ctx, baseKey); !exists {
		return fmt.Errorf("基础数据文件不存在")
	}
	if exists, _ := test.backend.Exists(ctx, incrementalKey); !exists {
		return fmt.Errorf("增量数据文件不存在")
	}

	test.backend.Delete(ctx, baseKey)
	test.backend.Delete(ctx, incrementalKey)
	test.logger.Info("增量备份和恢复测试通过")
	return nil
}

// testPointInTimeRecovery 测试时间点恢复
func (test *CloudStorageComprehensiveTest) testPointInTimeRecovery() error {
	test.logger.Info("执行时间点恢复验证测试")
	ctx := context.Background()

	t3Data := map[string]interface{}{
		"test_id":   test.testDataID,
		"timestamp": time.Now().Format(time.RFC3339),
		"records": []map[string]interface{}{
			{"id": 1, "name": "Initial Data 1"},
			{"id": 2, "name": "Initial Data 2"},
			{"id": 3, "name": "T2 Data 1"},
		},
	}

	t3BackupData, _ := json.Marshal(t3Data)
	t3Key := fmt.Sprintf("point-in-time-test/%s/t3.json", test.testDataID)
	if _, err := test.backend.Put(ctx, t3Key, bytes.NewReader(t3BackupData)); err != nil {
		return fmt.Errorf("T3时刻备份失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, t3Key)
	if err != nil {
		return fmt.Errorf("时间点恢复失败: %w", err)
	}
	defer reader.Close()

	restoredData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取恢复数据失败: %w", err)
	}

	var restoredMap map[string]interface{}
	if err := json.Unmarshal(restoredData, &restoredMap); err != nil {
		return fmt.Errorf("解析恢复数据失败: %w", err)
	}

	restoredRecords := restoredMap["records"].([]interface{})
	expectedRecords := 3
	if len(restoredRecords) != expectedRecords {
		return fmt.Errorf("时间点恢复数据不完整，期望 %d 条记录，实际 %d 条", expectedRecords, len(restoredRecords))
	}

	test.backend.Delete(ctx, t3Key)
	test.logger.Info("时间点恢复验证测试通过")
	return nil
}

// testMultiCloudStorage 测试多云存储类型
func (test *CloudStorageComprehensiveTest) testMultiCloudStorage() error {
	test.logger.Info("执行多云存储类型测试")

	if test.config.CloudStorage == nil || !test.config.CloudStorage.Enabled {
		return fmt.Errorf("云存储未启用")
	}

	storageType := test.config.CloudStorage.Type
	ctx := context.Background()
	testKey := fmt.Sprintf("multi-cloud-test/%s/test-file.txt", test.testDataID)
	testData := []byte(fmt.Sprintf("Multi-cloud storage test data for %s", storageType))

	if _, err := test.backend.Put(ctx, testKey, bytes.NewReader(testData)); err != nil {
		return fmt.Errorf("%s 存储写入失败: %w", storageType, err)
	}

	reader, err := test.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("%s 存储读取失败: %w", storageType, err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取数据失败: %w", err)
	}

	if string(retrievedData) != string(testData) {
		return fmt.Errorf("%s 存储数据不一致", storageType)
	}

	if err := test.backend.Delete(ctx, testKey); err != nil {
		return fmt.Errorf("%s 存储删除失败: %w", storageType, err)
	}

	test.logger.Info("多云存储类型测试通过", zap.String("type", storageType))
	return nil
}

// testNetworkInterruption 测试网络中断恢复
func (test *CloudStorageComprehensiveTest) testNetworkInterruption() error {
	test.logger.Info("执行网络中断恢复测试")
	ctx := context.Background()

	largeData := make([]byte, 1024*1024) // 1MB 测试数据
	for i := range largeData {
		largeData[i] = byte(i % 256)
	}

	testKey := fmt.Sprintf("network-interruption-test/%s/large-file.bin", test.testDataID)

	if _, err := test.backend.Put(ctx, testKey, bytes.NewReader(largeData)); err != nil {
		return fmt.Errorf("大文件上传失败: %w", err)
	}

	reader, err := test.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("大文件下载失败: %w", err)
	}
	defer reader.Close()

	retrievedData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取大文件失败: %w", err)
	}

	if len(retrievedData) != len(largeData) {
		return fmt.Errorf("文件大小不一致，期望 %d，实际 %d", len(largeData), len(retrievedData))
	}

	// 验证前1KB和后1KB的数据
	for i := 0; i < 1024; i++ {
		if retrievedData[i] != largeData[i] {
			return fmt.Errorf("文件开头数据不一致，位置 %d", i)
		}
		if retrievedData[len(largeData)-1024+i] != largeData[len(largeData)-1024+i] {
			return fmt.Errorf("文件结尾数据不一致，位置 %d", len(largeData)-1024+i)
		}
	}

	test.backend.Delete(ctx, testKey)
	test.logger.Info("网络中断恢复测试通过")
	return nil
}

// testConcurrentOperations 测试并发操作
func (test *CloudStorageComprehensiveTest) testConcurrentOperations() error {
	test.logger.Info("执行并发操作测试")
	ctx := context.Background()
	concurrency := 5
	errChan := make(chan error, concurrency)

	for i := 0; i < concurrency; i++ {
		go func(index int) {
			testKey := fmt.Sprintf("concurrent-test/%s/file-%d.txt", test.testDataID, index)
			testData := []byte(fmt.Sprintf("Concurrent test data %d", index))

			if _, err := test.backend.Put(ctx, testKey, bytes.NewReader(testData)); err != nil {
				errChan <- fmt.Errorf("并发写入失败 [%d]: %w", index, err)
				return
			}

			reader, err := test.backend.Get(ctx, testKey)
			if err != nil {
				errChan <- fmt.Errorf("并发读取失败 [%d]: %w", index, err)
				return
			}
			defer reader.Close()

			retrievedData, err := io.ReadAll(reader)
			if err != nil {
				errChan <- fmt.Errorf("并发数据读取失败 [%d]: %w", index, err)
				return
			}

			if string(retrievedData) != string(testData) {
				errChan <- fmt.Errorf("并发数据不一致 [%d]", index)
				return
			}

			if err := test.backend.Delete(ctx, testKey); err != nil {
				errChan <- fmt.Errorf("并发删除失败 [%d]: %w", index, err)
				return
			}

			errChan <- nil
		}(i)
	}

	for i := 0; i < concurrency; i++ {
		if err := <-errChan; err != nil {
			return err
		}
	}

	test.logger.Info("并发操作测试通过")
	return nil
}

// testLargeDataVolume 测试大数据量
func (test *CloudStorageComprehensiveTest) testLargeDataVolume() error {
	test.logger.Info("执行大数据量测试")
	ctx := context.Background()
	fileCount := 3
	fileSize := 2 * 1024 * 1024 // 2MB per file

	for i := 0; i < fileCount; i++ {
		test.logger.Info("测试大文件", zap.Int("index", i+1), zap.Int("total", fileCount))

		largeData := make([]byte, fileSize)
		for j := range largeData {
			largeData[j] = byte((i + j) % 256)
		}

		testKey := fmt.Sprintf("large-data-test/%s/large-file-%d.bin", test.testDataID, i)

		if _, err := test.backend.Put(ctx, testKey, bytes.NewReader(largeData)); err != nil {
			return fmt.Errorf("大文件上传失败 [%d]: %w", i, err)
		}

		if exists, err := test.backend.Exists(ctx, testKey); err != nil {
			return fmt.Errorf("检查大文件存在性失败 [%d]: %w", i, err)
		} else if !exists {
			return fmt.Errorf("大文件不存在 [%d]", i)
		}

		reader, err := test.backend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("大文件下载失败 [%d]: %w", i, err)
		}
		defer reader.Close()

		retrievedData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取大文件失败 [%d]: %w", i, err)
		}

		if len(retrievedData) != fileSize {
			return fmt.Errorf("大文件大小不一致 [%d]，期望 %d，实际 %d", i, fileSize, len(retrievedData))
		}

		// 验证前1KB和后1KB的数据
		for j := 0; j < 1024; j++ {
			if retrievedData[j] != largeData[j] {
				return fmt.Errorf("大文件开头数据不一致 [%d]，位置 %d", i, j)
			}
			if retrievedData[fileSize-1024+j] != largeData[fileSize-1024+j] {
				return fmt.Errorf("大文件结尾数据不一致 [%d]，位置 %d", i, fileSize-1024+j)
			}
		}

		if err := test.backend.Delete(ctx, testKey); err != nil {
			test.logger.Warn("清理大文件失败", zap.Int("index", i), zap.Error(err))
		}
	}

	test.logger.Info("大数据量测试通过")
	return nil
}

// generateTestReport 生成测试报告
func (test *CloudStorageComprehensiveTest) generateTestReport() error {
	test.logger.Info("生成云存储功能测试报告")

	totalTests := len(test.results)
	passedTests := 0
	failedTests := 0
	totalDuration := time.Duration(0)
	categoryStats := make(map[string]map[string]int)

	for _, result := range test.results {
		if result.Success {
			passedTests++
		} else {
			failedTests++
		}
		totalDuration += result.Duration

		if _, exists := categoryStats[result.Category]; !exists {
			categoryStats[result.Category] = map[string]int{"passed": 0, "failed": 0}
		}
		if result.Success {
			categoryStats[result.Category]["passed"]++
		} else {
			categoryStats[result.Category]["failed"]++
		}
	}

	// 控制台输出报告
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📊 UniBackup 云存储功能全面正确性验证测试报告")
	fmt.Println(strings.Repeat("=", 80))

	fmt.Printf("📈 总体统计:\n")
	fmt.Printf("   总测试数: %d\n", totalTests)
	fmt.Printf("   通过测试: %d\n", passedTests)
	fmt.Printf("   失败测试: %d\n", failedTests)
	fmt.Printf("   成功率: %.1f%%\n", float64(passedTests)/float64(totalTests)*100)
	fmt.Printf("   总耗时: %v\n", totalDuration)
	fmt.Println()

	fmt.Printf("📋 分类统计:\n")
	for category, stats := range categoryStats {
		total := stats["passed"] + stats["failed"]
		successRate := float64(stats["passed"]) / float64(total) * 100
		fmt.Printf("   %s: %d/%d (%.1f%%)\n", category, stats["passed"], total, successRate)
	}
	fmt.Println()

	fmt.Printf("📝 详细结果:\n")
	for _, result := range test.results {
		status := "✅"
		if !result.Success {
			status = "❌"
		}
		fmt.Printf("   %s [%s] %s (耗时: %v)\n", status, result.Category, result.TestName, result.Duration)
		if !result.Success {
			fmt.Printf("      错误: %s\n", result.Error)
		}
	}

	// 验证标准检查
	fmt.Println("\n" + strings.Repeat("-", 80))
	fmt.Printf("🎯 验证标准检查:\n")

	dataLossTests := []string{"全量备份和恢复测试", "增量备份和恢复测试", "时间点恢复验证测试"}
	dataLossPassed := true
	for _, testName := range dataLossTests {
		for _, result := range test.results {
			if result.TestName == testName && !result.Success {
				dataLossPassed = false
				break
			}
		}
	}

	if dataLossPassed {
		fmt.Printf("   ✅ 零数据丢失: 所有备份和恢复测试通过\n")
	} else {
		fmt.Printf("   ❌ 零数据丢失: 存在数据丢失风险\n")
	}

	consistencyTests := []string{"备份数据文件存储", "元数据文件存储"}
	consistencyPassed := true
	for _, testName := range consistencyTests {
		for _, result := range test.results {
			if result.TestName == testName && !result.Success {
				consistencyPassed = false
				break
			}
		}
	}

	if consistencyPassed {
		fmt.Printf("   ✅ 数据一致性: 所有一致性测试通过\n")
	} else {
		fmt.Printf("   ❌ 数据一致性: 存在数据一致性问题\n")
	}

	timePointPassed := false
	for _, result := range test.results {
		if result.TestName == "时间点恢复验证测试" && result.Success {
			timePointPassed = true
			break
		}
	}

	if timePointPassed {
		fmt.Printf("   ✅ 时间点准确性: 时间点恢复测试通过\n")
	} else {
		fmt.Printf("   ❌ 时间点准确性: 时间点恢复测试失败\n")
	}

	metadataPassed := false
	for _, result := range test.results {
		if strings.Contains(result.TestName, "元数据") && result.Success {
			metadataPassed = true
			break
		}
	}

	if metadataPassed {
		fmt.Printf("   ✅ 元数据完整性: 元数据相关测试通过\n")
	} else {
		fmt.Printf("   ❌ 元数据完整性: 元数据相关测试失败\n")
	}

	// 保存详细报告到文件
	reportData := map[string]interface{}{
		"timestamp":      time.Now().Format(time.RFC3339),
		"test_id":        test.testDataID,
		"cloud_storage":  test.config.CloudStorage,
		"total_tests":    totalTests,
		"passed_tests":   passedTests,
		"failed_tests":   failedTests,
		"success_rate":   float64(passedTests) / float64(totalTests) * 100,
		"total_duration": totalDuration.String(),
		"category_stats": categoryStats,
		"verification_standards": map[string]bool{
			"zero_data_loss":      dataLossPassed,
			"data_consistency":    consistencyPassed,
			"time_point_accuracy": timePointPassed,
			"metadata_integrity":  metadataPassed,
		},
		"detailed_results": test.results,
	}

	reportJSON, _ := json.MarshalIndent(reportData, "", "  ")
	reportFile := fmt.Sprintf("tests/reports/cloud_storage_comprehensive_test_%s.json",
		time.Now().Format("20060102_150405"))

	os.MkdirAll("tests/reports", 0755)

	if err := os.WriteFile(reportFile, reportJSON, 0644); err != nil {
		test.logger.Error("保存测试报告失败", zap.Error(err))
	} else {
		fmt.Printf("\n📄 详细测试报告已保存到: %s\n", reportFile)
	}

	// 最终结论
	fmt.Println("\n" + strings.Repeat("=", 80))
	if failedTests == 0 {
		fmt.Println("🎉 所有测试通过！云存储功能正确性验证成功！")
		fmt.Println("✅ 云存储功能满足生产环境使用要求")
		return nil
	} else {
		fmt.Printf("⚠️  存在 %d 个测试失败，请检查上述错误信息\n", failedTests)
		fmt.Println("❌ 云存储功能需要修复后才能用于生产环境")
		return fmt.Errorf("存在 %d 个测试失败", failedTests)
	}
}

// loadTestConfig 加载测试配置文件
func loadTestConfig(configPath string) (*types.Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config types.Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return &config, nil
}

// validateCloudStorageEnvironment 验证云存储测试环境
func validateCloudStorageEnvironment(config *types.Config) error {
	if config.CloudStorage == nil || !config.CloudStorage.Enabled {
		return fmt.Errorf("云存储未启用")
	}

	switch config.CloudStorage.Type {
	case "s3":
		return validateS3Environment(config.CloudStorage)
	case "gcs":
		return validateGCSEnvironment(config.CloudStorage)
	case "azure":
		return validateAzureEnvironment(config.CloudStorage)
	default:
		return fmt.Errorf("不支持的云存储类型: %s", config.CloudStorage.Type)
	}
}

// validateS3Environment 验证S3环境
func validateS3Environment(cloudCfg *types.CloudStorageConfig) error {
	if cloudCfg.Bucket == "" {
		return fmt.Errorf("S3 bucket 未配置")
	}

	if cloudCfg.Region == "" {
		return fmt.Errorf("S3 region 未配置")
	}

	// 检查认证信息（环境变量或配置文件）
	hasEnvAuth := os.Getenv("AWS_ACCESS_KEY_ID") != "" && os.Getenv("AWS_SECRET_ACCESS_KEY") != ""
	hasConfigAuth := cloudCfg.AccessKey != "" && cloudCfg.SecretKey != ""

	fmt.Printf("调试信息: hasEnvAuth=%v, hasConfigAuth=%v\n", hasEnvAuth, hasConfigAuth)
	fmt.Printf("调试信息: AccessKey='%s', SecretKey='%s'\n", cloudCfg.AccessKey, cloudCfg.SecretKey)

	if !hasEnvAuth && !hasConfigAuth {
		return fmt.Errorf("S3 认证信息未配置，请设置环境变量或配置文件中的认证信息")
	}

	return nil
}

// validateGCSEnvironment 验证GCS环境
func validateGCSEnvironment(cloudCfg *types.CloudStorageConfig) error {
	if cloudCfg.Bucket == "" {
		return fmt.Errorf("GCS bucket 未配置")
	}

	if os.Getenv("GOOGLE_APPLICATION_CREDENTIALS") == "" && cloudCfg.CredentialsFile == "" {
		return fmt.Errorf("GCS 认证信息未配置，请设置 GOOGLE_APPLICATION_CREDENTIALS 环境变量或配置文件中的 credentials_file")
	}

	return nil
}

// validateAzureEnvironment 验证Azure环境
func validateAzureEnvironment(cloudCfg *types.CloudStorageConfig) error {
	if cloudCfg.Container == "" {
		return fmt.Errorf("Azure container 未配置")
	}

	if os.Getenv("AZURE_STORAGE_ACCOUNT") == "" && cloudCfg.AccountName == "" {
		return fmt.Errorf("Azure 认证信息未配置，请设置 AZURE_STORAGE_ACCOUNT 环境变量或配置文件中的 account_name")
	}

	if os.Getenv("AZURE_STORAGE_KEY") == "" && cloudCfg.AccountKey == "" {
		return fmt.Errorf("Azure 认证信息未配置，请设置 AZURE_STORAGE_KEY 环境变量或配置文件中的 account_key")
	}

	return nil
}

// showUsage 显示使用说明
func showUsage() {
	fmt.Println("UniBackup 云存储功能全面正确性验证测试")
	fmt.Println("使用方法: cloud-storage-comprehensive-test [配置文件]")
	fmt.Println("")
	fmt.Println("测试内容:")
	fmt.Println("  1. 云存储实现正确性检查")
	fmt.Println("     - 存储完整性验证")
	fmt.Println("     - 备份数据文件存储")
	fmt.Println("     - 元数据文件存储")
	fmt.Println("     - 备份记录存储")
	fmt.Println("     - 任务记录存储")
	fmt.Println("     - 状态文件存储")
	fmt.Println("")
	fmt.Println("  2. 数据完整性测试场景")
	fmt.Println("     - 全量备份和恢复测试")
	fmt.Println("     - 增量备份和恢复测试")
	fmt.Println("     - 时间点恢复验证测试")
	fmt.Println("")
	fmt.Println("  3. 云存储特定验证")
	fmt.Println("     - 多云存储类型测试")
	fmt.Println("     - 网络中断恢复测试")
	fmt.Println("     - 并发操作测试")
	fmt.Println("     - 大数据量测试")
	fmt.Println("")
	fmt.Println("验证标准:")
	fmt.Println("  ✅ 零数据丢失：备份和恢复过程中不能丢失任何数据")
	fmt.Println("  ✅ 数据一致性：恢复后的数据必须与备份时的状态完全一致")
	fmt.Println("  ✅ 时间点准确性：时间点恢复必须精确到指定的时间点")
	fmt.Println("  ✅ 元数据完整性：所有备份元数据和任务记录都正确存储和恢复")
}
