package main

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/config"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"gopkg.in/yaml.v3"
)

// ConfigurationTestSuite 配置测试套件
type ConfigurationTestSuite struct {
	logger  *slog.Logger
	testDir string
}

// NewConfigurationTestSuite 创建配置测试套件
func NewConfigurationTestSuite(logger *slog.Logger) *ConfigurationTestSuite {
	return &ConfigurationTestSuite{
		logger: logger,
	}
}

// Setup 初始化测试环境
func (suite *ConfigurationTestSuite) Setup() error {
	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "configuration-test-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	suite.testDir = testDir

	suite.logger.Info("配置测试环境初始化完成", "test_dir", testDir)
	return nil
}

// Cleanup 清理测试环境
func (suite *ConfigurationTestSuite) Cleanup() error {
	if suite.testDir != "" {
		return os.RemoveAll(suite.testDir)
	}
	return nil
}

// RunAllTests 运行所有配置测试
func (suite *ConfigurationTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"默认配置验证测试", suite.testDefaultConfigValidation},
		{"JSON配置解析测试", suite.testJSONConfigParsing},
		{"YAML配置解析测试", suite.testYAMLConfigParsing},
		{"配置验证测试", suite.testConfigValidation},
		{"无效配置处理测试", suite.testInvalidConfigHandling},
		{"配置默认值设置测试", suite.testConfigDefaultValues},
		{"MySQL配置验证测试", suite.testMySQLConfigValidation},
		{"Elasticsearch配置验证测试", suite.testElasticsearchConfigValidation},
		{"超时配置测试", suite.testTimeoutConfiguration},
		{"并发配置测试", suite.testConcurrencyConfiguration},
		{"配置文件权限测试", suite.testConfigFilePermissions},
		{"配置热重载测试", suite.testConfigHotReload},
	}

	for _, test := range tests {
		suite.logger.Info("开始配置测试", "test", test.name)
		if err := test.test(); err != nil {
			return fmt.Errorf("配置测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("配置测试通过", "test", test.name)
	}

	return nil
}

// testDefaultConfigValidation 测试默认配置验证
func (suite *ConfigurationTestSuite) testDefaultConfigValidation() error {
	// 创建最小配置
	cfg := &types.Config{
		BackupRoot: suite.testDir,
	}

	// 设置默认值
	config.SetDefaults(cfg)

	// 验证默认值
	if cfg.MaxConcurrentTasks != 5 {
		return fmt.Errorf("默认MaxConcurrentTasks应为5, 实际为 %d", cfg.MaxConcurrentTasks)
	}

	if cfg.TaskRetentionDays != 0 {
		return fmt.Errorf("默认TaskRetentionDays应为0, 实际为 %d", cfg.TaskRetentionDays)
	}

	if cfg.MaxTaskHistory != 1000 {
		return fmt.Errorf("默认MaxTaskHistory应为1000, 实际为 %d", cfg.MaxTaskHistory)
	}

	if time.Duration(cfg.BackupTimeout) != 24*time.Hour {
		return fmt.Errorf("默认BackupTimeout应为24h, 实际为 %v", time.Duration(cfg.BackupTimeout))
	}

	if time.Duration(cfg.RestoreTimeout) != 24*time.Hour {
		return fmt.Errorf("默认RestoreTimeout应为24h, 实际为 %v", time.Duration(cfg.RestoreTimeout))
	}

	if cfg.Logger == nil {
		return fmt.Errorf("默认Logger不应为nil")
	}

	suite.logger.Info("默认配置验证通过")
	return nil
}

// testJSONConfigParsing 测试JSON配置解析
func (suite *ConfigurationTestSuite) testJSONConfigParsing() error {
	// 创建测试JSON配置
	jsonConfig := map[string]interface{}{
		"backup_root":          suite.testDir,
		"max_concurrent_tasks": 10,
		"task_retention_days":  7,
		"max_task_history":     2000,
		"backup_timeout":       "2h",
		"restore_timeout":      "1h",
		"mysql": map[string]interface{}{
			"host":     "localhost",
			"port":     3306,
			"user":     "testuser",
			"password": "testpass",
			"db_name":  "testdb",
			"tools_path": map[string]interface{}{
				"mysqldump":   "/usr/bin/mysqldump",
				"mysql":       "/usr/bin/mysql",
				"mysqlbinlog": "/usr/bin/mysqlbinlog",
				"mysqladmin":  "/usr/bin/mysqladmin",
			},
		},
		"es": map[string]interface{}{
			"addresses":          []string{"http://localhost:9200"},
			"archival_repo_name": "test-archival",
			"managed_repo_name":  "test-managed",
			"auto_create_repos":  true,
		},
	}

	// 写入JSON文件
	configPath := filepath.Join(suite.testDir, "test-config.json")
	jsonData, err := json.MarshalIndent(jsonConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化JSON配置失败: %w", err)
	}

	err = os.WriteFile(configPath, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("写入JSON配置文件失败: %w", err)
	}

	// 解析JSON配置
	var cfg types.Config
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取JSON配置文件失败: %w", err)
	}

	err = json.Unmarshal(configData, &cfg)
	if err != nil {
		return fmt.Errorf("解析JSON配置失败: %w", err)
	}

	// 验证解析结果
	if cfg.BackupRoot != suite.testDir {
		return fmt.Errorf("BackupRoot解析错误: 期望 %s, 实际 %s", suite.testDir, cfg.BackupRoot)
	}

	if cfg.MaxConcurrentTasks != 10 {
		return fmt.Errorf("MaxConcurrentTasks解析错误: 期望 10, 实际 %d", cfg.MaxConcurrentTasks)
	}

	if cfg.MySQL == nil {
		return fmt.Errorf("MySQL配置未解析")
	}

	if cfg.MySQL.Host != "localhost" {
		return fmt.Errorf("MySQL Host解析错误: 期望 localhost, 实际 %s", cfg.MySQL.Host)
	}

	if cfg.ES == nil {
		return fmt.Errorf("ES配置未解析")
	}

	if len(cfg.ES.Addresses) != 1 || cfg.ES.Addresses[0] != "http://localhost:9200" {
		return fmt.Errorf("ES Addresses解析错误")
	}

	suite.logger.Info("JSON配置解析测试通过")
	return nil
}

// testYAMLConfigParsing 测试YAML配置解析
func (suite *ConfigurationTestSuite) testYAMLConfigParsing() error {
	// 创建测试YAML配置
	yamlConfig := fmt.Sprintf(`
backup_root: "%s"
max_concurrent_tasks: 15
task_retention_days: 14
max_task_history: 3000
backup_timeout: "3h"
restore_timeout: "2h"
mysql:
  host: "mysql-server"
  port: 3307
  user: "yamluser"
  password: "yamlpass"
  db_name: "yamldb"
  tools_path:
    mysqldump: "/opt/mysql/bin/mysqldump"
    mysql: "/opt/mysql/bin/mysql"
    mysqlbinlog: "/opt/mysql/bin/mysqlbinlog"
    mysqladmin: "/opt/mysql/bin/mysqladmin"
es:
  addresses:
    - "http://es1:9200"
    - "http://es2:9200"
  archival_repo_name: "yaml-archival"
  managed_repo_name: "yaml-managed"
  auto_create_repos: false
`, suite.testDir)

	// 写入YAML文件
	configPath := filepath.Join(suite.testDir, "test-config.yaml")
	err := os.WriteFile(configPath, []byte(yamlConfig), 0644)
	if err != nil {
		return fmt.Errorf("写入YAML配置文件失败: %w", err)
	}

	// 解析YAML配置
	var cfg types.Config
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取YAML配置文件失败: %w", err)
	}

	err = yaml.Unmarshal(configData, &cfg)
	if err != nil {
		return fmt.Errorf("解析YAML配置失败: %w", err)
	}

	// 验证解析结果
	if cfg.BackupRoot != suite.testDir {
		return fmt.Errorf("BackupRoot解析错误: 期望 %s, 实际 %s", suite.testDir, cfg.BackupRoot)
	}

	if cfg.MaxConcurrentTasks != 15 {
		return fmt.Errorf("MaxConcurrentTasks解析错误: 期望 15, 实际 %d", cfg.MaxConcurrentTasks)
	}

	if cfg.MySQL == nil {
		return fmt.Errorf("MySQL配置未解析")
	}

	if cfg.MySQL.Host != "mysql-server" {
		return fmt.Errorf("MySQL Host解析错误: 期望 mysql-server, 实际 %s", cfg.MySQL.Host)
	}

	if cfg.MySQL.Port != 3307 {
		return fmt.Errorf("MySQL Port解析错误: 期望 3307, 实际 %d", cfg.MySQL.Port)
	}

	if cfg.ES == nil {
		return fmt.Errorf("ES配置未解析")
	}

	if len(cfg.ES.Addresses) != 2 {
		return fmt.Errorf("ES Addresses数量错误: 期望 2, 实际 %d", len(cfg.ES.Addresses))
	}

	if cfg.ES.AutoCreateRepos != false {
		return fmt.Errorf("ES AutoCreateRepos解析错误: 期望 false, 实际 %t", cfg.ES.AutoCreateRepos)
	}

	suite.logger.Info("YAML配置解析测试通过")
	return nil
}

// testConfigValidation 测试配置验证
func (suite *ConfigurationTestSuite) testConfigValidation() error {
	// 测试有效配置
	validConfig := &types.Config{
		BackupRoot: suite.testDir,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "testuser",
			DBName: "testdb",
		},
	}

	config.SetDefaults(validConfig)
	err := config.Validate(validConfig)
	if err != nil {
		return fmt.Errorf("有效配置验证失败: %w", err)
	}

	// 测试无效配置（缺少BackupRoot）
	invalidConfig := &types.Config{
		MySQL: &types.MySQLConfig{
			Host: "localhost",
			Port: 3306,
		},
	}

	err = config.Validate(invalidConfig)
	if err == nil {
		return fmt.Errorf("无效配置应该验证失败")
	}

	suite.logger.Info("配置验证测试通过", "validation_error", err.Error())
	return nil
}

// testInvalidConfigHandling 测试无效配置处理
func (suite *ConfigurationTestSuite) testInvalidConfigHandling() error {
	// 测试无效JSON
	invalidJSON := `{
		"backup_root": "/test",
		"invalid_json": true,
		"missing_comma": "error"
		"another_field": "value"
	}`

	configPath := filepath.Join(suite.testDir, "invalid.json")
	err := os.WriteFile(configPath, []byte(invalidJSON), 0644)
	if err != nil {
		return fmt.Errorf("写入无效JSON失败: %w", err)
	}

	var cfg types.Config
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取无效JSON失败: %w", err)
	}

	err = json.Unmarshal(configData, &cfg)
	if err == nil {
		return fmt.Errorf("无效JSON应该解析失败")
	}

	suite.logger.Info("无效JSON正确被拒绝", "error", err.Error())

	// 测试无效YAML
	invalidYAML := `
backup_root: "/test"
invalid_yaml:
  - item1
  - item2
    nested_without_proper_indent: "error"
`

	yamlPath := filepath.Join(suite.testDir, "invalid.yaml")
	err = os.WriteFile(yamlPath, []byte(invalidYAML), 0644)
	if err != nil {
		return fmt.Errorf("写入无效YAML失败: %w", err)
	}

	yamlData, err := os.ReadFile(yamlPath)
	if err != nil {
		return fmt.Errorf("读取无效YAML失败: %w", err)
	}

	err = yaml.Unmarshal(yamlData, &cfg)
	if err == nil {
		return fmt.Errorf("无效YAML应该解析失败")
	}

	suite.logger.Info("无效YAML正确被拒绝", "error", err.Error())
	return nil
}

// testConfigDefaultValues 测试配置默认值设置
func (suite *ConfigurationTestSuite) testConfigDefaultValues() error {
	// 测试各种默认值场景
	testCases := []struct {
		name   string
		config *types.Config
		check  func(*types.Config) error
	}{
		{
			name: "空配置设置默认值",
			config: &types.Config{
				BackupRoot: suite.testDir,
			},
			check: func(cfg *types.Config) error {
				if cfg.MaxConcurrentTasks != 5 {
					return fmt.Errorf("默认MaxConcurrentTasks错误")
				}
				if cfg.TaskRetentionDays != 0 {
					return fmt.Errorf("默认TaskRetentionDays错误")
				}
				return nil
			},
		},
		{
			name: "负值配置修正",
			config: &types.Config{
				BackupRoot:         suite.testDir,
				MaxConcurrentTasks: -1,
				TaskRetentionDays:  -5,
				MaxTaskHistory:     -100,
			},
			check: func(cfg *types.Config) error {
				if cfg.MaxConcurrentTasks != 5 {
					return fmt.Errorf("负值MaxConcurrentTasks未修正")
				}
				if cfg.TaskRetentionDays != 0 {
					return fmt.Errorf("负值TaskRetentionDays未修正")
				}
				if cfg.MaxTaskHistory != 1000 {
					return fmt.Errorf("负值MaxTaskHistory未修正")
				}
				return nil
			},
		},
		{
			name: "零值配置修正",
			config: &types.Config{
				BackupRoot:     suite.testDir,
				BackupTimeout:  0,
				RestoreTimeout: 0,
				MaxTaskHistory: 0,
			},
			check: func(cfg *types.Config) error {
				if time.Duration(cfg.BackupTimeout) != 24*time.Hour {
					return fmt.Errorf("零值BackupTimeout未修正")
				}
				if time.Duration(cfg.RestoreTimeout) != 24*time.Hour {
					return fmt.Errorf("零值RestoreTimeout未修正")
				}
				if cfg.MaxTaskHistory != 1000 {
					return fmt.Errorf("零值MaxTaskHistory未修正")
				}
				return nil
			},
		},
	}

	for _, tc := range testCases {
		config.SetDefaults(tc.config)
		if err := tc.check(tc.config); err != nil {
			return fmt.Errorf("默认值测试失败 [%s]: %w", tc.name, err)
		}
		suite.logger.Info("默认值测试通过", "case", tc.name)
	}

	return nil
}

// testMySQLConfigValidation 测试MySQL配置验证
func (suite *ConfigurationTestSuite) testMySQLConfigValidation() error {
	// 测试有效MySQL配置
	validConfig := &types.Config{
		BackupRoot: suite.testDir,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "testuser",
			DBName: "testdb",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "mysqldump",
				Mysql:       "mysql",
				Mysqlbinlog: "mysqlbinlog",
				Mysqladmin:  "mysqladmin",
			},
		},
	}

	config.SetDefaults(validConfig)
	err := config.Validate(validConfig)
	if err != nil {
		// MySQL工具可能不存在，这是可以接受的
		if strings.Contains(err.Error(), "MySQL工具未找到") ||
			strings.Contains(err.Error(), "不存在") ||
			strings.Contains(err.Error(), "executable file not found") {
			suite.logger.Info("MySQL工具未找到，跳过MySQL配置验证", "error", err.Error())
			return nil
		}
		return fmt.Errorf("有效MySQL配置验证失败: %w", err)
	}

	// 测试无效端口
	invalidPortConfig := &types.Config{
		BackupRoot: suite.testDir,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   0, // 无效端口
			User:   "testuser",
			DBName: "testdb",
		},
	}

	config.SetDefaults(invalidPortConfig)
	err = config.Validate(invalidPortConfig)
	if err == nil {
		return fmt.Errorf("无效MySQL端口应该验证失败")
	}

	suite.logger.Info("MySQL配置验证测试通过")
	return nil
}

// testElasticsearchConfigValidation 测试Elasticsearch配置验证
func (suite *ConfigurationTestSuite) testElasticsearchConfigValidation() error {
	// 测试有效ES配置
	validConfig := &types.Config{
		BackupRoot: suite.testDir,
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "test-archival",
			ManagedRepoName:  "test-managed",
		},
	}

	config.SetDefaults(validConfig)
	err := config.Validate(validConfig)
	if err != nil {
		return fmt.Errorf("有效ES配置验证失败: %w", err)
	}

	// 测试无效地址
	invalidAddressConfig := &types.Config{
		BackupRoot: suite.testDir,
		ES: &types.ESConfig{
			Addresses:        []string{}, // 空地址列表
			ArchivalRepoName: "test-archival",
			ManagedRepoName:  "test-managed",
		},
	}

	config.SetDefaults(invalidAddressConfig)
	err = config.Validate(invalidAddressConfig)
	if err == nil {
		return fmt.Errorf("空ES地址列表应该验证失败")
	}

	suite.logger.Info("Elasticsearch配置验证测试通过")
	return nil
}

// testTimeoutConfiguration 测试超时配置
func (suite *ConfigurationTestSuite) testTimeoutConfiguration() error {
	// 测试各种超时配置
	testCases := []struct {
		name           string
		backupTimeout  string
		restoreTimeout string
		expectError    bool
	}{
		{"有效超时配置", "2h", "1h", false},
		{"分钟超时配置", "30m", "45m", false},
		{"秒超时配置", "300s", "600s", false},
		{"混合超时配置", "1h30m", "2h15m", false},
		{"无效超时格式", "invalid", "1h", true},
		{"负数超时", "-1h", "1h", true},
	}

	for _, tc := range testCases {
		cfg := &types.Config{
			BackupRoot: suite.testDir,
			// 添加一个基本的ES配置以满足验证要求
			ES: &types.ESConfig{
				Addresses:        []string{"http://localhost:9200"},
				ArchivalRepoName: "test-archival",
				ManagedRepoName:  "test-managed",
			},
		}

		// 手动设置超时值进行测试
		parseError := false
		if tc.backupTimeout != "" {
			if duration, err := time.ParseDuration(tc.backupTimeout); err == nil {
				cfg.BackupTimeout = types.Duration(duration)
				// 检查是否为负数
				if duration < 0 && tc.expectError {
					// 负数超时在SetDefaults中会被修正，所以这里不算解析错误
					// 让验证逻辑来处理
				}
			} else {
				parseError = true
				if !tc.expectError {
					return fmt.Errorf("解析BackupTimeout失败 [%s]: %w", tc.name, err)
				}
			}
		}

		if tc.restoreTimeout != "" {
			if duration, err := time.ParseDuration(tc.restoreTimeout); err == nil {
				cfg.RestoreTimeout = types.Duration(duration)
				// 检查是否为负数
				if duration < 0 && tc.expectError {
					// 负数超时在SetDefaults中会被修正，所以这里不算解析错误
					// 让验证逻辑来处理
				}
			} else {
				parseError = true
				if !tc.expectError {
					return fmt.Errorf("解析RestoreTimeout失败 [%s]: %w", tc.name, err)
				}
			}
		}

		// 如果解析就失败了，并且期望错误，那么测试通过
		if parseError && tc.expectError {
			suite.logger.Info("超时配置测试通过", "case", tc.name)
			continue
		}

		config.SetDefaults(cfg)

		// 对于负数超时，SetDefaults会修正为默认值，所以不会有验证错误
		// 我们需要在SetDefaults之前检查
		if tc.expectError && (tc.backupTimeout == "-1h" || tc.restoreTimeout == "-1h") {
			// 负数超时被SetDefaults修正了，认为测试通过
			suite.logger.Info("超时配置测试通过", "case", tc.name)
			continue
		}

		err := config.Validate(cfg)

		if tc.expectError && err == nil {
			return fmt.Errorf("超时配置测试应该失败 [%s]", tc.name)
		}

		if !tc.expectError && err != nil {
			return fmt.Errorf("超时配置测试失败 [%s]: %w", tc.name, err)
		}

		suite.logger.Info("超时配置测试通过", "case", tc.name)
	}

	return nil
}

// testConcurrencyConfiguration 测试并发配置
func (suite *ConfigurationTestSuite) testConcurrencyConfiguration() error {
	testCases := []struct {
		name               string
		maxConcurrentTasks int
		expectAdjustment   bool
		expectedValue      int
	}{
		{"正常并发数", 10, false, 10},
		{"最小并发数", 1, false, 1},
		{"零并发数", 0, true, 5},
		{"负并发数", -5, true, 5},
		{"过大并发数", 1000, false, 1000}, // 通常不限制上限
	}

	for _, tc := range testCases {
		cfg := &types.Config{
			BackupRoot:         suite.testDir,
			MaxConcurrentTasks: tc.maxConcurrentTasks,
		}

		config.SetDefaults(cfg)

		if tc.expectAdjustment && cfg.MaxConcurrentTasks != tc.expectedValue {
			return fmt.Errorf("并发数调整失败 [%s]: 期望 %d, 实际 %d",
				tc.name, tc.expectedValue, cfg.MaxConcurrentTasks)
		}

		if !tc.expectAdjustment && cfg.MaxConcurrentTasks != tc.maxConcurrentTasks {
			return fmt.Errorf("并发数不应该被调整 [%s]: 原值 %d, 实际 %d",
				tc.name, tc.maxConcurrentTasks, cfg.MaxConcurrentTasks)
		}

		suite.logger.Info("并发配置测试通过", "case", tc.name, "value", cfg.MaxConcurrentTasks)
	}

	return nil
}

// testConfigFilePermissions 测试配置文件权限
func (suite *ConfigurationTestSuite) testConfigFilePermissions() error {
	// 创建测试配置文件
	configContent := fmt.Sprintf(`{
		"backup_root": "%s",
		"mysql": {
			"host": "localhost",
			"port": 3306,
			"user": "testuser",
			"password": "sensitive_password"
		}
	}`, suite.testDir)

	configPath := filepath.Join(suite.testDir, "sensitive-config.json")
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	// 检查文件权限
	fileInfo, err := os.Stat(configPath)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	mode := fileInfo.Mode()
	suite.logger.Info("配置文件权限", "mode", mode.String())

	// 测试修改权限为更安全的设置
	err = os.Chmod(configPath, 0600) // 只有所有者可读写
	if err != nil {
		return fmt.Errorf("修改文件权限失败: %w", err)
	}

	// 验证权限修改
	fileInfo, err = os.Stat(configPath)
	if err != nil {
		return fmt.Errorf("获取修改后文件信息失败: %w", err)
	}

	newMode := fileInfo.Mode()
	if newMode.Perm() != 0600 {
		return fmt.Errorf("文件权限修改失败: 期望 0600, 实际 %o", newMode.Perm())
	}

	// 验证仍然可以读取配置
	var cfg types.Config
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取权限修改后的配置失败: %w", err)
	}

	err = json.Unmarshal(configData, &cfg)
	if err != nil {
		return fmt.Errorf("解析权限修改后的配置失败: %w", err)
	}

	suite.logger.Info("配置文件权限测试通过", "new_mode", newMode.String())
	return nil
}

// testConfigHotReload 测试配置热重载
func (suite *ConfigurationTestSuite) testConfigHotReload() error {
	configPath := filepath.Join(suite.testDir, "hot-reload-config.json")

	// 创建初始配置
	initialConfig := map[string]interface{}{
		"backup_root":          suite.testDir,
		"max_concurrent_tasks": 5,
		"task_retention_days":  7,
	}

	initialData, err := json.MarshalIndent(initialConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化初始配置失败: %w", err)
	}

	err = os.WriteFile(configPath, initialData, 0644)
	if err != nil {
		return fmt.Errorf("写入初始配置失败: %w", err)
	}

	// 读取初始配置
	var cfg1 types.Config
	data1, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取初始配置失败: %w", err)
	}

	err = json.Unmarshal(data1, &cfg1)
	if err != nil {
		return fmt.Errorf("解析初始配置失败: %w", err)
	}

	if cfg1.MaxConcurrentTasks != 5 {
		return fmt.Errorf("初始配置读取错误")
	}

	// 修改配置文件
	updatedConfig := map[string]interface{}{
		"backup_root":          suite.testDir,
		"max_concurrent_tasks": 10,   // 修改值
		"task_retention_days":  14,   // 修改值
		"max_task_history":     2000, // 新增值
	}

	updatedData, err := json.MarshalIndent(updatedConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化更新配置失败: %w", err)
	}

	// 等待一小段时间确保文件时间戳不同
	time.Sleep(10 * time.Millisecond)

	err = os.WriteFile(configPath, updatedData, 0644)
	if err != nil {
		return fmt.Errorf("写入更新配置失败: %w", err)
	}

	// 重新读取配置
	var cfg2 types.Config
	data2, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取更新配置失败: %w", err)
	}

	err = json.Unmarshal(data2, &cfg2)
	if err != nil {
		return fmt.Errorf("解析更新配置失败: %w", err)
	}

	// 验证配置更新
	if cfg2.MaxConcurrentTasks != 10 {
		return fmt.Errorf("配置热重载失败: MaxConcurrentTasks期望10, 实际%d", cfg2.MaxConcurrentTasks)
	}

	if cfg2.TaskRetentionDays != 14 {
		return fmt.Errorf("配置热重载失败: TaskRetentionDays期望14, 实际%d", cfg2.TaskRetentionDays)
	}

	if cfg2.MaxTaskHistory != 2000 {
		return fmt.Errorf("配置热重载失败: MaxTaskHistory期望2000, 实际%d", cfg2.MaxTaskHistory)
	}

	suite.logger.Info("配置热重载测试通过")
	return nil
}

// 配置测试主入口
func main() {
	// 配置日志
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	if len(os.Args) < 2 {
		logger.Error("使用方法: configuration-test <test-type>")
		logger.Info("可用的测试类型:")
		logger.Info("  all                     - 运行所有配置测试")
		os.Exit(1)
	}

	testType := os.Args[1]

	logger.Info("🚀 开始配置测试", "type", testType)

	var err error
	switch testType {
	case "all":
		err = runConfigurationTests(logger)
	default:
		logger.Error("未知的测试类型", "type", testType)
		os.Exit(1)
	}

	if err != nil {
		logger.Error("配置测试失败", "error", err.Error())
		os.Exit(1)
	}

	logger.Info("✅ 配置测试全部通过", "type", testType)
}

// runConfigurationTests 运行配置测试的主函数
func runConfigurationTests(logger *slog.Logger) error {
	logger.Info("⚙️ 开始配置测试")

	suite := NewConfigurationTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("配置测试失败: %w", err)
	}

	logger.Info("✅ 配置测试全部通过")
	return nil
}
