package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// CloudStorageConfigTestSuite 云存储配置测试套件
type CloudStorageConfigTestSuite struct {
	logger *zap.Logger
}

// NewCloudStorageConfigTestSuite 创建云存储配置测试套件
func NewCloudStorageConfigTestSuite(logger *zap.Logger) *CloudStorageConfigTestSuite {
	return &CloudStorageConfigTestSuite{
		logger: logger,
	}
}

// RunAllTests 运行所有云存储配置测试
func (suite *CloudStorageConfigTestSuite) RunAllTests() error {
	suite.logger.Info("🚀 开始云存储配置测试")

	tests := []struct {
		name string
		test func() error
	}{
		{"S3配置验证测试", suite.testS3ConfigValidation},
		{"GCS配置验证测试", suite.testGCSConfigValidation},
		{"Azure配置验证测试", suite.testAzureConfigValidation},
		{"无效配置错误处理测试", suite.testInvalidConfigHandling},
		{"认证配置测试", suite.testAuthenticationConfig},
		{"端点配置测试", suite.testEndpointConfig},
		{"配置字段验证测试", suite.testConfigFieldValidation},
	}

	for _, test := range tests {
		suite.logger.Info("执行配置测试", zap.String("name", test.name))
		if err := test.test(); err != nil {
			suite.logger.Error("配置测试失败", zap.String("name", test.name), zap.Error(err))
			return fmt.Errorf("配置测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("配置测试通过", zap.String("name", test.name))
	}

	suite.logger.Info("✅ 所有云存储配置测试通过")
	return nil
}

// testS3ConfigValidation 测试S3配置验证
func (suite *CloudStorageConfigTestSuite) testS3ConfigValidation() error {
	// 有效的S3配置
	validS3Config := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-backup-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}

	backend, err := storage.NewBackend(validS3Config, suite.logger)
	if err != nil {
		return fmt.Errorf("有效S3配置创建Backend失败: %w", err)
	}

	// 测试基本功能
	ctx := context.Background()
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("S3配置健康检查失败: %w", err)
	}

	// 测试基本操作
	testKey := fmt.Sprintf("s3-config-test-%d", time.Now().Unix())
	testData := "S3 configuration test data"

	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("S3配置Put操作失败: %w", err)
	}

	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("S3配置Exists操作失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("S3配置文件应该存在")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testGCSConfigValidation 测试GCS配置验证
func (suite *CloudStorageConfigTestSuite) testGCSConfigValidation() error {
	// 使用MinIO模拟GCS配置（实际上还是S3兼容）
	gcsConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3", // 使用S3兼容模式模拟
			Bucket:    "test-gcs-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}

	backend, err := storage.NewBackend(gcsConfig, suite.logger)
	if err != nil {
		return fmt.Errorf("GCS配置创建Backend失败: %w", err)
	}

	// 测试基本功能
	ctx := context.Background()
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("GCS配置健康检查失败: %w", err)
	}

	// 测试基本操作
	testKey := fmt.Sprintf("gcs-config-test-%d", time.Now().Unix())
	testData := "GCS configuration test data"

	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("GCS配置Put操作失败: %w", err)
	}

	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("GCS配置Exists操作失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("GCS配置文件应该存在")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testAzureConfigValidation 测试Azure配置验证
func (suite *CloudStorageConfigTestSuite) testAzureConfigValidation() error {
	// 使用MinIO模拟Azure配置（实际上还是S3兼容）
	azureConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3", // 使用S3兼容模式模拟
			Bucket:    "test-azure-container",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}

	backend, err := storage.NewBackend(azureConfig, suite.logger)
	if err != nil {
		return fmt.Errorf("Azure配置创建Backend失败: %w", err)
	}

	// 测试基本功能
	ctx := context.Background()
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("Azure配置健康检查失败: %w", err)
	}

	// 测试基本操作
	testKey := fmt.Sprintf("azure-config-test-%d", time.Now().Unix())
	testData := "Azure configuration test data"

	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("Azure配置Put操作失败: %w", err)
	}

	exists, err := backend.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("Azure配置Exists操作失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("Azure配置文件应该存在")
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	return nil
}

// testInvalidConfigHandling 测试无效配置错误处理
func (suite *CloudStorageConfigTestSuite) testInvalidConfigHandling() error {
	invalidConfigs := []struct {
		name   string
		config *types.Config
	}{
		{
			name: "缺少Bucket",
			config: &types.Config{
				BackupRoot: "/tests/data/backup",
				CloudStorage: &types.CloudStorageConfig{
					Enabled:   true,
					Type:      "s3",
					Region:    "us-east-1",
					AccessKey: "test-key",
					SecretKey: "test-secret",
				},
			},
		},
		{
			name: "无效的Type",
			config: &types.Config{
				BackupRoot: "/tests/data/backup",
				CloudStorage: &types.CloudStorageConfig{
					Enabled:   true,
					Type:      "invalid-type",
					Bucket:    "test-bucket",
					Region:    "us-east-1",
					AccessKey: "test-key",
					SecretKey: "test-secret",
				},
			},
		},
		{
			name: "缺少AccessKey",
			config: &types.Config{
				BackupRoot: "/tests/data/backup",
				CloudStorage: &types.CloudStorageConfig{
					Enabled:   true,
					Type:      "s3",
					Bucket:    "test-bucket",
					Region:    "us-east-1",
					SecretKey: "test-secret",
				},
			},
		},
		{
			name: "缺少SecretKey",
			config: &types.Config{
				BackupRoot: "/tests/data/backup",
				CloudStorage: &types.CloudStorageConfig{
					Enabled:   true,
					Type:      "s3",
					Bucket:    "test-bucket",
					Region:    "us-east-1",
					AccessKey: "test-key",
				},
			},
		},
	}

	for _, invalidConfig := range invalidConfigs {
		_, err := storage.NewBackend(invalidConfig.config, suite.logger)
		if err == nil {
			return fmt.Errorf("无效配置 [%s] 应该返回错误", invalidConfig.name)
		}
		suite.logger.Info("无效配置正确返回错误", zap.String("config", invalidConfig.name), zap.Error(err))
	}

	return nil
}

// testAuthenticationConfig 测试认证配置
func (suite *CloudStorageConfigTestSuite) testAuthenticationConfig() error {
	// 测试正确的认证配置
	validAuthConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-backup-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}

	backend, err := storage.NewBackend(validAuthConfig, suite.logger)
	if err != nil {
		return fmt.Errorf("有效认证配置创建Backend失败: %w", err)
	}

	ctx := context.Background()
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("有效认证配置健康检查失败: %w", err)
	}

	// 测试错误的认证配置
	invalidAuthConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-backup-bucket",
			Region:    "us-east-1",
			AccessKey: "invalid-key",
			SecretKey: "invalid-secret",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}

	invalidBackend, err := storage.NewBackend(invalidAuthConfig, suite.logger)
	if err != nil {
		// 配置创建可能成功，但健康检查应该失败
		suite.logger.Info("无效认证配置创建Backend失败（预期）", zap.Error(err))
		return nil
	}

	// 健康检查应该失败
	if err := invalidBackend.HealthCheck(ctx); err == nil {
		return fmt.Errorf("无效认证配置的健康检查应该失败")
	}

	return nil
}

// testEndpointConfig 测试端点配置
func (suite *CloudStorageConfigTestSuite) testEndpointConfig() error {
	// 测试自定义端点配置
	customEndpointConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-backup-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},
	}

	backend, err := storage.NewBackend(customEndpointConfig, suite.logger)
	if err != nil {
		return fmt.Errorf("自定义端点配置创建Backend失败: %w", err)
	}

	ctx := context.Background()
	if err := backend.HealthCheck(ctx); err != nil {
		return fmt.Errorf("自定义端点配置健康检查失败: %w", err)
	}

	// 测试无效端点配置
	invalidEndpointConfig := &types.Config{
		BackupRoot: "/tests/data/backup",
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-backup-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://invalid-endpoint:9000",
		},
	}

	invalidBackend, err := storage.NewBackend(invalidEndpointConfig, suite.logger)
	if err != nil {
		// 配置创建可能失败
		suite.logger.Info("无效端点配置创建Backend失败（预期）", zap.Error(err))
		return nil
	}

	// 健康检查应该失败
	if err := invalidBackend.HealthCheck(ctx); err == nil {
		return fmt.Errorf("无效端点配置的健康检查应该失败")
	}

	return nil
}

// testConfigFieldValidation 测试配置字段验证
func (suite *CloudStorageConfigTestSuite) testConfigFieldValidation() error {
	// 测试各种边界情况
	testCases := []struct {
		name   string
		config *types.CloudStorageConfig
		valid  bool
	}{
		{
			name: "空Bucket名称",
			config: &types.CloudStorageConfig{
				Enabled:   true,
				Type:      "s3",
				Bucket:    "",
				Region:    "us-east-1",
				AccessKey: "test-key",
				SecretKey: "test-secret",
			},
			valid: false,
		},
		{
			name: "空Region",
			config: &types.CloudStorageConfig{
				Enabled:   true,
				Type:      "s3",
				Bucket:    "test-bucket",
				Region:    "",
				AccessKey: "test-key",
				SecretKey: "test-secret",
			},
			valid: false,
		},
		{
			name: "禁用状态",
			config: &types.CloudStorageConfig{
				Enabled:   false,
				Type:      "s3",
				Bucket:    "test-bucket",
				Region:    "us-east-1",
				AccessKey: "test-key",
				SecretKey: "test-secret",
			},
			valid: true, // 禁用状态下应该使用本地存储
		},
	}

	for _, testCase := range testCases {
		config := &types.Config{
			BackupRoot:   "/tests/data/backup",
			CloudStorage: testCase.config,
		}

		_, err := storage.NewBackend(config, suite.logger)

		if testCase.valid && err != nil {
			return fmt.Errorf("有效配置 [%s] 不应该返回错误: %w", testCase.name, err)
		}

		if !testCase.valid && err == nil {
			return fmt.Errorf("无效配置 [%s] 应该返回错误", testCase.name)
		}

		suite.logger.Info("配置字段验证", zap.String("case", testCase.name), zap.Bool("valid", testCase.valid), zap.Error(err))
	}

	return nil
}

// runCloudStorageConfigTests 运行云存储配置测试的入口函数
func runCloudStorageConfigTests(logger *zap.Logger) error {
	// 创建测试套件
	suite := NewCloudStorageConfigTestSuite(logger)

	// 运行所有测试
	return suite.RunAllTests()
}
