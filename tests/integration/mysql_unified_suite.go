package main

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	if len(os.Args) < 2 {
		showUsage()
		os.Exit(1)
	}

	testType := os.Args[1]
	logger, _ := zap.NewDevelopment()

	fmt.Printf("🧪 MySQL统一测试套件: %s\n", testType)
	fmt.Println(strings.Repeat("=", 60))

	// 创建配置 - 支持binlog直接访问
	cfg := &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             logger,
		MaxConcurrentTasks: 3,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		MySQL: &types.MySQLConfig{
			Host:           "unibackup-test-mysql",
			Port:           3306,
			User:           "backup_user",
			Password:       "backup_pass",
			DBName:         "testdb",
			BinlogBasePath: "/tests/mysql-binlogs", // 支持直接文件访问
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		fmt.Printf("❌ 创建备份管理器失败: %v\n", err)
		os.Exit(1)
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 根据测试类型执行测试
	switch testType {
	case "full-backup":
		testFullBackup(ctx, manager)
	case "incremental-backup":
		testIncrementalBackup(ctx, manager)
	case "backup-list":
		testBackupList(ctx, manager)
	case "backup-delete":
		testBackupDelete(ctx, manager)
	case "task-management":
		testTaskManagement(ctx, manager)
	case "restore":
		testRestore(ctx, manager)
	case "comprehensive":
		runComprehensiveTest(ctx, manager)
	case "all":
		runAllTests(ctx, manager)
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		showUsage()
		os.Exit(1)
	}

	fmt.Println("✅ MySQL测试完成")
}

// 数据库连接和数据验证辅助函数
func getTestDBConnection() (*sql.DB, error) {
	dsn := "backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/testdb?parseTime=true"
	return sql.Open("mysql", dsn)
}

// 插入测试数据
func insertTestData(testName string) error {
	db, err := getTestDBConnection()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 确保测试表存在
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS test_data (
		id INT PRIMARY KEY AUTO_INCREMENT,
		test_name VARCHAR(100),
		test_value VARCHAR(255),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`)
	if err != nil {
		return fmt.Errorf("创建测试表失败: %v", err)
	}

	// 插入测试数据
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	testValue := fmt.Sprintf("%s-data-%s", testName, timestamp)

	_, err = db.Exec("INSERT INTO test_data (test_name, test_value) VALUES (?, ?)", testName, testValue)
	if err != nil {
		return fmt.Errorf("插入测试数据失败: %v", err)
	}

	fmt.Printf("    ✅ 插入测试数据: %s = %s\n", testName, testValue)
	return nil
}

// 验证数据是否存在
func verifyTestData(testName string, targetDB string) error {
	// 构建连接字符串到目标数据库
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", targetDB)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接目标数据库失败: %v", err)
	}
	defer db.Close()

	// 查询测试数据
	rows, err := db.Query("SELECT test_value FROM test_data WHERE test_name = ? ORDER BY created_at DESC LIMIT 1", testName)
	if err != nil {
		return fmt.Errorf("查询测试数据失败: %v", err)
	}
	defer rows.Close()

	if !rows.Next() {
		return fmt.Errorf("未找到测试数据: %s", testName)
	}

	var testValue string
	err = rows.Scan(&testValue)
	if err != nil {
		return fmt.Errorf("读取测试数据失败: %v", err)
	}

	fmt.Printf("    ✅ 验证数据恢复成功: %s = %s\n", testName, testValue)
	return nil
}

// 获取数据行数进行对比
func getTestDataCount(dbName string) (int, error) {
	dsn := fmt.Sprintf("backup_user:backup_pass@tcp(unibackup-test-mysql:3306)/%s?parseTime=true", dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return 0, fmt.Errorf("连接数据库失败: %v", err)
	}
	defer db.Close()

	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM test_data").Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询数据行数失败: %v", err)
	}

	return count, nil
}

// 全量备份测试
func testFullBackup(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📦 测试全量备份")

	// 1. 插入测试数据
	fmt.Println("  📝 插入测试数据...")
	err := insertTestData("full-backup-test")
	if err != nil {
		fmt.Printf("❌ 插入测试数据失败: %v\n", err)
		return
	}

	// 2. 执行备份
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "全量备份测试")
	if err != nil {
		fmt.Printf("❌ 全量备份失败: %v\n", err)
		return
	}

	task := waitForTask(manager, taskID, "全量备份")
	if task != nil && task.Status == types.TaskStatusCompleted {
		backupID := task.Metadata["backup_record_id"].(string)

		// 增强验证：检查本地文件系统中的备份文件
		if err := verifyLocalBackupFile(backupID, "mysql"); err != nil {
			fmt.Printf("❌ 本地备份文件验证失败: %v\n", err)
			return
		}

		fmt.Printf("✅ 全量备份成功，ID: %s\n", backupID)
	}
}

// 增量备份测试
func testIncrementalBackup(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📈 测试增量备份")

	// 1. 插入初始数据并创建增量链初始备份
	fmt.Println("  📝 插入初始数据...")
	err := insertTestData("incremental-initial")
	if err != nil {
		fmt.Printf("❌ 插入初始数据失败: %v\n", err)
		return
	}

	fmt.Println("  📦 创建增量链初始备份...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainInitial, "增量链初始备份")
	if err != nil {
		fmt.Printf("❌ 增量链初始备份失败: %v\n", err)
		return
	}

	initialTask := waitForTask(manager, taskID, "增量链初始备份")
	if initialTask == nil || initialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量链初始备份未成功")
		return
	}

	initialBackupID := initialTask.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 增量链初始备份成功，ID: %s\n", initialBackupID)

	// 2. 等待并插入增量数据
	time.Sleep(2 * time.Second)

	fmt.Println("  📝 插入增量数据...")
	err = insertTestData("incremental-delta")
	if err != nil {
		fmt.Printf("❌ 插入增量数据失败: %v\n", err)
		return
	}

	fmt.Println("  📦 创建增量备份...")
	taskID, err = manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainIncremental, "增量备份")
	if err != nil {
		fmt.Printf("❌ 增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForTask(manager, taskID, "增量备份")
	if incrementalTask != nil && incrementalTask.Status == types.TaskStatusCompleted {
		incrementalBackupID := incrementalTask.Metadata["backup_record_id"].(string)
		fmt.Printf("  ✅ 增量备份成功，ID: %s\n", incrementalBackupID)
	}
}

// 备份列表测试
func testBackupList(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📋 测试备份列表")

	// 1. 列出归档备份
	fmt.Println("  📋 归档备份列表:")
	archivalBackups, err := manager.ListArchivalBackups(ctx, types.MySQL)
	if err != nil {
		fmt.Printf("❌ 列出归档备份失败: %v\n", err)
	} else {
		fmt.Printf("  找到 %d 个归档备份\n", len(archivalBackups))
		for i, backup := range archivalBackups {
			if i < 5 { // 只显示前5个
				fmt.Printf("    %d. ID: %s, 时间: %s, 描述: %s\n",
					i+1, backup.Record.ID,
					backup.Record.Timestamp.Format("2006-01-02 15:04:05"),
					backup.Record.Description)
			}
		}
		if len(archivalBackups) > 5 {
			fmt.Printf("    ... 还有 %d 个备份\n", len(archivalBackups)-5)
		}
	}

	// 2. 列出增量链
	fmt.Println("  🔗 增量链列表:")
	incrementalChains, err := manager.ListIncrementalChains(ctx, types.MySQL)
	if err != nil {
		fmt.Printf("❌ 列出增量链失败: %v\n", err)
	} else {
		fmt.Printf("  找到 %d 个增量链\n", len(incrementalChains))
		for i, chain := range incrementalChains {
			if i < 3 { // 只显示前3个链
				fmt.Printf("    %d. 链ID: %s, 备份数: %d\n", i+1, chain.ChainID, len(chain.Backups))
				for j, backup := range chain.Backups {
					if j < 2 { // 每个链只显示前2个备份
						fmt.Printf("       - %s (%s)\n", backup.ID, backup.Type)
					}
				}
			}
		}
		if len(incrementalChains) > 3 {
			fmt.Printf("    ... 还有 %d 个链\n", len(incrementalChains)-3)
		}
	}
}

// 备份删除测试
func testBackupDelete(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🗑️ 测试备份删除")

	// 1. 先创建一个测试备份
	fmt.Println("  📦 创建测试备份用于删除...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "删除测试备份")
	if err != nil {
		fmt.Printf("❌ 创建测试备份失败: %v\n", err)
		return
	}

	task := waitForTask(manager, taskID, "删除测试备份")
	if task == nil || task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 创建测试备份失败")
		return
	}

	backupID := task.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 测试备份创建成功，ID: %s\n", backupID)

	// 2. 删除备份
	fmt.Println("  🗑️ 删除测试备份...")
	deleteErr := manager.DeleteBackup(ctx, types.MySQL, backupID)
	if deleteErr != nil {
		fmt.Printf("❌ 删除备份失败: %v\n", deleteErr)
	} else {
		fmt.Printf("  ✅ 成功删除备份: %s\n", backupID)
	}

	// 3. 测试删除不存在的备份
	fmt.Println("  🔍 测试删除不存在的备份...")
	err = manager.DeleteBackup(ctx, types.MySQL, "non-existent-backup-id")
	if err != nil {
		fmt.Printf("  ✅ 删除不存在备份的错误处理正常: %s\n", err.Error())
	} else {
		fmt.Println("  ❌ 删除不存在备份应该返回错误")
	}
}

// 任务管理测试
func testTaskManagement(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📋 测试任务管理")

	// 1. 启动一个任务
	fmt.Println("  🚀 启动测试任务...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "任务管理测试")
	if err != nil {
		fmt.Printf("❌ 启动任务失败: %v\n", err)
		return
	}

	// 2. 测试GetTask
	fmt.Println("  🔍 测试GetTask功能...")
	task, err := manager.GetTask(taskID)
	if err != nil {
		fmt.Printf("❌ 获取任务失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 任务状态: %s, 进度: %d%%, 描述: %s\n",
			task.Status, int(task.Progress), task.Description)
	}

	// 3. 测试ListTasks
	fmt.Println("  📋 测试ListTasks功能...")
	tasks, err := manager.ListTasks()
	if err != nil {
		fmt.Printf("❌ 列出任务失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 找到 %d 个任务\n", len(tasks))

		// 按状态统计
		statusCounts := make(map[types.TaskStatus]int)
		for _, t := range tasks {
			statusCounts[t.Status]++
		}

		fmt.Println("  📊 任务状态统计:")
		for status, count := range statusCounts {
			fmt.Printf("    %s: %d个\n", status, count)
		}
	}

	// 4. 等待任务完成
	waitForTask(manager, taskID, "任务管理测试")

	// 5. 测试清理旧任务
	fmt.Println("  🧹 测试清理旧任务...")
	beforeCount := len(tasks)
	err = manager.ClearOldTasks()
	if err != nil {
		fmt.Printf("❌ 清理旧任务失败: %v\n", err)
	} else {
		afterTasks, _ := manager.ListTasks()
		afterCount := len(afterTasks)
		fmt.Printf("  ✅ 清理完成，任务数从 %d 变为 %d\n", beforeCount, afterCount)
	}
}

// 恢复测试
func testRestore(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🔄 测试恢复功能")

	// 测试1: 全量备份恢复
	fmt.Println("  📦 测试全量备份恢复...")
	testFullBackupRestore(ctx, manager)

	fmt.Println()

	// 测试2: 增量备份恢复
	fmt.Println("  🔗 测试增量备份恢复...")
	testIncrementalBackupRestore(ctx, manager)
}

// 全量备份恢复测试
func testFullBackupRestore(ctx context.Context, manager unibackup.BackupManager) {
	// 1. 记录恢复前的数据量基准
	originalCount, _ := getTestDataCount("testdb")
	fmt.Printf("    📊 恢复前数据量: %d 行\n", originalCount)

	// 2. 插入测试数据并创建全量备份
	fmt.Println("    📝 插入测试数据...")
	err := insertTestData("full-restore-test")
	if err != nil {
		fmt.Printf("❌ 插入测试数据失败: %v\n", err)
		return
	}

	fmt.Println("    📦 创建全量备份用于恢复测试...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "全量恢复测试备份")
	if err != nil {
		fmt.Printf("❌ 创建全量备份失败: %v\n", err)
		return
	}

	backupTask := waitForTask(manager, taskID, "全量恢复测试备份")
	if backupTask == nil || backupTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 创建全量备份失败")
		return
	}

	backupID := backupTask.Metadata["backup_record_id"].(string)
	fmt.Printf("    ✅ 全量备份创建成功，ID: %s\n", backupID)

	// 记录备份后的数据量（用于对比）
	beforeRestoreCount, _ := getTestDataCount("testdb")
	fmt.Printf("    📊 备份后数据量: %d 行\n", beforeRestoreCount)

	// 2. 执行全量恢复
	fmt.Println("    🔄 执行全量恢复...")
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", backupID)
	restoreConfig.Description = "全量恢复测试"

	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("❌ 全量恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, "全量恢复测试")
	if restoreTask != nil && restoreTask.Status == types.TaskStatusCompleted {
		fmt.Println("    ✅ 全量恢复成功")

		// 3. 验证恢复的数据
		fmt.Println("    🔍 验证恢复的数据...")
		err = verifyTestData("full-restore-test", "testdb")
		if err != nil {
			fmt.Printf("❌ 数据验证失败: %v\n", err)
		} else {
			fmt.Println("    ✅ 数据验证成功")
		}

		// 验证数据量对比
		afterRestoreCount, _ := getTestDataCount("testdb")
		fmt.Printf("    📊 数据量对比: 备份前 %d 行 → 恢复后 %d 行\n", beforeRestoreCount, afterRestoreCount)

		if afterRestoreCount == beforeRestoreCount {
			fmt.Println("    ✅ 数据量对比验证成功")
		} else {
			fmt.Printf("    ❌ 数据量对比失败：预期 %d 行，实际 %d 行\n", beforeRestoreCount, afterRestoreCount)
		}
	}
}

// 增量备份恢复测试
func testIncrementalBackupRestore(ctx context.Context, manager unibackup.BackupManager) {
	// 1. 记录恢复前的数据量基准
	originalCount, _ := getTestDataCount("testdb")
	fmt.Printf("    📊 恢复前数据量: %d 行\n", originalCount)

	// 2. 插入初始数据并创建增量链初始备份
	fmt.Println("    📝 插入初始数据...")
	err := insertTestData("incremental-restore-initial")
	if err != nil {
		fmt.Printf("❌ 插入初始数据失败: %v\n", err)
		return
	}

	fmt.Println("    📦 创建增量链初始备份...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainInitial, "增量恢复-初始备份")
	if err != nil {
		fmt.Printf("❌ 创建增量链初始备份失败: %v\n", err)
		return
	}

	initialTask := waitForTask(manager, taskID, "增量恢复-初始备份")
	if initialTask == nil || initialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量链初始备份未成功")
		return
	}

	initialBackupID := initialTask.Metadata["backup_record_id"].(string)
	fmt.Printf("    ✅ 增量链初始备份成功，ID: %s\n", initialBackupID)

	// 记录初始备份后的数据量
	afterInitialCount, _ := getTestDataCount("testdb")
	fmt.Printf("    📊 初始备份后数据量: %d 行\n", afterInitialCount)

	// 2. 等待并插入增量数据
	time.Sleep(2 * time.Second)

	fmt.Println("    📝 插入增量数据...")
	err = insertTestData("incremental-restore-delta")
	if err != nil {
		fmt.Printf("❌ 插入增量数据失败: %v\n", err)
		return
	}

	fmt.Println("    📦 创建增量备份...")
	taskID, err = manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeChainIncremental, "增量恢复-增量备份")
	if err != nil {
		fmt.Printf("❌ 创建增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForTask(manager, taskID, "增量恢复-增量备份")
	if incrementalTask == nil || incrementalTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量备份未成功")
		return
	}

	incrementalBackupID := incrementalTask.Metadata["backup_record_id"].(string)
	fmt.Printf("    ✅ 增量备份成功，ID: %s\n", incrementalBackupID)

	// 记录增量备份后的总数据量（用于对比）
	beforeRestoreCount, _ := getTestDataCount("testdb")
	fmt.Printf("    📊 增量备份后总数据量: %d 行\n", beforeRestoreCount)

	// 3. 恢复增量备份（包含完整链）
	fmt.Println("    🔄 恢复增量备份（包含完整链）...")

	// 直接使用增量备份ID进行恢复（会自动包含整个链）
	restoreConfig := types.NewRestoreConfig(types.MySQL, "testdb", incrementalBackupID)
	restoreConfig.Description = "增量备份恢复测试"

	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("❌ 增量备份恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, "增量备份恢复")
	if restoreTask != nil && restoreTask.Status == types.TaskStatusCompleted {
		fmt.Println("    ✅ 增量备份恢复成功")

		// 4. 验证恢复的数据 - 应该包含初始和增量数据
		fmt.Println("    🔍 验证恢复的数据...")

		// 验证初始数据
		err = verifyTestData("incremental-restore-initial", "testdb")
		if err != nil {
			fmt.Printf("❌ 初始数据验证失败: %v\n", err)
		} else {
			fmt.Println("    ✅ 初始数据验证成功")
		}

		// 验证增量数据
		err = verifyTestData("incremental-restore-delta", "testdb")
		if err != nil {
			fmt.Printf("❌ 增量数据验证失败: %v\n", err)
		} else {
			fmt.Println("    ✅ 增量数据验证成功")
		}

		// 验证总数据量对比
		afterRestoreCount, _ := getTestDataCount("testdb")
		fmt.Printf("    📊 数据量对比: 备份前 %d 行 → 恢复后 %d 行\n", beforeRestoreCount, afterRestoreCount)

		if afterRestoreCount == beforeRestoreCount {
			fmt.Println("    ✅ 数据量对比验证成功")
		} else {
			fmt.Printf("    ❌ 数据量对比失败：预期 %d 行，实际 %d 行\n", beforeRestoreCount, afterRestoreCount)
		}
	}
}

// 综合测试
func runComprehensiveTest(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🎯 运行综合测试")

	testFullBackup(ctx, manager)
	fmt.Println()
	testIncrementalBackup(ctx, manager)
	fmt.Println()
	testBackupList(ctx, manager)
	fmt.Println()
	testBackupDelete(ctx, manager)
	fmt.Println()
	testTaskManagement(ctx, manager)
	fmt.Println()
	testRestore(ctx, manager)
}

// 所有测试
func runAllTests(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🎉 运行所有测试")
	runComprehensiveTest(ctx, manager)
}

// 等待任务完成
func waitForTask(manager unibackup.BackupManager, taskID string, description string) *types.Task {
	fmt.Printf("⏳ 等待任务完成: %s\n", description)

	maxWait := 60 // 最多等待60秒
	waited := 0

	for waited < maxWait {
		task, err := manager.GetTask(taskID)
		if err != nil {
			fmt.Printf("❌ 获取任务状态失败: %v\n", err)
			return nil
		}

		switch task.Status {
		case types.TaskStatusCompleted:
			fmt.Printf("✅ 任务完成: %s\n", description)
			return task
		case types.TaskStatusFailed:
			fmt.Printf("❌ 任务失败: %s - %s\n", description, task.Error)
			return task
		case types.TaskStatusCancelled:
			fmt.Printf("⭕ 任务取消: %s\n", description)
			return task
		case types.TaskStatusRunning, types.TaskStatusPending:
			fmt.Printf("⏳ 任务进行中: %s (进度: %d%%)\n", description, int(task.Progress))
			time.Sleep(2 * time.Second)
			waited += 2
		default:
			fmt.Printf("⚠️ 未知任务状态: %s\n", task.Status)
			time.Sleep(1 * time.Second)
			waited += 1
		}
	}

	fmt.Printf("⏰ 等待超时，停止等待任务: %s\n", description)
	return nil
}

func showUsage() {
	fmt.Println("MySQL统一测试套件")
	fmt.Println("==================")
	fmt.Println("")
	fmt.Println("用法: mysql-unified-test <test-type>")
	fmt.Println("")
	fmt.Println("测试类型:")
	fmt.Println("  full-backup        全量备份测试")
	fmt.Println("  incremental-backup 增量备份测试")
	fmt.Println("  backup-list        备份列表测试")
	fmt.Println("  backup-delete      备份删除测试")
	fmt.Println("  task-management    任务管理测试")
	fmt.Println("  restore            恢复功能测试")
	fmt.Println("  comprehensive      综合测试")
	fmt.Println("  all                所有测试")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  mysql-unified-test comprehensive")
	fmt.Println("  mysql-unified-test full-backup")
	fmt.Println("  mysql-unified-test all")
}

// verifyLocalBackupFile 验证本地备份文件确实存在于文件系统中
func verifyLocalBackupFile(backupID, dataSource string) error {
	// 构建预期的备份文件路径
	backupRoot := "/tests/data/backup"
	var expectedPath string

	// 根据数据源类型构建路径
	switch dataSource {
	case "mysql":
		expectedPath = filepath.Join(backupRoot, "mysql", "archival", backupID)
	case "elasticsearch":
		expectedPath = filepath.Join(backupRoot, "elasticsearch", "archival", backupID)
	default:
		return fmt.Errorf("不支持的数据源类型: %s", dataSource)
	}

	// 检查目录是否存在
	if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
		return fmt.Errorf("备份目录不存在: %s", expectedPath)
	}

	// 检查备份元数据文件
	metadataFile := filepath.Join(expectedPath, "metadata.json")
	if _, err := os.Stat(metadataFile); os.IsNotExist(err) {
		return fmt.Errorf("备份元数据文件不存在: %s", metadataFile)
	}

	// 检查备份数据文件（MySQL的压缩dump文件）
	if dataSource == "mysql" {
		dumpFile := filepath.Join(expectedPath, "data.sql.gz")
		if _, err := os.Stat(dumpFile); os.IsNotExist(err) {
			return fmt.Errorf("MySQL备份数据文件不存在: %s", dumpFile)
		}

		// 检查文件大小（应该大于0）
		if info, err := os.Stat(dumpFile); err == nil {
			if info.Size() == 0 {
				return fmt.Errorf("MySQL备份数据文件为空: %s", dumpFile)
			}
		}
	}

	fmt.Printf("  ✅ 本地备份文件验证通过: %s\n", expectedPath)
	return nil
}
