package main

import (
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"
)

// CommonTestUtils 通用测试工具集
type CommonTestUtils struct {
	logger *zap.Logger
}

// NewCommonTestUtils 创建通用测试工具实例
func NewCommonTestUtils(logger *zap.Logger) *CommonTestUtils {
	return &CommonTestUtils{
		logger: logger,
	}
}

// VerifyLocalBackupFile 验证本地备份文件确实存在于文件系统中
func (utils *CommonTestUtils) VerifyLocalBackupFile(backupID, dataSource string) error {
	// 构建预期的备份文件路径
	backupRoot := "/tests/data/backup"
	var expectedPath string
	
	// 根据数据源类型构建路径
	switch dataSource {
	case "mysql":
		expectedPath = filepath.Join(backupRoot, "mysql", "archival", backupID)
	case "elasticsearch":
		expectedPath = filepath.Join(backupRoot, "elasticsearch", "archival", backupID)
	default:
		return fmt.Errorf("不支持的数据源类型: %s", dataSource)
	}
	
	// 检查目录是否存在
	if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
		return fmt.Errorf("备份目录不存在: %s", expectedPath)
	}
	
	// 检查备份元数据文件
	metadataFile := filepath.Join(expectedPath, "metadata.json")
	if _, err := os.Stat(metadataFile); os.IsNotExist(err) {
		return fmt.Errorf("备份元数据文件不存在: %s", metadataFile)
	}
	
	// 检查备份数据文件
	if dataSource == "mysql" {
		dumpFile := filepath.Join(expectedPath, "data.sql.gz")
		if _, err := os.Stat(dumpFile); os.IsNotExist(err) {
			return fmt.Errorf("MySQL备份数据文件不存在: %s", dumpFile)
		}

		// 检查文件大小（应该大于0）
		if info, err := os.Stat(dumpFile); err == nil {
			if info.Size() == 0 {
				return fmt.Errorf("MySQL备份数据文件为空: %s", dumpFile)
			}
		}
	}
	
	utils.logger.Info("本地备份文件验证通过", zap.String("path", expectedPath))
	return nil
}

// VerifyCloudStorageFile 验证云存储文件确实存在
func (utils *CommonTestUtils) VerifyCloudStorageFile(backend interfaces.Backend, key string) error {
	ctx := context.Background()
	
	// 检查文件是否存在
	exists, err := backend.Exists(ctx, key)
	if err != nil {
		return fmt.Errorf("检查云存储文件存在性失败: %w", err)
	}
	
	if !exists {
		return fmt.Errorf("云存储文件不存在: %s", key)
	}
	
	// 尝试读取文件内容验证可访问性
	reader, err := backend.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("读取云存储文件失败: %w", err)
	}
	defer reader.Close()
	
	// 读取前1KB验证文件可读
	buffer := make([]byte, 1024)
	n, err := reader.Read(buffer)
	if err != nil && err != io.EOF {
		return fmt.Errorf("读取云存储文件内容失败: %w", err)
	}
	
	if n == 0 {
		return fmt.Errorf("云存储文件为空: %s", key)
	}
	
	utils.logger.Info("云存储文件验证通过", zap.String("key", key), zap.Int("bytes_read", n))
	return nil
}

// VerifyDataIntegrity 验证数据完整性（通用的校验和验证）
func (utils *CommonTestUtils) VerifyDataIntegrity(backend interfaces.Backend, key string, expectedData []byte) error {
	ctx := context.Background()
	
	// 读取存储的数据
	reader, err := backend.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("读取数据失败: %w", err)
	}
	defer reader.Close()
	
	actualData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取数据内容失败: %w", err)
	}
	
	// 验证数据长度
	if len(actualData) != len(expectedData) {
		return fmt.Errorf("数据长度不匹配: 期望 %d, 实际 %d", len(expectedData), len(actualData))
	}
	
	// 计算并比较SHA256校验和
	expectedHash := sha256.Sum256(expectedData)
	actualHash := sha256.Sum256(actualData)
	
	if expectedHash != actualHash {
		return fmt.Errorf("数据校验和不匹配: 期望 %x, 实际 %x", expectedHash, actualHash)
	}
	
	utils.logger.Info("数据完整性验证通过", 
		zap.String("key", key), 
		zap.Int("size", len(actualData)),
		zap.String("checksum", fmt.Sprintf("%x", actualHash)))
	
	return nil
}

// WaitForTaskCompletion 等待任务完成（统一的任务等待逻辑）
func (utils *CommonTestUtils) WaitForTaskCompletion(manager unibackup.BackupManager, taskID string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("等待任务完成超时: %s", taskID)
		case <-ticker.C:
			task, err := manager.GetTask(context.Background(), taskID)
			if err != nil {
				return fmt.Errorf("获取任务状态失败: %w", err)
			}
			
			switch task.Status {
			case types.TaskStatusCompleted:
				utils.logger.Info("任务完成", zap.String("task_id", taskID))
				return nil
			case types.TaskStatusFailed:
				errorMsg := "未知错误"
				if task.Error != nil {
					errorMsg = task.Error.Message
				}
				return fmt.Errorf("任务失败: %s", errorMsg)
			case types.TaskStatusCancelled:
				return fmt.Errorf("任务被取消: %s", taskID)
			}
		}
	}
}

// VerifyBackupRestoreFlow 验证完整的备份-恢复流程
func (utils *CommonTestUtils) VerifyBackupRestoreFlow(
	manager unibackup.BackupManager,
	dataSource types.DataSourceType,
	sourceName string,
	backupType types.BackupType,
	description string,
) error {
	ctx := context.Background()
	
	// 1. 执行备份
	utils.logger.Info("开始备份", zap.String("description", description))
	taskID, err := manager.BackupAsync(ctx, dataSource, sourceName, backupType, description)
	if err != nil {
		return fmt.Errorf("启动备份失败: %w", err)
	}
	
	// 2. 等待备份完成
	if err := utils.WaitForTaskCompletion(manager, taskID, 60*time.Second); err != nil {
		return fmt.Errorf("等待备份完成失败: %w", err)
	}
	
	// 3. 获取备份ID
	task, err := manager.GetTask(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取任务信息失败: %w", err)
	}
	
	backupID, ok := task.Metadata["backup_record_id"].(string)
	if !ok {
		return fmt.Errorf("无法获取备份ID")
	}
	
	// 4. 验证本地文件（如果是本地存储）
	if err := utils.VerifyLocalBackupFile(backupID, strings.ToLower(string(dataSource))); err != nil {
		utils.logger.Warn("本地备份文件验证失败", zap.Error(err))
		// 注意：这里不返回错误，因为可能使用的是云存储
	}
	
	utils.logger.Info("备份-恢复流程验证通过", 
		zap.String("backup_id", backupID),
		zap.String("description", description))
	
	return nil
}

// BasicCRUDTest 基础CRUD操作测试（避免重复代码）
func (utils *CommonTestUtils) BasicCRUDTest(backend interfaces.Backend, testPrefix string) error {
	ctx := context.Background()
	
	testData := map[string]string{
		fmt.Sprintf("%s/file1.txt", testPrefix): "测试数据1",
		fmt.Sprintf("%s/file2.txt", testPrefix): "测试数据2",
		fmt.Sprintf("%s/file3.txt", testPrefix): "测试数据3",
	}
	
	// 1. 测试Put操作
	for key, data := range testData {
		written, err := backend.Put(ctx, key, strings.NewReader(data))
		if err != nil {
			return fmt.Errorf("Put操作失败 [%s]: %w", key, err)
		}
		if written != int64(len(data)) {
			return fmt.Errorf("写入字节数不匹配 [%s]: 期望 %d, 实际 %d", key, len(data), written)
		}
	}
	
	// 2. 测试Exists操作
	for key := range testData {
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			return fmt.Errorf("Exists操作失败 [%s]: %w", key, err)
		}
		if !exists {
			return fmt.Errorf("文件应该存在但Exists返回false [%s]", key)
		}
	}
	
	// 3. 测试Get操作和数据完整性
	for key, expectedData := range testData {
		if err := utils.VerifyDataIntegrity(backend, key, []byte(expectedData)); err != nil {
			return fmt.Errorf("数据完整性验证失败 [%s]: %w", key, err)
		}
	}
	
	// 4. 测试Delete操作
	for key := range testData {
		if err := backend.Delete(ctx, key); err != nil {
			return fmt.Errorf("Delete操作失败 [%s]: %w", key, err)
		}
		
		// 验证删除后文件不存在
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			return fmt.Errorf("删除后Exists检查失败 [%s]: %w", key, err)
		}
		if exists {
			return fmt.Errorf("文件应该已删除但仍然存在 [%s]", key)
		}
	}
	
	utils.logger.Info("基础CRUD测试通过", zap.String("test_prefix", testPrefix))
	return nil
}
