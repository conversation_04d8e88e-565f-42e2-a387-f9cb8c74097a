package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"

	// 导入gocloud S3驱动以支持云存储
	_ "gocloud.dev/blob/s3blob"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

func main() {
	if len(os.Args) < 2 {
		showUsage()
		os.Exit(1)
	}

	testType := os.Args[1]
	logger := zap.NewExample()

	fmt.Printf("🧪 Elasticsearch统一测试套件: %s\n", testType)
	fmt.Println(strings.Repeat("=", 60))

	// 创建配置 - 测试云存储模式（验证错误检测）
	cfg := &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             logger,
		MaxConcurrentTasks: 3,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		// 设置云存储配置，测试ES不支持时的错误处理
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "s3",
			Bucket:    "test-backup-bucket",
			Region:    "us-east-1",
			AccessKey: "minioadmin",
			SecretKey: "minioadmin123",
			Endpoint:  "http://unibackup-test-minio:9000",
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://unibackup-test-elasticsearch:9200"},
			ArchivalRepoName: "unibackup-test-archival",
			ManagedRepoName:  "unibackup-test-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     "/usr/share/elasticsearch/snapshots", // 云存储模式下被忽略
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		fmt.Printf("❌ 创建备份管理器失败: %v\n", err)
		os.Exit(1)
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 根据测试类型执行测试
	switch testType {
	case "snapshot-backup":
		testSnapshotBackup(ctx, manager)
	case "incremental-backup":
		testIncrementalBackup(ctx, manager)
	case "backup-list":
		testBackupList(ctx, manager)
	case "backup-delete":
		testBackupDelete(ctx, manager)
	case "task-management":
		testTaskManagement(ctx, manager)
	case "restore":
		testRestore(ctx, manager)
	case "comprehensive":
		runComprehensiveTest(ctx, manager)
	case "all":
		runAllTests(ctx, manager)
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		showUsage()
		os.Exit(1)
	}

	fmt.Println("✅ Elasticsearch测试完成")
}

// Elasticsearch连接和数据验证辅助函数
func getESConnection() string {
	return "http://unibackup-test-elasticsearch:9200"
}

// 创建测试索引和数据
func createTestIndex(indexName string, testData map[string]interface{}) error {
	esURL := getESConnection()

	// 1. 先删除索引（如果存在）
	indexURL := fmt.Sprintf("%s/%s", esURL, indexName)
	client := &http.Client{Timeout: 10 * time.Second}

	// 删除现有索引（忽略错误，因为索引可能不存在）
	deleteReq, _ := http.NewRequest("DELETE", indexURL, nil)
	client.Do(deleteReq) // 忽略删除结果

	// 2. 创建索引
	mapping := map[string]interface{}{
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				"test_name": map[string]interface{}{
					"type": "keyword",
				},
				"test_value": map[string]interface{}{
					"type": "text",
				},
				"created_at": map[string]interface{}{
					"type": "date",
				},
			},
		},
	}

	mappingBytes, _ := json.Marshal(mapping)
	req, _ := http.NewRequest("PUT", indexURL, bytes.NewBuffer(mappingBytes))
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("创建索引失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		// 读取错误响应内容以便调试
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("创建索引失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 2. 插入测试数据
	testData["created_at"] = time.Now().Format(time.RFC3339)

	docURL := fmt.Sprintf("%s/%s/_doc", esURL, indexName)
	dataBytes, _ := json.Marshal(testData)
	req, _ = http.NewRequest("POST", docURL, bytes.NewBuffer(dataBytes))
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.Do(req)
	if err != nil {
		return fmt.Errorf("插入测试数据失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("插入测试数据失败，状态码: %d", resp.StatusCode)
	}

	// 3. 刷新索引确保数据可见
	refreshURL := fmt.Sprintf("%s/%s/_refresh", esURL, indexName)
	req, _ = http.NewRequest("POST", refreshURL, nil)
	client.Do(req)

	fmt.Printf("    ✅ 创建测试索引和数据: %s\n", indexName)
	return nil
}

// 验证索引数据是否存在
func verifyIndexData(indexName string, expectedData map[string]interface{}) error {
	esURL := getESConnection()

	// 搜索测试数据
	searchURL := fmt.Sprintf("%s/%s/_search", esURL, indexName)
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"test_name": expectedData["test_name"],
			},
		},
	}

	queryBytes, _ := json.Marshal(query)
	req, _ := http.NewRequest("POST", searchURL, bytes.NewBuffer(queryBytes))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("搜索测试数据失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("搜索测试数据失败，状态码: %d", resp.StatusCode)
	}

	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)

	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("搜索结果格式错误")
	}

	total := hits["total"].(map[string]interface{})["value"].(float64)
	if total == 0 {
		return fmt.Errorf("未找到测试数据: %s", expectedData["test_name"])
	}

	fmt.Printf("    ✅ 验证数据恢复成功: %s\n", expectedData["test_name"])
	return nil
}

// 获取索引文档数量
func getIndexDocCount(indexName string) (int, error) {
	esURL := getESConnection()

	countURL := fmt.Sprintf("%s/%s/_count", esURL, indexName)
	req, _ := http.NewRequest("GET", countURL, nil)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return 0, fmt.Errorf("获取文档数量失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return 0, fmt.Errorf("获取文档数量失败，状态码: %d", resp.StatusCode)
	}

	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)

	count := int(result["count"].(float64))
	return count, nil
}

// 删除测试索引
func deleteTestIndex(indexName string) error {
	esURL := getESConnection()

	indexURL := fmt.Sprintf("%s/%s", esURL, indexName)
	req, _ := http.NewRequest("DELETE", indexURL, nil)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("删除索引失败: %v", err)
	}
	defer resp.Body.Close()

	// 404 是正常的，表示索引已经不存在
	if resp.StatusCode >= 400 && resp.StatusCode != 404 {
		return fmt.Errorf("删除索引失败，状态码: %d", resp.StatusCode)
	}

	return nil
}

// 快照备份测试
func testSnapshotBackup(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📦 测试快照备份")

	// 1. 创建测试索引和数据
	fmt.Println("  📝 创建测试索引...")
	testData := map[string]interface{}{
		"test_name":  "snapshot-backup-test",
		"test_value": fmt.Sprintf("snapshot-backup-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	err := createTestIndex("test-snapshot-index", testData)
	if err != nil {
		fmt.Printf("❌ 创建测试索引失败: %v\n", err)
		return
	}

	// 2. 执行快照备份
	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "test-snapshot-index", types.BackupTypeArchival, "快照备份测试")
	if err != nil {
		fmt.Printf("❌ 快照备份失败: %v\n", err)
		return
	}

	task := waitForTask(manager, taskID, "快照备份")
	if task != nil && task.Status == types.TaskStatusCompleted {
		backupID := task.Metadata["backup_record_id"].(string)
		fmt.Printf("✅ 快照备份成功，ID: %s\n", backupID)
	}
}

// 增量备份测试
func testIncrementalBackup(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📈 测试增量备份")

	// 1. 创建初始索引数据并创建增量链初始备份
	fmt.Println("  📝 创建初始索引...")
	initialData := map[string]interface{}{
		"test_name":  "incremental-initial",
		"test_value": fmt.Sprintf("incremental-initial-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	err := createTestIndex("test-incremental-index", initialData)
	if err != nil {
		fmt.Printf("❌ 创建初始索引失败: %v\n", err)
		return
	}

	fmt.Println("  📦 创建增量链初始备份...")
	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "test-incremental-index", types.BackupTypeChainInitial, "增量链初始备份")
	if err != nil {
		fmt.Printf("❌ 增量链初始备份失败: %v\n", err)
		return
	}

	initialTask := waitForTask(manager, taskID, "增量链初始备份")
	if initialTask == nil || initialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量链初始备份未成功")
		return
	}

	initialBackupID := initialTask.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 增量链初始备份成功，ID: %s\n", initialBackupID)

	// 2. 等待并添加增量数据
	time.Sleep(2 * time.Second)

	fmt.Println("  📝 添加增量数据...")
	deltaData := map[string]interface{}{
		"test_name":  "incremental-delta",
		"test_value": fmt.Sprintf("incremental-delta-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	// 向现有索引添加更多数据
	esURL := getESConnection()
	docURL := fmt.Sprintf("%s/test-incremental-index/_doc", esURL)
	deltaData["created_at"] = time.Now().Format(time.RFC3339)
	dataBytes, _ := json.Marshal(deltaData)
	req, _ := http.NewRequest("POST", docURL, bytes.NewBuffer(dataBytes))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err == nil {
		resp.Body.Close()
		// 刷新索引
		refreshURL := fmt.Sprintf("%s/test-incremental-index/_refresh", esURL)
		req, _ = http.NewRequest("POST", refreshURL, nil)
		client.Do(req)
		fmt.Printf("    ✅ 添加增量数据成功\n")
	}

	fmt.Println("  📦 创建增量备份...")
	taskID, err = manager.BackupAsync(ctx, types.Elasticsearch, "test-incremental-index", types.BackupTypeChainIncremental, "增量备份")
	if err != nil {
		fmt.Printf("❌ 增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForTask(manager, taskID, "增量备份")
	if incrementalTask != nil && incrementalTask.Status == types.TaskStatusCompleted {
		incrementalBackupID := incrementalTask.Metadata["backup_record_id"].(string)
		fmt.Printf("  ✅ 增量备份成功，ID: %s\n", incrementalBackupID)
	}
}

// 备份列表测试
func testBackupList(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📋 测试备份列表")

	// 1. 列出归档备份
	fmt.Println("  📋 归档备份列表:")
	archivalBackups, err := manager.ListArchivalBackups(ctx, types.Elasticsearch)
	if err != nil {
		fmt.Printf("❌ 列出归档备份失败: %v\n", err)
	} else {
		fmt.Printf("  找到 %d 个归档备份\n", len(archivalBackups))
		for i, backup := range archivalBackups {
			if i < 5 { // 只显示前5个
				fmt.Printf("    %d. ID: %s, 时间: %s, 描述: %s\n",
					i+1, backup.Record.ID,
					backup.Record.Timestamp.Format("2006-01-02 15:04:05"),
					backup.Record.Description)
			}
		}
		if len(archivalBackups) > 5 {
			fmt.Printf("    ... 还有 %d 个备份\n", len(archivalBackups)-5)
		}
	}

	// 2. 列出增量链
	fmt.Println("  🔗 增量链列表:")
	incrementalChains, err := manager.ListIncrementalChains(ctx, types.Elasticsearch)
	if err != nil {
		fmt.Printf("❌ 列出增量链失败: %v\n", err)
	} else {
		fmt.Printf("  找到 %d 个增量链\n", len(incrementalChains))
		for i, chain := range incrementalChains {
			if i < 3 { // 只显示前3个链
				fmt.Printf("    %d. 链ID: %s, 备份数: %d\n", i+1, chain.ChainID, len(chain.Backups))
				for j, backup := range chain.Backups {
					if j < 2 { // 每个链只显示前2个备份
						fmt.Printf("       - %s (%s)\n", backup.ID, backup.Type)
					}
				}
			}
		}
		if len(incrementalChains) > 3 {
			fmt.Printf("    ... 还有 %d 个链\n", len(incrementalChains)-3)
		}
	}
}

// 备份删除测试
func testBackupDelete(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🗑️ 测试备份删除")

	// 1. 先创建一个测试备份
	fmt.Println("  📦 创建测试备份用于删除...")

	// 创建临时测试索引
	testData := map[string]interface{}{
		"test_name":  "delete-test",
		"test_value": fmt.Sprintf("delete-test-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	err := createTestIndex("test-delete-index", testData)
	if err != nil {
		fmt.Printf("❌ 创建测试索引失败: %v\n", err)
		return
	}

	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "test-delete-index", types.BackupTypeArchival, "删除测试备份")
	if err != nil {
		fmt.Printf("❌ 创建测试备份失败: %v\n", err)
		return
	}

	task := waitForTask(manager, taskID, "删除测试备份")
	if task == nil || task.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 创建测试备份失败")
		return
	}

	backupID := task.Metadata["backup_record_id"].(string)
	fmt.Printf("  ✅ 测试备份创建成功，ID: %s\n", backupID)

	// 2. 删除备份
	fmt.Println("  🗑️ 删除测试备份...")
	deleteErr := manager.DeleteBackup(ctx, types.Elasticsearch, backupID)
	if deleteErr != nil {
		fmt.Printf("❌ 删除备份失败: %v\n", deleteErr)
	} else {
		fmt.Printf("  ✅ 成功删除备份: %s\n", backupID)
	}

	// 3. 测试删除不存在的备份
	fmt.Println("  🔍 测试删除不存在的备份...")
	deleteErr = manager.DeleteBackup(ctx, types.Elasticsearch, "non-existent-backup-id")
	if deleteErr != nil {
		fmt.Printf("  ✅ 删除不存在备份的错误处理正常: %s\n", deleteErr.Error())
	} else {
		fmt.Println("  ❌ 删除不存在备份应该返回错误")
	}

	// 清理测试索引
	deleteTestIndex("test-delete-index")
}

// 任务管理测试
func testTaskManagement(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("📋 测试任务管理")

	// 1. 创建测试索引
	testData := map[string]interface{}{
		"test_name":  "task-management-test",
		"test_value": fmt.Sprintf("task-management-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	err := createTestIndex("test-task-index", testData)
	if err != nil {
		fmt.Printf("❌ 创建测试索引失败: %v\n", err)
		return
	}

	// 2. 启动一个任务
	fmt.Println("  🚀 启动测试任务...")
	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "test-task-index", types.BackupTypeArchival, "任务管理测试")
	if err != nil {
		fmt.Printf("❌ 启动任务失败: %v\n", err)
		return
	}

	// 3. 测试GetTask
	fmt.Println("  🔍 测试GetTask功能...")
	task, err := manager.GetTask(taskID)
	if err != nil {
		fmt.Printf("❌ 获取任务失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 任务状态: %s, 进度: %d%%, 描述: %s\n",
			task.Status, int(task.Progress), task.Description)
	}

	// 4. 测试ListTasks
	fmt.Println("  📋 测试ListTasks功能...")
	tasks, err := manager.ListTasks()
	if err != nil {
		fmt.Printf("❌ 列出任务失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 找到 %d 个任务\n", len(tasks))

		// 按状态统计
		statusCounts := make(map[types.TaskStatus]int)
		for _, t := range tasks {
			statusCounts[t.Status]++
		}

		fmt.Println("  📊 任务状态统计:")
		for status, count := range statusCounts {
			fmt.Printf("    %s: %d个\n", status, count)
		}
	}

	// 5. 等待任务完成
	waitForTask(manager, taskID, "任务管理测试")

	// 6. 测试清理旧任务
	fmt.Println("  🧹 测试清理旧任务...")
	beforeCount := len(tasks)
	err = manager.ClearOldTasks()
	if err != nil {
		fmt.Printf("❌ 清理旧任务失败: %v\n", err)
	} else {
		afterTasks, _ := manager.ListTasks()
		afterCount := len(afterTasks)
		fmt.Printf("  ✅ 清理完成，任务数从 %d 变为 %d\n", beforeCount, afterCount)
	}

	// 清理测试索引
	deleteTestIndex("test-task-index")
}

// 恢复测试
func testRestore(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🔄 测试恢复功能")

	// 测试1: 快照备份恢复
	fmt.Println("  📦 测试快照备份恢复...")
	testSnapshotBackupRestore(ctx, manager)

	fmt.Println()

	// 测试2: 增量备份恢复
	fmt.Println("  🔗 测试增量备份恢复...")
	testIncrementalBackupRestore(ctx, manager)
}

// 快照备份恢复测试
func testSnapshotBackupRestore(ctx context.Context, manager unibackup.BackupManager) {
	indexName := "test-restore-snapshot"

	// 1. 确保索引不存在
	deleteTestIndex(indexName)

	// 2. 创建测试数据并备份
	fmt.Println("    📝 创建测试数据...")
	testData := map[string]interface{}{
		"test_name":  "snapshot-restore-test",
		"test_value": fmt.Sprintf("snapshot-restore-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	err := createTestIndex(indexName, testData)
	if err != nil {
		fmt.Printf("❌ 创建测试索引失败: %v\n", err)
		return
	}

	fmt.Println("    📦 创建快照备份用于恢复测试...")
	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, indexName, types.BackupTypeArchival, "快照恢复测试备份")
	if err != nil {
		fmt.Printf("❌ 创建快照备份失败: %v\n", err)
		return
	}

	backupTask := waitForTask(manager, taskID, "快照恢复测试备份")
	if backupTask == nil || backupTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 创建快照备份失败")
		return
	}

	backupID := backupTask.Metadata["backup_record_id"].(string)
	fmt.Printf("    ✅ 快照备份创建成功，ID: %s\n", backupID)

	// 记录备份后的文档数量
	beforeRestoreCount, _ := getIndexDocCount(indexName)
	fmt.Printf("    📊 备份后文档数量: %d 个\n", beforeRestoreCount)

	// 3. 删除索引模拟数据丢失
	fmt.Println("    🗑️ 删除索引模拟数据丢失...")
	deleteTestIndex(indexName)

	// 4. 执行快照恢复
	fmt.Println("    🔄 执行快照恢复...")
	restoreConfig := types.NewRestoreConfig(types.Elasticsearch, indexName, backupID)
	restoreConfig.Description = "快照恢复测试"

	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("❌ 快照恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, "快照恢复测试")
	if restoreTask != nil && restoreTask.Status == types.TaskStatusCompleted {
		fmt.Println("    ✅ 快照恢复成功")

		// 5. 验证恢复的数据
		fmt.Println("    🔍 验证恢复的数据...")
		err = verifyIndexData(indexName, testData)
		if err != nil {
			fmt.Printf("❌ 数据验证失败: %v\n", err)
		} else {
			fmt.Println("    ✅ 数据验证成功")
		}

		// 验证文档数量对比
		afterRestoreCount, _ := getIndexDocCount(indexName)
		fmt.Printf("    📊 文档数量对比: 备份前 %d 个 → 恢复后 %d 个\n", beforeRestoreCount, afterRestoreCount)

		if afterRestoreCount == beforeRestoreCount {
			fmt.Println("    ✅ 文档数量对比验证成功")
		} else {
			fmt.Printf("    ❌ 文档数量对比失败：预期 %d 个，实际 %d 个\n", beforeRestoreCount, afterRestoreCount)
		}
	}

	// 清理测试索引
	deleteTestIndex(indexName)
}

// 增量备份恢复测试
func testIncrementalBackupRestore(ctx context.Context, manager unibackup.BackupManager) {
	indexName := "test-restore-incremental"

	// 1. 确保索引不存在
	deleteTestIndex(indexName)

	// 2. 创建初始数据并创建增量链初始备份
	fmt.Println("    📝 创建初始数据...")
	initialData := map[string]interface{}{
		"test_name":  "incremental-restore-initial",
		"test_value": fmt.Sprintf("incremental-restore-initial-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	err := createTestIndex(indexName, initialData)
	if err != nil {
		fmt.Printf("❌ 创建初始索引失败: %v\n", err)
		return
	}

	fmt.Println("    📦 创建增量链初始备份...")
	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, indexName, types.BackupTypeChainInitial, "增量恢复-初始备份")
	if err != nil {
		fmt.Printf("❌ 创建增量链初始备份失败: %v\n", err)
		return
	}

	initialTask := waitForTask(manager, taskID, "增量恢复-初始备份")
	if initialTask == nil || initialTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量链初始备份未成功")
		return
	}

	initialBackupID := initialTask.Metadata["backup_record_id"].(string)
	fmt.Printf("    ✅ 增量链初始备份成功，ID: %s\n", initialBackupID)

	// 记录初始备份后的文档数量
	afterInitialCount, _ := getIndexDocCount(indexName)
	fmt.Printf("    📊 初始备份后文档数量: %d 个\n", afterInitialCount)

	// 3. 等待并添加增量数据
	time.Sleep(2 * time.Second)

	fmt.Println("    📝 添加增量数据...")
	deltaData := map[string]interface{}{
		"test_name":  "incremental-restore-delta",
		"test_value": fmt.Sprintf("incremental-restore-delta-data-%s", time.Now().Format("2006-01-02 15:04:05")),
	}

	// 向现有索引添加更多数据
	esURL := getESConnection()
	docURL := fmt.Sprintf("%s/%s/_doc", esURL, indexName)
	deltaData["created_at"] = time.Now().Format(time.RFC3339)
	dataBytes, _ := json.Marshal(deltaData)
	req, _ := http.NewRequest("POST", docURL, bytes.NewBuffer(dataBytes))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err == nil {
		resp.Body.Close()
		// 刷新索引
		refreshURL := fmt.Sprintf("%s/%s/_refresh", esURL, indexName)
		req, _ = http.NewRequest("POST", refreshURL, nil)
		client.Do(req)
		fmt.Printf("    ✅ 添加增量数据成功\n")
	}

	fmt.Println("    📦 创建增量备份...")
	taskID, err = manager.BackupAsync(ctx, types.Elasticsearch, indexName, types.BackupTypeChainIncremental, "增量恢复-增量备份")
	if err != nil {
		fmt.Printf("❌ 创建增量备份失败: %v\n", err)
		return
	}

	incrementalTask := waitForTask(manager, taskID, "增量恢复-增量备份")
	if incrementalTask == nil || incrementalTask.Status != types.TaskStatusCompleted {
		fmt.Println("❌ 增量备份未成功")
		return
	}

	incrementalBackupID := incrementalTask.Metadata["backup_record_id"].(string)
	fmt.Printf("    ✅ 增量备份成功，ID: %s\n", incrementalBackupID)

	// 记录增量备份后的总文档数量（用于对比）
	beforeRestoreCount, _ := getIndexDocCount(indexName)
	fmt.Printf("    📊 增量备份后总文档数量: %d 个\n", beforeRestoreCount)

	// 4. 删除索引模拟数据丢失
	fmt.Println("    🗑️ 删除索引模拟数据丢失...")
	deleteTestIndex(indexName)

	// 5. 恢复增量备份（包含完整链）
	fmt.Println("    🔄 恢复增量备份（包含完整链）...")

	// 直接使用增量备份ID进行恢复（会自动包含整个链）
	restoreConfig := types.NewRestoreConfig(types.Elasticsearch, indexName, incrementalBackupID)
	restoreConfig.Description = "增量备份恢复测试"

	restoreTaskID, err := manager.RestoreAsync(ctx, restoreConfig)
	if err != nil {
		fmt.Printf("❌ 增量备份恢复失败: %v\n", err)
		return
	}

	restoreTask := waitForTask(manager, restoreTaskID, "增量备份恢复")
	if restoreTask != nil && restoreTask.Status == types.TaskStatusCompleted {
		fmt.Println("    ✅ 增量备份恢复成功")

		// 6. 验证恢复的数据 - 应该包含初始和增量数据
		fmt.Println("    🔍 验证恢复的数据...")

		// 验证初始数据
		err = verifyIndexData(indexName, initialData)
		if err != nil {
			fmt.Printf("❌ 初始数据验证失败: %v\n", err)
		} else {
			fmt.Println("    ✅ 初始数据验证成功")
		}

		// 验证增量数据
		err = verifyIndexData(indexName, deltaData)
		if err != nil {
			fmt.Printf("❌ 增量数据验证失败: %v\n", err)
		} else {
			fmt.Println("    ✅ 增量数据验证成功")
		}

		// 验证总文档数量对比
		afterRestoreCount, _ := getIndexDocCount(indexName)
		fmt.Printf("    📊 文档数量对比: 备份前 %d 个 → 恢复后 %d 个\n", beforeRestoreCount, afterRestoreCount)

		if afterRestoreCount == beforeRestoreCount {
			fmt.Println("    ✅ 文档数量对比验证成功")
		} else {
			fmt.Printf("    ❌ 文档数量对比失败：预期 %d 个，实际 %d 个\n", beforeRestoreCount, afterRestoreCount)
		}
	}

	// 清理测试索引
	deleteTestIndex(indexName)
}

// 综合测试
func runComprehensiveTest(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🎯 运行综合测试")

	testSnapshotBackup(ctx, manager)
	fmt.Println()
	testIncrementalBackup(ctx, manager)
	fmt.Println()
	testBackupList(ctx, manager)
	fmt.Println()
	testBackupDelete(ctx, manager)
	fmt.Println()
	testTaskManagement(ctx, manager)
	fmt.Println()
	testRestore(ctx, manager)
}

// 所有测试
func runAllTests(ctx context.Context, manager unibackup.BackupManager) {
	fmt.Println("🎉 运行所有测试")
	runComprehensiveTest(ctx, manager)
}

// 等待任务完成
func waitForTask(manager unibackup.BackupManager, taskID string, description string) *types.Task {
	fmt.Printf("⏳ 等待任务完成: %s\n", description)

	maxWait := 60 // 最多等待60秒
	waited := 0

	for waited < maxWait {
		task, err := manager.GetTask(taskID)
		if err != nil {
			fmt.Printf("❌ 获取任务状态失败: %v\n", err)
			return nil
		}

		switch task.Status {
		case types.TaskStatusCompleted:
			fmt.Printf("✅ 任务完成: %s\n", description)
			return task
		case types.TaskStatusFailed:
			fmt.Printf("❌ 任务失败: %s - %s\n", description, task.Error)
			return task
		case types.TaskStatusCancelled:
			fmt.Printf("⭕ 任务取消: %s\n", description)
			return task
		case types.TaskStatusRunning, types.TaskStatusPending:
			fmt.Printf("⏳ 任务进行中: %s (进度: %d%%)\n", description, int(task.Progress))
			time.Sleep(2 * time.Second)
			waited += 2
		default:
			fmt.Printf("⚠️ 未知任务状态: %s\n", task.Status)
			time.Sleep(1 * time.Second)
			waited += 1
		}
	}

	fmt.Printf("⏰ 等待超时，停止等待任务: %s\n", description)
	return nil
}

func showUsage() {
	fmt.Println("Elasticsearch统一测试套件")
	fmt.Println("==========================")
	fmt.Println("")
	fmt.Println("用法: es-unified-test <test-type>")
	fmt.Println("")
	fmt.Println("测试类型:")
	fmt.Println("  snapshot-backup    快照备份测试")
	fmt.Println("  incremental-backup 增量备份测试")
	fmt.Println("  backup-list        备份列表测试")
	fmt.Println("  backup-delete      备份删除测试")
	fmt.Println("  task-management    任务管理测试")
	fmt.Println("  restore            恢复功能测试")
	fmt.Println("  comprehensive      综合测试")
	fmt.Println("  all                所有测试")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  es-unified-test comprehensive")
	fmt.Println("  es-unified-test snapshot-backup")
	fmt.Println("  es-unified-test all")
}
