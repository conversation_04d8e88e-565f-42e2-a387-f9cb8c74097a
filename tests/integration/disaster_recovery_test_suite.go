package main

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
	"go.uber.org/zap"
)

// DisasterRecoveryTestSuite 灾难恢复测试套件
type DisasterRecoveryTestSuite struct {
	logger   *zap.Logger
	testDir  string
	backend  interfaces.Backend
	testData map[string]string // 测试数据
}

// NewDisasterRecoveryTestSuite 创建灾难恢复测试套件
func NewDisasterRecoveryTestSuite(logger *zap.Logger) *DisasterRecoveryTestSuite {
	return &DisasterRecoveryTestSuite{
		logger:   logger,
		testData: make(map[string]string),
	}
}

// Setup 初始化测试环境
func (suite *DisasterRecoveryTestSuite) Setup() error {
	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "disaster-recovery-test-*")
	if err != nil {
		return fmt.Errorf("创建测试目录失败: %w", err)
	}
	suite.testDir = testDir

	// 创建LocalBackend实例
	backend, err := local.NewLocalBackend(testDir)
	if err != nil {
		return fmt.Errorf("创建LocalBackend失败: %w", err)
	}
	suite.backend = backend

	// 准备测试数据
	suite.prepareTestData()

	suite.logger.Info("灾难恢复测试环境初始化完成", zap.String("test_dir", testDir))
	return nil
}

// Cleanup 清理测试环境
func (suite *DisasterRecoveryTestSuite) Cleanup() error {
	if suite.testDir != "" {
		return os.RemoveAll(suite.testDir)
	}
	return nil
}

// prepareTestData 准备测试数据
func (suite *DisasterRecoveryTestSuite) prepareTestData() {
	suite.testData["critical.txt"] = "关键业务数据"
	suite.testData["backup.sql"] = "CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100));"
	suite.testData["config.json"] = `{"database": "prod", "timeout": 30}`
	suite.testData["large.bin"] = strings.Repeat("A", 1024*1024) // 1MB数据
}

// RunAllTests 运行所有灾难恢复测试
func (suite *DisasterRecoveryTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() error
	}{
		{"进程中断恢复测试", suite.testProcessInterruptionRecovery},
		{"磁盘空间不足恢复测试", suite.testDiskSpaceExhaustionRecovery},
		{"权限丢失恢复测试", suite.testPermissionLossRecovery},
		{"目录损坏恢复测试", suite.testDirectoryCorruptionRecovery},
		{"文件锁定恢复测试", suite.testFileLockingRecovery},
		{"网络中断模拟测试", suite.testNetworkInterruptionSimulation},
		{"部分写入故障恢复测试", suite.testPartialWriteFailureRecovery},
		{"系统资源耗尽恢复测试", suite.testSystemResourceExhaustionRecovery},
		{"并发访问冲突恢复测试", suite.testConcurrentAccessConflictRecovery},
		{"数据恢复完整性验证测试", suite.testDataRecoveryIntegrityVerification},
	}

	for _, test := range tests {
		suite.logger.Info("开始灾难恢复测试", zap.String("test", test.name))
		if err := test.test(); err != nil {
			return fmt.Errorf("灾难恢复测试失败 [%s]: %w", test.name, err)
		}
		suite.logger.Info("灾难恢复测试通过", zap.String("test", test.name))
	}

	return nil
}

// testProcessInterruptionRecovery 测试进程中断恢复
func (suite *DisasterRecoveryTestSuite) testProcessInterruptionRecovery() error {
	ctx := context.Background()

	// 准备测试数据
	testKey := "test/process-interrupt/data.txt"
	testData := "进程中断测试数据"

	// 正常写入数据
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("写入测试数据失败: %w", err)
	}

	// 模拟进程中断后的恢复
	// 创建一个新的Backend实例来模拟进程重启
	newBackend, err := local.NewLocalBackend(suite.testDir)
	if err != nil {
		return fmt.Errorf("创建新Backend实例失败: %w", err)
	}

	// 验证数据在"重启"后仍然可访问
	reader, err := newBackend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("进程重启后读取数据失败: %w", err)
	}
	defer reader.Close()

	recoveredData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取恢复数据失败: %w", err)
	}

	if string(recoveredData) != testData {
		return fmt.Errorf("恢复的数据不一致: 期望 %s, 实际 %s", testData, string(recoveredData))
	}

	suite.logger.Info("进程中断恢复测试通过")
	return nil
}

// testDiskSpaceExhaustionRecovery 测试磁盘空间不足恢复
func (suite *DisasterRecoveryTestSuite) testDiskSpaceExhaustionRecovery() error {
	ctx := context.Background()

	// 创建一个相对较大的文件来模拟磁盘空间问题
	largeData := strings.Repeat("X", 10*1024*1024) // 10MB
	testKey := "test/disk-space/large.bin"

	// 尝试写入大文件
	written, err := suite.backend.Put(ctx, testKey, strings.NewReader(largeData))
	if err != nil {
		// 如果因为磁盘空间不足而失败，这是预期的
		suite.logger.Info("磁盘空间不足错误被正确处理", zap.Error(err))

		// 验证系统能够从错误中恢复
		smallData := "小文件测试"
		smallKey := "test/disk-space/small.txt"

		_, err = suite.backend.Put(ctx, smallKey, strings.NewReader(smallData))
		if err != nil {
			return fmt.Errorf("磁盘空间不足后无法写入小文件: %w", err)
		}

		// 验证小文件写入成功
		reader, err := suite.backend.Get(ctx, smallKey)
		if err != nil {
			return fmt.Errorf("读取小文件失败: %w", err)
		}
		defer reader.Close()

		data, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取小文件内容失败: %w", err)
		}

		if string(data) != smallData {
			return fmt.Errorf("小文件内容不一致")
		}

		suite.logger.Info("磁盘空间不足恢复测试通过")
		return nil
	}

	// 如果大文件写入成功，验证其完整性
	if written != int64(len(largeData)) {
		return fmt.Errorf("大文件写入字节数不匹配: 期望 %d, 实际 %d", len(largeData), written)
	}

	suite.logger.Info("磁盘空间充足，大文件写入成功", zap.Int("size_mb", int(written/(1024*1024))))
	return nil
}

// testPermissionLossRecovery 测试权限丢失恢复
func (suite *DisasterRecoveryTestSuite) testPermissionLossRecovery() error {
	ctx := context.Background()
	testKey := "test/permission/data.txt"
	testData := "权限测试数据"

	// 正常写入数据
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("写入测试数据失败: %w", err)
	}

	// 获取文件路径
	filePath := filepath.Join(suite.testDir, testKey)

	// 模拟权限丢失（如果可能）
	if err := os.Chmod(filePath, 0000); err != nil {
		suite.logger.Warn("无法修改文件权限", zap.Error(err))
		return nil // 跳过这个测试
	}

	// 尝试读取权限被限制的文件
	_, err = suite.backend.Get(ctx, testKey)
	if err == nil {
		// 如果读取成功，可能是因为运行在特权模式下
		suite.logger.Info("在特权模式下运行，权限限制无效")
	} else {
		// 权限错误被正确检测
		suite.logger.Info("权限错误被正确检测", zap.Error(err))
	}

	// 恢复权限
	if err := os.Chmod(filePath, 0644); err != nil {
		return fmt.Errorf("恢复文件权限失败: %w", err)
	}

	// 验证权限恢复后可以正常访问
	reader, err := suite.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("权限恢复后读取文件失败: %w", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取文件内容失败: %w", err)
	}

	if string(data) != testData {
		return fmt.Errorf("权限恢复后数据不一致")
	}

	suite.logger.Info("权限丢失恢复测试通过")
	return nil
}

// testDirectoryCorruptionRecovery 测试目录损坏恢复
func (suite *DisasterRecoveryTestSuite) testDirectoryCorruptionRecovery() error {
	ctx := context.Background()

	// 创建测试目录结构
	testDir := "test/directory-corruption"
	testKey := filepath.Join(testDir, "data.txt")
	testData := "目录损坏测试数据"

	// 写入测试数据
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("写入测试数据失败: %w", err)
	}

	// 模拟目录损坏（删除中间目录）
	dirPath := filepath.Join(suite.testDir, testDir)
	if err := os.RemoveAll(dirPath); err != nil {
		return fmt.Errorf("模拟目录损坏失败: %w", err)
	}

	// 尝试读取文件（应该失败）
	_, err = suite.backend.Get(ctx, testKey)
	if err == nil {
		return fmt.Errorf("目录损坏后仍能读取文件，这是不正常的")
	}

	suite.logger.Info("目录损坏被正确检测", zap.Error(err))

	// 尝试重新写入数据（应该能够自动创建目录）
	_, err = suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("目录损坏后无法重新写入数据: %w", err)
	}

	// 验证数据恢复
	reader, err := suite.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("目录恢复后读取文件失败: %w", err)
	}
	defer reader.Close()

	recoveredData, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取恢复数据失败: %w", err)
	}

	if string(recoveredData) != testData {
		return fmt.Errorf("恢复的数据不一致")
	}

	suite.logger.Info("目录损坏恢复测试通过")
	return nil
}

// testFileLockingRecovery 测试文件锁定恢复
func (suite *DisasterRecoveryTestSuite) testFileLockingRecovery() error {
	ctx := context.Background()
	testKey := "test/file-locking/data.txt"
	testData := "文件锁定测试数据"

	// 写入测试数据
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("写入测试数据失败: %w", err)
	}

	// 获取文件路径
	filePath := filepath.Join(suite.testDir, testKey)

	// 尝试锁定文件（如果可能）
	file, err := os.OpenFile(filePath, os.O_RDWR, 0644)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}

	// 尝试获取文件锁
	err = syscall.Flock(int(file.Fd()), syscall.LOCK_EX|syscall.LOCK_NB)
	if err != nil {
		file.Close()
		suite.logger.Warn("无法获取文件锁", zap.Error(err))
		return nil // 跳过这个测试
	}

	// 在文件被锁定的情况下尝试访问
	_, err = suite.backend.Get(ctx, testKey)
	if err != nil {
		suite.logger.Info("文件锁定时访问被正确阻止", zap.Error(err))
	} else {
		suite.logger.Info("文件锁定时仍可访问（可能是读锁）")
	}

	// 释放文件锁
	syscall.Flock(int(file.Fd()), syscall.LOCK_UN)
	file.Close()

	// 验证锁释放后可以正常访问
	reader, err := suite.backend.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("锁释放后读取文件失败: %w", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("读取文件内容失败: %w", err)
	}

	if string(data) != testData {
		return fmt.Errorf("锁释放后数据不一致")
	}

	suite.logger.Info("文件锁定恢复测试通过")
	return nil
}

// testNetworkInterruptionSimulation 测试网络中断模拟
func (suite *DisasterRecoveryTestSuite) testNetworkInterruptionSimulation() error {
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	testKey := "test/network-interrupt/data.txt"
	testData := "网络中断测试数据"

	// 在短超时的上下文中尝试操作
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			suite.logger.Info("网络超时被正确处理")
		} else {
			return fmt.Errorf("意外的错误: %w", err)
		}
	} else {
		suite.logger.Info("操作在超时前完成")
	}

	// 使用正常的上下文重试操作
	normalCtx := context.Background()
	_, err = suite.backend.Put(normalCtx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("正常上下文下写入失败: %w", err)
	}

	suite.logger.Info("网络中断模拟测试通过")
	return nil
}

// testPartialWriteFailureRecovery 测试部分写入故障恢复
func (suite *DisasterRecoveryTestSuite) testPartialWriteFailureRecovery() error {
	ctx := context.Background()
	testKey := "test/partial-write/data.txt"
	testData := "部分写入故障测试数据"

	// 正常写入数据
	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("写入数据失败: %w", err)
	}

	// 模拟部分写入（通过直接修改文件）
	filePath := filepath.Join(suite.testDir, testKey)
	partialData := testData[:len(testData)/2]

	err = os.WriteFile(filePath, []byte(partialData), 0644)
	if err != nil {
		return fmt.Errorf("模拟部分写入失败: %w", err)
	}

	// 重新写入完整数据进行恢复
	_, err = suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("恢复写入失败: %w", err)
	}

	suite.logger.Info("部分写入故障恢复测试通过")
	return nil
}

// testSystemResourceExhaustionRecovery 测试系统资源耗尽恢复
func (suite *DisasterRecoveryTestSuite) testSystemResourceExhaustionRecovery() error {
	ctx := context.Background()

	// 模拟大量文件操作
	fileCount := 100 // 减少数量以避免测试时间过长

	for i := 0; i < fileCount; i++ {
		testKey := fmt.Sprintf("test/resource-exhaustion/file-%d.txt", i)
		testData := fmt.Sprintf("资源耗尽测试数据 %d", i)

		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
		if err != nil {
			suite.logger.Warn("资源耗尽时写入失败", zap.Int("file_index", i), zap.Error(err))
			break
		}
	}

	// 验证系统能够继续正常工作
	testKey := "test/resource-exhaustion/final.txt"
	testData := "最终测试数据"

	_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		return fmt.Errorf("资源耗尽后无法写入: %w", err)
	}

	suite.logger.Info("系统资源耗尽恢复测试通过", zap.Int("files_created", fileCount))
	return nil
}

// testConcurrentAccessConflictRecovery 测试并发访问冲突恢复
func (suite *DisasterRecoveryTestSuite) testConcurrentAccessConflictRecovery() error {
	ctx := context.Background()
	testKey := "test/concurrent-conflict/shared.txt"
	concurrency := 10

	errChan := make(chan error, concurrency)

	// 启动多个并发写入
	for i := 0; i < concurrency; i++ {
		go func(goroutineID int) {
			testData := fmt.Sprintf("并发冲突测试数据 %d", goroutineID)

			_, err := suite.backend.Put(ctx, testKey, strings.NewReader(testData))
			if err != nil {
				errChan <- fmt.Errorf("并发写入失败 [goroutine %d]: %w", goroutineID, err)
				return
			}

			errChan <- nil
		}(i)
	}

	// 检查结果
	successCount := 0
	for i := 0; i < concurrency; i++ {
		if err := <-errChan; err != nil {
			suite.logger.Warn("并发操作失败", zap.Error(err))
		} else {
			successCount++
		}
	}

	if successCount == 0 {
		return fmt.Errorf("所有并发操作都失败")
	}

	suite.logger.Info("并发访问冲突恢复测试通过",
		zap.Int("success_count", successCount),
		zap.Int("total", concurrency))
	return nil
}

// testDataRecoveryIntegrityVerification 测试数据恢复完整性验证
func (suite *DisasterRecoveryTestSuite) testDataRecoveryIntegrityVerification() error {
	ctx := context.Background()

	// 写入多个测试文件
	for filename, data := range suite.testData {
		testKey := fmt.Sprintf("test/recovery-integrity/%s", filename)

		_, err := suite.backend.Put(ctx, testKey, strings.NewReader(data))
		if err != nil {
			return fmt.Errorf("写入测试文件失败 [%s]: %w", filename, err)
		}
	}

	// 模拟系统重启（创建新的Backend实例）
	newBackend, err := local.NewLocalBackend(suite.testDir)
	if err != nil {
		return fmt.Errorf("创建新Backend实例失败: %w", err)
	}

	// 验证所有数据在重启后的完整性
	for filename, expectedData := range suite.testData {
		testKey := fmt.Sprintf("test/recovery-integrity/%s", filename)

		reader, err := newBackend.Get(ctx, testKey)
		if err != nil {
			return fmt.Errorf("重启后读取文件失败 [%s]: %w", filename, err)
		}
		defer reader.Close()

		actualData, err := io.ReadAll(reader)
		if err != nil {
			return fmt.Errorf("读取文件内容失败 [%s]: %w", filename, err)
		}

		if string(actualData) != expectedData {
			return fmt.Errorf("重启后数据不一致 [%s]", filename)
		}

		suite.logger.Info("数据恢复完整性验证通过",
			zap.String("file", filename),
			zap.Int("size", len(actualData)))
	}

	return nil
}

// 灾难恢复测试主入口
func main() {
	// 配置日志
	logger, _ := zap.NewDevelopment()

	if len(os.Args) < 2 {
		logger.Error("使用方法: disaster-recovery-test <test-type>")
		logger.Info("可用的测试类型:")
		logger.Info("  all                     - 运行所有灾难恢复测试")
		os.Exit(1)
	}

	testType := os.Args[1]

	logger.Info("🚀 开始灾难恢复测试", zap.String("type", testType))

	var err error
	switch testType {
	case "all":
		err = runDisasterRecoveryTests(logger)
	default:
		logger.Error("未知的测试类型", zap.String("type", testType))
		os.Exit(1)
	}

	if err != nil {
		logger.Error("灾难恢复测试失败", zap.Error(err))
		os.Exit(1)
	}

	logger.Info("✅ 灾难恢复测试全部通过", zap.String("type", testType))
}

// runDisasterRecoveryTests 运行灾难恢复测试的主函数
func runDisasterRecoveryTests(logger *zap.Logger) error {
	logger.Info("🛡️ 开始灾难恢复测试")

	suite := NewDisasterRecoveryTestSuite(logger)

	if err := suite.Setup(); err != nil {
		return fmt.Errorf("测试环境初始化失败: %w", err)
	}
	defer suite.Cleanup()

	if err := suite.RunAllTests(); err != nil {
		return fmt.Errorf("灾难恢复测试失败: %w", err)
	}

	logger.Info("✅ 灾难恢复测试全部通过")
	return nil
}
