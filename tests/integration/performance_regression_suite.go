package main

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"
)

// PerformanceRegressionTestSuite 性能回归测试套件
type PerformanceRegressionTestSuite struct {
	logger  *zap.Logger
	manager unibackup.BackupManager
	config  *types.Config
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Duration       time.Duration
	MemoryUsage    uint64
	CPUUsage       float64
	ThroughputMBps float64
	ErrorRate      float64
}

// NewPerformanceRegressionTestSuite 创建性能回归测试套件
func NewPerformanceRegressionTestSuite(logger *zap.Logger, config *types.Config) *PerformanceRegressionTestSuite {
	return &PerformanceRegressionTestSuite{
		logger: logger,
		config: config,
	}
}

// Setup 初始化测试环境
func (suite *PerformanceRegressionTestSuite) Setup() error {
	manager, err := unibackup.NewManager(suite.config)
	if err != nil {
		return fmt.Errorf("创建备份管理器失败: %w", err)
	}
	suite.manager = manager
	return nil
}

// Cleanup 清理测试环境
func (suite *PerformanceRegressionTestSuite) Cleanup() {
	if suite.manager != nil {
		suite.manager.Shutdown()
	}
}

// RunAllTests 运行所有性能回归测试
func (suite *PerformanceRegressionTestSuite) RunAllTests() error {
	tests := []struct {
		name string
		test func() (*PerformanceMetrics, error)
	}{
		{"并发操作性能基准", suite.testConcurrentOperationPerformance},
		{"内存使用监控测试", suite.testMemoryUsageMonitoring},
		{"CPU使用率监控测试", suite.testCPUUsageMonitoring},
		{"备份恢复吞吐量测试", suite.testBackupRestoreThroughput},
		{"错误率监控测试", suite.testErrorRateMonitoring},
		{"资源清理效率测试", suite.testResourceCleanupEfficiency},
		{"任务队列性能测试", suite.testTaskQueuePerformance},
	}

	for _, test := range tests {
		suite.logger.Info("开始性能测试", zap.String("test", test.name))
		metrics, err := test.test()
		if err != nil {
			return fmt.Errorf("性能测试失败 [%s]: %w", test.name, err)
		}
		suite.logPerformanceMetrics(test.name, metrics)
		suite.logger.Info("性能测试通过", zap.String("test", test.name))
	}

	return nil
}

// testConcurrentOperationPerformance 测试并发操作性能基准
// 修复：使用不同的数据源名称来避免锁冲突，测试真正的并发性能
func (suite *PerformanceRegressionTestSuite) testConcurrentOperationPerformance() (*PerformanceMetrics, error) {
	ctx := context.Background()
	startTime := time.Now()

	// 监控内存使用
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	initialMemory := memStats.Alloc

	concurrency := 3 // 减少并发数，避免过度负载
	var wg sync.WaitGroup
	errChan := make(chan error, concurrency)

	// 并发执行备份操作 - 使用不同的数据源名称避免锁冲突
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 使用不同的数据源名称，这样可以真正并发执行
			sourceName := fmt.Sprintf("testdb_%d", index)
			taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, sourceName, types.BackupTypeArchival, fmt.Sprintf("并发性能测试_%d", index))
			if err != nil {
				errChan <- fmt.Errorf("启动并发备份失败 [%d]: %w", index, err)
				return
			}

			// 等待任务完成
			if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
				errChan <- fmt.Errorf("等待并发备份完成失败 [%d]: %w", index, err)
				return
			}
		}(i)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	errorCount := 0
	for err := range errChan {
		if err != nil {
			suite.logger.Error("并发操作错误", zap.Error(err))
			errorCount++
		}
	}

	duration := time.Since(startTime)

	// 计算最终内存使用
	runtime.ReadMemStats(&memStats)
	finalMemory := memStats.Alloc
	memoryUsage := finalMemory - initialMemory

	errorRate := float64(errorCount) / float64(concurrency) * 100

	return &PerformanceMetrics{
		Duration:    duration,
		MemoryUsage: memoryUsage,
		ErrorRate:   errorRate,
	}, nil
}

// testMemoryUsageMonitoring 测试内存使用监控
func (suite *PerformanceRegressionTestSuite) testMemoryUsageMonitoring() (*PerformanceMetrics, error) {
	ctx := context.Background()
	startTime := time.Now()

	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	initialMemory := memStats.Alloc

	// 执行一系列操作来监控内存使用
	operations := []func() error{
		func() error {
			taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "内存监控测试1")
			if err != nil {
				return err
			}
			return suite.waitForTaskCompletion(taskID, 60*time.Second)
		},
		func() error {
			backups, backupErr := suite.manager.ListArchivalBackups(ctx, types.MySQL)
			if backupErr != nil {
				return fmt.Errorf("列出备份失败: %s", backupErr.Message)
			}
			suite.logger.Info("列出备份", zap.Int("count", len(backups)))
			return nil
		},
		func() error {
			// 强制垃圾回收
			runtime.GC()
			return nil
		},
	}

	maxMemoryUsage := uint64(0)
	for i, op := range operations {
		if err := op(); err != nil {
			return nil, fmt.Errorf("内存监控操作失败 [%d]: %w", i, err)
		}

		runtime.ReadMemStats(&memStats)
		currentMemory := memStats.Alloc - initialMemory
		if currentMemory > maxMemoryUsage {
			maxMemoryUsage = currentMemory
		}
	}

	duration := time.Since(startTime)

	return &PerformanceMetrics{
		Duration:    duration,
		MemoryUsage: maxMemoryUsage,
	}, nil
}

// testCPUUsageMonitoring 测试CPU使用率监控
func (suite *PerformanceRegressionTestSuite) testCPUUsageMonitoring() (*PerformanceMetrics, error) {
	ctx := context.Background()
	startTime := time.Now()

	// 执行CPU密集型操作
	taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "CPU监控测试")
	if err != nil {
		return nil, fmt.Errorf("启动CPU监控测试失败: %w", err)
	}

	if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
		return nil, fmt.Errorf("等待CPU监控测试完成失败: %w", err)
	}

	duration := time.Since(startTime)

	// 简单的CPU使用率估算（基于操作时间）
	cpuUsage := float64(duration.Milliseconds()) / float64(time.Minute.Milliseconds()) * 100
	if cpuUsage > 100 {
		cpuUsage = 100
	}

	return &PerformanceMetrics{
		Duration: duration,
		CPUUsage: cpuUsage,
	}, nil
}

// testBackupRestoreThroughput 测试备份恢复吞吐量
func (suite *PerformanceRegressionTestSuite) testBackupRestoreThroughput() (*PerformanceMetrics, error) {
	ctx := context.Background()
	startTime := time.Now()

	// 执行备份
	taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, "吞吐量测试备份")
	if err != nil {
		return nil, fmt.Errorf("启动吞吐量测试备份失败: %w", err)
	}

	if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
		return nil, fmt.Errorf("等待吞吐量测试备份完成失败: %w", err)
	}

	// 执行恢复 - 使用 RestoreByTaskID 方法
	restoreTaskID, err := suite.manager.RestoreByTaskID(ctx, taskID, false)
	if err != nil {
		return nil, fmt.Errorf("启动吞吐量测试恢复失败: %w", err)
	}

	if err := suite.waitForTaskCompletion(restoreTaskID, 60*time.Second); err != nil {
		return nil, fmt.Errorf("等待吞吐量测试恢复完成失败: %w", err)
	}

	duration := time.Since(startTime)

	// 估算吞吐量（假设处理了10MB数据）
	estimatedDataSizeMB := 10.0
	throughputMBps := estimatedDataSizeMB / duration.Seconds()

	return &PerformanceMetrics{
		Duration:       duration,
		ThroughputMBps: throughputMBps,
	}, nil
}

// testErrorRateMonitoring 测试错误率监控
// 修复：使用串行执行避免锁冲突，专注于测试错误率而非并发性能
func (suite *PerformanceRegressionTestSuite) testErrorRateMonitoring() (*PerformanceMetrics, error) {
	ctx := context.Background()
	startTime := time.Now()

	totalOperations := 5 // 减少操作数量，避免过度负载
	errorCount := 0

	// 串行执行多个操作并统计错误率
	for i := 0; i < totalOperations; i++ {
		// 使用同一个数据源但串行执行，测试错误率
		taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, "testdb", types.BackupTypeArchival, fmt.Sprintf("错误率监控测试_%d", i))
		if err != nil {
			errorCount++
			continue
		}

		if err := suite.waitForTaskCompletion(taskID, 30*time.Second); err != nil {
			errorCount++
		}
	}

	duration := time.Since(startTime)
	errorRate := float64(errorCount) / float64(totalOperations) * 100

	return &PerformanceMetrics{
		Duration:  duration,
		ErrorRate: errorRate,
	}, nil
}

// testResourceCleanupEfficiency 测试资源清理效率
func (suite *PerformanceRegressionTestSuite) testResourceCleanupEfficiency() (*PerformanceMetrics, error) {
	startTime := time.Now()

	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	initialMemory := memStats.Alloc

	// 创建一些临时资源
	tempData := make([][]byte, 100)
	for i := range tempData {
		tempData[i] = make([]byte, 1024*1024) // 1MB each
	}

	// 清理资源
	tempData = nil
	runtime.GC()
	runtime.GC() // 强制两次GC确保清理

	runtime.ReadMemStats(&memStats)
	finalMemory := memStats.Alloc

	duration := time.Since(startTime)
	memoryFreed := initialMemory - finalMemory

	return &PerformanceMetrics{
		Duration:    duration,
		MemoryUsage: memoryFreed,
	}, nil
}

// testTaskQueuePerformance 测试任务队列性能
// 修复：使用不同的数据源名称来避免锁冲突，测试任务队列的真正性能
func (suite *PerformanceRegressionTestSuite) testTaskQueuePerformance() (*PerformanceMetrics, error) {
	ctx := context.Background()
	startTime := time.Now()

	// 快速提交多个任务
	taskCount := 3 // 减少任务数量，避免过度负载
	taskIDs := make([]string, taskCount)

	for i := 0; i < taskCount; i++ {
		// 使用不同的数据源名称，这样任务可以并发执行
		sourceName := fmt.Sprintf("testdb_queue_%d", i)
		taskID, err := suite.manager.BackupAsync(ctx, types.MySQL, sourceName, types.BackupTypeArchival, fmt.Sprintf("队列性能测试_%d", i))
		if err != nil {
			return nil, fmt.Errorf("提交任务失败 [%d]: %w", i, err)
		}
		taskIDs[i] = taskID
	}

	// 等待所有任务完成
	for i, taskID := range taskIDs {
		if err := suite.waitForTaskCompletion(taskID, 60*time.Second); err != nil {
			return nil, fmt.Errorf("等待任务完成失败 [%d]: %w", i, err)
		}
	}

	duration := time.Since(startTime)
	throughput := float64(taskCount) / duration.Seconds()

	return &PerformanceMetrics{
		Duration:       duration,
		ThroughputMBps: throughput, // 这里用作任务/秒
	}, nil
}

// waitForTaskCompletion 等待任务完成
func (suite *PerformanceRegressionTestSuite) waitForTaskCompletion(taskID string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		task, err := suite.manager.GetTask(taskID)
		if err != nil {
			return fmt.Errorf("获取任务状态失败: %w", err)
		}

		switch task.Status {
		case types.TaskStatusCompleted:
			return nil
		case types.TaskStatusFailed:
			return fmt.Errorf("任务失败: %s", task.Error)
		case types.TaskStatusCancelled:
			return fmt.Errorf("任务被取消")
		}

		time.Sleep(1 * time.Second)
	}
	return fmt.Errorf("任务超时")
}

// logPerformanceMetrics 记录性能指标
func (suite *PerformanceRegressionTestSuite) logPerformanceMetrics(testName string, metrics *PerformanceMetrics) {
	suite.logger.Info("性能指标",
		zap.String("test", testName),
		zap.Duration("duration", metrics.Duration),
		zap.Uint64("memory_usage_bytes", metrics.MemoryUsage),
		zap.Float64("cpu_usage_percent", metrics.CPUUsage),
		zap.Float64("throughput_mbps", metrics.ThroughputMBps),
		zap.Float64("error_rate_percent", metrics.ErrorRate),
	)
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: performance-regression-suite <test-type>")
		fmt.Println("测试类型:")
		fmt.Println("  concurrent          - 并发操作性能基准")
		fmt.Println("  memory              - 内存使用监控测试")
		fmt.Println("  cpu                 - CPU使用率监控测试")
		fmt.Println("  throughput          - 备份恢复吞吐量测试")
		fmt.Println("  error-rate          - 错误率监控测试")
		fmt.Println("  cleanup             - 资源清理效率测试")
		fmt.Println("  task-queue          - 任务队列性能测试")
		fmt.Println("  all                 - 运行所有测试")
		os.Exit(1)
	}

	testType := os.Args[1]

	logger, _ := zap.NewDevelopment()

	fmt.Printf("⚡ 开始性能回归测试: %s\n", testType)
	fmt.Println(strings.Repeat("=", 60))

	// 创建配置
	config := &types.Config{
		BackupRoot:         "/tests/data/backup",
		Logger:             logger,
		MaxConcurrentTasks: 5,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		MySQL: &types.MySQLConfig{
			Host:     "unibackup-test-mysql",
			Port:     3306,
			User:     "backup_user",
			Password: "backup_pass",
			DBName:   "testdb",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://unibackup-test-elasticsearch:9200"},
			ArchivalRepoName: "unibackup-test-archival",
			ManagedRepoName:  "unibackup-test-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     "/usr/share/elasticsearch/snapshots",
		},
	}

	// 创建测试套件
	suite := NewPerformanceRegressionTestSuite(logger, config)

	if err := suite.Setup(); err != nil {
		fmt.Printf("❌ 测试环境初始化失败: %v\n", err)
		os.Exit(1)
	}
	defer suite.Cleanup()

	// 根据测试类型执行测试
	var metrics *PerformanceMetrics
	var err error

	switch testType {
	case "concurrent":
		metrics, err = suite.testConcurrentOperationPerformance()
	case "memory":
		metrics, err = suite.testMemoryUsageMonitoring()
	case "cpu":
		metrics, err = suite.testCPUUsageMonitoring()
	case "throughput":
		metrics, err = suite.testBackupRestoreThroughput()
	case "error-rate":
		metrics, err = suite.testErrorRateMonitoring()
	case "cleanup":
		metrics, err = suite.testResourceCleanupEfficiency()
	case "task-queue":
		metrics, err = suite.testTaskQueuePerformance()
	case "all":
		err = suite.RunAllTests()
	default:
		fmt.Printf("❌ 未知的测试类型: %s\n", testType)
		os.Exit(1)
	}

	if err != nil {
		fmt.Printf("❌ 性能回归测试失败: %v\n", err)
		os.Exit(1)
	}

	if metrics != nil {
		suite.logPerformanceMetrics(testType, metrics)
	}

	fmt.Println("✅ 性能回归测试通过")
}
