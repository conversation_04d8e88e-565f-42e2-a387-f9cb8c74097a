# UniBackup 测试套件

本目录包含 UniBackup 项目的完整测试套件，确保代码质量和功能正确性。

## 🧪 测试概述

### 测试策略
- **单元测试**: 针对各个模块的独立功能测试
- **集成测试**: 测试模块间的协作和数据流
- **性能测试**: 基准测试和性能回归检测
- **并发测试**: 竞态条件和并发安全测试

### 测试覆盖率目标
- **总体覆盖率**: ≥ 80%
- **核心模块**: ≥ 90% (api, task, storage)
- **关键路径**: 100% (错误处理, 数据一致性)

## 📋 测试分类

### 单元测试
```
internal/
├── api/backup_manager_test.go      # API层测试
├── config/config_test.go           # 配置验证测试
├── errors/constructors_test.go     # 错误处理测试
├── lock/locker_test.go            # 锁机制测试
├── provider/                       # 提供者测试
│   ├── elasticsearch/provider_test.go
│   ├── mysql/provider_test.go
│   └── interface_test.go
├── storage/manager_test.go         # 存储管理测试
└── task/manager_test.go           # 任务管理测试
```

### 类型测试
```
pkg/types/
├── duration_test.go               # 时间类型测试
└── types_test.go                  # 核心类型测试
```

### CLI测试
```
cmd/
├── unibackup/                     # CLI工具测试
│   ├── config_test.go
│   └── main_test.go
```

## 🚀 运行测试

### 基本测试命令

```bash
# 运行所有测试
make test

# 快速测试（无详细输出）
make test-quick

# 生成覆盖率报告
make test-coverage

# 运行竞态检测
make test-race

# 运行性能基准测试
make test-bench
```

### 高级测试选项

```bash
# 测试特定模块
make test-module MODULE=pkg/types

# 测试特定函数
make test-func FUNC=TestTaskStatus

# 详细输出
go test -v ./...

# 并行测试
go test -parallel 4 ./...

# 测试超时设置
go test -timeout 30m ./...
```

## 📊 测试报告

### 自动生成报告
测试运行后会自动生成以下报告：

```
test_output/
├── coverage.html              # HTML覆盖率报告
├── test_results.txt           # 测试结果详情
├── benchmark_results.txt      # 基准测试结果
└── race_results.txt          # 竞态检测结果
```

### 查看报告
```bash
# 在浏览器中查看覆盖率
open test_output/coverage.html

# 查看测试结果
cat test_output/test_results.txt

# 查看基准测试
cat test_output/benchmark_results.txt
```

## ⚡ 性能测试

### 基准测试覆盖
- 备份操作性能
- 恢复操作性能  
- 任务管理性能
- 并发控制性能
- 存储I/O性能

### 运行基准测试
```bash
# 运行所有基准测试
make test-bench

# 运行特定基准测试
go test -bench=BenchmarkBackup ./internal/api

# 内存分析
go test -bench=. -memprofile=mem.prof ./internal/task

# CPU分析
go test -bench=. -cpuprofile=cpu.prof ./internal/storage
```

## 🔒 并发测试

### 竞态检测
```bash
# 运行竞态检测
make test-race

# 手动竞态检测
go test -race ./...

# 特定模块竞态检测
go test -race ./internal/task
```

### 并发压力测试
- 模拟高并发备份场景
- 测试锁机制有效性
- 验证任务队列稳定性
- 检查资源泄漏

## 🧩 集成测试

### 环境配置
集成测试需要真实的服务环境：

```bash
# 设置集成测试配置
export INTEGRATION_TEST=true
export MYSQL_DSN="user:password@tcp(localhost:3306)/testdb"
export ES_URL="http://localhost:9200"
```

### 运行集成测试
```bash
# 运行集成测试
go test -tags integration ./tests/integration

# 跳过集成测试
go test -short ./...
```

## 🎯 测试最佳实践

### 单元测试原则
1. **独立性**: 每个测试独立运行
2. **确定性**: 测试结果可重现
3. **快速性**: 单元测试应该快速执行
4. **清晰性**: 测试目的明确，易于理解

### 测试数据管理
```go
// 使用表驱动测试
tests := []struct {
    name     string
    input    types.Config
    expected error
}{
    {"valid config", validConfig, nil},
    {"invalid config", invalidConfig, errInvalid},
}

for _, tt := range tests {
    t.Run(tt.name, func(t *testing.T) {
        result := validate(tt.input)
        assert.Equal(t, tt.expected, result)
    })
}
```

### Mock和Stub
- 使用接口进行依赖注入
- 模拟外部服务调用
- 控制测试环境状态
- 验证交互行为

## 🔧 测试工具

### 使用的测试库
- **testify**: 断言和Mock框架
- **go-test**: Go内置测试框架
- **pprof**: 性能分析工具
- **race detector**: 竞态检测器

### 自定义测试工具
```go
// 测试助手函数
func createTestConfig() *types.Config { ... }
func setupTestEnvironment() { ... }
func cleanupTestData() { ... }
```

## 📈 持续集成

### CI/CD流程
1. **代码提交触发**
2. **运行完整测试套件**
3. **生成覆盖率报告**
4. **性能回归检测**
5. **测试结果通知**

### 质量门禁
- 所有测试必须通过
- 覆盖率不得低于设定阈值
- 无竞态条件
- 性能不得显著下降

---

📊 **当前覆盖率**: 通过 `make test-coverage` 查看最新覆盖率  
🔄 **最后更新**: 测试报告随每次运行自动更新