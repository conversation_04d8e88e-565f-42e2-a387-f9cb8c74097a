#!/bin/bash

# ES keystore设置脚本
# 用于在ES启动前设置S3认证信息到keystore

set -e

echo "🔐 设置ES keystore认证信息..."

# 检查是否需要设置keystore
if [ -n "$S3_ACCESS_KEY" ] && [ -n "$S3_SECRET_KEY" ]; then
    echo "📝 检测到S3认证环境变量，设置keystore..."
    
    # 创建keystore（如果不存在）
    if [ ! -f /usr/share/elasticsearch/config/elasticsearch.keystore ]; then
        echo "🆕 创建新的keystore..."
        elasticsearch-keystore create
    fi
    
    # 添加S3认证信息到keystore（强制覆盖已存在的设置）
    echo "🔑 添加S3 access key到keystore..."
    echo "$S3_ACCESS_KEY" | elasticsearch-keystore add --stdin --force s3.client.default.access_key

    echo "🔑 添加S3 secret key到keystore..."
    echo "$S3_SECRET_KEY" | elasticsearch-keystore add --stdin --force s3.client.default.secret_key
    
    echo "✅ keystore设置完成"
    
    # 列出keystore中的设置（不显示值）
    echo "📋 keystore中的设置："
    elasticsearch-keystore list
else
    echo "⚠️  未检测到S3认证环境变量 (S3_ACCESS_KEY, S3_SECRET_KEY)"
    echo "💡 将使用AWS环境变量或IAM角色认证"
fi

echo "🚀 启动Elasticsearch..."

# 启动ES
exec /usr/local/bin/docker-entrypoint.sh "$@"
