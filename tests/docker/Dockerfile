# 统一镜像：包含 Go + MySQL 8.0 完整工具集 + 测试环境
FROM ubuntu:22.04

# 避免交互式安装提示
ENV DEBIAN_FRONTEND=noninteractive

# 安装基础工具
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    wget \
    gnupg \
    lsb-release \
    git \
    build-essential

# 安装 MySQL 服务器（包含完整的 MySQL 工具集，包括 mysqlbinlog）
RUN apt-get update && apt-get install -y --no-install-recommends \
    mysql-server-8.0 \
    && rm -rf /var/lib/apt/lists/*

# 安装 Go 环境（官方 ARM64 二进制）
RUN curl -fsSL https://go.dev/dl/go1.23.0.linux-arm64.tar.gz -o go.tar.gz && \
    tar -C /usr/local -xzf go.tar.gz && \
    rm go.tar.gz

# 设置 Go 环境变量
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV GOPROXY="https://goproxy.cn,direct"

# 创建测试用户（UID 1000）和 GOPATH（确保权限）
RUN useradd -u 1000 -m testuser && \
    mkdir -p /go && chown -R testuser:testuser /go

# 设置工作目录并复制源代码
WORKDIR /app
COPY . .

# 构建Go应用程序
RUN CGO_ENABLED=0 GOOS=linux go build -o /usr/local/bin/unibackup-main ./cmd/unibackup/main.go

# 创建测试目录结构
RUN mkdir -p /tests/{bin,config,data,reports}

# 验证版本和关键参数支持
RUN echo "=== MySQL工具版本信息 ===" && \
    mysql --version && \
    mysqldump --version && \
    echo "=== 查找mysqlbinlog位置 ===" && \
    which mysqlbinlog || find /usr -name "mysqlbinlog" 2>/dev/null || echo "mysqlbinlog未找到"

# 验证mysqlbinlog和--skip-gtids参数支持
RUN if command -v mysqlbinlog >/dev/null 2>&1; then \
        echo "=== mysqlbinlog版本 ===" && \
        mysqlbinlog --version && \
        echo "=== 检查--skip-gtids参数支持 ===" && \
        (mysqlbinlog --help | grep -q "skip-gtids" && \
         echo "✅ 支持 --skip-gtids 参数" || \
         echo "❌ 不支持 --skip-gtids 参数"); \
    else \
        echo "❌ mysqlbinlog工具未安装"; \
    fi

RUN echo "=== Go版本 ===" && go version

# 设置测试环境变量
ENV TEST_CONFIG_PATH="/tests/config/test_config.json"
ENV TEST_REPORT_DIR="/tests/reports"

# 切换为非 root 用户
USER testuser

# 默认保持容器运行（便于进入调试环境）
CMD ["tail", "-f", "/dev/null"]