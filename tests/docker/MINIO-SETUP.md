# MinIO 独立部署指南

这个 Docker Compose 文件设置了一个独立的 MinIO 对象存储服务，可以被其他服务访问。

## 快速开始

1. **启动 MinIO 服务：**
   ```bash
   docker-compose -f docker-compose-minio.yml up -d
   ```

2. **访问 MinIO 控制台：**
   - 地址: http://localhost:9001
   - 用户名: `minioadmin`
   - 密码: `minioadmin123`

3. **MinIO API 端点：**
   - 地址: http://localhost:9000
   - 访问密钥: `minioadmin`
   - 秘密密钥: `minioadmin123`

## 其他服务连接方式

### 使用 Docker Compose
如果你想让其他服务连接到这个 MinIO 实例，将它们添加到同一个网络：

```yaml
version: '3.8'
services:
  your-app:
    image: your-app:latest
    networks:
      - unibackup_unibackup-minio-net  # 引用外部网络
    environment:
      MINIO_ENDPOINT: http://minio-storage:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123

networks:
  unibackup_unibackup-minio-net:
    external: true
```

### 从主机访问
- **本地访问端点:** `http://localhost:9000`
- **宿主机IP访问端点:** `http://[宿主机IP]:9000`
- **控制台本地访问:** `http://localhost:9001`
- **控制台宿主机IP访问:** `http://[宿主机IP]:9001`
- **访问密钥:** `minioadmin`
- **秘密密钥:** `minioadmin123`

**获取宿主机IP的方法：**
```bash
# macOS/Linux
ifconfig | grep "inet " | grep -v 127.0.0.1
# 或者
hostname -I

# Windows
ipconfig | findstr "IPv4"
```

**示例：**
如果你的宿主机IP是 `*************`，那么：
- MinIO API: `http://*************:9000`
- MinIO 控制台: `http://*************:9001`

**注意事项：**
- 通过宿主机IP访问需要确保防火墙允许 9000 和 9001 端口
- 在生产环境中，建议使用 HTTPS 和限制访问来源
- 如果需要跨网络访问，请配置适当的网络安全策略

## 网络隔离说明

为了避免与其他 Docker 项目的网络冲突，本配置使用了独立的网络 `unibackup-minio-net`。如果你需要与现有项目集成，可以：

1. **使用外部网络模式：** 将现有项目的网络设为 external，然后在这里引用
2. **修改网络名称：** 将 `unibackup-minio-net` 改为其他项目特定的名称
3. **使用默认网络：** 移除 networks 配置，使用 Docker 的默认网络

## 可用的存储桶

安装程序会自动创建以下存储桶：
- `backup-bucket` - 用于备份操作
- `test-bucket` - 用于测试（公共读取权限）
- `uploads` - 用于文件上传

## 管理命令

```bash
# 停止服务
docker-compose -f docker-compose-minio.yml down

# 停止并删除卷（警告：这会删除所有数据）
docker-compose -f docker-compose-minio.yml down -v

# 查看日志
docker-compose -f docker-compose-minio.yml logs -f

# 重启服务
docker-compose -f docker-compose-minio.yml restart

# 检查网络状态
docker network ls | grep unibackup-minio
```

## SDK 使用配置

### UniBackup SDK 连接配置

```go
config := &unibackup.Config{
    BackupRoot: "/tmp/unibackup",  // 必填：本地目录，用于路径生成和元数据
    CloudStorage: &unibackup.CloudStorageConfig{
        Enabled:   true,
        Type:      "s3",
        Bucket:    "backup-bucket",
        Region:    "us-east-1",        // 必填字段
        AccessKey: "minioadmin",
        SecretKey: "minioadmin123",
        Endpoint:  "http://localhost:9000",  // 或使用宿主机IP
    },
}
```

### 重要说明：BackupRoot 目录配置

**即使使用云存储，BackupRoot 仍然是必填字段**，但在云存储模式下它的作用与本地存储不同：

**云存储模式下的作用**：
1. **路径计算**：用于计算云存储中的对象键（key）
2. **虚拟目录结构**：为云存储中的数据提供逻辑组织结构
3. **系统文件存储**：**所有文件都存储在云存储中**，包括 `tasks.json`

**重要纠正**：
- 在云存储模式下，`tasks.json` 也存储在云存储中，不是本地
- `BackupRoot` 不需要在本地存在，它只是一个路径前缀
- 但为了兼容性和配置验证，建议使用存在的目录

**配置建议**：
```bash
# 为了避免“存储目录不存在”错误，创建一个空目录
mkdir -p /tmp/unibackup

# 或者使用已存在的目录
# BackupRoot: "/tmp"  # 使用已存在的目录
```

**云存储中的实际存储结构**：
```
backup_root: "/tmp/unibackup"

云存储中的文件：
- backup-bucket/tasks.json                           # 任务状态文件
- backup-bucket/mysql/archival/backup-123/data.sql.gz  # 备份数据
- backup-bucket/mysql/archival/backup-123/metadata.json # 备份元数据
```

### Elasticsearch 云存储认证配置

如果你的应用中有 Elasticsearch 需要连接到这个 MinIO 作为快照存储，需要在 ES 容器中执行以下命令：

```bash
# 进入 ES 容器
docker exec -it your-es-container-name /bin/bash

# 添加 S3 认证信息到 keystore
echo "minioadmin" | /usr/share/elasticsearch/bin/elasticsearch-keystore add --stdin s3.client.default.access_key
echo "minioadmin123" | /usr/share/elasticsearch/bin/elasticsearch-keystore add --stdin s3.client.default.secret_key

# 重启 ES 服务使配置生效
exit
docker restart your-es-container-name
```

### ES 快照仓库配置

在 ES 中创建 S3 快照仓库：

```bash
# 创建快照仓库
curl -X PUT "localhost:9200/_snapshot/minio-backup" -H 'Content-Type: application/json' -d'
{
  "type": "s3",
  "settings": {
    "bucket": "backup-bucket",
    "endpoint": "http://minio-storage:9000",
    "protocol": "http",
    "path_style_access": true
  }
}'
```

**注意：** 如果 ES 和 MinIO 不在同一 Docker 网络中，将 `minio-storage` 替换为实际的 MinIO 访问地址。

## 安全注意事项

- **生产环境请更改默认凭据**
- `test-bucket` 具有公共读取权限 - 根据需要修改
- 生产环境建议使用环境文件存储敏感数据
- 考虑启用 HTTPS 和访问控制策略
