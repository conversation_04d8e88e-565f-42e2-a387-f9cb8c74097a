# Elasticsearch with S3 repository plugin for testing
FROM docker.eustis.top/library/elasticsearch:7.17.26

# 安装repository-s3插件以支持云存储测试
RUN elasticsearch-plugin install --batch repository-s3

# 确保插件安装成功
RUN elasticsearch-plugin list | grep repository-s3

# 创建启动脚本，用于设置keystore认证
COPY setup-keystore.sh /usr/local/bin/setup-keystore.sh
RUN chmod +x /usr/local/bin/setup-keystore.sh

# 使用自定义启动脚本
ENTRYPOINT ["/usr/local/bin/setup-keystore.sh"]
