version: '3.8'

services:
  mysql:
    image: docker.eustis.top/library/mysql:8.0.38
    container_name: unibackup-test-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: testdb
      MYSQL_USER: backup_user
      MYSQL_PASSWORD: backup_pass
    ports:
      - "3307:3306"  # 使用不同端口避免冲突
    volumes:
      - mysql_test_data:/var/lib/mysql
      - mysql_test_binlogs:/var/lib/mysql-binlogs  # 专用binlog数据卷
      - ../data/mysql_init.sql:/docker-entrypoint-initdb.d/init.sql
    command: >
      bash -c "
      mkdir -p /var/lib/mysql-binlogs &&
      chown mysql:mysql /var/lib/mysql-binlogs &&
      exec docker-entrypoint.sh mysqld
      --log-bin=/var/lib/mysql-binlogs/mysql-bin
      --server-id=1
      --binlog-format=ROW
      --gtid-mode=ON
      --enforce-gtid-consistency=ON
      --max-binlog-size=100M
      --expire-logs-days=7
      --binlog-expire-logs-seconds=604800
      "
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      timeout: 5s
      retries: 10
      interval: 10s
      start_period: 30s
    networks:
      - unibackup-test-net
    restart: unless-stopped

  elasticsearch:
    build:
      context: .
      dockerfile: Dockerfile.elasticsearch
    container_name: unibackup-test-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - path.repo=/usr/share/elasticsearch/snapshots
      # S3认证信息（方式2：keystore认证）
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin123
    ports:
      - "9201:9200"  # 使用不同端口避免冲突
    volumes:
      - es_test_data:/usr/share/elasticsearch/data
      - ../data/backup/es_snapshots:/usr/share/elasticsearch/snapshots
    user: "1000:1000"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      timeout: 10s
      retries: 10
      interval: 10s
      start_period: 30s
    networks:
      - unibackup-test-net
    restart: unless-stopped

  es-init:
    image: curlimages/curl:7.85.0
    container_name: unibackup-test-es-init
    depends_on:
      elasticsearch:
        condition: service_healthy
    volumes:
      - ../data/es_init.sh:/es_init.sh
    networks:
      - unibackup-test-net
    command: sh /es_init.sh
    restart: "no"

  minio:
    image: minio/minio:latest
    container_name: unibackup-test-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_test_data:/data
    networks:
      - unibackup-test-net
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      timeout: 5s
      retries: 10
      interval: 10s
      start_period: 30s
    restart: unless-stopped

  minio-init:
    image: minio/mc:latest
    container_name: unibackup-test-minio-init
    depends_on:
      minio:
        condition: service_healthy
    networks:
      - unibackup-test-net
    entrypoint: >
      /bin/sh -c "
      echo 'Configuring MinIO...';
      mc alias set minio http://minio:9000 minioadmin minioadmin123;
      echo 'Creating test buckets...';
      mc mb minio/test-backup-bucket --ignore-existing;
      mc mb minio/test-gcs-bucket --ignore-existing;
      mc mb minio/test-azure-container --ignore-existing;
      mc mb minio/unibackup-test-bucket --ignore-existing;
      echo 'Setting bucket policies...';
      mc anonymous set public minio/unibackup-test-bucket;
      echo 'MinIO buckets created and configured successfully';
      mc ls minio/;
      "
    restart: "no"

  unibackup-test:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile
    container_name: unibackup-test-runner
    user: "999:999"  # 使用与MySQL相同的UID/GID
    volumes:
      - ../../:/app
      - ../bin:/tests/bin
      - ../config:/tests/config
      - ../data:/tests/data
      - ../reports:/tests/reports
      - ../data/backup:/tests/data/backup
      - mysql_test_binlogs:/tests/mysql-binlogs:ro  # 只读挂载binlog目录
    depends_on:
      mysql:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-init:
        condition: service_completed_successfully  # 确保存储桶已创建
    networks:
      - unibackup-test-net
    environment:
      # MySQL 配置
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=backup_user
      - MYSQL_PASSWORD=backup_pass
      - MYSQL_DATABASE=testdb
      # Elasticsearch 配置
      - ES_HOST=elasticsearch
      - ES_PORT=9200
      # MinIO/S3 配置
      - MINIO_ENDPOINT=http://minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      # AWS S3 兼容认证（用于云存储测试）
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin123
      - AWS_REGION=us-east-1
      - AWS_ENDPOINT_URL=http://minio:9000
      - AWS_S3_FORCE_PATH_STYLE=true
      # GCS 配置（如果需要真实GCS测试）
      # - GOOGLE_APPLICATION_CREDENTIALS=/tests/config/gcs-credentials.json
      # - GOOGLE_CLOUD_PROJECT=test-project
      # Azure 配置（如果需要真实Azure测试）
      # - AZURE_STORAGE_ACCOUNT=testaccount
      # - AZURE_STORAGE_KEY=testkey
      # 测试环境标识
      - UNIBACKUP_TEST_ENV=docker
      - UNIBACKUP_LOG_LEVEL=info
    restart: unless-stopped
    command: tail -f /dev/null

volumes:
  mysql_test_data:
    name: unibackup_mysql_test_data
  mysql_test_binlogs:
    name: unibackup_mysql_test_binlogs
  es_test_data:
    name: unibackup_es_test_data
  minio_test_data:
    name: unibackup_minio_test_data

networks:
  unibackup-test-net:
    name: unibackup-test-network
    driver: bridge