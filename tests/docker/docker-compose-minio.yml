version: '3.8'

services:
  minio:
    image: minio/minio:latest
    container_name: minio-storage
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
      MINIO_CONSOLE_ADDRESS: ":9001"
    ports:
      - "9000:9000"   # MinIO API port
      - "9001:9001"   # MinIO Console port
    volumes:
      - minio_data:/data
    networks:
      - unibackup-minio-net
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      timeout: 5s
      retries: 10
      interval: 10s
      start_period: 30s
    restart: unless-stopped

  minio-init:
    image: minio/mc:latest
    container_name: minio-init
    depends_on:
      minio:
        condition: service_healthy
    networks:
      - unibackup-minio-net
    entrypoint: >
      /bin/sh -c "
      echo 'Waiting for MinIO to be ready...';
      sleep 5;
      echo 'Configuring MinIO...';
      mc alias set minio http://minio:9000 minioadmin minioadmin123;
      echo 'Creating buckets...';
      mc mb minio/backup-bucket --ignore-existing;
      mc mb minio/test-bucket --ignore-existing;
      mc mb minio/uploads --ignore-existing;
      echo 'Setting bucket policies (optional - for public access)...';
      mc anonymous set download minio/test-bucket;
      echo 'MinIO setup completed successfully';
      mc ls minio/;
      echo 'Available buckets:';
      mc ls minio/ --recursive;
      "
    restart: "no"

volumes:
  minio_data:
    driver: local

networks:
  unibackup-minio-net:
    driver: bridge
    external: false
