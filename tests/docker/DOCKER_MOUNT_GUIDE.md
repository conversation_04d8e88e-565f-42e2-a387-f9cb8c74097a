# Docker挂载模式使用指南

## 概述

UniBackup Docker挂载模式通过Docker容器卷挂载实现MySQL binlog文件的直接访问，提供最佳性能的增量备份解决方案。此模式特别适用于生产环境中对备份性能要求较高的场景。

## ⚠️ 重要：Binlog保留策略配置

**增量备份的核心依赖**：增量备份依赖binlog文件的连续性，如果binlog文件被MySQL自动清理，会导致增量备份失败。

### 必需配置

在MySQL容器启动时必须配置适当的binlog保留策略：

```yaml
mysql:
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin
    --binlog-format=ROW
    --server-id=1
    --expire-logs-days=7                    # 关键：保留7天binlog
    --max-binlog-size=1G                    # 控制文件大小
```

### 保留时间建议

根据备份频率调整保留时间：
- **每日增量备份**：保留7-14天
- **每小时增量备份**：保留3-7天
- **高频增量备份**：保留1-3天
- **生产环境**：建议保留14天以上

### 常见错误

❌ **错误配置示例**：
```yaml
# 危险：没有配置binlog保留策略
mysql:
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin
    --binlog-format=ROW
    # 缺少 expire-logs-days 配置
```

**结果**：MySQL使用默认保留策略（通常很短），导致增量备份频繁失败。

✅ **正确配置示例**：
```yaml
# 安全：明确配置binlog保留策略
mysql:
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin
    --binlog-format=ROW
    --server-id=1
    --expire-logs-days=7                    # 明确保留7天
    --max-binlog-size=1G
```

## 🔧 智能备份链管理

UniBackup v2.0+ 引入了智能的备份链管理功能，能够自动处理常见的binlog问题：

### 自动检测系统重装
当检测到以下情况时，系统会自动重置备份链：
- binlog序号大幅回退（如从000123回到000001）
- 系统重装或MySQL重新安装

### 自动处理流程
```
检测到系统重装
    ↓
保留旧备份链（用于恢复重装前数据）
    ↓
创建新备份链
    ↓
自动执行全量备份作为新起点
    ↓
记录重置原因和时间戳
```

### 备份链查看
```bash
# 查看所有备份链
docker exec unibackup-container unibackup list-chains --database mydb

# 输出示例：
# 备份链 chain-20250120: 2025-01-20 ~ 2025-01-21 (断裂 - 系统重装)
# 备份链 chain-20250121: 2025-01-21 ~ 现在 (活跃)
```

### 恢复选择
```bash
# 恢复到重装前的数据
docker exec unibackup-container unibackup restore \
  --chain-id chain-20250120 \
  --point-in-time "2025-01-21T02:00:00Z"

# 恢复到重装后的数据
docker exec unibackup-container unibackup restore \
  --chain-id chain-20250121 \
  --point-in-time "2025-01-21T15:00:00Z"
```

## 架构说明

### 核心原理
- **Volume挂载**: MySQL容器和UniBackup容器共享同一个Docker卷
- **直接文件访问**: UniBackup通过文件系统直接读取binlog文件
- **智能检测**: 自动检测是否可以使用直接文件访问模式
- **自动降级**: 当文件访问不可用时，自动切换到网络流式模式

### 容器架构
```
MySQL容器 (unibackup-test-mysql)
├── /var/lib/mysql-binlogs (binlog专用卷)
└── /var/lib/mysql (数据卷)

UniBackup容器 (unibackup-test)
└── /tests/mysql-binlogs (只读挂载binlog卷)
```

## 快速开始

### 1. 启动Docker测试环境

```bash
# 启动完整的测试环境
make test-env-up

# 检查服务状态
make test-env-status
```

### 2. 运行挂载模式测试

```bash
# 运行Docker挂载模式测试
make test-docker

# 查看详细日志
make test-env-logs
```

### 3. 停止测试环境

```bash
# 停止服务
make test-env-down

# 完整清理（包括数据卷）
make test-env-clean
```

## 配置详解

### Docker Compose配置

#### MySQL服务配置
```yaml
mysql:
  image: docker.eustis.top/library/mysql:8.0.38
  container_name: unibackup-test-mysql
  ports:
    - "3306:3306"
  environment:
    MYSQL_ROOT_PASSWORD: rootpass
    MYSQL_DATABASE: testdb
    MYSQL_USER: backup_user
    MYSQL_PASSWORD: backup_pass
  volumes:
    - mysql_test_data:/var/lib/mysql           # 数据卷
    - mysql_test_binlogs:/var/lib/mysql-binlogs # binlog专用卷
    - ./mysql/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin  # binlog存储路径
    --server-id=1
    --binlog-format=ROW
    --sync-binlog=1
    --innodb-flush-log-at-trx-commit=1
  networks:
    - unibackup-test
```

#### UniBackup测试容器配置
```yaml
unibackup-test:
  image: docker.eustis.top/library/golang:1.22-alpine
  container_name: unibackup-test
  volumes:
    - mysql_test_binlogs:/tests/mysql-binlogs:ro  # 只读挂载binlog卷
    - ./../../:/workspace:rw                      # 项目代码
    - test_backup_data:/tests/data:rw             # 备份数据存储
  working_dir: /workspace
  depends_on:
    - mysql
    - elasticsearch
  networks:
    - unibackup-test
```

#### 关键卷定义
```yaml
volumes:
  mysql_test_data:        # MySQL数据存储
  mysql_test_binlogs:     # MySQL binlog专用卷
  test_backup_data:       # 备份文件存储
  elasticsearch_data:     # Elasticsearch数据存储
```

### UniBackup配置

#### 挂载模式配置文件 (test_config_binlog_mount.json)
```json
{
  "backup_root": "/tests/data/backup",
  "mysql": {
    "host": "unibackup-test-mysql",
    "port": 3306,
    "user": "backup_user", 
    "password": "backup_pass",
    "db_name": "testdb",
    "binlog_base_path": "/tests/mysql-binlogs",  // 挂载的binlog路径
    "tools_path": {
      "mysqldump": "/usr/local/bin/mysqldump",
      "mysql": "/usr/local/bin/mysql",
      "mysqlbinlog": "/usr/local/bin/mysqlbinlog",  // 必需：直接文件访问工具
      "mysqladmin": "/usr/local/bin/mysqladmin"
    }
  }
}
```

## 生产环境部署

### 1. 生产级Docker Compose配置

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql-prod
    restart: always
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_binlogs:/var/lib/mysql-binlogs
      - /etc/mysql/conf.d:/etc/mysql/conf.d:ro
    command: >
      --log-bin=/var/lib/mysql-binlogs/mysql-bin
      --server-id=1
      --binlog-format=ROW
      --sync-binlog=1
      --innodb-flush-log-at-trx-commit=1
      --binlog-expire-logs-seconds=604800
      --max-binlog-size=1G
    networks:
      - backup-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  unibackup:
    image: unibackup:latest
    container_name: unibackup-prod
    restart: always
    volumes:
      - mysql_binlogs:/opt/mysql-binlogs:ro
      - backup_data:/opt/backup:rw
      - ./config:/opt/config:ro
    environment:
      - CONFIG_PATH=/opt/config/production.json
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - backup-network

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/mysql
  mysql_binlogs:
    driver: local
    driver_opts:
      type: none
      o: bind  
      device: /data/mysql-binlogs
  backup_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /data/backup

networks:
  backup-network:
    driver: bridge
```

### 2. 生产环境配置

```json
{
  "backup_root": "/opt/backup",
  "logger": {
    "level": "info",
    "file_path": "/opt/logs/unibackup.log",
    "max_size": 100,
    "max_age": 30,
    "max_backups": 10
  },
  "max_concurrent_tasks": 5,
  "task_retention_days": 30,
  "max_task_history": 1000,
  "backup_timeout": "2h",
  "restore_timeout": "4h",
  "mysql": {
    "host": "mysql-prod",
    "port": 3306,
    "user": "backup_user",
    "password": "${MYSQL_BACKUP_PASSWORD}",
    "db_name": "production_db",
    "binlog_base_path": "/opt/mysql-binlogs",
    "tools_path": {
      "mysqldump": "/usr/bin/mysqldump",
      "mysql": "/usr/bin/mysql", 
      "mysqlbinlog": "/usr/bin/mysqlbinlog",
      "mysqladmin": "/usr/bin/mysqladmin"
    }
  }
}
```

## 智能模式检测

UniBackup会自动检测并选择最佳的备份模式：

### 检测流程
1. **工具检查**: 验证mysqlbinlog工具是否可用
2. **路径检查**: 检查binlog_base_path配置
3. **文件访问测试**: 尝试访问最新的binlog文件
4. **权限验证**: 确认读取权限

### 模式选择逻辑
```
配置了mysqlbinlog工具路径？
├─ 是 → 工具可执行？
│   ├─ 是 → binlog文件可访问？
│   │   ├─ 是 → 直接文件访问模式 (最佳性能)
│   │   └─ 否 → 网络流式模式 (降级)
│   └─ 否 → 网络流式模式
└─ 否 → 网络流式模式
```

## 性能优化

### MySQL配置优化
```ini
# /etc/mysql/conf.d/binlog.cnf
[mysqld]
# binlog配置
log-bin=/var/lib/mysql-binlogs/mysql-bin
server-id=1
binlog-format=ROW
sync-binlog=1
binlog-expire-logs-seconds=604800  # 7天
max-binlog-size=1G

# 性能优化
innodb-flush-log-at-trx-commit=1
innodb-log-file-size=256M
innodb-log-files-in-group=2
```

### Docker优化
```yaml
# 性能优化配置
services:
  mysql:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    tmpfs:
      - /tmp
      - /var/tmp
    volumes:
      - type: volume
        source: mysql_binlogs
        target: /var/lib/mysql-binlogs
        volume:
          nocopy: true
```

## 故障排除

### 常见问题

#### 1. binlog文件访问失败
**症状**: 日志显示"使用网络流式模式（降级方案）"

**解决方案**:
```bash
# 检查卷挂载
docker volume inspect mysql_test_binlogs

# 检查容器内挂载点
docker exec unibackup-test ls -la /tests/mysql-binlogs

# 检查MySQL容器binlog配置
docker exec unibackup-test-mysql ls -la /var/lib/mysql-binlogs
```

#### 2. 权限问题
**症状**: "Permission denied" 错误

**解决方案**:
```bash
# 检查文件权限
docker exec unibackup-test-mysql ls -la /var/lib/mysql-binlogs

# 修改权限（如果需要）
docker exec unibackup-test-mysql chown mysql:mysql /var/lib/mysql-binlogs
docker exec unibackup-test-mysql chmod 755 /var/lib/mysql-binlogs
```

#### 3. 工具路径问题
**症状**: "mysqlbinlog工具不可执行"

**解决方案**:
```bash
# 检查工具是否存在
docker exec unibackup-test which mysqlbinlog

# 安装MySQL客户端工具
docker exec unibackup-test apk add --no-cache mysql-client
```

### 调试模式

启用详细日志进行调试：
```json
{
  "logger": {
    "level": "debug",
    "enable_console": true
  }
}
```

### 监控和日志

#### 关键日志信息
- `检测到binlog访问模式: direct` - 成功使用直接文件访问
- `检测到binlog访问模式: stream` - 降级到网络流式模式
- `找到可访问的binlog路径` - binlog路径检测成功

#### 性能监控
```bash
# 监控容器资源使用
docker stats unibackup-test-mysql unibackup-test

# 监控备份性能
docker exec unibackup-test tail -f /opt/logs/unibackup.log | grep "备份成功完成"
```

## 最佳实践

### 1. 数据持久化
- 使用命名卷或绑定挂载确保数据持久化
- 定期备份Docker卷
- 配置合适的binlog保留策略

### 2. 安全考虑
- 使用环境变量管理敏感信息
- 限制容器权限
- 定期更新镜像版本

### 3. 监控和运维
- 配置健康检查
- 设置日志轮转
- 监控磁盘空间使用

### 4. 备份策略
- 结合全量备份和增量备份
- 定期测试恢复流程
- 配置备份保留策略

## 参考资料

- [Docker Compose官方文档](https://docs.docker.com/compose/)
- [MySQL Docker镜像文档](https://hub.docker.com/_/mysql)
- [MySQL Binlog配置参考](https://dev.mysql.com/doc/refman/8.0/en/replication-options-binary-log.html)
- [UniBackup项目文档](../../../README.md)

## 技术支持

如遇到问题，请检查：
1. Docker和Docker Compose版本兼容性
2. 系统资源是否充足
3. 网络连接是否正常
4. 配置文件语法是否正确

更多技术支持，请参考项目Issues或联系维护团队。