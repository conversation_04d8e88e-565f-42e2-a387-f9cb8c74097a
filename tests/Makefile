# UniBackup 测试套件 Makefile

.PHONY: test-build test-env-up test-env-down test-all test-basic test-backup test-restore test-clean \
        mysql-test-all es-test-all group-test-all data-validation-comprehensive \
        storage-backend-consistency storage-failure-recovery storage-performance-comparison storage-test-all \
        security-test-all boundary-test-all enhanced-concurrency-test-all \
        cloud-storage-test-all cloud-storage-unit-test cloud-storage-integration-test cloud-storage-config-test \
        dynamic-config-test dynamic-config-minimal-test dynamic-config-mysql-test dynamic-config-es-test \
        dynamic-config-backuproot-test dynamic-config-sync-test dynamic-config-group-backup-test \
        dynamic-config-group-restore-test dynamic-config-group-delete-test dynamic-config-interface-test \
        dynamic-config-comprehensive-test \
        cloud-storage-failure-test cloud-storage-failure-network-test cloud-storage-failure-auth-test \
        enhanced-data-validation-test enhanced-data-validation-mysql-test enhanced-data-validation-es-test \
        enhanced-data-validation-checksum-test performance-regression-test performance-regression-concurrent-test \
        performance-regression-memory-test performance-regression-throughput-test enhanced-test-all enhanced-test-quick \
        test-matrix-validation test-matrix-mysql-local test-matrix-mysql-cloud test-matrix-es-local \
        test-matrix-es-cloud test-matrix-group-operations test-matrix-boundary-cases test-coverage-validation \
        rollback-test-single rollback-test-parallel rollback-test-serial rollback-test-config \
        rollback-test-failure rollback-test-cleanup rollback-test-comprehensive rollback-test-all \
        rollback-test-quick rollback-test-full \
        help

# 构建测试二进制文件
test-build:
	@echo "🔨 构建测试二进制文件..."
	@mkdir -p bin
	@GOOS=linux GOARCH=amd64 go build -o bin/test-suite integration/test_suite_enhanced.go
	@GOOS=linux GOARCH=amd64 go build -o bin/mysql-unified-test integration/mysql_unified_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/es-unified-test integration/es_unified_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/group-unified-test integration/group_unified_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/data-validation-test integration/data_validation_suite.go

	@GOOS=linux GOARCH=amd64 go build -o bin/security-test integration/security_test_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/boundary-test integration/boundary_test_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/enhanced-concurrency-test integration/enhanced_concurrency_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/data-integrity-test integration/data_integrity_test_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/disaster-recovery-test integration/disaster_recovery_test_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/configuration-test integration/configuration_test_suite.go

	@GOOS=linux GOARCH=amd64 go build -o bin/cloud-storage-test integration/cloud_storage_main.go integration/cloud_storage_unit.go integration/cloud_storage_integration.go integration/cloud_storage_config.go
	@GOOS=linux GOARCH=amd64 go build -o bin/cloud-storage-comprehensive-test integration/cloud_storage_comprehensive.go
	@GOOS=linux GOARCH=amd64 go build -o bin/enhanced-comprehensive-test integration/enhanced_comprehensive_test_main.go integration/database_structure_validation_suite.go integration/large_data_volume_test_suite.go integration/cross_cloud_compatibility_test_suite.go integration/precise_point_in_time_recovery_test_suite.go integration/concurrent_consistency_test_suite.go
	@cd .. && GOOS=linux GOARCH=amd64 go build -o tests/bin/dynamic-config-test tests/integration/dynamic_config_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/cloud-storage-failure-test integration/cloud_storage_failure_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/backend-consistency-test integration/backend_consistency_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/cloud-storage-connectivity-test integration/cloud_storage_connectivity_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/enhanced-concurrency-test integration/enhanced_concurrency_suite.go

	@GOOS=linux GOARCH=amd64 go build -o bin/performance-regression-test integration/performance_regression_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/test-matrix-validation integration/test_matrix_validation_suite.go
	@GOOS=linux GOARCH=amd64 go build -o bin/rollback-integration-test integration/rollback_integration_suite.go
	@echo "✅ 测试二进制文件构建完成"

# 启动测试环境
test-env-up:
	@echo "🚀 启动测试环境..."
	@mkdir -p data/backup/es_snapshots
	@cd docker && docker-compose up -d
	@echo "⏳ 等待服务启动..."
	@sleep 30
	@echo "✅ 测试环境已启动"

# 停止测试环境
test-env-down:
	@echo "🛑 停止测试环境..."
	@cd docker && docker-compose down
	@echo "✅ 测试环境已停止"

# 清理测试环境
test-clean: test-env-down
	@echo "🧹 清理测试文件..."
	@cd docker && docker-compose down -v
	@rm -rf data/backup/*
	@rm -rf reports/*
	@rm -rf bin/*
	@echo "✅ 测试环境已清理"

# 运行所有测试
test-all: test-build
	@echo "🧪 运行所有测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite all

# 运行基础测试
test-basic: test-build
	@echo "🧪 运行基础测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite basic

# 运行备份测试
test-backup: test-build
	@echo "🧪 运行备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite backup

# 运行恢复测试
test-restore: test-build
	@echo "🧪 运行恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite restore

# 运行并发测试
test-concurrency: test-build
	@echo "🧪 运行并发测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite concurrency

# 运行错误处理测试
test-error: test-build
	@echo "🧪 运行错误处理测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite error

# 运行性能测试
test-performance: test-build
	@echo "🧪 运行性能测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-suite performance

# 查看测试日志
test-logs:
	@cd docker && docker-compose logs -f unibackup-test

# 进入测试容器
test-shell:
	@cd docker && docker-compose exec unibackup-test /bin/bash

# MySQL统一测试
mysql-test-full: test-build
	@echo "🧪 运行MySQL全量备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test full-backup

mysql-test-incremental: test-build
	@echo "🧪 运行MySQL增量备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test incremental-backup

mysql-test-list: test-build
	@echo "🧪 运行MySQL备份列表测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test backup-list

mysql-test-delete: test-build
	@echo "🧪 运行MySQL备份删除测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test backup-delete

mysql-test-tasks: test-build
	@echo "🧪 运行MySQL任务管理测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test task-management

mysql-test-restore: test-build
	@echo "🧪 运行MySQL恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test restore

mysql-test-comprehensive: test-build
	@echo "🧪 运行MySQL综合测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test comprehensive

mysql-test-all: test-build
	@echo "🧪 运行MySQL所有测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test all

# ES统一测试
es-test-snapshot: test-build
	@echo "🧪 运行ES快照备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test snapshot-backup

es-test-incremental: test-build
	@echo "🧪 运行ES增量备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test incremental-backup

es-test-list: test-build
	@echo "🧪 运行ES备份列表测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test backup-list

es-test-delete: test-build
	@echo "🧪 运行ES备份删除测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test backup-delete

es-test-tasks: test-build
	@echo "🧪 运行ES任务管理测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test task-management

es-test-restore: test-build
	@echo "🧪 运行ES恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test restore

es-test-comprehensive: test-build
	@echo "🧪 运行ES综合测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test comprehensive

es-test-all: test-build
	@echo "🧪 运行ES所有测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test all

# 分组测试
group-test-archival-backup: test-build
	@echo "🧪 运行分组归档备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-archival-backup

group-test-incremental-backup: test-build
	@echo "🧪 运行分组增量备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-incremental-backup

group-test-restore: test-build
	@echo "🧪 运行分组恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-restore

group-test-task-management: test-build
	@echo "🧪 运行分组任务管理测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-task-management

group-test-backup-list: test-build
	@echo "🧪 运行分组备份列表测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-backup-list

group-test-atomic-operations: test-build
	@echo "🧪 运行原子操作测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-atomic-operations

group-test-failure-scenarios: test-build
	@echo "🧪 运行失败场景测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test group-failure-scenarios

group-test-comprehensive: test-build
	@echo "🧪 运行分组综合测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test comprehensive

group-test-all: test-build
	@echo "🧪 运行所有分组测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test all

# 数据验证和错误场景测试
data-validation-integrity: test-build
	@echo "🔍 运行数据完整性验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test data-integrity

data-validation-wrong-backup-id: test-build
	@echo "🧪 运行错误备份ID场景测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test wrong-backup-id

data-validation-incremental-errors: test-build
	@echo "🧪 运行增量链错误场景测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test incremental-chain-errors

data-validation-cross-source: test-build
	@echo "🧪 运行跨数据源错误测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test cross-source-errors

data-validation-consistency: test-build
	@echo "🧪 运行数据一致性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test data-consistency

data-validation-advanced-errors: test-build
	@echo "🚨 运行高级错误场景测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test advanced-error-scenarios

data-validation-multi-point: test-build
	@echo "⏰ 运行多时间点恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test multi-point-restore

data-validation-incremental-vs-archival: test-build
	@echo "📈 运行增量vs全量备份数据差异测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test incremental-vs-archival

data-validation-comprehensive: test-build
	@echo "🔍 运行综合数据验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test comprehensive

# 完整功能测试（包含时间点恢复）
test-full-validation: test-build
	@echo "🎯 运行完整功能验证测试..."
	@echo "包括：MySQL、ES、分组功能、数据验证和时间点恢复测试"
	@cd docker && docker-compose exec unibackup-test /tests/bin/mysql-unified-test comprehensive
	@echo ""
	@cd docker && docker-compose exec unibackup-test /tests/bin/es-unified-test comprehensive  
	@echo ""
	@cd docker && docker-compose exec unibackup-test /tests/bin/group-unified-test comprehensive
	@echo ""
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-validation-test comprehensive

# 快速测试 - 重启环境并运行基础测试
test-quick: test-clean test-env-up test-basic
	@echo "✅ 快速测试完成"

# MySQL快速测试
mysql-test-quick: test-clean test-env-up mysql-test-comprehensive
	@echo "✅ MySQL快速测试完成"

# ES快速测试
es-test-quick: test-clean test-env-up es-test-comprehensive
	@echo "✅ ES快速测试完成"

# 分组功能快速测试
group-test-quick: test-clean test-env-up group-test-comprehensive
	@echo "✅ 分组功能快速测试完成"

# 数据验证快速测试
data-validation-quick: test-clean test-env-up data-validation-comprehensive
	@echo "✅ 数据验证快速测试完成"

# 超级综合测试（包含所有功能）
test-super-comprehensive: test-clean test-env-up test-full-validation
	@echo "✅ 超级综合测试完成"



# 云存储专项测试
cloud-storage-test-all: test-build
	@echo "☁️ 运行所有云存储测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-test all

cloud-storage-unit-test: test-build
	@echo "🧪 运行云存储单元测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-test unit

cloud-storage-integration-test: test-build
	@echo "🔗 运行云存储集成测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-test integration

cloud-storage-config-test: test-build
	@echo "⚙️ 运行云存储配置测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-test config

# 存储抽象化专项测试
storage-backend-consistency: test-build
	@echo "🔧 运行Backend接口一致性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/backend-consistency-test all

# 云存储连通性测试
cloud-storage-connectivity-test: test-build
	@echo "🔗 运行云存储连通性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-connectivity-test all

cloud-storage-connectivity-s3: test-build
	@echo "🔗 运行S3连通性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-connectivity-test s3

cloud-storage-connectivity-invalid: test-build
	@echo "🔗 运行无效配置测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-connectivity-test invalid

cloud-storage-connectivity-timeout: test-build
	@echo "🔗 运行超时测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-connectivity-test timeout

# 并发控制测试
concurrency-control-test: test-build
	@echo "🔄 运行并发控制测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/concurrency-control-test all

concurrency-control-full-conflict: test-build
	@echo "🔄 运行全量备份冲突测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/concurrency-control-test full-conflict

concurrency-control-inc-conflict: test-build
	@echo "🔄 运行增量备份冲突测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/concurrency-control-test inc-conflict

concurrency-control-full-inc-allow: test-build
	@echo "🔄 运行全量+增量允许测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/concurrency-control-test full-inc-allow

concurrency-control-group-backup: test-build
	@echo "🔄 运行分组备份测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/concurrency-control-test group-backup

storage-failure-recovery:
	@echo "⚠️ 存储层故障恢复测试暂时不可用"
	@echo "相关测试文件不存在，需要重新实现"

storage-performance-comparison:
	@echo "⚠️ 存储层性能对比测试暂时不可用"
	@echo "相关测试文件不存在，需要重新实现"

storage-test-all: storage-backend-consistency storage-failure-recovery storage-performance-comparison
	@echo "✅ 存储抽象化专项测试完成"

# 安全性测试
security-test-all: test-build
	@echo "🔒 运行安全性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/security-test all

# 边界条件和极限测试
boundary-test-all: test-build
	@echo "🔧 运行边界条件和极限测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/boundary-test all

# 增强并发测试
enhanced-concurrency-test-all: test-build
	@echo "⚡ 运行增强并发测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-concurrency-test all

# 数据完整性测试
data-integrity-test-all: test-build
	@echo "🔍 运行数据完整性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/data-integrity-test all

# 灾难恢复测试
disaster-recovery-test-all: test-build
	@echo "🛡️ 运行灾难恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/disaster-recovery-test all

# 配置测试
configuration-test-all: test-build
	@echo "⚙️ 运行配置测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/configuration-test all



# 云存储全面正确性验证测试（自动化版本）
cloud-storage-comprehensive-test: test-build
	@echo "☁️ 运行云存储功能全面正确性验证测试（自动化配置）..."
	@echo "📋 环境变量已自动配置，MinIO存储桶已自动创建"
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-comprehensive-test /tests/config/test_config_cloud_storage_comprehensive.json

# 云存储全面验证测试（指定配置）
cloud-storage-comprehensive-test-s3: test-build
	@echo "☁️ 运行 S3 云存储全面验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-comprehensive-test /tests/config/test_config_s3.json

cloud-storage-comprehensive-test-gcs: test-build
	@echo "☁️ 运行 GCS 云存储全面验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-comprehensive-test /tests/config/test_config_gcs.json

cloud-storage-comprehensive-test-azure: test-build
	@echo "☁️ 运行 Azure 云存储全面验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-comprehensive-test /tests/config/test_config_azure.json

# 云存储全面验证测试（所有类型）
cloud-storage-comprehensive-test-all: cloud-storage-comprehensive-test-s3 cloud-storage-comprehensive-test-gcs cloud-storage-comprehensive-test-azure
	@echo "✅ 所有云存储类型的全面验证测试完成"

# 快速云存储测试（推荐使用）
cloud-test-quick: test-build
	@echo "🚀 快速云存储功能验证测试..."
	@echo "📋 使用自动配置的MinIO环境"

# ========================================
# 增强测试套件
# ========================================

# 数据库结构验证测试
enhanced-database-structure-test: test-build
	@echo "🏗️ 运行数据库结构验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-comprehensive-test database-structure

# 大数据量测试
enhanced-large-volume-test: test-build
	@echo "📊 运行大数据量测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-comprehensive-test large-volume

# 跨云存储兼容性测试
enhanced-cross-cloud-test: test-build
	@echo "🌐 运行跨云存储兼容性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-comprehensive-test cross-cloud

# 精确时间点恢复测试
enhanced-precise-recovery-test: test-build
	@echo "⏰ 运行精确时间点恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-comprehensive-test precise-recovery

# 并发一致性测试
enhanced-concurrent-consistency-test: test-build
	@echo "🔄 运行并发一致性测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-comprehensive-test concurrent-consistency

# 运行所有增强测试
enhanced-test-all: test-build
	@echo "🎯 运行所有增强测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-comprehensive-test all-enhanced

# ========================================
# 帮助信息
# ========================================
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-comprehensive-test /tests/config/test_config_cloud_storage_comprehensive.json

# 显示帮助信息
help:
	@echo "UniBackup 测试套件"
	@echo ""
	@echo "🏗️ 环境管理:"
	@echo "  test-env-up              - 启动测试环境"
	@echo "  test-env-down            - 停止测试环境"
	@echo "  test-clean               - 清理测试环境"
	@echo "  test-build               - 构建测试二进制文件"
	@echo ""
	@echo "🧪 基础测试:"
	@echo "  test-all                 - 运行所有基础测试"
	@echo "  mysql-test-all           - MySQL专项测试"
	@echo "  es-test-all              - Elasticsearch专项测试"
	@echo "  group-test-all           - 分组备份测试"
	@echo ""
	@echo "🔧 存储测试:"
	@echo "  storage-test-all         - 存储抽象化测试"
	@echo "  storage-backend-consistency - 存储后端一致性测试"
	@echo "  storage-failure-recovery - 存储故障恢复测试"
	@echo "  storage-performance-comparison - 存储性能对比测试"
	@echo ""
	@echo "☁️ 云存储测试:"
	@echo "  cloud-storage-test-all   - 云存储全面测试"
	@echo "  cloud-test-quick         - 快速云存储测试"
	@echo "  cloud-storage-comprehensive-test-all - 云存储全面验证测试"
	@echo ""
	@echo "🎯 增强测试套件:"
	@echo "  enhanced-database-structure-test - 数据库结构验证测试"
	@echo "  enhanced-large-volume-test       - 大数据量测试"
	@echo "  enhanced-cross-cloud-test        - 跨云存储兼容性测试"
	@echo "  enhanced-precise-recovery-test   - 精确时间点恢复测试"
	@echo "  enhanced-concurrent-consistency-test - 并发一致性测试"
	@echo "  enhanced-test-all               - 运行所有增强测试"
	@echo ""
	@echo "🛡️ 专项测试:"
	@echo "  security-test-all        - 安全性测试"
	@echo "  boundary-test-all        - 边界条件测试"
	@echo "  data-validation-comprehensive - 数据验证测试"
	@echo ""
	@echo "🔄 动态配置更新测试:"
	@echo "  dynamic-config-test                - 动态配置更新综合测试"
	@echo "  dynamic-config-minimal-test        - 最小配置启动测试"
	@echo "  dynamic-config-mysql-test          - 动态MySQL配置测试"
	@echo "  dynamic-config-es-test             - 动态Elasticsearch配置测试"
	@echo "  dynamic-config-backuproot-test     - 动态BackupRoot更新测试"
	@echo "  dynamic-config-sync-test           - 组件同步验证测试"
	@echo "  dynamic-config-group-backup-test   - 分组备份功能测试"
	@echo "  dynamic-config-group-restore-test  - 分组恢复功能测试"
	@echo "  dynamic-config-group-delete-test   - 分组删除功能测试"
	@echo "  dynamic-config-interface-test      - 接口覆盖验证测试"
	@echo "  dynamic-config-comprehensive-test  - 动态配置综合测试"
	@echo ""
	@echo "🔄 回滚功能测试:"
	@echo "  rollback-test-single       - 单个恢复任务回滚测试"
	@echo "  rollback-test-parallel     - 并行恢复模式回滚测试"
	@echo "  rollback-test-serial       - 串行恢复模式回滚测试"
	@echo "  rollback-test-config       - 配置驱动回滚测试"
	@echo "  rollback-test-failure      - 回滚失败处理测试"
	@echo "  rollback-test-cleanup      - 快照清理测试"
	@echo "  rollback-test-comprehensive - 综合回滚测试"
	@echo "  rollback-test-all          - 所有回滚测试"
	@echo "  rollback-test-quick        - 快速回滚验证"
	@echo "  rollback-test-full         - 完整回滚验证"
	@echo ""
	@echo "📋 使用示例:"
	@echo "  make test-env-up && make enhanced-test-all"
	@echo "  make enhanced-database-structure-test"
	@echo "  make enhanced-large-volume-test"
	@echo "  make dynamic-config-test"
	@echo "  make rollback-test-comprehensive"

# 动态配置更新测试命令
dynamic-config-test: test-build
	@echo "🧪 运行动态配置更新综合测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test all
	@echo "✅ 动态配置更新测试完成"

dynamic-config-minimal-test: test-build
	@echo "🧪 运行最小配置启动测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test minimal-startup
	@echo "✅ 最小配置启动测试完成"

dynamic-config-mysql-test: test-build
	@echo "🧪 运行动态MySQL配置测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test dynamic-mysql
	@echo "✅ 动态MySQL配置测试完成"

dynamic-config-es-test: test-build
	@echo "🧪 运行动态Elasticsearch配置测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test dynamic-es
	@echo "✅ 动态Elasticsearch配置测试完成"

dynamic-config-backuproot-test: test-build
	@echo "🧪 运行动态BackupRoot更新测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test dynamic-backuproot
	@echo "✅ 动态BackupRoot更新测试完成"

dynamic-config-sync-test: test-build
	@echo "🧪 运行组件同步验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test component-sync
	@echo "✅ 组件同步验证测试完成"

dynamic-config-group-backup-test: test-build
	@echo "🧪 运行分组备份功能测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test group-backup
	@echo "✅ 分组备份功能测试完成"

dynamic-config-group-restore-test: test-build
	@echo "🧪 运行分组恢复功能测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test group-restore
	@echo "✅ 分组恢复功能测试完成"

dynamic-config-group-delete-test: test-build
	@echo "🧪 运行分组删除功能测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test group-delete
	@echo "✅ 分组删除功能测试完成"

dynamic-config-interface-test: test-build
	@echo "🧪 运行接口覆盖验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test interface-coverage
	@echo "✅ 接口覆盖验证测试完成"

dynamic-config-comprehensive-test: test-build
	@echo "🧪 运行动态配置综合测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/dynamic-config-test comprehensive
	@echo "✅ 动态配置综合测试完成"

# 云存储故障场景测试
cloud-storage-failure-test: test-build
	@echo "☁️ 运行云存储故障场景测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-failure-test /tests/config/test_config_s3_mock.json
	@echo "✅ 云存储故障场景测试完成"

cloud-storage-failure-network-test: test-build
	@echo "🌐 运行网络中断恢复测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-failure-test network-interruption
	@echo "✅ 网络中断恢复测试完成"

cloud-storage-failure-auth-test: test-build
	@echo "🔐 运行认证失效处理测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/cloud-storage-failure-test authentication-failure
	@echo "✅ 认证失效处理测试完成"

# 增强数据验证测试
enhanced-data-validation-test: test-build
	@echo "🧪 运行增强数据验证测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-data-validation-test all
	@echo "✅ 增强数据验证测试完成"

enhanced-data-validation-mysql-test: test-build
	@echo "🗄️ 运行MySQL数据完整性验证..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-data-validation-test mysql-integrity
	@echo "✅ MySQL数据完整性验证完成"

enhanced-data-validation-es-test: test-build
	@echo "🔍 运行Elasticsearch数据完整性验证..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-data-validation-test es-integrity
	@echo "✅ Elasticsearch数据完整性验证完成"

enhanced-data-validation-checksum-test: test-build
	@echo "🔐 运行数据校验和验证..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/enhanced-data-validation-test checksum
	@echo "✅ 数据校验和验证完成"

# 性能回归测试
performance-regression-test: test-build
	@echo "⚡ 运行性能回归测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/performance-regression-test all
	@echo "✅ 性能回归测试完成"

performance-regression-concurrent-test: test-build
	@echo "🔄 运行并发操作性能基准测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/performance-regression-test concurrent
	@echo "✅ 并发操作性能基准测试完成"

performance-regression-memory-test: test-build
	@echo "💾 运行内存使用监控测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/performance-regression-test memory
	@echo "✅ 内存使用监控测试完成"

performance-regression-throughput-test: test-build
	@echo "📊 运行备份恢复吞吐量测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/performance-regression-test throughput
	@echo "✅ 备份恢复吞吐量测试完成"



# 快速增强测试（用于开发调试）
enhanced-test-quick: enhanced-data-validation-checksum-test performance-regression-memory-test
	@echo "⚡ 快速增强测试完成"

# 测试矩阵验证
test-matrix-validation: test-build
	@echo "🎯 运行完整测试矩阵验证..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation matrix
	@echo "✅ 完整测试矩阵验证完成"

test-matrix-mysql-local: test-build
	@echo "🗄️ 运行MySQL本地存储矩阵测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation mysql-local
	@echo "✅ MySQL本地存储矩阵测试完成"

test-matrix-mysql-cloud: test-build
	@echo "☁️ 运行MySQL云存储矩阵测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation mysql-cloud
	@echo "✅ MySQL云存储矩阵测试完成"

test-matrix-es-local: test-build
	@echo "🔍 运行Elasticsearch本地存储矩阵测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation es-local
	@echo "✅ Elasticsearch本地存储矩阵测试完成"

test-matrix-es-cloud: test-build
	@echo "🌐 运行Elasticsearch云存储矩阵测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation es-cloud
	@echo "✅ Elasticsearch云存储矩阵测试完成"

test-matrix-group-operations: test-build
	@echo "👥 运行分组操作矩阵测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation group-operations
	@echo "✅ 分组操作矩阵测试完成"

test-matrix-boundary-cases: test-build
	@echo "⚠️ 运行边界情况矩阵测试..."
	@cd docker && docker-compose exec unibackup-test /tests/bin/test-matrix-validation boundary-cases
	@echo "✅ 边界情况矩阵测试完成"

# 完整的测试覆盖度验证
test-coverage-validation: test-matrix-validation enhanced-test-all
	@echo "📊 完整测试覆盖度验证完成"

# 回滚功能集成测试
rollback-test-single:
	@echo "🔄 运行单个恢复任务回滚测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test single-restore-rollback

rollback-test-parallel:
	@echo "🔄 运行并行恢复模式回滚测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test parallel-restore-rollback

rollback-test-serial:
	@echo "🔄 运行串行恢复模式回滚测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test serial-restore-rollback

rollback-test-config:
	@echo "🔧 运行配置驱动回滚测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test config-driven-rollback

rollback-test-failure:
	@echo "⚠️ 运行回滚失败处理测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test rollback-failure-handling

rollback-test-cleanup:
	@echo "🧹 运行快照清理测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test snapshot-cleanup

rollback-test-comprehensive:
	@echo "🎯 运行综合回滚测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test comprehensive

rollback-test-all:
	@echo "🎉 运行所有回滚测试..."
	@docker exec unibackup-test-runner /tests/bin/rollback-integration-test all

# 回滚测试快速验证
rollback-test-quick: rollback-test-single rollback-test-config
	@echo "⚡ 回滚功能快速验证完成"

# 回滚测试完整验证
rollback-test-full: rollback-test-all
	@echo "🔄 回滚功能完整验证完成"

