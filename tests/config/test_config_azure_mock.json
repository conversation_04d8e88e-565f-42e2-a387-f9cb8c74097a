{"backup_root": "/tests/data/backup", "logger": null, "max_concurrent_tasks": 3, "task_retention_days": 7, "max_task_history": 100, "backup_timeout": "1h", "restore_timeout": "1h", "cloud_storage": {"enabled": true, "type": "s3", "bucket": "test-azure-container", "region": "us-east-1", "access_key": "minioadmin", "secret_key": "minioadmin123", "endpoint": "http://unibackup-test-minio:9000"}, "mysql": {"host": "unibackup-test-mysql", "port": 3306, "user": "backup_user", "password": "backup_pass", "db_name": "testdb", "tools_path": {"mysqldump": "/usr/bin/mysqldump", "mysql": "/usr/bin/mysql", "mysqlbinlog": "/usr/bin/mysqlbinlog", "mysqladmin": "/usr/bin/mysqladmin"}}, "es": {"addresses": ["http://unibackup-test-elasticsearch:9200"], "archival_repo_name": "unibackup-test-archival", "managed_repo_name": "unibackup-test-managed", "auto_create_repos": true, "repo_base_path": "/usr/share/elasticsearch/snapshots"}}