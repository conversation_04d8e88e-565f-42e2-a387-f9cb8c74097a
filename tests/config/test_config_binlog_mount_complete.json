{"backup_root": "/tests/data/backup", "log_level": "info", "log_file": "/tmp/unibackup-binlog-test.log", "max_concurrent_tasks": 3, "task_retention_days": 7, "max_task_history": 100, "backup_timeout": "1h", "restore_timeout": "1h", "cloud_storage": {"enabled": false}, "mysql": {"enabled": true, "host": "unibackup-test-mysql", "port": 3306, "user": "backup_user", "password": "backup_pass", "database": "testdb", "backup_type": "incremental", "backup_schedule": "0 */6 * * *", "retention_days": 7, "compression": true, "encryption": false, "binlog_base_path": "/tests/mysql-binlogs", "enable_binlog_backup": true, "binlog_retention_hours": 168, "tools_path": {"mysqldump": "/usr/local/bin/mysqldump", "mysql": "/usr/local/bin/mysql", "mysqlbinlog": "/usr/local/bin/mysqlbinlog", "mysqladmin": "/usr/local/bin/mysqladmin"}}, "elasticsearch": {"enabled": true, "host": "unibackup-test-elasticsearch", "port": 9200, "indices": ["test-index"], "backup_type": "snapshot", "backup_schedule": "0 3 * * *", "retention_days": 7, "compression": true, "repository": "test-repo", "archival_repo_name": "unibackup-test-archival", "managed_repo_name": "unibackup-test-managed", "auto_create_repos": true, "repo_base_path": "/usr/share/elasticsearch/snapshots"}, "groups": [{"name": "binlog-test-group", "description": "Binlog挂载测试分组", "sources": ["mysql"], "backup_schedule": "0 */2 * * *", "retention_days": 3, "enable_binlog_backup": true}], "notifications": {"enabled": false}, "monitoring": {"enabled": true, "metrics_port": 8080, "health_check_interval": 30}, "security": {"encryption": {"enabled": false}, "authentication": {"enabled": false}}, "performance": {"max_concurrent_backups": 2, "backup_timeout": 3600, "compression_level": 6, "buffer_size": 1048576, "binlog_buffer_size": 2097152}}