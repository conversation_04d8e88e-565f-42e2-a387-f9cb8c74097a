{"backup_root": "/tmp/unibackup-test", "log_level": "info", "log_file": "/tmp/unibackup-test.log", "cloud_storage": {"enabled": false}, "mysql": {"enabled": true, "host": "mysql", "port": 3306, "user": "backup_user", "password": "backup_pass", "database": "testdb", "backup_type": "full", "backup_schedule": "0 2 * * *", "retention_days": 7, "compression": true, "encryption": false}, "elasticsearch": {"enabled": true, "host": "elasticsearch", "port": 9200, "indices": ["test-index"], "backup_type": "snapshot", "backup_schedule": "0 3 * * *", "retention_days": 7, "compression": true, "repository": "test-repo"}, "groups": [{"name": "test-group", "description": "测试分组", "sources": ["mysql", "elasticsearch"], "backup_schedule": "0 1 * * *", "retention_days": 14}], "notifications": {"enabled": false}, "monitoring": {"enabled": true, "metrics_port": 8080, "health_check_interval": 30}, "security": {"encryption": {"enabled": false}, "authentication": {"enabled": false}}, "performance": {"max_concurrent_backups": 2, "backup_timeout": 3600, "compression_level": 6, "buffer_size": 1048576}}