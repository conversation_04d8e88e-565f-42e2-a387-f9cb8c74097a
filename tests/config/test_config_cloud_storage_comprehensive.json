{"backup_root": "/tmp/backup", "cloud_storage": {"enabled": true, "type": "s3", "bucket": "unibackup-test-bucket", "region": "us-east-1", "endpoint": "http://minio:9000", "access_key": "minioadmin", "secret_key": "minioadmin123"}, "mysql": {"host": "mysql", "port": 3306, "user": "backup_user", "password": "backup_password", "db_name": "test_db", "tools_path": {"mysqldump": "/usr/bin/mysqldump", "mysql": "/usr/bin/mysql", "mysqlbinlog": "/usr/bin/mysqlbinlog", "mysqladmin": "/usr/bin/mysqladmin"}}, "es": {"addresses": ["http://elasticsearch:9200"], "archival_repo_name": "comprehensive-test-archival", "managed_repo_name": "comprehensive-test-managed", "auto_create_repos": true}, "max_concurrent_tasks": 3, "task_retention_days": 7, "max_task_history": 100, "backup_timeout": "30m", "restore_timeout": "30m", "logger": {"level": "info", "enable_console": true}}