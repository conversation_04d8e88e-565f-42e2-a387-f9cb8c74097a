{"backup_root": "/tests/data/backup", "logger": null, "max_concurrent_tasks": 3, "task_retention_days": 7, "max_task_history": 100, "backup_timeout": "1h", "restore_timeout": "1h", "mysql": {"host": "unibackup-test-mysql", "port": 3306, "user": "backup_user", "password": "backup_pass", "db_name": "testdb", "binlog_base_path": "/tests/mysql-binlogs", "tools_path": {"mysqldump": "/usr/local/bin/mysqldump", "mysql": "/usr/local/bin/mysql", "mysqlbinlog": "/usr/local/bin/mysqlbinlog", "mysqladmin": "/usr/local/bin/mysqladmin"}}, "es": {"addresses": ["http://unibackup-test-elasticsearch:9200"], "archival_repo_name": "unibackup-test-archival", "managed_repo_name": "unibackup-test-managed", "auto_create_repos": true, "repo_base_path": "/usr/share/elasticsearch/snapshots"}}