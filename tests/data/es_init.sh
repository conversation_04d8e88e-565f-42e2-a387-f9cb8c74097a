#!/bin/bash

# ES 初始化脚本
# 等待 Elasticsearch 启动完成
echo "等待 Elasticsearch 启动完成..."
ES_URL="http://unibackup-test-elasticsearch:9200"

until curl -s ${ES_URL}/_cluster/health | grep -q '"status":"green"'; do
    echo "等待 Elasticsearch 变为绿色状态..."
    sleep 2
done

echo "Elasticsearch 已就绪，开始初始化测试数据..."

# 创建快照仓库
echo "创建快照仓库..."
curl -X PUT "${ES_URL}/_snapshot/backup_repo" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/usr/share/elasticsearch/snapshots",
    "compress": true
  }
}'

# 创建测试索引
echo "创建测试索引..."
curl -X PUT "${ES_URL}/test_index" -H 'Content-Type: application/json' -d'
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0
  },
  "mappings": {
    "properties": {
      "title": {
        "type": "text"
      },
      "content": {
        "type": "text"
      },
      "timestamp": {
        "type": "date"
      },
      "category": {
        "type": "keyword"
      }
    }
  }
}'

# 添加测试文档
echo "添加测试文档..."
curl -X POST "${ES_URL}/test_index/_doc" -H 'Content-Type: application/json' -d'
{
  "title": "测试文档1",
  "content": "这是一个测试文档的内容，用于验证备份和恢复功能",
  "timestamp": "2025-07-10T12:00:00",
  "category": "test"
}'

curl -X POST "${ES_URL}/test_index/_doc" -H 'Content-Type: application/json' -d'
{
  "title": "测试文档2",
  "content": "这是另一个测试文档的内容，包含了更多的数据",
  "timestamp": "2025-07-10T12:01:00",
  "category": "test"
}'

curl -X POST "${ES_URL}/test_index/_doc" -H 'Content-Type: application/json' -d'
{
  "title": "产品文档",
  "content": "这是产品相关的文档内容",
  "timestamp": "2025-07-10T12:02:00",
  "category": "product"
}'

curl -X POST "${ES_URL}/test_index/_doc" -H 'Content-Type: application/json' -d'
{
  "title": "技术文档",
  "content": "这是技术相关的文档内容，包含了详细的技术说明",
  "timestamp": "2025-07-10T12:03:00",
  "category": "tech"
}'

# 创建另一个测试索引
echo "创建第二个测试索引..."
curl -X PUT "${ES_URL}/user_index" -H 'Content-Type: application/json' -d'
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0
  },
  "mappings": {
    "properties": {
      "name": {
        "type": "text"
      },
      "email": {
        "type": "keyword"
      },
      "age": {
        "type": "integer"
      },
      "created_at": {
        "type": "date"
      }
    }
  }
}'

# 添加用户数据
echo "添加用户数据..."
curl -X POST "${ES_URL}/user_index/_doc" -H 'Content-Type: application/json' -d'
{
  "name": "张三",
  "email": "<EMAIL>",
  "age": 30,
  "created_at": "2025-07-10T10:00:00"
}'

curl -X POST "${ES_URL}/user_index/_doc" -H 'Content-Type: application/json' -d'
{
  "name": "李四",
  "email": "<EMAIL>",
  "age": 25,
  "created_at": "2025-07-10T10:01:00"
}'

curl -X POST "${ES_URL}/user_index/_doc" -H 'Content-Type: application/json' -d'
{
  "name": "王五",
  "email": "<EMAIL>",
  "age": 35,
  "created_at": "2025-07-10T10:02:00"
}'

# 刷新索引确保数据可搜索
echo "刷新索引..."
curl -X POST "${ES_URL}/test_index/_refresh"
curl -X POST "${ES_URL}/user_index/_refresh"

# 验证数据
echo "验证创建的数据..."
echo "test_index 文档数量:"
curl -s "${ES_URL}/test_index/_count" | grep -o '"count":[0-9]*' | grep -o '[0-9]*'

echo "user_index 文档数量:"
curl -s "${ES_URL}/user_index/_count" | grep -o '"count":[0-9]*' | grep -o '[0-9]*'

echo "快照仓库状态:"
curl -s "${ES_URL}/_snapshot/backup_repo"

echo "Elasticsearch 初始化完成！"