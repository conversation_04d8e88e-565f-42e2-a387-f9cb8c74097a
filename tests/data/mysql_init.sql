-- 首先设置全局参数以支持函数和存储过程创建
SET GLOBAL log_bin_trust_function_creators = 1;

-- 修改backup_user的认证方式为与MariaDB客户端兼容的方式
-- 并授予完整的备份和恢复权限
ALTER USER 'backup_user'@'%' IDENTIFIED WITH mysql_native_password BY 'backup_pass';
GRANT ALL PRIVILEGES ON *.* TO 'backup_user'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

-- 初始化测试数据
CREATE DATABASE IF NOT EXISTS testdb;
USE testdb;

-- 创建审计日志表（先创建，因为触发器要用到）
CREATE TABLE IF NOT EXISTS audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50),
    action VARCHAR(50),
    record_id INT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    product_name VARCHAR(200) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建测试触发器（表已存在）
DELIMITER //
CREATE TRIGGER order_audit
AFTER INSERT ON orders
FOR EACH ROW
BEGIN
    INSERT INTO audit_log (table_name, action, record_id, timestamp)
    VALUES ('orders', 'INSERT', NEW.id, NOW());
END //
DELIMITER ;

-- 插入测试数据
INSERT INTO users (name, email) VALUES
('张三', '<EMAIL>'),
('李四', '<EMAIL>'),
('王五', '<EMAIL>');

INSERT INTO orders (user_id, product_name, amount) VALUES
(1, 'iPhone 15', 7999.00),
(2, 'MacBook Pro', 15999.00),
(3, 'iPad Air', 4599.00),
(1, 'AirPods Pro', 1899.00);

-- 创建测试存储过程
DELIMITER //
CREATE PROCEDURE GetUserOrders(IN user_id INT)
BEGIN
    SELECT u.name, o.product_name, o.amount, o.order_date
    FROM users u
    JOIN orders o ON u.id = o.user_id
    WHERE u.id = user_id;
END //
DELIMITER ;

-- 验证backup_user权限
SHOW GRANTS FOR 'backup_user'@'%';