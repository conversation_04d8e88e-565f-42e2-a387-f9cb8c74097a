# 备份系统 Makefile
# 提供常用的开发和测试命令

.PHONY: help build build-admin test test-quick test-coverage test-race test-bench clean lint fmt vet deps docs docs-serve

# 默认目标
.DEFAULT_GOAL := help

# 项目信息
PROJECT_NAME := unibackup
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION := $(shell go version | cut -d' ' -f3)

# 目录
BUILD_DIR := build
TEST_OUTPUT_DIR := test_output
SCRIPTS_DIR := scripts

# Go 相关
GOCMD := go
GOBUILD := $(GOCMD) build
GOTEST := $(GOCMD) test
GOCLEAN := $(GOCMD) clean
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# 构建标志
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME)"

## help: 显示帮助信息
help:
	@echo "备份系统开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build              构建项目"
	@echo "  build-admin        构建管理工具"
	@echo "  test               运行完整测试套件"
	@echo "  test-quick         运行快速测试"
	@echo "  test-coverage      运行详细测试覆盖率分析"
	@echo "  test-coverage-simple 运行简单覆盖率测试"
	@echo "  test-race          运行竞态条件检测"
	@echo "  test-bench         运行性能基准测试"
	@echo "  lint               运行代码检查"
	@echo "  fmt                格式化代码"
	@echo "  vet                运行go vet检查"
	@echo "  deps               下载依赖"
	@echo "  clean              清理构建文件"
	@echo "  docs               生成API文档"
	@echo "  docs-serve         启动文档服务器"
	@echo ""
	@echo "测试相关:"
	@echo "  make test-module MODULE=pkg/types    # 测试特定模块"
	@echo "  make test-func FUNC=TestTaskStatus   # 测试特定函数"
	@echo ""
	@echo "文档相关:"
	@echo "  make docs          # 生成API文档到docs/api目录"
	@echo "  make docs-serve    # 启动godoc服务器 (http://localhost:6060)"
	@echo ""

## build: 构建项目
build:
	@echo "🔨 构建项目..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(PROJECT_NAME) ./cmd/unibackup
	@echo "✅ 构建完成: $(BUILD_DIR)/$(PROJECT_NAME)"

## build-admin: 构建管理工具
build-admin:
	@echo "🔨 构建管理工具..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(PROJECT_NAME)-admin ./cmd/unibackup-admin
	@echo "✅ 构建完成: $(BUILD_DIR)/$(PROJECT_NAME)-admin"

## test: 运行完整测试套件
test:
	@echo "🧪 运行完整测试套件..."
	@chmod +x $(SCRIPTS_DIR)/run_tests.sh
	@$(SCRIPTS_DIR)/run_tests.sh

## test-quick: 运行快速测试
test-quick:
	@echo "⚡ 运行快速测试..."
	@chmod +x $(SCRIPTS_DIR)/quick_test.sh
	@$(SCRIPTS_DIR)/quick_test.sh

## test-coverage: 运行测试并生成详细覆盖率报告
test-coverage:
	@echo "📊 运行详细测试覆盖率分析..."
	@chmod +x $(SCRIPTS_DIR)/test_coverage.sh
	@$(SCRIPTS_DIR)/test_coverage.sh

## test-coverage-simple: 运行简单覆盖率测试
test-coverage-simple:
	@echo "📊 运行简单测试覆盖率分析..."
	@mkdir -p $(TEST_OUTPUT_DIR)
	$(GOTEST) -coverprofile=$(TEST_OUTPUT_DIR)/coverage.out -covermode=atomic ./...
	$(GOCMD) tool cover -html=$(TEST_OUTPUT_DIR)/coverage.out -o $(TEST_OUTPUT_DIR)/coverage.html
	$(GOCMD) tool cover -func=$(TEST_OUTPUT_DIR)/coverage.out
	@echo "📄 覆盖率报告: $(TEST_OUTPUT_DIR)/coverage.html"

## test-race: 运行竞态条件检测
test-race:
	@echo "🔍 运行竞态条件检测..."
	$(GOTEST) -race ./...

## test-bench: 运行性能基准测试
test-bench:
	@echo "⚡ 运行性能基准测试..."
	@mkdir -p $(TEST_OUTPUT_DIR)
	$(GOTEST) -bench=. -benchmem ./... | tee $(TEST_OUTPUT_DIR)/benchmark.txt

## test-module: 测试特定模块
test-module:
	@if [ -z "$(MODULE)" ]; then \
		echo "❌ 请指定模块: make test-module MODULE=pkg/types"; \
		exit 1; \
	fi
	@echo "🎯 测试模块: $(MODULE)"
	$(GOTEST) -v ./$(MODULE)

## test-func: 测试特定函数
test-func:
	@if [ -z "$(FUNC)" ]; then \
		echo "❌ 请指定函数: make test-func FUNC=TestTaskStatus"; \
		exit 1; \
	fi
	@echo "🎯 测试函数: $(FUNC)"
	$(GOTEST) -v -run $(FUNC) ./...

## lint: 运行代码检查
lint:
	@echo "🔍 运行代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "⚠️  golangci-lint 未安装，跳过检查"; \
		echo "💡 安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

## fmt: 格式化代码
fmt:
	@echo "🎨 格式化代码..."
	$(GOCMD) fmt ./...

## vet: 运行go vet检查
vet:
	@echo "🔍 运行go vet检查..."
	$(GOCMD) vet ./...

## deps: 下载依赖
deps:
	@echo "📦 下载依赖..."
	$(GOMOD) download
	$(GOMOD) tidy

## clean: 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@rm -rf $(TEST_OUTPUT_DIR)
	@echo "✅ 清理完成"

## install-tools: 安装开发工具
install-tools:
	@echo "🛠️  安装开发工具..."
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/stretchr/testify@latest
	@echo "✅ 工具安装完成"

## ci: CI/CD流水线命令
ci: deps fmt vet lint test-coverage test-race
	@echo "🚀 CI检查完成"

## dev: 开发环境设置
dev: deps install-tools
	@echo "🔧 开发环境设置完成"
	@echo ""
	@echo "💡 常用命令:"
	@echo "  make test-quick    # 快速测试"
	@echo "  make test-coverage # 覆盖率测试"
	@echo "  make build         # 构建项目"

# 生成API文档
docs:
	@echo "📚 生成API文档..."
	@./$(SCRIPTS_DIR)/generate_docs.sh

# 启动文档服务器
docs-serve:
	@echo "🌐 启动文档服务器..."
	@echo "访问 http://localhost:6060/pkg/unibackup/ 查看API文档"
	@if command -v godoc >/dev/null 2>&1; then \
		godoc -http=:6060; \
	elif [ -f "$$HOME/go/bin/godoc" ]; then \
		$$HOME/go/bin/godoc -http=:6060; \
	else \
		echo "❌ godoc未找到，请运行: make docs 先生成文档"; \
		exit 1; \
	fi

# Docker测试环境管理命令
## test-env-up: 启动Docker测试环境
test-env-up:
	@echo "🐳 启动Docker测试环境..."
	@cd tests/docker && docker-compose up -d
	@echo "⏳ 等待服务启动..."
	@sleep 10
	@echo "✅ Docker测试环境已启动"

## test-env-down: 停止Docker测试环境
test-env-down:
	@echo "🛑 停止Docker测试环境..."
	@cd tests/docker && docker-compose down
	@echo "✅ Docker测试环境已停止"

## test-env-restart: 重启Docker测试环境
test-env-restart:
	@echo "🔄 重启Docker测试环境..."
	@cd tests/docker && docker-compose restart
	@echo "✅ Docker测试环境已重启"

## test-env-logs: 查看Docker测试环境日志
test-env-logs:
	@echo "📝 显示Docker测试环境日志..."
	@cd tests/docker && docker-compose logs -f

## test-env-status: 查看Docker测试环境状态
test-env-status:
	@echo "📊 Docker测试环境状态:"
	@cd tests/docker && docker-compose ps

## test-env-clean: 清理Docker测试环境和数据
test-env-clean:
	@echo "🧹 清理Docker测试环境和数据..."
	@cd tests/docker && docker-compose down -v
	@cd tests/docker && docker-compose rm -f
	@echo "✅ Docker测试环境已清理"

## test-docker: 使用Docker环境运行测试
test-docker: test-env-up
	@echo "🧪 在Docker环境中运行测试..."
	@sleep 5
	@./tests/bin/test_suite_mysql_enhanced -config=tests/config/test_config_binlog_mount.json || true
	@echo "📋 测试完成，查看上方输出结果"

# 显示项目信息
info:
	@echo "项目信息:"
	@echo "  名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  Go版本: $(GO_VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
