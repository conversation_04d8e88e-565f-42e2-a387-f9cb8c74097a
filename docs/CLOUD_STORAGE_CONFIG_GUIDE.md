# UniBackup 存储配置说明

本指南基于 UniBackup 项目的实际代码实现，为本地存储和各种云存储服务提供详细的配置说明。

## 📋 CloudStorageConfig 结构体说明

```go
type CloudStorageConfig struct {
    Enabled     bool   `json:"enabled" yaml:"enabled"`                    // 是否启用云存储
    Type        string `json:"type" yaml:"type"`                          // 云存储类型: "s3", "gcs", "azure"
    
    // S3 配置字段
    Bucket      string `json:"bucket,omitempty" yaml:"bucket,omitempty"`
    Region      string `json:"region,omitempty" yaml:"region,omitempty"`
    AccessKey   string `json:"access_key,omitempty" yaml:"access_key,omitempty"`
    SecretKey   string `json:"-" yaml:"secret_key,omitempty"`              // 敏感字段，不序列化到JSON
    Endpoint    string `json:"endpoint,omitempty" yaml:"endpoint,omitempty"` // S3兼容存储端点
    
    // GCS 配置字段
    ProjectID       string `json:"project_id,omitempty" yaml:"project_id,omitempty"`
    CredentialsFile string `json:"credentials_file,omitempty" yaml:"credentials_file,omitempty"`
    
    // Azure 配置字段
    Container   string `json:"container,omitempty" yaml:"container,omitempty"`
    AccountName string `json:"account_name,omitempty" yaml:"account_name,omitempty"`
    AccountKey  string `json:"-" yaml:"account_key,omitempty"`             // 敏感字段，不序列化到JSON
}
```

## 🔧 backup_root 字段说明

**backup_root 在云存储中的作用**：
- **必须配置**：即使使用云存储，backup_root 仍然是必需字段
- **路径生成**：用于生成云存储中的对象键（key）路径结构
- **本地缓存**：某些操作可能需要本地临时存储
- **元数据管理**：任务状态等元数据仍存储在本地

**示例路径结构**：
```
backup_root: "/var/lib/unibackup"
云存储中的路径: unibackup/mysql/archival/backup-123/data.sql.gz
```

---

## 💾 本地存储配置

### UniBackup 应用层配置

```json
{
  "backup_root": "/var/lib/unibackup"            // 必需：本地备份根目录，所有备份数据存储的基础路径
}
```

**说明**：
- **默认存储方式**：不配置 `cloud_storage` 时，UniBackup 默认使用本地存储
- **路径结构**：备份数据按照 `backup_root/数据源类型/备份类型/备份ID/` 的结构组织
- **权限要求**：确保 UniBackup 进程对 `backup_root` 目录有读写权限

**本地存储路径示例**：
```
/var/lib/unibackup/
├── tasks.json                    # 任务状态文件
├── mysql/                        # MySQL 备份
│   ├── archival/                 # 归档备份
│   │   └── backup-123/
│   │       ├── metadata.json
│   │       └── data.sql.gz
│   └── chains/                   # 增量链
│       └── chain-456/
└── elasticsearch/                # ES 备份
    └── archival/
        └── backup-789/
            └── metadata.json
```

---

## 🚀 AWS S3 配置（支持 S3 协议的云存储）

**支持的 S3 协议云存储服务**：
- **Amazon S3** - AWS 原生对象存储服务
- **MinIO** - 开源 S3 兼容对象存储
- **阿里云 OSS** - 阿里云对象存储服务（S3 兼容模式）
- **腾讯云 COS** - 腾讯云对象存储（S3 兼容模式）
- **华为云 OBS** - 华为云对象存储服务（S3 兼容模式）
- **七牛云 Kodo** - 七牛云对象存储（S3 兼容模式）
- **其他 S3 兼容存储** - 任何实现 S3 API 的对象存储服务

### UniBackup 应用层配置

```json
{
  "backup_root": "/var/lib/unibackup",           // 必需：本地备份根目录，用于路径生成和元数据存储
  "cloud_storage": {
    "enabled": true,                             // 必需：启用云存储功能
    "type": "s3",                               // 必需：指定云存储类型为 S3
    "bucket": "my-backup-bucket",               // 必需：S3 存储桶名称
    "region": "us-east-1",                      // 必需：AWS 区域
    "access_key": "AKIAIOSFODNN7EXAMPLE",       // 可选：S3 访问密钥 ID（推荐通过环境变量提供）
    "secret_key": "wJalrXUtnFEMI/K7MDENG..."    // 可选：S3 秘密访问密钥（推荐通过环境变量提供）
  }
}
```

**环境变量**：
```bash
# AWS 认证信息（必需）
export AWS_ACCESS_KEY_ID="AKIAIOSFODNN7EXAMPLE"
export AWS_SECRET_ACCESS_KEY="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
export AWS_DEFAULT_REGION="us-east-1"
```

### Elasticsearch 容器配置

**1. 安装 repository-s3 插件**：
```bash
bin/elasticsearch-plugin install repository-s3
```

**2. 配置 keystore（推荐）**：
```bash
# 添加 S3 认证信息
bin/elasticsearch-keystore add s3.client.default.access_key
bin/elasticsearch-keystore add s3.client.default.secret_key

# 重启 ES 使配置生效
```

### MinIO S3 兼容存储配置

```json
{
  "backup_root": "/var/lib/unibackup",           // 必需：本地备份根目录
  "cloud_storage": {
    "enabled": true,                             // 必需：启用云存储功能
    "type": "s3",                               // 必需：指定云存储类型为 S3
    "bucket": "backup-bucket",                  // 必需：存储桶名称
    "region": "us-east-1",                      // 必需：区域（S3兼容存储可以是任意值）
    "endpoint": "http://minio:9000",            // 必需：S3兼容存储服务端点
    "access_key": "minioadmin",                 // 必需：S3兼容存储访问密钥
    "secret_key": "minioadmin123"               // 必需：S3兼容存储秘密密钥
  }
}
```

**环境变量**：
```bash
export AWS_ACCESS_KEY_ID="minioadmin"
export AWS_SECRET_ACCESS_KEY="minioadmin123"
```

---

## 🌐 Google Cloud Storage 配置

### UniBackup 应用层配置

```json
{
  "backup_root": "/var/lib/unibackup",           // 必需：本地备份根目录，用于路径生成和元数据存储
  "cloud_storage": {
    "enabled": true,                             // 必需：启用云存储功能
    "type": "gcs",                              // 必需：指定云存储类型为 GCS
    "bucket": "my-gcs-backup-bucket",           // 必需：GCS 存储桶名称
    "project_id": "my-gcp-project-123",         // 必需：GCP 项目 ID
    "credentials_file": "/app/credentials/service-account.json" // 可选：服务账户密钥文件路径（推荐通过环境变量提供）
  }
}
```

**环境变量**：
```bash
# GCS 认证信息（必需）
export GOOGLE_APPLICATION_CREDENTIALS="/app/credentials/service-account.json"
export GOOGLE_CLOUD_PROJECT="my-gcp-project-123"
```

### Elasticsearch 容器配置

**1. 安装 repository-gcs 插件**：
```bash
bin/elasticsearch-plugin install repository-gcs
```

**2. 配置服务账户密钥**：
```bash
# 将服务账户密钥文件添加到 keystore
bin/elasticsearch-keystore add-file gcs.client.default.credentials_file /path/to/service-account.json

# 重启 ES 使配置生效
```

---

## 🔷 Azure Blob Storage 配置

### UniBackup 应用层配置

```json
{
  "backup_root": "/var/lib/unibackup",           // 必需：本地备份根目录，用于路径生成和元数据存储
  "cloud_storage": {
    "enabled": true,                             // 必需：启用云存储功能
    "type": "azure",                            // 必需：指定云存储类型为 Azure
    "container": "backup-container",            // 必需：Azure 容器名称
    "account_name": "mystorageaccount",         // 必需：Azure 存储账户名称
    "account_key": "Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==" // 可选：Azure 存储账户密钥（推荐通过环境变量提供）
  }
}
```

**环境变量**：
```bash
# Azure 认证信息（必需）
export AZURE_STORAGE_ACCOUNT="mystorageaccount"
export AZURE_STORAGE_KEY="Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw=="
```

### Elasticsearch 容器配置

**1. 安装 repository-azure 插件**：
```bash
bin/elasticsearch-plugin install repository-azure
```

**2. 配置 Azure 认证**：
```bash
# 添加 Azure 存储账户信息到 keystore
bin/elasticsearch-keystore add azure.client.default.account
bin/elasticsearch-keystore add azure.client.default.key

# 重启 ES 使配置生效
```

---

## 📋 配置字段总结

### 必需字段

| 字段 | S3 | GCS | Azure | 说明 |
|------|----|----|-------|------|
| `backup_root` | ✅ | ✅ | ✅ | 本地备份根目录，用于路径生成和元数据存储 |
| `enabled` | ✅ | ✅ | ✅ | 启用云存储功能 |
| `type` | ✅ | ✅ | ✅ | 云存储类型标识 |
| `bucket` | ✅ | ✅ | ❌ | S3/GCS 存储桶名称 |
| `container` | ❌ | ❌ | ✅ | Azure 容器名称 |
| `region` | ✅ | ❌ | ❌ | AWS 区域 |
| `project_id` | ❌ | ✅ | ❌ | GCP 项目 ID |
| `account_name` | ❌ | ❌ | ✅ | Azure 存储账户名称 |
| `access_key` | 🔧 | ❌ | ❌ | S3 访问密钥 ID（可选，推荐环境变量） |
| `secret_key` | 🔧 | ❌ | ❌ | S3 秘密访问密钥（可选，推荐环境变量） |
| `account_key` | ❌ | ❌ | 🔧 | Azure 存储账户密钥（可选，推荐环境变量） |
| `credentials_file` | ❌ | 🔧 | ❌ | GCS 服务账户密钥文件路径（可选，推荐环境变量） |

### 认证字段配置方式

**配置优先级**：环境变量 > 配置文件 > 默认值

| 配置字段 | 环境变量 | S3 | GCS | Azure | 说明 |
|---------|---------|----|----|-------|------|
| `access_key` | `AWS_ACCESS_KEY_ID` | ✅ | ❌ | ❌ | S3 访问密钥 ID |
| `secret_key` | `AWS_SECRET_ACCESS_KEY` | ✅ | ❌ | ❌ | S3 秘密访问密钥 |
| `credentials_file` | `GOOGLE_APPLICATION_CREDENTIALS` | ❌ | ✅ | ❌ | GCS 服务账户密钥文件路径 |
| `account_name` | `AZURE_STORAGE_ACCOUNT` | ❌ | ❌ | ✅ | Azure 存储账户名称 |
| `account_key` | `AZURE_STORAGE_KEY` | ❌ | ❌ | ✅ | Azure 存储账户密钥 |

### 可选字段

| 字段 | 适用存储 | 说明 |
|------|---------|------|
| `endpoint` | S3 | S3 兼容存储的自定义端点（如 MinIO、阿里云 OSS 等） |
| `credentials_file` | GCS | 服务账户密钥文件路径（可通过环境变量替代） |

---

## 💡 重要提示

1. **backup_root 必须配置**：无论使用本地存储还是云存储，backup_root 都是必需的，用于路径生成和本地元数据管理
2. **本地存储为默认方式**：不配置 cloud_storage 时，UniBackup 自动使用本地存储
3. **认证信息配置方式**：
   - **推荐方式**：通过环境变量提供认证信息（更安全）
   - **备选方式**：在配置文件中直接配置（开发环境可用）
   - **配置优先级**：环境变量 > 配置文件 > 默认值
4. **S3 协议兼容性**：任何支持 S3 API 的对象存储服务都可以使用 S3 配置方式
5. **ES 插件必须安装**：使用云存储时，需要在 ES 容器中安装对应的插件
6. **keystore 配置推荐**：生产环境建议使用 ES keystore 而不是环境变量来配置认证信息
