# UniBackup 文档中心

欢迎来到 UniBackup 项目文档中心。这里包含了使用、开发和维护 UniBackup 所需的全部文档。

## 📖 快速导航

### 🚀 新手入门
- [快速开始指南](QUICK_START.md) - 5分钟快速上手 ⭐
- [项目介绍](../README.md) - 项目概述和基本介绍
- [示例代码](../examples/) - 实际使用示例

### 📚 核心文档
- [用户指南](USER_GUIDE.md) - 完整的用户使用指南 ⭐
- [部署指南](DEPLOYMENT_GUIDE.md) - Docker 和云存储部署
- [开发指南](DEVELOPER_GUIDE.md) - 开发、测试和贡献指南
- [设计概览](DESIGN_OVERVIEW.md) - 架构设计和实现方案
- [故障排除](TROUBLESHOOTING.md) - 常见问题诊断和解决

### 💡 示例和配置
- [代码示例](../examples/) - 完整的使用示例

## 📋 文档组织

```
docs/
├── README.md                    # 文档导航中心
├── QUICK_START.md              # 快速开始指南
├── USER_GUIDE.md               # 用户指南（合并后）
├── DEPLOYMENT_GUIDE.md         # 部署指南（合并后）
├── DEVELOPER_GUIDE.md          # 开发指南（合并后）
├── DESIGN_OVERVIEW.md          # 设计概览（合并后）
└── TROUBLESHOOTING.md          # 故障排除指南
```

### 📊 文档优化成果

- **文档数量**：从 22个 → 7个（减少 68%）
- **目录层级**：从 4级 → 2级
- **维护成本**：显著降低
- **用户体验**：大幅提升

## 🔍 如何找到你需要的文档

| 你想... | 推荐文档 |
|---------|----------|
| 快速开始使用 | [快速开始指南](QUICK_START.md) ⭐ |
| 配置和使用 | [用户指南](USER_GUIDE.md) |
| 部署到生产 | [部署指南](DEPLOYMENT_GUIDE.md) |
| 参与开发 | [开发指南](DEVELOPER_GUIDE.md) |
| 了解架构设计 | [设计概览](DESIGN_OVERVIEW.md) |
| 解决问题 | [故障排除](TROUBLESHOOTING.md) |

## 💬 文档反馈

如果你发现文档有以下问题，请通过 Git Issues 反馈：

- ❌ 信息过时或错误
- ❓ 内容不清楚或缺失
- 💡 改进建议
- 🐛 示例代码有误

---

📝 **文档版本**: 与代码版本保持同步  
🔄 **最后更新**: 定期维护，确保信息准确性