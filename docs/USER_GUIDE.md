# UniBackup 用户指南

本指南提供 UniBackup 的完整使用说明，包括配置、SDK 使用、CLI 工具和云存储等所有用户功能。

## 📋 目录

- [配置指南](#配置指南)
- [SDK 使用指南](#sdk-使用指南)
- [CLI 工具指南](#cli-工具指南)
- [云存储指南](#云存储指南)
- [配置迁移指南](#配置迁移指南)

---

# 配置指南

## 📋 配置概览

UniBackup 支持 MySQL 和 Elasticsearch 两种数据源的统一备份，通过灵活的配置系统适应不同的部署环境。

### 核心特性
- **智能模式检测**：自动选择最佳备份模式
- **统一配置结构**：一致的配置体验
- **环境适配**：支持本地和容器化部署
- **安全默认值**：开箱即用的安全配置
- **云存储支持**：支持 AWS S3、Google Cloud Storage、Azure Blob Storage

## 🔧 配置结构

### 存储架构说明

UniBackup 采用分层存储架构，通过 StorageManager 和 Backend 接口实现存储抽象：

#### 存储层次结构
```
UniBackup 应用层
    ↓
StorageManager (存储管理器)
    ↓
Backend 接口 (存储抽象)
    ↓
LocalBackend / GocloudBackend (具体实现)
    ↓
本地文件系统 / 云存储服务
```

#### 核心组件职责
- **StorageManager**: 管理备份记录、元数据、增量链完整性
- **Backend 接口**: 提供统一的存储操作抽象（Put、Get、Delete、List等）
- **LocalBackend**: 本地文件系统实现
- **GocloudBackend**: 云存储实现（基于 gocloud.dev）

#### 存储目录结构
```
{BackupRoot}/
├── tasks.json                    # 任务状态持久化
├── mysql/                        # MySQL 备份
│   ├── archival/                 # 归档备份
│   │   └── {backup_id}/
│   │       ├── metadata.json
│   │       └── data.sql.gz
│   ├── chains/                   # 增量链
│   │   └── {chain_id}/
│   │       ├── chain_meta.json
│   │       ├── initial/          # 初始全量备份
│   │       └── incremental/      # 增量备份
│   └── status.json               # 数据源状态
└── elasticsearch/                # ES 备份
    ├── archival/
    └── chains/
```

## 🚀 快速开始配置

### 最小配置示例

**Go 代码最小配置：**
```go
cfg := &types.Config{
    BackupRoot: "/data/backups",
    Logger:     slog.Default(),
}
```

**JSON 文件最小配置：**
```json
{
  "backup_root": "/data/backups"
}
```

## 🏠 本地部署配置

### 基础配置示例

```go
cfg := &types.Config{
    BackupRoot: "/var/lib/unibackup",
    Logger:     slog.Default(),

    // 任务管理配置
    MaxConcurrentTasks: 5,            // 最大并发恢复任务数（备份任务不受限制）
    TaskRetentionDays:  365,
    MaxTaskHistory:     1000,
    CleanupBackupData:  false,
    
    // 超时配置
    BackupTimeout:  "24h",
    RestoreTimeout: "24h",
    
    // MySQL 配置
    MySQL: &types.MySQLConfig{
        Host:     "localhost",
        Port:     3306,
        User:     "backup_user",
        Password: os.Getenv("MYSQL_PASSWORD"),
        DBName:   "production_db",
        
        // 工具路径配置
        ToolsPath: types.MySQLToolsPath{
            Mysqldump:   "/usr/bin/mysqldump",
            Mysql:       "/usr/bin/mysql",
            Mysqlbinlog: "/usr/bin/mysqlbinlog",
            Mysqladmin:  "/usr/bin/mysqladmin",
        },
    },
    
    // Elasticsearch 配置
    ES: &types.ESConfig{
        Addresses:        []string{"http://localhost:9200"},
        User:             "elastic",
        Password:         os.Getenv("ES_PASSWORD"),
        ArchivalRepoName: "backup-archival",
        ManagedRepoName:  "backup-managed",
        AutoCreateRepos:  true,
    },
}
```

### JSON 配置文件示例

```json
{
  "backup_root": "/var/lib/unibackup",
  "max_concurrent_tasks": 5,
  "task_retention_days": 365,
  "max_task_history": 1000,
  "cleanup_backup_data": false,
  "backup_timeout": "24h",
  "restore_timeout": "24h",
  "mysql": {
    "host": "localhost",
    "port": 3306,
    "user": "backup_user",
    "db_name": "production_db",
    "tools_path": {
      "mysqldump": "/usr/bin/mysqldump",
      "mysql": "/usr/bin/mysql",
      "mysqlbinlog": "/usr/bin/mysqlbinlog",
      "mysqladmin": "/usr/bin/mysqladmin"
    }
  },
  "es": {
    "addresses": ["http://localhost:9200"],
    "user": "elastic",
    "archival_repo_name": "backup-archival",
    "managed_repo_name": "backup-managed",
    "auto_create_repos": true
  }
}
```

## 🐳 Docker 部署配置

### Docker Compose 示例

```yaml
version: '3.8'
services:
  unibackup:
    image: unibackup:latest
    volumes:
      - ./config.json:/app/config.json
      - backup_data:/app/backup
      - mysql_binlogs:/var/lib/mysql-binlogs:ro  # MySQL binlog 访问
    environment:
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - ES_PASSWORD=${ES_PASSWORD}
    depends_on:
      - mysql
      - elasticsearch

  mysql:
    image: mysql:8.0
    command: >
      --log-bin=/var/lib/mysql-binlogs/mysql-bin
      --binlog-format=ROW
      --server-id=1
      --expire-logs-days=7
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_binlogs:/var/lib/mysql-binlogs
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}

volumes:
  backup_data:
  mysql_data:
  mysql_binlogs:
```

### Docker 配置要点

**MySQL Binlog 配置**：
```yaml
mysql:
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin
    --binlog-format=ROW
    --server-id=1
    --expire-logs-days=7                    # 保留7天binlog
    --max-binlog-size=1G                    # 控制单个binlog文件大小
```

**UniBackup 容器配置**：
```go
cfg := &types.Config{
    BackupRoot: "/app/backup",
    
    MySQL: &types.MySQLConfig{
        Host:           "mysql",  // Docker 服务名
        Port:           3306,
        User:           "backup_user",
        Password:       os.Getenv("MYSQL_PASSWORD"),
        DBName:         "production_db",
        BinlogBasePath: "/var/lib/mysql-binlogs",  // 挂载的 binlog 路径
    },
    
    ES: &types.ESConfig{
        Addresses: []string{"http://elasticsearch:9200"},  // Docker 服务名
        User:      "elastic",
        Password:  os.Getenv("ES_PASSWORD"),
    },
}
```

## 🔐 安全配置

### 环境变量管理

**推荐的环境变量**：
```bash
# MySQL 认证
export MYSQL_PASSWORD="secure_mysql_password"

# Elasticsearch 认证
export ES_PASSWORD="secure_es_password"
export ES_API_KEY="base64_encoded_api_key"

# 云存储认证
export AWS_ACCESS_KEY_ID="your_access_key"
export AWS_SECRET_ACCESS_KEY="your_secret_key"
```

### 权限配置

**MySQL 用户权限**：
```sql
-- 创建备份专用用户
CREATE USER 'backup_user'@'%' IDENTIFIED BY 'secure_password';

-- 授予必要权限
GRANT SELECT, RELOAD, LOCK TABLES, REPLICATION CLIENT ON *.* TO 'backup_user'@'%';
GRANT SHOW DATABASES ON *.* TO 'backup_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

**文件系统权限**：
```bash
# 创建备份目录
sudo mkdir -p /var/lib/unibackup
sudo chown backup_user:backup_group /var/lib/unibackup
sudo chmod 750 /var/lib/unibackup

# 设置 MySQL binlog 访问权限
sudo usermod -a -G mysql backup_user
```

## ⚙️ 高级配置

### 云存储配置

**AWS S3 配置示例：**
```go
cfg := &types.Config{
    BackupRoot: "/tmp/unibackup-s3",
    CloudStorage: &types.CloudStorageConfig{
        Enabled: true,
        Type:    "s3",
        Bucket:  "my-backup-bucket",
        Region:  "us-east-1",
        // AWS_ACCESS_KEY_ID 和 AWS_SECRET_ACCESS_KEY 通过环境变量提供
    },
}
```

**Google Cloud Storage 配置示例：**
```go
cfg := &types.Config{
    BackupRoot: "/tmp/unibackup-gcs",
    CloudStorage: &types.CloudStorageConfig{
        Enabled:   true,
        Type:      "gcs",
        Bucket:    "my-gcs-backup-bucket",
        ProjectID: "my-gcp-project",
        // GOOGLE_APPLICATION_CREDENTIALS 通过环境变量提供
    },
}
```

**Azure Blob Storage 配置示例：**
```go
cfg := &types.Config{
    BackupRoot: "/tmp/unibackup-azure",
    CloudStorage: &types.CloudStorageConfig{
        Enabled:     true,
        Type:        "azure",
        Container:   "my-azure-container",
        AccountName: "mystorageaccount",
        // AZURE_STORAGE_ACCOUNT 和 AZURE_STORAGE_KEY 通过环境变量提供
    },
}
```

### 性能调优

```go
cfg := &types.Config{
    // 并发控制（仅限制恢复任务，备份任务可无限制并发）
    MaxConcurrentTasks: 10,           // 最大并发恢复任务数，根据系统资源调整

    // 任务管理
    TaskRetentionDays:  90,           // 任务历史保留时间
    MaxTaskHistory:     5000,         // 内存中最大任务数
    CleanupBackupData:  false,        // 是否清理备份数据
    
    // 超时配置
    BackupTimeout:      "6h",         // 备份操作超时
    RestoreTimeout:     "4h",         // 恢复操作超时
}
```

### 存储模式说明

UniBackup 采用统一存储架构，不支持混合存储模式：

**本地存储模式**：
- `cloud_storage.enabled = false` 或不配置 `cloud_storage`
- 所有数据源（MySQL、ES）的备份都存储在本地 `backup_root` 目录

**云存储模式**：
- `cloud_storage.enabled = true`
- 所有数据源（MySQL、ES）的备份都存储在配置的云存储中
- `backup_root` 仅用于路径生成和临时文件

**重要限制**：
- 不支持部分数据源使用本地存储、部分数据源使用云存储的混合模式
- 所有数据源必须使用相同的存储后端

---

# SDK 使用指南

## 📖 概述

UniBackup SDK 提供了完整的 Go 语言接口，支持 MySQL 和 Elasticsearch 的统一备份恢复操作。

### 核心特性
- **异步操作**：非阻塞的备份和恢复操作
- **任务管理**：完整的任务生命周期管理
- **错误处理**：结构化的错误信息和重试机制
- **并发控制**：智能的并发任务管理
- **状态持久化**：任务状态的持久化存储

## 🚀 快速开始

### 安装

```bash
go get git.gobies.org/fobrain/unibackup
```

### 基本使用

```go
package main

import (
    "context"
    "log/slog"
    "os"

    "git.gobies.org/fobrain/unibackup/pkg/unibackup"
    "git.gobies.org/fobrain/unibackup/pkg/types"
)

func main() {
    // 创建配置
    cfg := &types.Config{
        BackupRoot: "/data/backups",
        Logger:     slog.Default(),
        MySQL: &types.MySQLConfig{
            Host:     "localhost",
            Port:     3306,
            User:     "backup_user",
            Password: os.Getenv("MYSQL_PASSWORD"),
            DBName:   "mydb",
        },
    }

    // 创建备份管理器
    manager, err := unibackup.NewManager(cfg)
    if err != nil {
        panic(err)
    }
    defer manager.Shutdown()

    ctx := context.Background()

    // 异步备份
    taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "每日备份")
    if err != nil {
        panic(err)
    }

    // 查询任务状态
    task, err := manager.GetTask(taskID)
    if err != nil {
        panic(err)
    }
    
    println("任务状态:", string(task.Status))
}
```

## 🔧 核心接口

### BackupManager 接口

```go
type BackupManager interface {
    // === 异步操作接口（推荐使用） ===

    // BackupAsync 异步备份操作，返回任务ID
    BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error)

    // RestoreAsync 异步恢复操作（使用完全重建策略），返回任务ID
    RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error)

    // BackupAllAsync 异步分组备份操作，返回任务ID
    BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error)

    // RestoreAllAsync 异步分组恢复操作（使用完全重建策略），返回任务ID
    RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error)

    // === 任务管理接口 ===

    // GetTask 获取任务详情
    GetTask(taskID string) (*types.Task, error)

    // ListTasks 列出任务
    ListTasks() ([]*types.Task, error)

    // CancelTask 取消任务
    CancelTask(taskID string) error

    // ClearOldTasks 清理旧任务
    ClearOldTasks() error

    // === 新的统一查询接口（推荐使用）===

    // ListAllBackups 基于tasks.json的统一备份查询接口
    // 提供高性能的内存查询，支持过滤、搜索和分页功能
    ListAllBackups(ctx context.Context, filter types.BackupFilter) (*types.BackupListResult, error)

    // GetBackupDetails 获取备份任务的详细信息
    // 支持单个备份和分组备份的详情获取，分组备份会递归显示所有子任务
    GetBackupDetails(ctx context.Context, taskID string) (*types.Task, error)

    // DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型
    // 重要说明：增量备份删除时会删除整条增量链，保持数据完整性
    DeleteBackupByTaskID(ctx context.Context, taskID string) error

    // GetBackupDeletionInfo 获取备份删除的影响信息
    // 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等
    GetBackupDeletionInfo(ctx context.Context, taskID string) (*types.BackupDeletionInfo, error)

    // RestoreByTaskID 基于taskID恢复备份到原始位置
    // 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置
    RestoreByTaskID(ctx context.Context, taskID string) (string, error)

    // === 同步操作接口（向后兼容） ===

    // Backup 同步备份操作，阻塞直到完成
    Backup(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (*types.BackupRecord, *types.BackupError)

    // Restore 同步恢复操作，阻塞直到完成
    Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError

    // === 系统管理接口 ===

    // Shutdown 优雅关闭
    Shutdown() error

    // UpdateConfig 动态更新配置，支持运行时配置变更
    UpdateConfig(cfg *types.Config) error

    // === 原有接口（保留支持，但推荐使用新接口）===

    // Deprecated: 使用 ListAllBackups 替代，性能更好且支持更灵活的过滤条件
    ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError)

    // Deprecated: 使用 ListAllBackups 替代，性能更好且支持更灵活的过滤条件
    ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError)

    // Deprecated: 使用 DeleteBackupByTaskID 替代，提供更好的用户体验和错误处理
    DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) *types.BackupError
}
```

## 📝 基本操作

### 备份操作

**MySQL 备份**：
```go
// 归档备份
taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "每日归档备份")

// 增量链初始备份
taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeChainInitial, "增量链初始备份")

// 增量链增量备份
taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeChainIncremental, "增量备份")
```

**Elasticsearch 备份**：
```go
// ES 归档备份
taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "my_cluster", types.BackupTypeArchival, "ES 每日备份")

// ES 增量链备份
taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "my_cluster", types.BackupTypeChainInitial, "ES 增量链初始备份")
```

### 恢复操作

#### 方式一：基于任务ID恢复（推荐）

**单个恢复**：
```go
// 查询可用备份
filter := types.BackupFilter{
    SourceType: &types.MySQL,
    Status:     &types.TaskStatusCompleted,
    Limit:      10,
}

result, err := manager.ListAllBackups(ctx, filter)
if err != nil {
    return err
}

if len(result.Tasks) == 0 {
    return fmt.Errorf("没有找到可用的备份")
}

// 使用最新备份进行恢复（恢复到原始位置）
taskID, err := manager.RestoreByTaskID(ctx, result.Tasks[0].ID, false)
if err != nil {
    return err
}

fmt.Printf("恢复任务已启动，任务ID: %s\n", taskID)

// 强制恢复（中断正在运行的备份任务）
taskID, err = manager.RestoreByTaskID(ctx, result.Tasks[0].ID, true)
if err != nil {
    return err
}

// 带回调的恢复示例
taskID, err = manager.RestoreByTaskIDWithCallback(ctx, result.Tasks[0].ID, false,
    func(ctx context.Context) error {
        log.Printf("基于任务ID %s 的恢复完成", result.Tasks[0].ID)
        return sendNotificationEmail("restore_by_task_completed", result.Tasks[0].ID)
    })
if err != nil {
    return err
}

fmt.Printf("强制恢复任务已启动，任务ID: %s\n", taskID)

// 基于分组备份任务ID恢复（自动转换为分组恢复）
groupTaskID := "group-backup-task-456"
taskID, err = manager.RestoreByTaskID(ctx, groupTaskID, false)
if err != nil {
    return err
}

fmt.Printf("分组恢复任务已启动，任务ID: %s\n", taskID)
```

**分组恢复**：
```go
// 准备分组恢复配置（带回调示例）
restores := []types.RestoreConfig{
    types.NewRestoreConfig(types.MySQL, "production_db", "mysql-backup-123"),
    types.NewRestoreConfig(types.Elasticsearch, "logs", "es-backup-456"),
}

batchConfig := types.NewBatchRestoreConfig(restores, true) // 原子性恢复
batchConfig.Description = "灾难恢复"

// 设置分组恢复成功回调
batchConfig.OnSuccess = func(ctx context.Context) error {
    log.Println("分组恢复全部成功完成")

    // 通知运维团队
    return notifyOpsTeam("disaster_recovery_completed", len(restores))
}
    Force:       false, // 是否强制恢复
}

// 执行分组恢复
taskID, err := manager.RestoreAllAsync(ctx, batchConfig)
if err != nil {
    return err
}

fmt.Printf("分组恢复任务已启动，任务ID: %s\n", taskID)

// 强制分组恢复示例
batchConfig.Force = true
batchConfig.Description = "紧急灾难恢复"
taskID, err = manager.RestoreAllAsync(ctx, batchConfig)
if err != nil {
    return err
}

fmt.Printf("强制分组恢复任务已启动，任务ID: %s\n", taskID)
```

#### 方式二：自定义恢复配置

**单个恢复**：
```go
// 自定义恢复配置（带回调示例）
restoreConfig := types.NewRestoreConfig(types.MySQL, "production_db", "backup-123")
restoreConfig.Description = "生产数据库恢复"
restoreConfig.Force = false

// 设置恢复成功回调
restoreConfig.OnSuccess = func(ctx context.Context) error {
    // 恢复成功后的处理逻辑
    log.Printf("数据库 %s 恢复成功", restoreConfig.SourceName)

    // 发送通知邮件
    if err := sendNotificationEmail("restore_completed", restoreConfig.SourceName); err != nil {
        return fmt.Errorf("发送通知邮件失败: %w", err)
    }

    // 更新外部监控系统
    return updateMonitoringSystem("database_restored", restoreConfig.SourceName)
}

taskID, err := manager.RestoreAsync(ctx, restoreConfig)
if err != nil {
    return err
}

// 强制恢复示例
restoreConfig.Force = true
taskID, err = manager.RestoreAsync(ctx, restoreConfig)
if err != nil {
    return err
}
```

**增量链恢复到指定时间点**：
```go
// 恢复到指定时间点
pointInTime := time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC)

restoreConfig := types.RestoreConfig{
    SourceType:    types.MySQL,
    SourceName:    "production_db",
    BackupTaskID:  "incremental-chain-task-123",
    PointInTime:   &pointInTime,         // 指定恢复时间点
    TargetName:    "production_db",
    OverwriteData: true,
    Force:         false,                // 是否强制恢复
}

taskID, err := manager.RestoreAsync(ctx, restoreConfig)
```

### 任务管理

#### 查询任务状态

**获取单个任务详情**：
```go
task, err := manager.GetTask(taskID)
if err != nil {
    return err
}

switch task.Status {
case types.TaskStatusPending:
    fmt.Printf("任务等待中: %s\n", task.Description)
case types.TaskStatusRunning:
    fmt.Printf("任务进行中: %s\n", task.Progress)
case types.TaskStatusCompleted:
    fmt.Printf("任务已完成: %s\n", task.Description)
    if task.BackupID != "" {
        fmt.Printf("备份ID: %s\n", task.BackupID)
    }
case types.TaskStatusFailed:
    fmt.Printf("任务失败: %s\n", task.Error)
case types.TaskStatusCancelled:
    fmt.Printf("任务已取消: %s\n", task.Description)
}
```

**获取备份任务详情（支持分组备份）**：
```go
// 获取备份任务的详细信息，分组备份会递归显示所有子任务
taskDetails, err := manager.GetBackupDetails(ctx, taskID)
if err != nil {
    return err
}

fmt.Printf("任务类型: %s\n", taskDetails.Type)
fmt.Printf("任务状态: %s\n", taskDetails.Status)

// 如果是分组任务，显示子任务信息
if taskDetails.Type == types.TaskTypeBackupAll {
    if subTasks, ok := taskDetails.Metadata["sub_tasks"].([]interface{}); ok {
        fmt.Printf("包含 %d 个子任务:\n", len(subTasks))
        for i, subTask := range subTasks {
            fmt.Printf("  子任务 %d: %v\n", i+1, subTask)
        }
    }
}
```

#### 列出和过滤任务

**列出所有任务**：
```go
tasks, err := manager.ListTasks()
if err != nil {
    return err
}

for _, task := range tasks {
    fmt.Printf("任务ID: %s, 状态: %s, 类型: %s\n",
        task.ID, task.Status, task.Type)
}
```

#### 任务控制操作

**取消正在运行的任务**：
```go
// 取消任务
err := manager.CancelTask(taskID)
if err != nil {
    fmt.Printf("取消任务失败: %v\n", err)
    return err
}

fmt.Printf("任务 %s 已成功取消\n", taskID)

// 验证取消状态
task, err := manager.GetTask(taskID)
if err == nil && task.Status == types.TaskStatusCancelled {
    fmt.Println("任务状态已更新为已取消")
}
```

**清理旧任务**：
```go
// 清理旧任务（根据配置的保留策略）
err := manager.ClearOldTasks()
if err != nil {
    fmt.Printf("清理旧任务失败: %v\n", err)
    return err
}

fmt.Println("旧任务清理完成")

// 查看清理后的任务数量
tasks, err := manager.ListTasks()
if err == nil {
    fmt.Printf("当前任务数量: %d\n", len(tasks))
}
```

### 备份管理

#### 统一查询接口（推荐使用）

**基础查询**：
```go
// 查询所有任务（默认行为）
filter := types.BackupFilter{
    Limit: 50,  // 限制返回数量
}

result, err := manager.ListAllBackups(ctx, filter)
if err != nil {
    return err
}

fmt.Printf("总共找到 %d 个任务\n", result.Total)
for _, task := range result.Tasks {
    fmt.Printf("任务ID: %s, 类型: %s, 状态: %s, 开始时间: %s\n",
        task.ID, task.Type, task.Status, task.StartTime.Format("2006-01-02 15:04:05"))
}
```

**多值过滤查询**：
```go
// 查询分组任务（备份和恢复）
filter := types.BackupFilter{
    TaskTypes: []types.TaskType{
        types.BackupAllTask,
        types.RestoreAllTask,
    },
    Limit: 20,
}

result, err := manager.ListAllBackups(ctx, filter)

// 查询MySQL的增量备份
filter = types.BackupFilter{
    TaskTypes: []types.TaskType{types.BackupTask},
    SourceTypes: []types.SourceType{types.MySQL},
    BackupTypes: []types.BackupType{
        types.BackupTypeChainInitial,
        types.BackupTypeChainIncremental,
    },
    Limit: 20,
}

result, err = manager.ListAllBackups(ctx, filter)

// 查询已完成的任务
filter = types.BackupFilter{
    Statuses: []types.TaskStatus{types.TaskStatusCompleted},
    Limit:    20,
}

result, err = manager.ListAllBackups(ctx, filter)
```

**时间范围查询**：
```go
// 查询最近7天的备份
startTime := time.Now().AddDate(0, 0, -7)
endTime := time.Now()

filter := types.BackupFilter{
    StartTime: &startTime,
    EndTime:   &endTime,
    Limit:     100,
}

result, err := manager.ListAllBackups(ctx, filter)
if err != nil {
    return err
}

fmt.Printf("最近7天共有 %d 个备份\n", result.Total)
```

**搜索功能**：
```go
// 按描述搜索任务
filter := types.BackupFilter{
    SearchText: "每日备份",  // 搜索描述中包含"每日备份"的任务
    Limit:      20,
}

result, err := manager.ListAllBackups(ctx, filter)
```

**复杂组合查询**：
```go
// 查询最近7天MySQL的失败备份任务
startTime := time.Now().AddDate(0, 0, -7)
filter := types.BackupFilter{
    TaskTypes: []types.TaskType{
        types.BackupTask,
        types.BackupAllTask,
    },
    SourceTypes: []types.SourceType{types.MySQL},
    Statuses:    []types.TaskStatus{types.TaskStatusFailed},
    StartTime:   &startTime,
    SearchText:  "备份",
    Limit:       10,
}

result, err := manager.ListAllBackups(ctx, filter)
fmt.Printf("找到 %d 个符合条件的任务\n", result.Total)
fmt.Printf("实际使用的过滤条件: %+v\n", result.Filter)
```

#### 备份删除操作

**安全删除备份**：
```go
// 获取删除影响信息
deletionInfo, err := manager.GetBackupDeletionInfo(ctx, taskID)
if err != nil {
    return err
}

fmt.Printf("删除影响分析:\n")
fmt.Printf("  备份类型: %s\n", deletionInfo.BackupType)
fmt.Printf("  数据源: %s\n", deletionInfo.SourceName)

if deletionInfo.IsIncrementalChain {
    fmt.Printf("  ⚠️  这是增量备份，删除将影响整条增量链\n")
    fmt.Printf("  影响的备份数量: %d\n", len(deletionInfo.AffectedBackups))

    fmt.Println("  受影响的备份:")
    for _, backup := range deletionInfo.AffectedBackups {
        fmt.Printf("    - %s (%s)\n", backup.ID, backup.Description)
    }
}

// 确认后执行删除
fmt.Print("确认删除? (y/N): ")
var confirm string
fmt.Scanln(&confirm)

if confirm == "y" || confirm == "Y" {
    err := manager.DeleteBackupByTaskID(ctx, taskID)
    if err != nil {
        fmt.Printf("删除失败: %v\n", err)
        return err
    }

    fmt.Printf("备份 %s 已成功删除\n", taskID)
} else {
    fmt.Println("删除操作已取消")
}
```

#### 原有接口（兼容性支持）

**注意**：以下接口仍然可用，但推荐使用上述新接口以获得更好的性能和用户体验。

```go
// 原有接口示例（不推荐新项目使用）
archivalBackups, err := manager.ListArchivalBackups(ctx, types.MySQL)
chains, err := manager.ListIncrementalChains(ctx, types.MySQL)
err = manager.DeleteBackup(ctx, types.MySQL, "backup-id")
```

## 🔄 高级功能

### 分组操作

#### 分组备份

**基础分组备份**：
```go
request := types.BackupAllRequest{
    Requests: []types.BackupRequest{
        {
            SourceType:  types.MySQL,
            SourceName:  "production_db",
            BackupType:  types.BackupTypeArchival,
            Description: "生产数据库备份",
        },
        {
            SourceType:  types.Elasticsearch,
            SourceName:  "search_cluster",
            BackupType:  types.BackupTypeArchival,
            Description: "搜索集群备份",
        },
    },
    Description: "每日全量备份",
}

taskID, err := manager.BackupAllAsync(ctx, request)
if err != nil {
    return err
}

fmt.Printf("分组备份任务已启动，任务ID: %s\n", taskID)
```

**增量链分组备份**：
```go
// 创建增量链初始备份
initialRequest := types.BackupAllRequest{
    Requests: []types.BackupRequest{
        {
            SourceType:  types.MySQL,
            SourceName:  "production_db",
            BackupType:  types.BackupTypeChainInitial,
            Description: "MySQL增量链初始备份",
        },
        {
            SourceType:  types.Elasticsearch,
            SourceName:  "search_cluster",
            BackupType:  types.BackupTypeChainInitial,
            Description: "ES增量链初始备份",
        },
    },
    Description: "增量链初始备份",
}

initialTaskID, err := manager.BackupAllAsync(ctx, initialRequest)
if err != nil {
    return err
}

// 等待初始备份完成
for {
    task, err := manager.GetTask(initialTaskID)
    if err != nil {
        return err
    }

    if task.Status == types.TaskStatusCompleted {
        fmt.Println("增量链初始备份完成")
        break
    } else if task.Status == types.TaskStatusFailed {
        return fmt.Errorf("增量链初始备份失败: %s", task.Error)
    }

    time.Sleep(5 * time.Second)
}

// 创建增量备份
incrementalRequest := types.BackupAllRequest{
    Requests: []types.BackupRequest{
        {
            SourceType:  types.MySQL,
            SourceName:  "production_db",
            BackupType:  types.BackupTypeChainIncremental,
            Description: "MySQL增量备份",
        },
        {
            SourceType:  types.Elasticsearch,
            SourceName:  "search_cluster",
            BackupType:  types.BackupTypeChainIncremental,
            Description: "ES增量备份",
        },
    },
    Description: "增量备份",
}

incrementalTaskID, err := manager.BackupAllAsync(ctx, incrementalRequest)
```

#### 分组恢复

**基于任务ID的分组恢复**：
```go
// 准备分组恢复配置
batchConfig := types.BatchRestoreConfig{
    TaskIDs: []string{
        "mysql-backup-task-123",
        "es-backup-task-456",
    },
    Description: "灾难恢复 - 恢复到生产环境",
}

// 执行分组恢复
restoreTaskID, err := manager.RestoreAllAsync(ctx, batchConfig)
if err != nil {
    return err
}

fmt.Printf("分组恢复任务已启动，任务ID: %s\n", restoreTaskID)

// 监控恢复进度
for {
    task, err := manager.GetTask(restoreTaskID)
    if err != nil {
        return err
    }

    switch task.Status {
    case types.TaskStatusCompleted:
        fmt.Println("分组恢复完成")
        return nil
    case types.TaskStatusFailed:
        return fmt.Errorf("分组恢复失败: %s", task.Error)
    case types.TaskStatusRunning:
        fmt.Printf("分组恢复进行中: %s\n", task.Progress)
        time.Sleep(10 * time.Second)
    }
}
```

### 错误处理

```go
taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "测试备份")
if err != nil {
    // 处理启动错误
    if backupErr, ok := err.(*types.BackupError); ok {
        fmt.Printf("错误代码: %s\n", backupErr.Code)
        fmt.Printf("错误信息: %s\n", backupErr.Message)
        fmt.Printf("是否可重试: %v\n", backupErr.Retryable)
    }
    return err
}

// 监控任务执行
for {
    task, err := manager.GetTask(taskID)
    if err != nil {
        return err
    }

    switch task.Status {
    case types.TaskStatusCompleted:
        fmt.Println("备份成功完成")
        return nil
    case types.TaskStatusFailed:
        fmt.Printf("备份失败: %s\n", task.Error)
        return fmt.Errorf("backup failed: %s", task.Error)
    case types.TaskStatusRunning:
        fmt.Printf("备份进行中: %s\n", task.Progress)
        time.Sleep(5 * time.Second)
    }
}
```

### 配置验证

```go
// 验证配置
err := cfg.Validate()
if err != nil {
    fmt.Printf("配置验证失败: %v\n", err)
    return
}

// 测试连接
manager, err := unibackup.NewManager(cfg)
if err != nil {
    fmt.Printf("创建管理器失败: %v\n", err)
    return
}
defer manager.Shutdown()
```

## 🛠️ 工具使用

### 配置验证

```bash
# 验证配置文件
./build/unibackup-admin -validate config.json

# 生成示例配置
./build/unibackup-admin -generate sample-config.json
```

### 文档查看

```bash
# 生成API文档
make docs

# 启动文档服务器
make docs-serve
# 访问 http://localhost:6060/pkg/unibackup/
```

### 测试

```bash
# 运行所有测试
make test

# 运行特定模块测试
make test-module MODULE=pkg/types

# 查看测试覆盖率
make test-coverage
```

## 🚨 故障排查

### 常见问题

1. **MySQL工具未找到**
   - 检查工具路径配置
   - 确保MySQL客户端已安装
   - 使用配置验证工具检查

2. **ES连接失败**
   - 检查地址格式
   - 验证认证配置
   - 确保快照仓库已配置

3. **权限问题**
   - 检查备份目录权限
   - 验证数据库用户权限
   - 确保工具可执行权限

### 调试技巧

```go
// 启用详细日志
logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelDebug,
    AddSource: true,
}))

cfg := &types.Config{
    Logger: logger,
    // ... 其他配置
}
```

---

# CLI 工具指南

## 📖 概述

UniBackup CLI 是一个功能完整的企业级备份恢复命令行工具，支持 MySQL 和 Elasticsearch 的单个及分组操作。

## 🚀 基本使用

### 查看帮助
```bash
unibackup --help
unibackup [command] --help
```

### 全局参数
- `--backup-root` - 备份根目录 (默认: /app/backup)
- `--config` - 配置文件路径
- `--verbose` - 详细输出
- `--timeout` - 操作超时时间 (默认: 30m)

## 📝 单个数据源操作

### 备份操作

#### MySQL 备份
```bash
# 归档备份
unibackup backup mysql --source mydb --type archival

# 增量链初始备份
unibackup backup mysql --source mydb --type chain-initial

# 增量链增量备份
unibackup backup mysql --source mydb --type chain-incremental
```

#### Elasticsearch 备份
```bash
# 归档备份
unibackup backup elasticsearch --source cluster1 --type archival

# 增量链备份
unibackup backup elasticsearch --source cluster1 --type chain-initial
```

### 恢复操作

#### MySQL 恢复
```bash
# 列出可用备份
unibackup list mysql --type archival

# 恢复指定备份
unibackup restore mysql --backup-id backup-20240115-143022 --target mydb

# 恢复到指定时间点（增量链）
unibackup restore mysql --chain-id chain-20240115 --point-in-time "2024-01-15 15:30:00" --target mydb

# 强制恢复（中断正在运行的备份任务）
unibackup restore mysql --backup-id backup-20240115-143022 --target mydb --force
```

> **⚠️ 强制恢复说明**：
> - `--force` 选项会中断目标数据源正在运行的备份任务
> - 适用于高频备份场景下的紧急恢复需求
> - 系统会要求用户确认后才执行强制恢复

#### Elasticsearch 恢复
```bash
# 列出ES备份
unibackup list elasticsearch --type archival

# 恢复ES备份
unibackup restore elasticsearch --backup-id es-backup-20240115-143022 --target default

# 强制恢复ES备份
unibackup restore elasticsearch --backup-id es-backup-20240115-143022 --target default --force
```

## 🔄 分组操作

### 分组备份
```bash
# 使用配置文件进行分组备份
unibackup backup-all --config backup-config.json --description "每日全量备份"

# 命令行指定多个数据源
unibackup backup-all \
  --mysql mydb1,mydb2 \
  --elasticsearch cluster1 \
  --type archival \
  --description "多数据源备份"
```

### 分组恢复
```bash
# 分组恢复
unibackup group restore \
  --mysql-restores backup-123:mydb1,backup-456:mydb2 \
  --es-restores es-backup-789:cluster1 \
  --description "灾难恢复"

# 强制分组恢复
unibackup group restore \
  --mysql-restores backup-123:mydb1,backup-456:mydb2 \
  --es-restores es-backup-789:cluster1 \
  --description "紧急灾难恢复" \
  --force
```

## 📊 查询和管理

### 备份查询
```bash
# 列出所有备份
unibackup list --all

# 按类型查询
unibackup list mysql --type archival
unibackup list elasticsearch --type chain

# 按时间范围查询
unibackup list --from "2024-01-01" --to "2024-01-31"

# 搜索备份
unibackup list --search "daily"
```

### 任务管理
```bash
# 列出任务
unibackup tasks list

# 查看任务详情
unibackup tasks show --task-id task-123

# 取消任务
unibackup tasks cancel --task-id task-123

# 基于任务ID恢复（推荐方式）
unibackup restore-by-task task-123

# 强制基于任务ID恢复
unibackup restore-by-task task-123 --force

# 基于分组备份任务ID恢复（自动转换为分组恢复）
unibackup restore-by-task group-backup-task-456 --force
```

> **💡 基于任务ID恢复的优势**：
> - 自动识别数据源类型和配置
> - 确保恢复到备份时的原始状态
> - 支持强制恢复模式
> - **支持分组备份任务**：自动转换为分组恢复操作
> - **自动回滚保护**：失败时自动回滚到原始状态

#### 🔄 自动回滚功能

UniBackup 为所有恢复操作提供自动回滚功能，确保数据安全：

**默认安全配置**：
- `CreateSafetyBackup: true` - 恢复前自动创建安全备份
- `RollbackOnFailure: true` - 失败时自动回滚到原始状态

**支持的恢复模式**：
- ✅ 单个恢复任务 - 完整回滚支持
- ✅ 分组串行恢复 - 完整回滚支持
- ✅ 分组并行恢复 - 完整回滚支持

**回滚流程**：
1. 恢复前创建预恢复快照
2. 执行恢复操作
3. 如果失败，自动使用快照回滚
4. 成功后自动清理临时快照

**使用示例**：
```bash
# 使用默认安全配置恢复（推荐）
unibackup restore-by-task task-123

# 强制恢复（仍然包含回滚保护）
unibackup restore-by-task task-123 --force
```

# 清理旧任务
unibackup tasks cleanup --older-than 30d
```

### 备份管理
```bash
# 删除备份
unibackup delete mysql --backup-id backup-20240115-143022

# 删除增量链
unibackup delete mysql --chain-id chain-20240115

# 验证备份完整性
unibackup verify --backup-id backup-20240115-143022
```

## ⚙️ 配置管理

### 配置文件操作
```bash
# 验证配置文件
unibackup config validate --file config.json

# 生成示例配置
unibackup config generate --output sample-config.json

# 显示当前配置
unibackup config show
```

### 环境变量
```bash
# 设置常用环境变量
export UNIBACKUP_BACKUP_ROOT="/data/backups"
export UNIBACKUP_CONFIG="/etc/unibackup/config.json"
export MYSQL_PASSWORD="your_password"
export ES_PASSWORD="your_es_password"
```

## 🔍 监控和状态

### 状态检查
```bash
# 检查系统状态
unibackup status

# 检查数据源连接
unibackup status --check-connections

# 检查存储健康
unibackup status --check-storage
```

### 统计信息
```bash
# 备份统计
unibackup stats --summary

# 详细统计
unibackup stats --detailed

# 存储使用情况
unibackup stats --storage
```

## 🚨 故障排除

### 诊断命令
```bash
# 诊断配置问题
unibackup diagnose config

# 诊断连接问题
unibackup diagnose connections

# 诊断权限问题
unibackup diagnose permissions
```

### 日志查看
```bash
# 查看最近的日志
unibackup logs --tail 100

# 查看特定任务的日志
unibackup logs --task-id task-123

# 实时查看日志
unibackup logs --follow
```

---

# 云存储指南

## 📋 概述

UniBackup 支持多种云存储服务，提供企业级的备份存储解决方案：

- **AWS S3** - Amazon Simple Storage Service
- **Google Cloud Storage (GCS)** - Google 云存储
- **Azure Blob Storage** - Microsoft Azure 对象存储

## 🚀 快速开始

### 基本配置结构

```yaml
backup_root: "/var/backups/unibackup"  # 必填，用于路径生成
cloud_storage:
  enabled: true                        # 启用云存储
  type: "s3"                          # 云存储类型：s3/gcs/azure
  # 其他配置字段根据存储类型而定
```

### 存储模式说明

**统一存储架构**：
- 云存储启用时，所有数据源（MySQL、ES）都使用云存储
- 云存储禁用时，所有数据源都使用本地存储
- 不支持混合模式（部分本地、部分云端）

## ☁️ AWS S3 配置

### 完整配置示例

```yaml
backup_root: "/var/backups/unibackup"
cloud_storage:
  enabled: true
  type: "s3"
  # S3 必填字段
  bucket: "my-backup-bucket"           # S3 存储桶名称
  region: "us-east-1"                  # AWS 区域
  # S3 认证字段（可选，推荐使用环境变量或 IAM 角色）
  access_key: "AKIAIOSFODNN7EXAMPLE"   # AWS 访问密钥
  secret_key: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"  # AWS 密钥

mysql:
  host: "localhost"
  port: 3306
  user: "backup_user"
  db_name: "production"
```

### 认证方式

#### 1. 配置文件认证（推荐用于开发环境）
```yaml
cloud_storage:
  type: "s3"
  access_key: "your-access-key"
  secret_key: "your-secret-key"
```

#### 2. 环境变量认证（推荐用于生产环境）
```bash
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
```

```yaml
cloud_storage:
  type: "s3"
  # access_key 和 secret_key 留空，使用环境变量
```

#### 3. IAM 角色认证（推荐用于 EC2 实例）
```yaml
cloud_storage:
  type: "s3"
  # 不配置 access_key 和 secret_key，使用 IAM 角色
```

### MinIO 兼容存储

```yaml
cloud_storage:
  enabled: true
  type: "s3"
  bucket: "backup-bucket"
  region: "us-east-1"
  endpoint: "http://minio.example.com:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
```

## 🔍 Google Cloud Storage 配置

### 完整配置示例

```yaml
backup_root: "/var/backups/unibackup"
cloud_storage:
  enabled: true
  type: "gcs"
  # GCS 必填字段
  bucket: "my-backup-bucket"           # GCS 存储桶名称
  project_id: "my-project-id"          # Google Cloud 项目 ID
  # GCS 认证字段
  credentials_file: "/path/to/service-account.json"  # 服务账户凭据文件路径

mysql:
  host: "localhost"
  port: 3306
  user: "backup_user"
  db_name: "production"
```

### 认证方式

#### 1. 服务账户文件认证（推荐）
```yaml
cloud_storage:
  type: "gcs"
  credentials_file: "/path/to/service-account.json"
```

#### 2. 环境变量认证
```bash
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
```

```yaml
cloud_storage:
  type: "gcs"
  # credentials_file 留空，使用环境变量
```

## 🔷 Azure Blob Storage 配置

### 完整配置示例

```yaml
backup_root: "/var/backups/unibackup"
cloud_storage:
  enabled: true
  type: "azure"
  # Azure 必填字段
  container: "my-backup-container"     # Azure 容器名称
  account_name: "mystorageaccount"     # 存储账户名称
  # Azure 认证字段
  account_key: "your-storage-account-key"  # 存储账户密钥

mysql:
  host: "localhost"
  port: 3306
  user: "backup_user"
  db_name: "production"
```

### 认证方式

#### 1. 账户密钥认证
```yaml
cloud_storage:
  type: "azure"
  account_key: "your-storage-account-key"
```

#### 2. 环境变量认证
```bash
export AZURE_STORAGE_ACCOUNT="mystorageaccount"
export AZURE_STORAGE_KEY="your-storage-account-key"
```

```yaml
cloud_storage:
  type: "azure"
  # account_key 留空，使用环境变量
```

## 🔧 Elasticsearch 云存储集成

### 自动仓库管理

UniBackup 会自动为 Elasticsearch 创建和配置云存储仓库：

```yaml
es:
  addresses: ["http://localhost:9200"]
  archival_repo_name: "s3-archival"     # 归档仓库名称
  managed_repo_name: "s3-managed"       # 管理仓库名称
  auto_create_repos: true               # 自动创建仓库
```

### 自动验证流程

UniBackup 启动时会自动验证：
1. ✅ ES 集群连接状态
2. ✅ repository-s3 插件安装状态
3. ✅ S3 认证配置正确性
4. ✅ 云存储连接性测试
5. ✅ 快照仓库创建和验证

### 详细部署指南

完整的生产环境部署步骤请参考：
 **[ES 云存储部署指南](../deployment/ES_CLOUD_STORAGE_DEPLOYMENT.md)**

## 🚨 故障排除

### 常见问题

#### 1. 认证失败
```
错误：创建云存储后端失败: 认证失败
```

**解决方案**：
- 检查认证信息是否正确
- 验证环境变量是否设置
- 确认服务账户文件路径正确

#### 2. 权限不足
```
错误：权限被拒绝
```

**解决方案**：
- 检查 IAM 权限配置
- 确认存储桶/容器访问权限
- 验证访问密钥的权限范围

#### 3. 网络连接问题
```
错误：连接超时
```

**解决方案**：
- 检查网络连接
- 验证防火墙设置
- 确认端点 URL 正确（如使用自定义端点）

### 调试模式

启用详细日志以获取更多调试信息：

```yaml
logger:
  level: "debug"
  format: "json"
```

## 🔧 性能优化

### 1. 网络优化
- 选择地理位置接近的存储区域
- 使用 CDN 加速（如适用）
- 配置适当的超时时间

### 2. 存储优化
- 使用存储类别优化成本（如 S3 的 IA、Glacier）
- 配置生命周期策略自动管理旧备份
- 启用压缩减少存储空间

### 3. 并发优化
- 根据网络带宽调整并发数
- 使用分片上传处理大文件
- 配置适当的重试策略

## 📊 监控和告警

### 健康检查

UniBackup 提供内置的云存储健康检查：

```go
// 检查云存储连接状态
err := backend.HealthCheck(ctx)
if err != nil {
    log.Error("云存储健康检查失败", "error", err)
}
```

### 监控指标

建议监控以下指标：
- 备份上传成功率
- 备份文件大小和数量
- 云存储 API 调用延迟
- 存储成本和使用量

---

# 配置迁移指南

## 📋 概述

本指南详细说明如何在不同配置模式之间进行迁移，包括从本地存储切换到云存储、认证方式变更、以及配置升级等场景。

## 🔄 存储模式迁移

### 从本地存储迁移到云存储

#### 迁移前准备

**1. 备份现有配置和数据**
```bash
# 备份配置文件
cp /etc/unibackup/config.json /etc/unibackup/config.json.backup

# 备份现有备份数据
tar -czf /tmp/unibackup-backup-$(date +%Y%m%d).tar.gz /var/lib/unibackup/

# 记录当前备份状态
./unibackup-admin backup list --format json > /tmp/backup-inventory.json
```

**2. 验证云存储环境**
```bash
# 测试 S3 连接
aws s3 ls s3://your-backup-bucket

# 验证 ES 插件
curl -X GET "localhost:9200/_cat/plugins?v" | grep repository-s3
```

#### 迁移步骤

**步骤1：更新配置文件**
```json
{
  "backup_root": "/var/lib/unibackup",
  "cloud_storage": {
    "enabled": true,
    "type": "s3",
    "bucket": "your-backup-bucket",
    "region": "us-east-1"
  },
  "mysql": {
    "host": "localhost",
    "port": 3306,
    "user": "backup_user",
    "db_name": "production"
  },
  "es": {
    "addresses": ["http://localhost:9200"],
    "archival_repo_name": "s3-archival",
    "managed_repo_name": "s3-managed",
    "auto_create_repos": true
  }
}
```

**步骤2：验证新配置**
```bash
# 验证配置文件
./unibackup-admin -validate config.json

# 测试云存储连接
./unibackup-admin -test-storage config.json
```

**步骤3：执行测试备份**
```bash
# 执行小规模测试备份
unibackup backup mysql --source test_db --type archival --description "迁移测试"
```

**步骤4：数据迁移（可选）**
```bash
# 如需迁移现有备份到云存储
./unibackup-admin migrate-storage \
  --from local \
  --to s3 \
  --config config.json \
  --backup-root /var/lib/unibackup
```

### 从云存储迁移到本地存储

#### 迁移步骤

**步骤1：更新配置**
```json
{
  "backup_root": "/var/lib/unibackup",
  "cloud_storage": {
    "enabled": false
  },
  "mysql": {
    "host": "localhost",
    "port": 3306,
    "user": "backup_user",
    "db_name": "production"
  }
}
```

**步骤2：下载云端备份（可选）**
```bash
# 下载现有云端备份
./unibackup-admin download-backups \
  --from s3 \
  --to /var/lib/unibackup \
  --config config.json
```

## 🔐 认证方式迁移

### 从配置文件认证迁移到环境变量

**迁移前配置**：
```json
{
  "cloud_storage": {
    "enabled": true,
    "type": "s3",
    "bucket": "backup-bucket",
    "region": "us-east-1",
    "access_key": "AKIAIOSFODNN7EXAMPLE",
    "secret_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
  }
}
```

**迁移后配置**：
```json
{
  "cloud_storage": {
    "enabled": true,
    "type": "s3",
    "bucket": "backup-bucket",
    "region": "us-east-1"
  }
}
```

**环境变量设置**：
```bash
export AWS_ACCESS_KEY_ID="AKIAIOSFODNN7EXAMPLE"
export AWS_SECRET_ACCESS_KEY="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
```

### 迁移到 IAM 角色认证

**步骤1：创建 IAM 角色**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::backup-bucket",
        "arn:aws:s3:::backup-bucket/*"
      ]
    }
  ]
}
```

**步骤2：更新配置**
```json
{
  "cloud_storage": {
    "enabled": true,
    "type": "s3",
    "bucket": "backup-bucket",
    "region": "us-east-1"
    // 移除 access_key 和 secret_key
  }
}
```

## 🔄 配置格式迁移

### JSON 转 YAML

**原 JSON 配置**：
```json
{
  "backup_root": "/var/lib/unibackup",
  "max_concurrent_tasks": 5,
  "mysql": {
    "host": "localhost",
    "port": 3306,
    "user": "backup_user",
    "db_name": "production"
  }
}
```

**转换后的 YAML 配置**：
```yaml
backup_root: "/var/lib/unibackup"
max_concurrent_tasks: 5
mysql:
  host: "localhost"
  port: 3306
  user: "backup_user"
  db_name: "production"
```

**转换工具**：
```bash
# 使用 yq 工具转换
yq eval -P config.json > config.yaml

# 使用 Python 转换
python3 -c "
import json, yaml
with open('config.json') as f:
    data = json.load(f)
with open('config.yaml', 'w') as f:
    yaml.dump(data, f, default_flow_style=False)
"
```

### 配置文件转环境变量

**配置映射规则**：
```bash
# backup_root -> UNIBACKUP_BACKUP_ROOT
export UNIBACKUP_BACKUP_ROOT="/var/lib/unibackup"

# cloud_storage.enabled -> UNIBACKUP_CLOUD_STORAGE_ENABLED
export UNIBACKUP_CLOUD_STORAGE_ENABLED="true"

# cloud_storage.type -> UNIBACKUP_CLOUD_STORAGE_TYPE
export UNIBACKUP_CLOUD_STORAGE_TYPE="s3"

# cloud_storage.bucket -> UNIBACKUP_CLOUD_STORAGE_BUCKET
export UNIBACKUP_CLOUD_STORAGE_BUCKET="backup-bucket"
```

**自动转换脚本**：
```bash
#!/bin/bash
CONFIG_FILE="config.json"

# 生成环境变量
cat "$CONFIG_FILE" | jq -r '
  def to_env_key(path):
    "UNIBACKUP_" + (path | map(ascii_upcase) | join("_"));

  def flatten(prefix):
    . as $in
    | reduce keys[] as $key (
        {};
        . + (
          if ($in[$key] | type) == "object" then
            ($in[$key] | flatten(prefix + [$key]))
          else
            {(to_env_key(prefix + [$key])): $in[$key]}
          end
        )
      );

  flatten([]) | to_entries[] | "export \(.key)=\"\(.value)\""
'
```

## ✅ 迁移验证清单

### 配置验证
- [ ] 配置文件语法正确
- [ ] 所有必需字段已配置
- [ ] 认证信息正确设置
- [ ] 网络连接正常

### 功能验证
- [ ] 备份功能正常
- [ ] 恢复功能正常
- [ ] 任务管理正常
- [ ] 日志记录正常

### 性能验证
- [ ] 备份速度符合预期
- [ ] 存储空间使用合理
- [ ] 系统资源占用正常
- [ ] 网络带宽使用合理

### 安全验证
- [ ] 认证配置安全
- [ ] 数据传输加密
- [ ] 访问权限正确
- [ ] 敏感信息保护

## 🔄 回滚计划

### 快速回滚
```bash
# 1. 恢复配置文件
cp /etc/unibackup/config.json.backup /etc/unibackup/config.json

# 2. 重启服务
systemctl restart unibackup

# 3. 验证功能
unibackup status --check-all
```

### 数据回滚
```bash
# 如果需要恢复本地备份数据
tar -xzf /tmp/unibackup-backup-$(date +%Y%m%d).tar.gz -C /
```

## 📚 相关文档

- [配置指南](#配置指南) - 完整的配置说明
- [云存储指南](#云存储指南) - 云存储详细配置
- [配置示例代码](../../examples/configurations/) - 可运行的配置示例

---

## 🚨 强制恢复最佳实践

### 使用场景

**适合使用强制恢复的场景**：
- 高频增量备份环境（如每5分钟一次）
- 紧急数据恢复需求
- 备份任务异常长时间运行

**不建议使用强制恢复的场景**：
- 全量备份正在进行中（可能导致备份数据不完整）
- 系统负载较高时
- 非紧急恢复需求

### 安全注意事项

1. **确认备份完整性**：
   ```bash
   # 强制恢复前，确认备份数据完整
   unibackup list --backup-id backup-123 --verify
   ```

2. **创建安全备份**：
   ```bash
   # 恢复前创建当前数据的安全备份
   unibackup restore mysql --backup-id backup-123 --target mydb --create-safety-backup
   ```

3. **监控系统状态**：
   ```bash
   # 检查系统当前任务状态
   unibackup tasks list --status running
   ```

### 操作流程建议

1. **评估紧急程度**：确认是否真的需要强制恢复
2. **检查备份状态**：确认要恢复的备份数据完整
3. **通知相关人员**：强制恢复会中断正在运行的备份
4. **执行强制恢复**：使用 `--force` 选项
5. **验证恢复结果**：确认数据恢复正确
6. **重新启动备份**：如有必要，手动启动被中断的备份

---

> 💡 **提示**: 配置完成后，建议使用 `unibackup-admin -validate` 工具验证配置的正确性。
>
> 🔧 **工具**: 使用 `unibackup-admin -generate` 可以生成标准的配置模板。
```
