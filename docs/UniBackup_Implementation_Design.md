# UniBackup: 详细设计与目录结构

本文档基于《UniBackup: 统一备份恢复SDK 设计文档》，旨在为开发者提供一份详细的、可直接用于编码实现的模块化设计和项目结构指南。

---

## 1. SDK 目录结构 (Go Project Layout)

我们将遵循标准的Go项目布局，以分离公共API和内部实现，确保代码的清晰性和可维护性。

```
backup-corsor/                   // 企业级项目结构
├── .gitignore
├── go.mod
├── go.sum
├── Makefile                     // 构建和测试自动化
├── README.md                    // 项目文档
├── CLAUDE.md                    // 开发指导文档
│
├── cmd/                         // 命令行工具
│   ├── unibackup/
│   │   ├── main.go              // 主CLI入口
│   │   └── main_test.go
│   └── unibackup-admin/
│       ├── main.go              // 配置管理工具
│       └── config_validator.go  // 配置验证器
│
├── pkg/                         // 公共SDK接口
│   ├── unibackup/
│   │   └── manager.go           // 公共BackupManager接口定义
│   └── types/
│       ├── types.go             // 公共类型定义
│       └── types_test.go        // 类型测试
│
├── internal/                    // 内部实现细节
│   ├── api/                     // API层实现
│   │   ├── backup_manager.go    // BackupManager接口实现
│   │   └── backup_manager_test.go
│   ├── config/                  // 配置管理（内部实现）
│   │   ├── config.go            // 配置验证和默认值
│   │   ├── config_test.go
│   │   └── config_validation_test.go
│   ├── task/                    // 任务编排和生命周期管理
│   │   ├── manager.go           // 任务管理器实现
│   │   └── manager_test.go
│   ├── storage/                 // 存储抽象和元数据管理
│   │   ├── manager.go           // 存储管理器实现
│   │   ├── manager_test.go
│   │   ├── factory.go           // 存储后端工厂
│   │   ├── interfaces/          // 存储接口定义
│   │   │   ├── backend.go       // Backend 统一存储接口
│   │   │   └── errors.go        // 标准错误类型
│   │   ├── local/               // 本地存储后端
│   │   │   ├── backend.go       // 本地文件系统实现
│   │   │   └── backend_test.go
│   │   └── gocloud/             // 云存储后端
│   │       ├── backend.go       // gocloud.dev 适配器
│   │       └── backend_test.go
│   ├── lock/                    // 资源锁管理
│   │   ├── locker.go            // 并发控制实现
│   │   └── locker_test.go
│   ├── provider/                // 数据源提供者实现
│   │   ├── interface.go         // BackupProvider接口定义
│   │   ├── interface_test.go
│   │   ├── mysql/
│   │   │   ├── provider.go      // MySQL提供者实现
│   │   │   ├── provider_test.go
│   │   │   └── tool_impl.go     // MySQL工具实现
│   │   └── elasticsearch/
│   │       ├── provider.go      // ES提供者实现
│   │       └── provider_test.go
│   └── testutil/                // 测试工具和模拟
│       ├── helpers.go           // 测试辅助函数
│       ├── integration.go       // 集成测试工具
│       ├── mock_es.go           // ES模拟器
│       └── benchmark_test.go    // 性能测试
│
├── examples/                    // 使用示例
│   ├── README.md
│   ├── complete_demo/
│   │   └── basic_usage.go       // 完整功能演示
│   ├── configurations/
│   │   ├── README.md
│   │   ├── basic_configs.go     // 基础配置示例
│   │   ├── cloud_storage.go     // 云存储配置示例
│   │   └── production.go        // 生产环境配置示例
│   ├── error_handling/
│   │   └── error_handling.go    // 错误处理示例
│   ├── integration_test/
│   │   └── integration_example.go
│   └── quick_start/
│       └── quick_start.go       // 快速开始示例
│
├── docs/                        // 文档系统
│   ├── README.md                // 文档导航
│   ├── usage/                   // 使用指南
│   │   ├── SDK_USAGE.md
│   │   └── CLI_USAGE.md
│   ├── design/                  // 设计文档
│   │   ├── UniBackup_Design_Doc.md
│   │   └── UniBackup_Implementation_Design.md
│   ├── development/             // 开发文档
│   │   ├── API_REFERENCE.md
│   │   ├── TESTING.md
│   │   └── CONTRIBUTING.md


│
├── scripts/                     // 构建和测试脚本
│   ├── quick_test.sh
│   ├── run_tests.sh
│   ├── test_coverage.sh
│   └── generate_docs.sh
│
├── tests/                       // 集成测试套件
│   ├── README.md
│   ├── Makefile
│   ├── config/
│   │   └── test_config.json
│   ├── data/                    // 测试数据
│   └── src/
│       └── auto_test_manager.go
│
├── build/                       // 构建产物
│   ├── unibackup               // 主程序二进制
│   └── unibackup-admin         // 管理工具二进制
│
├── test_output/                 // 测试输出
│   ├── coverage.out
│   ├── coverage.html
│   ├── test_results.txt
│   └── benchmark_results.txt
│
└── unibackup                   // 软链接到主程序
└── unibackup-admin             // 软链接到管理工具
```

## 2. 备份产物存储布局与元数据

`BackupRoot` 目录下的元数据和备份产物被设计为两种独立的组织结构，以分别管理不同生命周期的备份。

### 2.1. 存储布局

// 为明确区分两种备份产物，目录结构被设计为两种模式。
// "archival" 目录用于存放独立的、可随时删除的全量备份。
// "chains" 目录用于存放受保护的增量链，每条链一个子目录，以保证其完整性。
```
{BackupRoot}/
└── {source_type}/              (e.g., mysql/)
    ├── archival/               // 存放所有独立的【归档全量备份】
    │   ├── {backup_id_1}/
    │   │   ├── metadata.json   // 描述此备份的元数据
    │   │   └── data.sql.gz     // 备份数据文件
    │   └── {backup_id_2}/
    │       └── ...
    │
    └── chains/                 // 存放所有【受保护的增量链】
        ├── {chain_id_A}/       // 每个子目录代表一条独立的链
        │   ├── chain_meta.json // 描述整条链的聚合元数据
        │   ├── {backup_id_A1_full}/
        │   │   ├── metadata.json
        │   │   └── data.sql.gz
        │   └── {backup_id_A2_incr}/
        │       ├── metadata.json
        │       └── binlog.gz
        └── {chain_id_B}/
            └── ...
```

### 2.2. 元数据设计

- **`metadata.json`**: 描述单个备份产物的完整元数据。
    - **基础信息**: ID、时间戳、数据源类型、**数据源实例名称**、备份类型、文件路径、描述
    - **增量链字段**: ChainID、ParentID（用于构建增量链关系）
    - **状态信息**: 备份状态、错误信息、文件大小、执行时间
    - **数据源特定**: MySQL的binlog信息、ES的快照信息等
    - **扩展字段**: Extra字段存储provider特定的JSON元数据
- **`chain_meta.json`**: 描述整条增量链的聚合信息。
    - **链标识**: ChainID唯一标识符
    - **成员列表**: BackupIDs有序列表，从全量到最后的增量
    - **快速查询**: 避免遍历链内所有备份点，提高性能
- **`tasks.json`**: 异步任务状态持久化。
    - **任务信息**: ID、类型、状态、进度、开始/结束时间
    - **分组任务**: SubTaskIDs支持分组任务的子任务管理
    - **元数据**: Metadata字段存储任务相关的扩展信息

---

## 3. 核心模块详细设计

### 3.0. `unibackup.go` - SDK 主入口

- **职责**: 作为SDK的主入口点，负责初始化所有内部组件并组装成完整的BackupManager实例。
- **关键功能**:
    - **组件初始化**: 按正确顺序初始化StorageManager、Providers、TaskManager、Locker
    - **配置处理**: 设置默认值并校验配置
    - **依赖注入**: 将所有组件注入到API层管理器中
    - **错误处理**: 统一处理初始化过程中的错误
- **使用方式**: `manager, err := unibackup.NewManager(cfg)`

### 3.1. `internal/api/backup_manager.go` - SDK 公共 API 层

- **职责**: 定义SDK的公共接口契约，并提供具体实现。采用接口设计模式，遵循Go最佳实践，便于测试、扩展和依赖注入。
- **关键组件**:
    - **`BackupManager` interface**: 定义所有对外暴露的操作契约
    - 异步操作接口：`BackupAsync`, `RestoreAsync`, `BackupAllAsync`, `RestoreAllAsync`
    - 任务管理接口：`GetTask`, `ListTasks`, `CancelTask`, `ClearOldTasks`
    - 同步操作接口：`Backup`, `Restore`（向后兼容）
    - 系统管理接口：`Shutdown`
    - 备份管理接口：`ListArchivalBackups`, `ListIncrementalChains`, `DeleteBackup`
    - 新增统一查询接口（v2.0+）：`ListAllBackups`, `GetBackupDetails`, `DeleteBackupByTaskID`, `GetBackupDeletionInfo`
    - **`manager` struct**: BackupManager接口的具体实现
        - `cfg *types.Config`: 持有经过校验的完整配置
        - `storageManager storage.StorageManager`: 存储管理器实例
        - `taskManager *task.Manager`: 任务管理器实例
        - `locker lock.Locker`: 资源锁管理器，支持细粒度锁定
- **关键流程实现**:
    - **`BackupAsync` 统一备份入口**:
        1. 根据 `backupType` 参数确定备份策略
        2. 获取资源锁（按 `sourceName` 锁定）
        3. 创建异步任务并返回任务ID
        4. 在后台goroutine中执行具体备份逻辑：
            - `types.BackupTypeArchival`: 创建独立的归档备份
            - `types.BackupTypeChainInitial`: 创建新的增量链初始备份
            - `types.BackupTypeChainIncremental`: 向现有链添加增量备份
        5. 调用相应的 `provider.Backup(ctx, record, prevRecord)` 执行备份
        6. 更新任务状态和元数据
    - **`RestoreAsync` 智能恢复入口**:
        1. 获取资源锁（按 `sourceName` 锁定）
        2. 创建异步任务并返回任务ID
        3. 在后台goroutine中执行恢复逻辑：
            a. 调用 `storage.Manager.GetRestorationChain()` 自动构建恢复链
            b. 对于归档备份：返回单个备份记录
            c. 对于增量链备份：返回从链起点到指定备份点的有序记录列表
        4. **按顺序**循环恢复列表，调用 `provider.Restore(ctx, record, prevRecord)`
        5. 支持恢复到增量链的任意时间点（不仅限于最新状态）
        6. 更新任务状态，记录恢复结果
    - **`BackupAllAsync` 分组备份管理**:
        1. 验证请求参数和并发限制
        2. 创建主任务和子任务列表
        3. 根据配置选择执行策略：
            - 并行执行（基础版本）：同时启动多个备份任务
            - 原子性执行（高级版本）：使用事务性保证
        4. 支持失败清理：`CleanupOnFailure=true` 时，任何子任务失败都会清理已完成的备份
        5. 实时更新主任务进度和状态
    - **Panic 安全 (Panic-Safe) 设计**: 所有异步执行的 goroutine 都通过 `defer/recover` 机制进行了保护。任何潜在的 `panic` 都会被捕获并转换为结构化的 `BackupError`，确保了单个任务的意外失败不会导致整个应用程序崩溃，并能保证资源锁被正确释放。

### 3.2. `internal/storage` - 存储抽象和元数据管理

- **职责**: 统一管理备份产物的存储布局、元数据读写和恢复链构建，支持归档备份和增量链的分离存储，包含关键的安全检查以保护备份数据完整性。通过 Backend 接口抽象存储层，支持本地存储和云存储的统一管理。

- **核心接口**:
  - `StorageManager` 定义了管理备份存储和元数据的完整契约（32个方法）
  - `Backend` 定义了统一的存储抽象接口，支持本地和云存储
  - `TaskManager` 简化接口，用于统一查询功能

- **关键功能**:
    - **备份记录管理**: `CreateBackupRecord`, `SaveBackupRecord`, `SaveBackupRecordAtomic`, `GetBackupRecord`, `DeleteBackupRecord`
    - **增量链管理**: `ListIncrementalChains`, `GetLatestChainRecord`, `GetRestorationChain`, `DeleteIncrementalChain`
    - **归档备份管理**: `ListArchivalBackups`, `FindBackupRecord`, `FindBackupRecordAcrossTypes`
    - **统一查询接口**: `ListAllBackups`, `GetBackupDetails`, `DeleteBackupByTaskID`, `GetBackupDeletionInfo`
    - **系统文件操作**: `WriteSystemFile`, `ReadSystemFile`, `EnsureSystemDir`, `RenameSystemFile`
        - `WriteSystemFile`: 通过 Backend.Put 写入系统文件，支持相对路径转换
        - `ReadSystemFile`: 通过 Backend.Get 读取系统文件，文件不存在时返回 `os.ErrNotExist`
        - `EnsureSystemDir`: 空操作，依赖 Backend 自动创建目录结构
        - `RenameSystemFile`: 通过读取-写入-删除序列实现文件重命名
    - **健康检查**: `CheckDataSourceHealth` 委托给 Provider 执行实际的连接检查
    - **动态配置**: `UpdateConfig` 支持运行时更新配置和备份根目录
    - **链元数据管理**: `ReadChainMeta`, `WriteChainMeta` 管理增量链的聚合元数据
    - **跨类型查找**: `FindBackupRecordAcrossTypes` 提供跨所有数据源类型查找备份记录的能力
    - **Provider 支持**: `GetBackupWriter` 为 Provider 提供流式写入器，支持直接写入存储后端
    - **路径管理**: 自动生成和管理分层存储路径
    - **安全检查**: 防止删除增量链的部分备份，保护数据完整性
    - **智能恢复**: 自动构建恢复链，支持恢复到任意时间点
- **核心组件**:
    - **`Manager` struct**: StorageManager接口的具体实现
        - `backend interfaces.Backend`: 存储后端接口，负责实际的存储操作
        - `backupRoot string`: 备份文件的根目录（用于路径生成）
        - `logger *slog.Logger`: 结构化日志记录器
        - `cfg *types.Config`: 全局配置，用于获取所有数据源类型
        - `taskManager TaskManager`: 任务管理器接口，用于访问tasks.json数据

- **关键实现细节**:
    - **路径转换机制**: `getStorageKey` 方法将绝对路径转换为相对于备份根目录的存储键
    - **错误映射**: Backend 错误统一映射为 `*types.BackupError` 类型，保持接口一致性
    - **原子性保证**: 依赖 Backend 接口的原子性，本地文件系统和云存储都提供单文件写入原子性
    - **安全检查**: 删除操作包含严格的类型检查，防止破坏增量链完整性
    - **性能优化**: 统一查询接口基于内存中的 tasks.json，避免文件系统遍历

- **存储架构**:
    - **Backend 接口抽象**: 通过统一的 Backend 接口支持多种存储后端
    - **本地存储**: LocalBackend 实现本地文件系统存储
    - **云存储**: GocloudBackend 基于 gocloud.dev 支持 S3、GCS、Azure 等云存储
    - **存储工厂**: 配置驱动的后端创建，支持本地和云存储的自动选择
    - **透明切换**: 用户可以通过配置在本地存储和云存储之间无缝切换
- **存储布局**:
    - `{BackupRoot}/{source_type}/archival/{backup_id}/` - 归档备份
    - `{BackupRoot}/{source_type}/chains/{chain_id}/` - 增量链
    - `{BackupRoot}/{source_type}/status.json` - 数据源状态
    - `{BackupRoot}/tasks.json` - 任务状态持久化
- **企业级特性**:
    - **增量链完整性保护 (Safety-Critical)**: 这是存储管理器的核心安全特性。系统**禁止**删除增量备份链中的任何单个备份点。任何删除属于某个链的备份记录的尝试都会被明确拒绝，并提示用户必须删除整条链。这从机制上防止了因误操作破坏增量链而导致数据无法恢复的灾难性后果。
    - **安全检查**: 多重路径验证，防止意外删除和路径注入
    - **事务性操作**: 元数据更新采用原子操作，确保一致性
    - **错误恢复**: 操作失败时自动回滚目录创建和元数据写入
    - **兼容性处理**: 支持旧格式备份链的兼容性读取
    - **性能优化**: 使用聚合元数据避免目录遍历，提高查询效率

### 3.3. `internal/task` - 任务编排和生命周期管理

- **职责**: 承担双重职责：1) 实现TaskManager接口，负责执行底层的备份/恢复流程；2) 管理异步任务的生命周期，包括创建、状态更新、持久化和并发控制。
- **关键组件**:
    - **`Manager` struct**:
        - **任务执行引擎**: 实现了 `TaskManager` 接口，负责执行底层的备份/恢复流程
        - **异步任务管理**: 管理异步任务的生命周期，包括创建、状态更新、持久化和并发控制
        - **深拷贝机制**: 在持久化任务状态时采用深拷贝机制，避免并发修改导致的JSON序列化问题
        - **并发控制**: 通过信号量限制最大并发任务数，防止资源过载
        - **状态持久化**: 任务状态实时持久化到 `tasks.json` 文件，支持重启恢复
        - **故障恢复**: 在服务重启时自动将运行中的任务标记为失败，并触发补偿逻辑
        - **完全重建策略**: 恢复操作采用完全重建策略，确保数据一致性和清洁状态
        - **原子性保证**: 为分组操作提供真正的原子性，包括预恢复快照和失败回滚机制
        - **事件驱动回调**: 支持恢复成功后的自动回调机制，回调失败不影响恢复成功状态
        - `storageMgr storage.StorageManager`: 存储管理器实例
        - `providers map[types.SourceType]provider.BackupProvider`: 数据源提供者映射
        - `tasks sync.Map`: 并发安全的任务存储，以任务ID为键
        - `tasksFilepath string`: 任务持久化文件路径（`{BackupRoot}/tasks.json`）
        - `mu sync.Mutex`: 保护对持久化文件的写操作
        - `maxConcurrent int`: 最大并发任务数
        - `semaphore chan struct{}`: 并发控制信号量
        - `taskContexts sync.Map`: 任务上下文管理，支持任务取消
        - `taskRetentionDays int`: 任务记录的保留天数（配置项）
        - `maxTaskHistory int`: 内存和文件中保留的最大任务历史数量（配置项）
- **核心功能**:
    - **并发控制**: 使用信号量限制并发任务数量，避免资源过载
    - **任务取消**: 支持通过上下文取消正在执行的任务
    - **状态持久化**: 任务状态实时持久化到tasks.json，支持重启恢复
    - **进度跟踪**: 实时更新任务进度和状态信息
    - **自动清理**: 定期清理旧任务记录，同时清理失败的备份文件
    - **分组备份原子性 (`BackupAll`)**:
        - 支持并行执行子备份任务。
        - 当 `CleanupOnFailure` 为 `true` 且任何子任务失败时，系统会根据子备份类型执行清理：
            - 对于**归档全量备份**和**链初始全量备份**：已完成的子备份会被自动清理（删除），以防止产生不完整的备份集。
            - 对于**增量备份**：即使失败，已完成的增量备份通常**不会**被自动清理，因为删除它们会破坏增量链的完整性。此时会记录警告，但不会执行物理删除。
    - **分组恢复原子性 (`RestoreAll`)**:
        - **预恢复快照**: 在执行原子恢复前，为所有目标数据源创建临时的“预恢复快照”（归档全量备份），作为回滚的基石。
        - **串行执行**: 恢复操作按数据源串行执行，确保每一步的顺序性和可控性。
        - **失败回滚**: 如果恢复过程中任何一个数据源的恢复失败，系统会立即中断后续恢复，并**同步阻塞**地利用之前创建的预恢复快照，将所有已成功恢复的数据源回滚到操作开始前的状态，确保整个分组恢复的原子性。
        - **资源清理**: 无论分组恢复成功或失败回滚，系统都会**异步**清理掉所有创建的临时预恢复快照，回收存储资源。
    - **智能恢复**: 自动构建恢复链，支持恢复到任意时间点
    - **任务记录清理**: 定期自动清理旧的、已完成的任务记录，防止任务列表无限增长。清理策略支持基于时间（`TaskRetentionDays`）和基于数量（`MaxTaskHistory`）的配置。

### 3.4. `internal/lock` - 资源锁管理

- **职责**: 提供细粒度的资源锁定机制，支持按数据源名称进行锁定，避免全局锁的性能瓶颈。
- **关键特性**:
    - **细粒度锁定**: 按 `sourceName` 进行锁定，不同数据源可以并发操作
    - **死锁避免**: 使用有序锁定策略，避免死锁情况
    - **超时控制**: 支持锁定超时，防止长时间阻塞
    - **锁状态跟踪**: 记录锁的持有者和持有时间，便于调试
    - **自动恢复性判断**: 根据错误类型自动判断是否可恢复
    - **修复建议系统**: 为每种错误提供具体的修复建议
    - **错误标准化**: 将各种错误转换为统一的结构化格式
    - **Panic处理**: 自动捕获和转换panic为结构化错误
    - **上下文信息**: 记录错误发生的详细上下文和环境信息

### 3.5. `internal/lock/locker.go` - 资源锁管理

- **职责**: 提供基于名称的互斥锁机制，支持细粒度的资源锁定。
- **核心功能**:
    - **细粒度锁定**: 按资源名称（如数据源名称）进行锁定
    - **并发安全**: 使用元锁保护锁映射表的并发访问
    - **死锁避免**: 通过合理的锁获取和释放顺序避免死锁
    - **高性能**: 不同资源可以并发操作，相同资源串行操作
- **关键方法**:
    - `Lock(name string)`: 获取指定名称的锁
    - `Unlock(name string)`: 释放指定名称的锁
    - `IsLocked(name string)`: 检查锁状态（主要用于测试）

### 3.6. `pkg/types/types.go` - 结构化错误处理系统

- **职责**: 提供结构化错误处理能力，支持错误分类和详细的错误信息。
- **核心组件**:
    - **BackupError**: 结构化错误类型，包含错误代码、组件、操作、时间戳、详细信息等
    - **错误链支持**: 通过Unwrap方法支持Go 1.13+的错误链
    - **可重试性标识**: Retryable字段标识错误是否可重试
    - **Config**: 包含SDK的全局配置，新增 `MaxConcurrentTasks` 用于控制并发任务数量，以及 `TaskRetentionDays` 和 `MaxTaskHistory` 用于配置任务记录的自动清理策略。
- **错误信息**:
    - **Code**: 错误代码（如"DB_CONN_FAILED"）
    - **Component**: 错误发生的组件（如"MySQLProvider"）
    - **Operation**: 失败的操作（如"Backup"）
    - **Timestamp**: 错误发生时间
    - **Details**: 详细错误描述
- **使用方式**: 所有Provider和Manager都返回*BackupError而不是标准error
    - **故障恢复**: TaskManager在初始化时会加载持久化的任务状态，将任何处于Running状态的任务标记为Failed，确保系统状态一致性。

### 3.4. `internal/provider/interface.go` - 提供者抽象层

- **职责**: 定义所有数据源都必须遵守的通用契约，将数据源的特定实现细节与上层编排逻辑解耦，支持扩展新的数据源类型。
- **设计改进**: 相比原始设计，增加了 `prevRecord` 参数支持增量备份的上下文传递，提高了接口的表达能力。
- **`BackupProvider` 接口定义**:
    ```go
    package provider

    import (
        "context"
        "unibackup/pkg/types"
    )

    // BackupProvider 定义了数据源提供者必须实现的核心接口
    type BackupProvider interface {
        // Backup 执行一次备份操作
        // backupRecord: 当前备份的元数据记录，包含备份类型、路径等信息
        // prevRecord: 上一个备份的元数据记录，用于增量备份的上下文传递（全量备份时为nil）
        // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
        Backup(ctx context.Context, backupRecord *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError

        // Restore 恢复一次独立的备份产物
        // backupRecord: 要恢复的备份记录
        // prevRecord: 恢复链中前一个备份的记录，用于增量恢复的上下文（全量恢复时为nil）
        // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
        Restore(ctx context.Context, backupRecord *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError

        // List 返回该数据源所有可用的备份记录元数据
        // 主要用于那些将元数据存储在外部系统的provider
        // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
        List(ctx context.Context) ([]*types.BackupRecord, *types.BackupError)

        // Delete 执行特定于提供者的删除操作
        // 注意：不应包含业务逻辑，安全逻辑由上层负责
        // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
        Delete(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError
    }
    ```

### 3.7. `internal/provider/mysql` - MySQL 提供者实现

MySQL 提供者采用基于外部工具的实现策略，使用 `mysqldump`、`mysql`、`mysqlbinlog` 和 `mysqladmin` 等官方命令行工具来执行备份和恢复操作。

#### 3.7.1. 基于工具实现的优势

基于工具的实现将备份和恢复的核心逻辑委托给 `mysqldump`、`mysql` 和 `mysqlbinlog` 等官方工具：

-   **稳定性与可靠性**: 直接利用了官方工具的全部功能和健壮性，能够完美处理各种复杂的数据库对象和字符集。
-   **高性能**: 通过 `os/exec` 调用外部进程，其数据处理性能接近于原生手动操作。
-   **低内存占用**: 数据通过标准输入输出（stdin/stdout）进行流式处理，避免了将大数据集加载到应用内存中。

**结论**: 使用工具实现是一个基于**稳定性、可靠性和工程实用性**的决策，依赖于业界标准，确保了 UniBackup 在处理 MySQL 备份时的质量。

### 3.8. `internal/provider/elasticsearch` - Elasticsearch 提供者

- **职责**: 实现所有与Elasticsearch相关的备份恢复逻辑，将双模备份概念映射到ES快照机制上。
- **核心设计：元数据注入**:
    - **自描述快照**: 这是 ES Provider 的一个关键实现亮点。在创建ES快照时，UniBackup 会将自身的关键元数据（如 `BackupID`, `ChainID`, `BackupType` 等）作为一个 `metadata` 对象注入到快照本身的元数据中。
    - **优势**: 这种设计使得ES快照本身变得"自描述"。即使脱离了外部的 `metadata.json` 文件，用户或管理员依然可以直接通过查询ES的 `_snapshot` API 来了解每个快照的来源、类型和所属的增量链。这极大地增强了备份资产的可管理性、独立性和灾难恢复能力。
- **关键组件**:
    - **`Provider` struct**:
        - `client *elasticsearch.Client`: 官方ES Go客户端实例。
        - `managedRepoName string`: 用于增量链的快照仓库名称。
        - `archivalRepoName string`: 用于归档备份的快照仓库名称。
- **`Backup()` 方法实现**:
    1.  根据 `backupMeta.Type` 判断目标仓库是 `managedRepoName` 还是 `archivalRepoName`。
    2.  构建`PUT /_snapshot/{repo}/{snapshot}`的API请求。
    3.  将`BackupRecord`中的所有信息序列化后放入请求体的`metadata`字段。
    4.  发送`wait_for_completion=true`的阻塞式API请求，或`false`的非阻塞请求并轮询状态。
- **`List()` 方法实现**:
    1.  分别调用`GET /_snapshot/{managedRepoName}/_all`和`GET /_snapshot/{archivalRepoName}/_all` API。
    2.  遍历返回的所有快照，从每个快照的`metadata`字段中反序列化出我们存入的`BackupRecord`信息。
    3.  返回合并后的`BackupRecord`列表。
- **`Restore()` 实现**: 调用 `POST /_snapshot/{repo}/{snapshot}/_restore` API。
- **`Delete()` 实现**: 调用 `DELETE /_snapshot/{repo}/{snapshot}` API。

---

## 4. 关键设计决策与权衡

在本SDK的设计过程中，我们始终遵循"稳定性和数据安全优先"的原则，并做出了一系列关键的技术决策：

- **MySQL备份：基于工具实现**: 选择依赖外部官方工具链，确保稳定性和可靠性。
- **存储层：安全优先**: 强制保护增量链的完整性，防止用户误操作导致数据丢失。通过 Backend 接口抽象实现存储层统一，支持本地存储和云存储的无缝切换。
- **并发模型：细粒度锁**: 采用按数据源名称的锁机制，而不是全局锁，以最大化不同数据源之间的操作并行度。

这些决策共同构建了一个健壮、可靠且易于维护的备份恢复解决方案。