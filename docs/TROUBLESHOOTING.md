# UniBackup 故障排除指南

本指南帮助您诊断和解决 UniBackup 使用过程中遇到的常见问题。

## 🔧 增量备份问题

### 问题：增量备份失败，提示"未找到起始binlog文件"

**错误示例**：
```
错误代码: BINLOG_CONTINUITY_BROKEN
错误信息: 增量备份失败：起始binlog文件 mysql-bin.000123 不存在，可能已被清理
```

**原因分析**：
1. **binlog保留策略过短** - MySQL自动清理了旧的binlog文件
2. **系统重装** - 服务器重装后binlog文件重新编号
3. **手动清理** - 管理员手动删除了binlog文件

**解决方案**：

#### 1. 检查binlog保留配置
```sql
-- 检查当前配置
SHOW VARIABLES LIKE 'expire_logs_days';
SHOW VARIABLES LIKE 'binlog_expire_logs_seconds';

-- 调整保留策略
SET GLOBAL expire_logs_days = 7;  -- 保留7天
```

#### 2. 永久配置（推荐）
在 `my.cnf` 中添加：
```ini
[mysqld]
expire-logs-days = 7
max-binlog-size = 1G
```

#### 3. 根据备份频率调整
- **每日备份**：保留7-14天
- **每小时备份**：保留3-7天
- **高频备份**：保留1-3天

### 问题：系统重装后备份链断裂

**场景描述**：
服务器重装或MySQL重新安装后，binlog文件从 `mysql-bin.000001` 重新开始，导致与之前的备份记录不匹配。

**自动处理机制**：
UniBackup v2.0+ 具有智能检测和自动处理功能：

1. **自动检测系统重装**：
   - 检测binlog序号大幅回退（如从000123回到000001）
   - 自动识别为系统重装场景

2. **自动重置备份链**：
   ```
   检测到系统重装，备份链将自动重建
   
   原因: binlog从 mysql-bin.000123 重置为 mysql-bin.000001
   处理: 自动创建新备份链，执行全量备份
   恢复: 旧链用于重装前数据，新链用于重装后数据
   ```

3. **备份链管理**：
   - 旧备份链保留，用于恢复重装前的数据
   - 新备份链从全量备份开始
   - 两个链完全独立，互不影响

**手动处理**（如果需要）：
```bash
# 查看备份链状态
unibackup list-chains --database mydb

# 手动重置备份链（如果自动处理失败）
unibackup reset-chain --database mydb --confirm
```

### 问题：Docker环境中binlog配置

**常见错误**：
Docker容器重启后binlog文件丢失或重置。

**解决方案**：

1. **正确的Docker Compose配置**：
```yaml
mysql:
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin
    --binlog-format=ROW
    --server-id=1
    --expire-logs-days=7              # 关键配置
    --max-binlog-size=1G
  volumes:
    - mysql_data:/var/lib/mysql
    - mysql_binlogs:/var/lib/mysql-binlogs  # 持久化binlog
```

2. **持久化存储**：
确保binlog目录被正确挂载到持久化存储。

## 🔍 诊断工具

### 检查binlog状态
```sql
-- 查看当前binlog文件
SHOW MASTER STATUS;

-- 查看所有binlog文件
SHOW BINARY LOGS;

-- 检查binlog配置
SHOW VARIABLES LIKE '%binlog%';
SHOW VARIABLES LIKE '%expire%';
```

### 检查备份链状态
```bash
# 列出所有备份链
unibackup list-chains --database mydb

# 检查特定备份链
unibackup chain-info --chain-id chain-20250121

# 验证备份链完整性
unibackup verify-chain --chain-id chain-20250121
```

## 📊 监控和预防

### 1. 设置监控告警
- **binlog空间使用率** > 80%
- **增量备份失败**立即告警
- **备份链断裂**立即告警

### 2. 定期检查
```bash
# 每日检查脚本
#!/bin/bash
echo "检查binlog状态..."
mysql -e "SHOW BINARY LOGS;"

echo "检查备份链健康..."
unibackup verify-all-chains

echo "检查磁盘空间..."
df -h /var/lib/mysql-binlogs/
```

### 3. 最佳实践
1. **合理设置binlog保留时间**：根据备份频率和恢复需求
2. **监控磁盘空间**：确保有足够空间存储binlog
3. **定期测试恢复**：验证备份链的可用性
4. **文档化配置**：记录binlog配置和变更历史

## 🔄 恢复回调问题

### 回调函数执行失败
**现象**：恢复成功但回调函数报错

**排查步骤**：
1. **检查任务元数据**：
```go
task, err := manager.GetTask(taskID)
if err != nil {
    return err
}

// 检查回调状态
if status, ok := task.Metadata["callback_status"]; ok {
    if status == "failed" {
        if errMsg, ok := task.Metadata["callback_error"]; ok {
            fmt.Printf("回调失败原因: %s\n", errMsg)
        }
    }
}
```

2. **常见回调错误**：
   - 网络超时：回调中的外部API调用超时
   - 权限问题：回调函数访问外部资源权限不足
   - 资源不可用：回调依赖的外部服务不可用

**解决方案**：
- 在回调函数中添加重试机制
- 设置合理的超时时间
- 添加错误日志记录
- 考虑使用异步消息队列处理回调逻辑

### 回调函数最佳实践
1. **超时控制**：
```go
config.OnSuccess = func(ctx context.Context) error {
    // 设置回调自己的超时
    ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()

    return doCallback(ctx)
}
```

2. **错误处理**：
```go
config.OnSuccess = func(ctx context.Context) error {
    if err := sendNotification(ctx); err != nil {
        // 记录错误但不影响恢复成功
        log.Printf("通知发送失败: %v", err)
        return err
    }
    return nil
}
```

## 🚨 紧急恢复

### 如果备份链完全断裂
1. **立即执行全量备份**：
```bash
unibackup backup --type full --database mydb --description "紧急重建备份链"
```

2. **评估数据丢失**：
   - 检查最后一次成功备份的时间
   - 评估可能丢失的数据范围

3. **从其他源补偿**：
   - 从从库备份
   - 从应用日志重建
   - 从业务系统导出

### 联系支持
如果遇到无法解决的问题，请提供以下信息：
- UniBackup版本
- MySQL版本和配置
- 错误日志和堆栈信息
- binlog状态和配置
- 备份链状态信息
