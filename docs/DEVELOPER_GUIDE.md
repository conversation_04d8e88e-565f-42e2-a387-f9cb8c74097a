# UniBackup 开发指南

本指南为 UniBackup 项目的开发者提供完整的开发、测试和贡献指导。

## 📋 目录

- [贡献指南](#贡献指南)
- [测试指南](#测试指南)
- [API 参考](#api-参考)
- [错误代码](#错误代码)
- [技术方案](#技术方案)

---

# 贡献指南

欢迎为 UniBackup 项目做出贡献！本指南将帮助您了解如何参与项目开发。

## 🛠️ 开发环境设置

### 前置要求

- Go 1.19+
- Git
- Make
- MySQL 8.0+ (可选，用于集成测试)
- Elasticsearch 8.0+ (可选，用于集成测试)

### 克隆和设置

```bash
# 克隆仓库
git clone https://git.gobies.org/fobrain/unibackup.git
cd unibackup

# 安装依赖和开发工具
make dev

# 验证环境
make test-quick
```

## 🔄 开发流程

### 分支策略

- `main` - 主分支，稳定版本
- `develop` - 开发分支，最新功能
- `feature/*` - 功能分支
- `bugfix/*` - 修复分支
- `hotfix/*` - 紧急修复分支

### 提交流程

1. **创建功能分支**：
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   ```

2. **开发和测试**：
   ```bash
   # 开发您的功能
   # 运行测试
   make test

   # 运行代码检查
   make lint

   # 格式化代码
   make fmt
   ```

3. **提交代码**：
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   git push origin feature/your-feature-name
   ```

4. **创建 Pull Request**：
   - 在 GitLab 上创建 Merge Request
   - 填写详细的描述和测试说明
   - 等待代码审查

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(mysql): add incremental backup support

Add support for MySQL incremental backups using binlog.
This feature allows users to create backup chains with
initial full backup followed by incremental backups.

Closes #123
```

## 📝 代码规范

### Go 代码规范

1. **遵循 Go 官方规范**：
   - 使用 `gofmt` 格式化代码
   - 遵循 `golint` 建议
   - 使用 `go vet` 检查代码

2. **命名规范**：
   ```go
   // 包名：小写，简短，有意义
   package backup

   // 接口名：以 -er 结尾
   type BackupManager interface {}

   // 结构体：驼峰命名
   type BackupConfig struct {}

   // 方法名：驼峰命名，公开方法首字母大写
   func (b *BackupManager) CreateBackup() error {}
   ```

3. **注释规范**：
   ```go
   // BackupManager 管理备份操作的核心接口
   // 提供统一的备份、恢复和任务管理功能
   type BackupManager interface {
       // BackupAsync 异步执行备份操作
       // 返回任务ID用于跟踪备份进度
       BackupAsync(ctx context.Context, sourceType SourceType, sourceName string, backupType BackupType, description string) (string, error)
   }
   ```

### 错误处理

1. **使用结构化错误**：
   ```go
   return &types.BackupError{
       Code:      "MYSQL_CONNECTION_FAILED",
       Message:   "Failed to connect to MySQL server",
       Retryable: true,
       Details: map[string]interface{}{
           "host": config.Host,
           "port": config.Port,
       },
   }
   ```

2. **错误包装**：
   ```go
   if err != nil {
       return fmt.Errorf("failed to create backup: %w", err)
   }
   ```

### 日志规范

```go
// 使用结构化日志
logger.Info("Starting backup operation",
    "source_type", sourceType,
    "source_name", sourceName,
    "backup_type", backupType,
)

// 错误日志包含上下文
logger.Error("Backup operation failed",
    "error", err,
    "task_id", taskID,
    "duration", time.Since(startTime),
)
```

## 🧪 测试要求

### 测试覆盖率

- 单元测试覆盖率 ≥ 80%
- 核心模块覆盖率 ≥ 90%
- 新功能必须包含测试

### 测试类型

1. **单元测试**：
   ```go
   func TestBackupManager_CreateBackup(t *testing.T) {
       // 测试逻辑
   }
   ```

2. **集成测试**：
   ```go
   func TestMySQLBackup_Integration(t *testing.T) {
       if testing.Short() {
           t.Skip("Skipping integration test")
       }
       // 集成测试逻辑
   }
   ```

3. **基准测试**：
   ```go
   func BenchmarkBackupOperation(b *testing.B) {
       // 性能测试逻辑
   }
   ```

## 📚 文档要求

### 代码文档

- 所有公开接口必须有注释
- 复杂逻辑需要详细说明
- 示例代码要完整可运行

### 用户文档

- 新功能需要更新用户指南
- 配置变更需要更新配置文档
- 重大变更需要更新迁移指南

## 🔍 代码审查

### 审查清单

- [ ] 代码符合规范
- [ ] 测试覆盖充分
- [ ] 文档更新完整
- [ ] 性能影响评估
- [ ] 安全性检查
- [ ] 向后兼容性

### 审查流程

1. 自动化检查通过
2. 至少一位维护者审查
3. 所有讨论解决
4. CI/CD 流水线通过

## 🚀 发布流程

### 版本号规范

使用 [Semantic Versioning](https://semver.org/)：
- `MAJOR.MINOR.PATCH`
- 例如：`1.2.3`

### 发布步骤

1. **准备发布**：
   ```bash
   # 更新版本号
   make version VERSION=1.2.3

   # 更新 CHANGELOG
   make changelog
   ```

2. **创建发布分支**：
   ```bash
   git checkout -b release/1.2.3
   git commit -m "chore: prepare release 1.2.3"
   ```

3. **测试和验证**：
   ```bash
   make test-all
   make build-all
   ```

4. **合并和标记**：
   ```bash
   git checkout main
   git merge release/1.2.3
   git tag v1.2.3
   git push origin main --tags
   ```

---

# 测试指南

## 📋 测试概述

UniBackup 采用多层次的测试策略，确保代码质量和系统稳定性。

### 测试层次

1. **单元测试** - 测试单个函数和方法
2. **集成测试** - 测试组件间交互
3. **端到端测试** - 测试完整的用户场景
4. **性能测试** - 测试系统性能和资源使用

## 🛠️ 测试环境设置

### 本地测试环境

```bash
# 安装测试依赖
make test-deps

# 启动测试服务
make test-services

# 运行所有测试
make test
```

### Docker 测试环境

```bash
# 使用 Docker 运行测试
make test-docker

# 运行特定模块测试
make test-docker MODULE=pkg/mysql
```

### CI/CD 环境

```yaml
# .gitlab-ci.yml 示例
test:
  stage: test
  image: golang:1.19
  services:
    - mysql:8.0
    - elasticsearch:8.0.0
  script:
    - make test-ci
  coverage: '/coverage: \d+\.\d+% of statements/'
```

## 🧪 测试类型详解

### 单元测试

**目标**：测试单个函数的逻辑正确性

```go
func TestBackupRecord_Validate(t *testing.T) {
    tests := []struct {
        name    string
        record  *types.BackupRecord
        wantErr bool
    }{
        {
            name: "valid record",
            record: &types.BackupRecord{
                ID:         "backup-123",
                SourceType: types.MySQL,
                SourceName: "testdb",
            },
            wantErr: false,
        },
        {
            name: "invalid record - empty ID",
            record: &types.BackupRecord{
                SourceType: types.MySQL,
                SourceName: "testdb",
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := tt.record.Validate()
            if (err != nil) != tt.wantErr {
                t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

### 集成测试

**目标**：测试组件间的交互

```go
func TestMySQLProvider_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test")
    }

    // 设置测试数据库
    db := setupTestDatabase(t)
    defer cleanupTestDatabase(t, db)

    // 创建 MySQL Provider
    provider := mysql.NewProvider(testConfig)

    // 测试备份操作
    ctx := context.Background()
    record, err := provider.CreateArchivalBackup(ctx, "testdb", "integration test")
    require.NoError(t, err)
    require.NotEmpty(t, record.ID)

    // 验证备份文件存在
    backupPath := filepath.Join(testConfig.BackupRoot, "mysql", "archival", record.ID)
    assert.DirExists(t, backupPath)
}
```

### 端到端测试

**目标**：测试完整的用户场景

```go
func TestE2E_BackupAndRestore(t *testing.T) {
    // 创建测试环境
    env := setupE2EEnvironment(t)
    defer env.Cleanup()

    // 创建备份管理器
    manager, err := unibackup.NewManager(env.Config)
    require.NoError(t, err)
    defer manager.Shutdown()

    // 执行备份
    taskID, err := manager.BackupAsync(context.Background(),
        types.MySQL, "testdb", types.BackupTypeArchival, "E2E test")
    require.NoError(t, err)

    // 等待备份完成
    task := waitForTaskCompletion(t, manager, taskID)
    assert.Equal(t, types.TaskStatusCompleted, task.Status)

    // 执行恢复（带回调示例）
    config := types.NewRestoreConfig(types.MySQL, "testdb", task.BackupID)
    config.OnSuccess = func(ctx context.Context) error {
        // 恢复成功后的处理逻辑
        log.Printf("数据库 %s 恢复成功", config.SourceName)
        return sendNotificationEmail("restore_completed", config.SourceName)
    }

    restoreTaskID, err := manager.RestoreAsync(context.Background(), config)
    require.NoError(t, err)

    // 验证恢复结果
    restoreTask := waitForTaskCompletion(t, manager, restoreTaskID)
    assert.Equal(t, types.TaskStatusCompleted, restoreTask.Status)
}
```

### 性能测试

**目标**：测试系统性能和资源使用

```go
func BenchmarkMySQLBackup(b *testing.B) {
    provider := setupBenchmarkProvider(b)
    ctx := context.Background()

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := provider.CreateArchivalBackup(ctx, "benchdb", "benchmark test")
        if err != nil {
            b.Fatal(err)
        }
    }
}

func TestMemoryUsage(t *testing.T) {
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    // 执行测试操作
    performBackupOperations(t)

    runtime.GC()
    runtime.ReadMemStats(&m2)

    // 检查内存使用
    memUsed := m2.Alloc - m1.Alloc
    if memUsed > 100*1024*1024 { // 100MB
        t.Errorf("Memory usage too high: %d bytes", memUsed)
    }
}
```

## 🔧 测试工具和命令

### Make 命令

```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 运行性能测试
make test-bench

# 生成测试覆盖率报告
make test-coverage

# 运行特定模块测试
make test-module MODULE=pkg/mysql

# 运行竞态条件检测
make test-race
```

### 测试标签

```bash
# 跳过集成测试
go test -short ./...

# 只运行集成测试
go test -tags=integration ./...

# 运行性能测试
go test -bench=. ./...
```

### 测试配置

```go
// 测试配置文件
func getTestConfig() *types.Config {
    return &types.Config{
        BackupRoot: "/tmp/unibackup-test",
        Logger:     slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})),
        MySQL: &types.MySQLConfig{
            Host:     getEnvOrDefault("TEST_MYSQL_HOST", "localhost"),
            Port:     3306,
            User:     "test_user",
            Password: "test_password",
            DBName:   "test_db",
        },
    }
}
```

## 📊 测试报告

### 覆盖率报告

```bash
# 生成 HTML 覆盖率报告
make test-coverage-html

# 查看覆盖率报告
open coverage.html
```

### 测试结果

```bash
# 生成 JUnit 格式报告
go test -v ./... | go-junit-report > test-results.xml

# 生成 JSON 格式报告
go test -json ./... > test-results.json
```

## 🚨 测试最佳实践

### 测试命名

```go
// 好的测试命名
func TestBackupManager_CreateBackup_WithValidConfig_ShouldSucceed(t *testing.T) {}
func TestBackupManager_CreateBackup_WithInvalidConfig_ShouldReturnError(t *testing.T) {}

// 避免的命名
func TestCreateBackup(t *testing.T) {}
func TestBackup1(t *testing.T) {}
```

### 测试隔离

```go
func TestSomething(t *testing.T) {
    // 每个测试都应该独立
    tempDir := t.TempDir() // 使用临时目录

    // 清理资源
    t.Cleanup(func() {
        // 清理逻辑
    })
}
```

### 测试数据

```go
// 使用表驱动测试
func TestValidateConfig(t *testing.T) {
    tests := []struct {
        name    string
        config  *Config
        wantErr bool
    }{
        // 测试用例
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试逻辑
        })
    }
}
```

---

# API 参考

## 📋 核心接口

### BackupManager 接口

UniBackup 的核心管理接口，提供所有备份和恢复功能。

```go
type BackupManager interface {
    // === 异步操作接口（推荐使用） ===

    // BackupAsync 异步备份操作，返回任务ID
    BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error)

    // RestoreAsync 异步恢复操作（使用完全重建策略），返回任务ID
    RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error)

    // BackupAllAsync 异步分组备份操作，返回任务ID
    BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error)

    // RestoreAllAsync 异步分组恢复操作（使用完全重建策略），返回任务ID
    RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error)

    // === 任务管理接口 ===

    // GetTask 获取任务详情
    GetTask(taskID string) (*types.Task, error)

    // ListTasks 列出任务
    ListTasks() ([]*types.Task, error)

    // CancelTask 取消任务
    CancelTask(taskID string) error

    // ClearOldTasks 清理旧任务
    ClearOldTasks() error

    // === 新的统一查询接口（推荐使用）===

    // ListAllBackups 基于tasks.json的统一备份查询接口
    ListAllBackups(ctx context.Context, filter types.BackupFilter) (*types.BackupListResult, error)

    // GetBackupDetails 获取备份任务的详细信息
    GetBackupDetails(ctx context.Context, taskID string) (*types.Task, error)

    // DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型
    DeleteBackupByTaskID(ctx context.Context, taskID string) error

    // GetBackupDeletionInfo 获取备份删除的影响信息
    GetBackupDeletionInfo(ctx context.Context, taskID string) (*types.BackupDeletionInfo, error)

    // RestoreByTaskID 基于taskID恢复备份到原始位置（不带回调）
    RestoreByTaskID(ctx context.Context, taskID string, force bool) (string, error)

    // RestoreByTaskIDWithCallback 基于taskID恢复备份到原始位置（带回调）
    RestoreByTaskIDWithCallback(ctx context.Context, taskID string, force bool, callback types.RestoreSuccessCallback) (string, error)

    // === 同步操作接口（向后兼容） ===

    // Backup 同步备份操作，阻塞直到完成
    Backup(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (*types.BackupRecord, *types.BackupError)

    // Restore 同步恢复操作，阻塞直到完成
    Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError

    // === 系统管理接口 ===

    // Shutdown 优雅关闭
    Shutdown() error

    // UpdateConfig 动态更新配置，支持运行时配置变更
    UpdateConfig(cfg *types.Config) error

    // === 原有接口（保留支持，但推荐使用新接口）===

    // Deprecated: 使用 ListAllBackups 替代
    ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError)

    // Deprecated: 使用 ListAllBackups 替代
    ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError)

    // Deprecated: 使用 DeleteBackupByTaskID 替代
    DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) *types.BackupError
}
```

### Provider 接口

数据源提供者接口，定义了各种数据源的备份和恢复操作。

```go
type Provider interface {
    // 归档备份
    CreateArchivalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error)

    // 增量链备份
    CreateChainInitialBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error)
    CreateChainIncrementalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error)

    // 恢复操作
    RestoreArchivalBackup(ctx context.Context, backupID string, config *RestoreConfig) error
    RestoreChainBackup(ctx context.Context, chainID string, pointInTime *time.Time, config *RestoreConfig) error

    // 查询操作（统一接口）
    ListAllBackups(ctx context.Context, filter BackupFilter) (*BackupQueryResult, error)
    
    // 管理操作
    DeleteBackupByTaskID(ctx context.Context, taskID string) error
    ValidateBackup(ctx context.Context, backupID string) error

    // 生命周期
    Initialize(ctx context.Context) error
    Shutdown() error
}
```

## 📊 数据类型

### 配置类型

```go
// 主配置结构
type Config struct {
    BackupRoot         string              `json:"backup_root" yaml:"backup_root"`
    Logger             *slog.Logger        `json:"-" yaml:"-"`
    MaxConcurrentTasks int                 `json:"max_concurrent_tasks" yaml:"max_concurrent_tasks"`
    TaskRetentionDays  int                 `json:"task_retention_days" yaml:"task_retention_days"`
    MaxTaskHistory     int                 `json:"max_task_history" yaml:"max_task_history"`
    CleanupBackupData  bool                `json:"cleanup_backup_data" yaml:"cleanup_backup_data"`
    BackupTimeout      string              `json:"backup_timeout" yaml:"backup_timeout"`
    RestoreTimeout     string              `json:"restore_timeout" yaml:"restore_timeout"`
    MySQL              *MySQLConfig        `json:"mysql" yaml:"mysql"`
    ES                 *ESConfig           `json:"es" yaml:"es"`
    CloudStorage       *CloudStorageConfig `json:"cloud_storage" yaml:"cloud_storage"`
}

// 云存储配置
type CloudStorageConfig struct {
    Enabled     bool   `json:"enabled" yaml:"enabled"`
    Type        string `json:"type" yaml:"type"` // s3, gcs, azure
    Bucket      string `json:"bucket" yaml:"bucket"`
    Region      string `json:"region" yaml:"region"`
    ProjectID   string `json:"project_id" yaml:"project_id"` // GCS专用
    Container   string `json:"container" yaml:"container"`   // Azure专用
    AccountName string `json:"account_name" yaml:"account_name"` // Azure专用
}

// MySQL 配置
type MySQLConfig struct {
    Host           string         `json:"host" yaml:"host"`
    Port           int            `json:"port" yaml:"port"`
    User           string         `json:"user" yaml:"user"`
    Password       string         `json:"password" yaml:"password"`
    DBName         string         `json:"db_name" yaml:"db_name"`
    BinlogBasePath string         `json:"binlog_base_path" yaml:"binlog_base_path"`
    ToolsPath      MySQLToolsPath `json:"tools_path" yaml:"tools_path"`
}

// MySQL 工具路径配置
type MySQLToolsPath struct {
    Mysqldump string `json:"mysqldump" yaml:"mysqldump"`
    Mysql     string `json:"mysql" yaml:"mysql"`
    Xtrabackup string `json:"xtrabackup" yaml:"xtrabackup"`
}

// Elasticsearch 配置
type ESConfig struct {
    Addresses        []string          `json:"addresses" yaml:"addresses"`
    User             string            `json:"user" yaml:"user"`
    Password         string            `json:"password" yaml:"password"`
    APIKey           string            `json:"api_key" yaml:"api_key"`
    ArchivalRepoName string            `json:"archival_repo_name" yaml:"archival_repo_name"`
    ManagedRepoName  string            `json:"managed_repo_name" yaml:"managed_repo_name"`
    AutoCreateRepos  bool              `json:"auto_create_repos" yaml:"auto_create_repos"`
    RepoBasePath     string            `json:"repo_base_path" yaml:"repo_base_path"`
    RepoSettings     map[string]interface{} `json:"repo_settings" yaml:"repo_settings"`
}
```

### 任务类型

```go
// 任务状态
type TaskStatus string

const (
    TaskStatusPending   TaskStatus = "pending"
    TaskStatusRunning   TaskStatus = "running"
    TaskStatusCompleted TaskStatus = "completed"
    TaskStatusFailed    TaskStatus = "failed"
    TaskStatusCancelled TaskStatus = "cancelled"
)

// 任务结构
type Task struct {
    ID          string                 `json:"id"`
    Type        TaskType               `json:"type"`
    Source      SourceType             `json:"source,omitempty"`
    Status      TaskStatus             `json:"status"`
    Progress    float64                `json:"progress"`
    Error       string                 `json:"error,omitempty"`
    ErrorDetail *BackupError           `json:"error_detail,omitempty"`
    StartTime   time.Time              `json:"start_time"`
    EndTime     time.Time              `json:"end_time,omitempty"`
    SubTaskIDs  []string               `json:"sub_task_ids,omitempty"`
    Description string                 `json:"description,omitempty"`
    Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// BackupFilter 定义了任务查询的过滤条件（重构后）
// 默认查询所有任务类型，按开始时间倒序排列（最新的在前）
// 所有过滤条件都是AND逻辑，即必须同时满足所有指定的条件
type BackupFilter struct {
    // === 核心过滤字段（支持多值） ===
    TaskTypes   []TaskType   `json:"task_types,omitempty"`   // 任务类型过滤（backup, restore, backup_all, restore_all）
    SourceTypes []SourceType `json:"source_types,omitempty"` // 数据源类型过滤（mysql, elasticsearch）
    BackupTypes []BackupType `json:"backup_types,omitempty"` // 备份类型过滤（archival, chain_initial, chain_incremental）
    Statuses    []TaskStatus `json:"statuses,omitempty"`     // 任务状态过滤（pending, running, completed, failed, cancelled）

    // === 时间范围查询 ===
    StartTime *time.Time `json:"start_time,omitempty"` // 开始时间（包含）
    EndTime   *time.Time `json:"end_time,omitempty"`   // 结束时间（包含）

    // === 搜索功能 ===
    SearchText string `json:"search_text,omitempty"` // 对任务描述进行模糊搜索

    // 分页参数
    Limit  int `json:"limit,omitempty"`  // 分页大小，0表示不限制
    Offset int `json:"offset,omitempty"` // 分页偏移量
}
```

### 备份类型

```go
// 数据源类型
type SourceType string

const (
    MySQL         SourceType = "mysql"
    Elasticsearch SourceType = "elasticsearch"
)

// 备份类型
type BackupType string

const (
    BackupTypeArchival          BackupType = "archival"
    BackupTypeChainInitial      BackupType = "chain_initial"
    BackupTypeChainIncremental  BackupType = "chain_incremental"
)

// 备份状态
type BackupStatus string

const (
    BackupStatusPending   BackupStatus = "pending"
    BackupStatusRunning   BackupStatus = "running"
    BackupStatusCompleted BackupStatus = "completed"
    BackupStatusFailed    BackupStatus = "failed"
    BackupStatusDeleted   BackupStatus = "deleted"
)

// 备份记录
type BackupRecord struct {
    ID          string                 `json:"id"`
    SourceType  SourceType             `json:"source_type"`
    SourceName  string                 `json:"source_name"`
    BackupType  BackupType             `json:"backup_type"`
    Description string                 `json:"description"`
    CreatedAt   time.Time              `json:"created_at"`
    Size        int64                  `json:"size"`
    Status      BackupStatus           `json:"status"`
    Metadata    map[string]interface{} `json:"metadata"`
    ChainID     string                 `json:"chain_id,omitempty"`
}

// 恢复配置
type RestoreConfig struct {
    SourceType         SourceType `json:"source_type"`
    SourceName         string     `json:"source_name"`
    BackupID           string     `json:"backup_id"`
    Description        string     `json:"description,omitempty"`
    CreateSafetyBackup bool       `json:"create_safety_backup"` // 恢复前创建安全备份（默认 true）
    RollbackOnFailure  bool       `json:"rollback_on_failure"`  // 失败时自动回滚（默认 true）
    SkipDataCleanup    bool       `json:"skip_data_cleanup"`
    Force              bool       `json:"force,omitempty"`

    // 回调配置
    OnSuccess RestoreSuccessCallback `json:"-"` // 恢复成功时的回调函数
}

// 回滚功能说明：
// - CreateSafetyBackup: 恢复前创建预恢复快照，用于失败时回滚
// - RollbackOnFailure: 恢复失败时自动使用预恢复快照回滚到原始状态
// - 支持所有恢复模式：单个恢复、分组串行恢复、分组并行恢复
// - 回滚流程：预恢复快照 -> 执行恢复 -> 失败回滚 -> 清理快照

// 批量恢复配置
type BatchRestoreConfig struct {
    Restores    []RestoreConfig `json:"restores"`
    Description string          `json:"description"`
    Atomic      bool            `json:"atomic"`
    Parallel    bool            `json:"parallel"`
    Force       bool            `json:"force,omitempty"`

    // 回调配置
    OnSuccess RestoreSuccessCallback `json:"-"` // 分组恢复成功时的回调函数
}

// 恢复成功回调函数类型
type RestoreSuccessCallback func(ctx context.Context) error
```

## 🔧 错误处理

### 错误类型

```go
// 备份错误
type BackupError struct {
    Code      string                 `json:"code"`
    Message   string                 `json:"message"`
    Retryable bool                   `json:"retryable"`
    Details   map[string]interface{} `json:"details,omitempty"`
}

func (e *BackupError) Error() string {
    return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 常见错误代码
const (
    ErrCodeConfigInvalid        = "CONFIG_INVALID"
    ErrCodeConnectionFailed     = "CONNECTION_FAILED"
    ErrCodeBackupFailed         = "BACKUP_FAILED"
    ErrCodeRestoreFailed        = "RESTORE_FAILED"
    ErrCodeTaskNotFound         = "TASK_NOT_FOUND"
    ErrCodeBackupNotFound       = "BACKUP_NOT_FOUND"
    ErrCodePermissionDenied     = "PERMISSION_DENIED"
    ErrCodeStorageError         = "STORAGE_ERROR"
    ErrCodeToolNotFound         = "TOOL_NOT_FOUND"
    ErrCodeInvalidOperation     = "INVALID_OPERATION"
)
```

### 错误处理示例

```go
func handleBackupError(err error) {
    if backupErr, ok := err.(*BackupError); ok {
        switch backupErr.Code {
        case ErrCodeConnectionFailed:
            if backupErr.Retryable {
                // 重试逻辑
                log.Warn("Connection failed, retrying...", "error", backupErr)
            } else {
                // 不可重试错误
                log.Error("Connection failed permanently", "error", backupErr)
            }
        case ErrCodePermissionDenied:
            // 权限错误处理
            log.Error("Permission denied", "error", backupErr)
        default:
            // 其他错误
            log.Error("Backup operation failed", "error", backupErr)
        }
    } else {
        // 非备份错误
        log.Error("Unexpected error", "error", err)
    }
}
```

## 📚 使用示例

### 基本使用

```go
package main

import (
    "context"
    "log/slog"
    "os"

    "unibackup/pkg/unibackup"
    "unibackup/pkg/types"
)

func main() {
    // 创建配置
    cfg := &types.Config{
        BackupRoot: "/data/backups",
        Logger:     slog.Default(),
        MySQL: &types.MySQLConfig{
            Host:     "localhost",
            Port:     3306,
            User:     "backup_user",
            Password: os.Getenv("MYSQL_PASSWORD"),
            DBName:   "production",
        },
    }

    // 创建管理器
    manager, err := unibackup.NewManager(cfg)
    if err != nil {
        panic(err)
    }
    defer manager.Shutdown()

    ctx := context.Background()

    // 执行备份
    taskID, err := manager.BackupAsync(ctx, types.MySQL, "production", types.BackupTypeArchival, "每日备份")
    if err != nil {
        panic(err)
    }

    // 监控任务
    for {
        task, err := manager.GetTask(taskID)
        if err != nil {
            panic(err)
        }

        switch task.Status {
        case types.TaskStatusCompleted:
            fmt.Println("备份完成")
            return
        case types.TaskStatusFailed:
            fmt.Printf("备份失败: %s\n", task.Error)
            return
        case types.TaskStatusRunning:
            fmt.Printf("备份进行中: %s\n", task.Progress)
            time.Sleep(5 * time.Second)
        }
    }
}
```

### 高级使用

```go
// 分组备份
func performGroupBackup(manager unibackup.BackupManager) error {
    request := &types.BackupAllRequest{
        Requests: []*types.BackupRequest{
            {
                SourceType:  types.MySQL,
                SourceName:  "db1",
                BackupType:  types.BackupTypeArchival,
                Description: "数据库1备份",
            },
            {
                SourceType:  types.Elasticsearch,
                SourceName:  "cluster1",
                BackupType:  types.BackupTypeArchival,
                Description: "ES集群备份",
            },
        },
        Description: "每日全量备份",
    }

    taskID, err := manager.BackupAllAsync(context.Background(), request)
    if err != nil {
        return err
    }

    // 等待完成
    return waitForTaskCompletion(manager, taskID)
}

// 增量备份链
func performIncrementalBackup(manager unibackup.BackupManager) error {
    ctx := context.Background()

    // 创建初始备份
    initialTaskID, err := manager.BackupAsync(ctx, types.MySQL, "production", types.BackupTypeChainInitial, "增量链初始备份")
    if err != nil {
        return err
    }

    if err := waitForTaskCompletion(manager, initialTaskID); err != nil {
        return err
    }

    // 创建增量备份
    incrementalTaskID, err := manager.BackupAsync(ctx, types.MySQL, "production", types.BackupTypeChainIncremental, "增量备份")
    if err != nil {
        return err
    }

    return waitForTaskCompletion(manager, incrementalTaskID)
}
```

---

# 错误代码

## 📋 错误代码分类

UniBackup 使用结构化的错误代码系统，便于问题诊断和自动化处理。

### 配置错误 (CONFIG_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `CONFIG_INVALID` | 配置文件格式错误或必需字段缺失 | ❌ | 检查配置文件格式和必需字段 |
| `CONFIG_VALIDATION_FAILED` | 配置验证失败 | ❌ | 根据验证错误信息修正配置 |
| `CONFIG_FILE_NOT_FOUND` | 配置文件不存在 | ❌ | 检查配置文件路径 |
| `CONFIG_PARSE_ERROR` | 配置文件解析错误 | ❌ | 检查 JSON/YAML 语法 |

### 连接错误 (CONNECTION_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `CONNECTION_FAILED` | 数据库连接失败 | ✅ | 检查网络连接和认证信息 |
| `CONNECTION_TIMEOUT` | 连接超时 | ✅ | 检查网络延迟，增加超时时间 |
| `CONNECTION_REFUSED` | 连接被拒绝 | ✅ | 检查服务是否运行，端口是否正确 |
| `CONNECTION_AUTH_FAILED` | 认证失败 | ❌ | 检查用户名和密码 |

### 备份错误 (BACKUP_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `BACKUP_FAILED` | 备份操作失败 | ✅ | 检查日志获取详细错误信息 |
| `BACKUP_TOOL_NOT_FOUND` | 备份工具未找到 | ❌ | 安装或配置正确的工具路径 |
| `BACKUP_PERMISSION_DENIED` | 权限不足 | ❌ | 检查文件系统和数据库权限 |
| `BACKUP_DISK_FULL` | 磁盘空间不足 | ❌ | 清理磁盘空间或更改备份位置 |
| `BACKUP_ALREADY_RUNNING` | 备份已在进行中 | ❌ | 等待当前备份完成 |

### 恢复错误 (RESTORE_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `RESTORE_FAILED` | 恢复操作失败 | ✅ | 检查备份文件完整性 |
| `RESTORE_BACKUP_NOT_FOUND` | 备份文件不存在 | ❌ | 检查备份 ID 和存储位置 |
| `RESTORE_BACKUP_CORRUPTED` | 备份文件损坏 | ❌ | 使用其他备份文件 |
| `RESTORE_TARGET_EXISTS` | 目标已存在且未设置覆盖 | ❌ | 设置覆盖选项或更改目标名称 |

### 任务错误 (TASK_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `TASK_NOT_FOUND` | 任务不存在 | ❌ | 检查任务 ID |
| `TASK_ALREADY_CANCELLED` | 任务已被取消 | ❌ | 无需操作 |
| `TASK_CANNOT_CANCEL` | 任务无法取消 | ❌ | 等待任务完成 |
| `TASK_QUEUE_FULL` | 任务队列已满 | ✅ | 等待或增加队列大小 |

### 存储错误 (STORAGE_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `STORAGE_ERROR` | 存储操作失败 | ✅ | 检查存储系统状态 |
| `STORAGE_PERMISSION_DENIED` | 存储权限不足 | ❌ | 检查文件系统权限 |
| `STORAGE_NOT_FOUND` | 存储路径不存在 | ❌ | 创建存储目录 |
| `STORAGE_QUOTA_EXCEEDED` | 存储配额超限 | ❌ | 清理空间或增加配额 |

### 云存储错误 (CLOUD_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `CLOUD_AUTH_FAILED` | 云存储认证失败 | ❌ | 检查访问密钥和权限 |
| `CLOUD_BUCKET_NOT_FOUND` | 存储桶不存在 | ❌ | 创建存储桶或检查名称 |
| `CLOUD_NETWORK_ERROR` | 网络错误 | ✅ | 检查网络连接 |
| `CLOUD_QUOTA_EXCEEDED` | 云存储配额超限 | ❌ | 增加配额或清理数据 |

### MySQL 特定错误 (MYSQL_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `MYSQL_BINLOG_NOT_FOUND` | Binlog 文件不存在 | ❌ | 检查 binlog 配置和保留策略 |
| `MYSQL_BINLOG_ACCESS_DENIED` | Binlog 访问权限不足 | ❌ | 检查文件权限和用户权限 |
| `MYSQL_DUMP_FAILED` | mysqldump 执行失败 | ✅ | 检查数据库状态和权限 |
| `MYSQL_LOCK_TIMEOUT` | 表锁定超时 | ✅ | 减少并发或增加超时时间 |

### Elasticsearch 特定错误 (ES_*)

| 错误代码 | 描述 | 可重试 | 解决方案 |
|---------|------|--------|----------|
| `ES_CLUSTER_UNAVAILABLE` | ES 集群不可用 | ✅ | 检查集群状态 |
| `ES_REPOSITORY_NOT_FOUND` | 快照仓库不存在 | ❌ | 创建仓库或检查配置 |
| `ES_SNAPSHOT_FAILED` | 快照创建失败 | ✅ | 检查集群状态和仓库配置 |
| `ES_PLUGIN_NOT_INSTALLED` | 所需插件未安装 | ❌ | 安装相应的存储插件 |

## 🔧 错误处理示例

### 基本错误处理

```go
func handleError(err error) {
    if backupErr, ok := err.(*types.BackupError); ok {
        switch backupErr.Code {
        case "CONNECTION_FAILED":
            if backupErr.Retryable {
                log.Warn("Connection failed, will retry", "error", backupErr)
                // 实现重试逻辑
            } else {
                log.Error("Connection failed permanently", "error", backupErr)
            }
        case "BACKUP_DISK_FULL":
            log.Error("Disk full, backup cannot continue", "error", backupErr)
            // 发送告警
            sendAlert("Disk Full", backupErr.Message)
        case "MYSQL_BINLOG_NOT_FOUND":
            log.Error("Binlog not found, check retention policy", "error", backupErr)
            // 建议解决方案
            log.Info("Suggestion: Check MySQL binlog retention settings")
        default:
            log.Error("Backup operation failed", "error", backupErr)
        }
    } else {
        log.Error("Unexpected error", "error", err)
    }
}
```

### 重试机制

```go
func performBackupWithRetry(manager BackupManager, maxRetries int) error {
    var lastErr error

    for i := 0; i < maxRetries; i++ {
        taskID, err := manager.BackupAsync(context.Background(),
            types.MySQL, "production", types.BackupTypeArchival, "retry backup")

        if err == nil {
            return waitForTaskCompletion(manager, taskID)
        }

        lastErr = err

        // 检查是否可重试
        if backupErr, ok := err.(*types.BackupError); ok {
            if !backupErr.Retryable {
                return err // 不可重试错误，直接返回
            }

            // 根据错误类型调整重试间隔
            delay := calculateRetryDelay(backupErr.Code, i)
            time.Sleep(delay)
        } else {
            return err // 非备份错误，直接返回
        }
    }

    return fmt.Errorf("backup failed after %d retries: %w", maxRetries, lastErr)
}

func calculateRetryDelay(errorCode string, attempt int) time.Duration {
    baseDelay := time.Second * 5

    switch errorCode {
    case "CONNECTION_TIMEOUT":
        return baseDelay * time.Duration(attempt+1) // 递增延迟
    case "TASK_QUEUE_FULL":
        return baseDelay * 2 // 固定延迟
    default:
        return baseDelay
    }
}
```

### 错误监控和告警

```go
type ErrorMonitor struct {
    alertThreshold map[string]int
    errorCounts    map[string]int
    alertFunc      func(string, string)
}

func (m *ErrorMonitor) RecordError(err error) {
    if backupErr, ok := err.(*types.BackupError); ok {
        m.errorCounts[backupErr.Code]++

        if threshold, exists := m.alertThreshold[backupErr.Code]; exists {
            if m.errorCounts[backupErr.Code] >= threshold {
                m.alertFunc(backupErr.Code,
                    fmt.Sprintf("Error %s occurred %d times",
                        backupErr.Code, m.errorCounts[backupErr.Code]))

                // 重置计数器
                m.errorCounts[backupErr.Code] = 0
            }
        }
    }
}
```

---

# 技术方案

## 📋 备份查询优化方案

### 背景

随着备份数据的增长，查询性能成为关键问题。本方案旨在优化备份查询性能，提供高效的数据检索能力。

### 当前问题

1. **查询性能差**：大量备份记录导致查询缓慢
2. **内存占用高**：全量加载备份记录消耗大量内存
3. **并发性能差**：多用户查询时性能下降明显
4. **缺乏索引**：没有有效的索引机制

### 解决方案

#### 1. 分页查询机制（重构后）

```go
type BackupFilter struct {
    // === 核心过滤字段（支持多值） ===
    TaskTypes   []TaskType   `json:"task_types,omitempty"`   // 任务类型过滤
    SourceTypes []SourceType `json:"source_types,omitempty"` // 数据源类型过滤
    BackupTypes []BackupType `json:"backup_types,omitempty"` // 备份类型过滤
    Statuses    []TaskStatus `json:"statuses,omitempty"`     // 任务状态过滤

    // === 时间范围查询 ===
    StartTime *time.Time `json:"start_time,omitempty"` // 开始时间（包含）
    EndTime   *time.Time `json:"end_time,omitempty"`   // 结束时间（包含）

    // === 搜索功能 ===
    SearchText string `json:"search_text,omitempty"` // 对任务描述进行模糊搜索

    // === 分页参数 ===
    Limit  int `json:"limit,omitempty"`  // 分页大小（默认50，最大1000）
    Offset int `json:"offset,omitempty"` // 偏移量
}

type BackupQueryResult struct {
    Backups    []*BackupRecord `json:"backups"`
    Total      int             `json:"total"`
    HasMore    bool            `json:"has_more"`
    NextOffset int             `json:"next_offset,omitempty"`
}
```

#### 2. 索引优化

```go
type BackupIndex struct {
    BySourceType map[SourceType][]*BackupRecord
    ByBackupType map[BackupType][]*BackupRecord
    ByTimeRange  *TimeRangeIndex
    BySearchText *TextIndex
}

type TimeRangeIndex struct {
    intervals []TimeInterval
}

type TimeInterval struct {
    Start   time.Time
    End     time.Time
    Records []*BackupRecord
}
```

#### 3. 缓存机制

```go
type QueryCache struct {
    cache    map[string]*CacheEntry
    mutex    sync.RWMutex
    maxSize  int
    ttl      time.Duration
}

type CacheEntry struct {
    Result    *BackupQueryResult
    CreatedAt time.Time
}

func (c *QueryCache) Get(key string) (*BackupQueryResult, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()

    entry, exists := c.cache[key]
    if !exists {
        return nil, false
    }

    if time.Since(entry.CreatedAt) > c.ttl {
        delete(c.cache, key)
        return nil, false
    }

    return entry.Result, true
}
```

### 性能指标

| 指标 | 优化前 | 优化后 | 改善 |
|------|--------|--------|------|
| 查询延迟 | 2-5s | 50-200ms | 90%+ |
| 内存使用 | 500MB+ | 50-100MB | 80%+ |
| 并发性能 | 10 QPS | 100+ QPS | 10x |

## 🔧 GoCloud 集成方案

### 背景

为了支持更多云存储提供商，计划集成 GoCloud 库，提供统一的云存储抽象。

### 目标

1. **统一接口**：提供一致的云存储操作接口
2. **多云支持**：支持更多云存储提供商
3. **易于扩展**：简化新存储后端的添加
4. **性能优化**：利用 GoCloud 的性能优化

### 架构设计

```go
// 统一的云存储接口
type CloudBackend interface {
    Put(ctx context.Context, key string, data io.Reader) error
    Get(ctx context.Context, key string) (io.ReadCloser, error)
    Delete(ctx context.Context, key string) error
    List(ctx context.Context, prefix string) ([]string, error)
    Exists(ctx context.Context, key string) (bool, error)
    HealthCheck(ctx context.Context) error
}

// GoCloud 实现
type GocloudBackend struct {
    bucket *blob.Bucket
    config *CloudStorageConfig
}

func NewGocloudBackend(config *CloudStorageConfig) (*GocloudBackend, error) {
    var bucket *blob.Bucket
    var err error

    switch config.Type {
    case "s3":
        bucket, err = s3blob.OpenBucket(ctx, sess, config.Bucket, nil)
    case "gcs":
        bucket, err = gcsblob.OpenBucket(ctx, client, config.Bucket, nil)
    case "azure":
        bucket, err = azureblob.OpenBucket(ctx, pipeline, config.Container, nil)
    default:
        return nil, fmt.Errorf("unsupported storage type: %s", config.Type)
    }

    if err != nil {
        return nil, err
    }

    return &GocloudBackend{
        bucket: bucket,
        config: config,
    }, nil
}
```

### 实施计划

#### 阶段一：基础集成
- [ ] 集成 GoCloud blob 包
- [ ] 实现基础的 CRUD 操作
- [ ] 添加错误处理和重试机制

#### 阶段二：性能优化
- [ ] 实现并发上传/下载
- [ ] 添加压缩支持
- [ ] 优化大文件处理

#### 阶段三：功能扩展
- [ ] 支持更多云存储提供商
- [ ] 添加加密支持
- [ ] 实现智能重试和故障转移

### 兼容性考虑

```go
// 保持向后兼容的配置
type CloudStorageConfig struct {
    Enabled bool   `json:"enabled"`
    Type    string `json:"type"`

    // 通用配置
    Bucket    string `json:"bucket,omitempty"`
    Container string `json:"container,omitempty"`
    Region    string `json:"region,omitempty"`

    // 认证配置
    AccessKey       string `json:"access_key,omitempty"`
    SecretKey       string `json:"secret_key,omitempty"`
    CredentialsFile string `json:"credentials_file,omitempty"`

    // GoCloud 特定配置
    URL        string            `json:"url,omitempty"`        // GoCloud URL
    Options    map[string]string `json:"options,omitempty"`    // 额外选项
}
```

## 📚 相关文档

- [用户指南](USER_GUIDE.md) - 完整的用户使用指南
- [部署指南](DEPLOYMENT_GUIDE.md) - 部署和运维指南
- [故障排除指南](TROUBLESHOOTING.md) - 常见问题解决

---

> 💡 **提示**: 开发过程中遇到问题，请先查看相关文档和测试用例。
>
> 🔧 **工具**: 使用 `make dev` 可以快速设置开发环境。