**支持的 S3 协议云存储服务**：
- **Amazon S3** - AWS 原生对象存储服务
- **MinIO** - 开源 S3 兼容对象存储
- **阿里云 OSS** - 阿里云对象存储服务（S3 兼容模式）
- **腾讯云 COS** - 腾讯云对象存储（S3 兼容模式）
- **华为云 OBS** - 华为云对象存储服务（S3 兼容模式）
- **七牛云 Kodo** - 七牛云对象存储（S3 兼容模式）
- **其他 S3 兼容存储** - 任何实现 S3 API 的对象存储服务

### 应用层配置

```json
{
  "backup_root": "/var/lib/unibackup",           // 必需：本地备份根目录，用于路径生成和元数据存储
  "cloud_storage": {
    "enabled": true,                             // 必需：启用云存储功能
    "type": "s3",                               // 必需：指定云存储类型为 S3
    "bucket": "my-backup-bucket",               // 必需：S3 存储桶名称
    "region": "us-east-1",                      // 必需：AWS 区域
    "access_key": "AKIAIOSFODNN7EXAMPLE",       // 可选：S3 访问密钥 ID（推荐通过环境变量提供）
    "secret_key": "wJalrXUtnFEMI/K7MDENG..."    // 可选：S3 秘密访问密钥（推荐通过环境变量提供）
  }
}
```

**环境变量**：
```bash
# AWS 认证信息（必需）
export AWS_ACCESS_KEY_ID="AKIAIOSFODNN7EXAMPLE"
export AWS_SECRET_ACCESS_KEY="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
export AWS_DEFAULT_REGION="us-east-1"
```

### Elasticsearch 容器配置

**1. 安装 repository-s3 插件**：
```bash
bin/elasticsearch-plugin install repository-s3
```

**2. 配置 keystore（推荐）**：
```bash
# 添加 S3 认证信息
bin/elasticsearch-keystore add s3.client.default.access_key
bin/elasticsearch-keystore add s3.client.default.secret_key

# 重启 ES 使配置生效
```

### MinIO S3 兼容存储配置

```json
{
  "backup_root": "/var/lib/unibackup",           // 必需：本地备份根目录
  "cloud_storage": {
    "enabled": true,                             // 必需：启用云存储功能
    "type": "s3",                               // 必需：指定云存储类型为 S3
    "bucket": "backup-bucket",                  // 必需：存储桶名称
    "region": "us-east-1",                      // 必需：区域（S3兼容存储可以是任意值）
    "endpoint": "http://minio:9000",            // 必需：S3兼容存储服务端点
    "access_key": "minioadmin",                 // 必需：S3兼容存储访问密钥
    "secret_key": "minioadmin123"               // 必需：S3兼容存储秘密密钥
  }
}
```

**环境变量**：
```bash
export AWS_ACCESS_KEY_ID="minioadmin"
export AWS_SECRET_ACCESS_KEY="minioadmin123"
```
