# UniBackup: 统一备份恢复SDK 设计文档

本文档旨在详细阐述一个统一的、支持多数据源备份与恢复的Go语言SDK（`UniBackup`）的技术需求、架构设计与实现方案。该方案的核心是提供两种不同安全保证和生命周期策略的备份类型，以满足不同的业务场景。

## 1. 需求要点总结

- **统一管理**: 提供单一SDK来同时管理Elasticsearch和MySQL的备份与恢复。
- **双模备份策略**:
    - **归档全量备份 (Archival Full Backup)**: 创建完全独立的、自包含的全量数据快照。每个快照都是一个独立的恢复点，可以随时被安全删除，而不影响任何其他备份。主要用于灾难恢复、版本归档和迁移。
    - **受保护的增量链 (Protected Incremental Chain)**: 创建一个由"初始全量备份 + N个后续增量备份"组成的、严格的恢复链。为确保数据可恢复至最新状态，系统将强制禁止删除此链条中的任何部分。此模式旨在最大程度地减少数据丢失窗口 (RPO)。
- **核心容灾能力**: 系统必须能在原始环境完全损坏的情况下，仅通过备份目录就能完成所有数据。
- **易用性与自动化**: 操作需非阻塞，支持任务状态查询，并能自动处理复杂的恢复流程。
- **工程质量**: 产出物必须是遵循Go语言最佳实践、易于集成的、高质量的SDK。

---

## 2. 项目概述 (Project Overview)

`UniBackup`是一个提供统一容灾（Disaster Recovery）能力的企业级Go语言SDK。它采用现代化的接口设计和异步任务模式，为开发者提供对多种数据源（MySQL和Elasticsearch）进行高效、可靠的备份与恢复操作。

**核心特性**：
- **接口驱动设计**: 遵循Go最佳实践，便于测试和扩展
- **双模式操作**: 支持异步（推荐）和同步两种操作模式
- **智能恢复**: 自动识别备份类型并构建最优恢复策略，采用完全重建策略确保数据一致性
- **并发控制**: 采用全局信号量限制最大并发任务数，并结合细粒度Locker实现数据源级别互斥。支持最大并发数配置，不同数据源可并发，单一数据源串行。
- **完整任务管理**: 支持任务取消、进度跟踪、状态持久化和清理，具备深拷贝机制防止并发修改
- **强扩展性**: 模块化架构设计，易于添加新的数据源支持
- **原子性保证**: 分组操作支持原子性，包括预恢复快照和失败回滚机制
- **云存储支持**: 支持 AWS S3、Google Cloud Storage、Azure Blob Storage，提供企业级云备份能力

---

## 3. 核心需求 (Core Requirements)

### 功能性需求 (Functional)

- **FR1: 统一API**: 提供单一、简洁的API入口，用于管理不同数据源的备份与恢复。
- **FR2: 数据源支持**: 首期必须支持 `MySQL` 和 `Elasticsearch`。
- **FR3: 备份类型与策略**:
    - `MySQL`:
        - **基于工具实现**: 使用 `mysqldump`、`mysql`、`mysqlbinlog` 和 `mysqladmin` 命令行工具进行备份和恢复。
        - **归档全量备份**: 创建一个完整的、独立的数据库快照。
        - **受保护的增量链**:
            - **初始备份**: 创建基础全量备份。
            - **增量备份**: 基于上一个点导出二进制日志。
            - **安全约束**: 整条链（初始全量+所有增量）被视为一个实体，禁止删除其中任何一个单独的备份点。
    - `Elasticsearch`:
        - 其快照机制与"受保护的增量链"高度契合。仓库（Repository）中的所有快照构成了一个逻辑上连续、物理上增量、且删除安全的恢复历史。
        - **受保护的增量链**: SDK将直接映射到ES的快照机制上，每次备份都是链条上的一个新节点。
        - **归档全量备份**: SDK可通过将特定快照复制到另一个专用的"归档仓库"来实现，以达到物理隔离和独立生命周期的目的。
- **FR4: 恢复能力**:
    - **归档全量备份**: 支持从任一指定的归档备份点进行恢复。
    - **受保护的增量链**: 支持从链中的任意备份点进行恢复。SDK会自动构建并应用从链的起点到该指定点的完整恢复序列，确保数据的一致性。
- **FR5: 异步任务**: 所有备份和恢复操作必须是**非阻塞**的，调用后立即返回任务ID。
- **FR6: 状态查询**: 能够通过任务ID查询任一备份/恢复任务的当前状态（如 `pending`, `running`, `completed`, `failed`）、进度及错误信息。
- **FR7: 备份列表**: **必须能够清晰地区分并列出两种类型的可用备份**：独立的归档备份列表，以及受保护的增量链列表（每条链展示为一个可恢复实体）。
- **FR8: 可配置存储**: 用户必须能指定一个唯一的**备份根目录** (`BackupRoot`)，所有备份产物和元数据都将在此目录下进行管理。
- **FR9: 分组备份 (BackupAll)**:
    - 必须支持将多个数据源（MySQL、ES）的备份操作组合成一个单一的逻辑组。
    - `BackupAll` 操作支持为每个数据源执行**归档全量备份**、**链初始全量备份**或**增量备份**，以创建一致性的独立恢复快照或增量点。
- **FR10: 并发控制**: 采用多层次并发控制策略，支持可配置的并发数量。
    - **信号量控制**: 支持设置最大并发任务数，允许多个任务并发执行。
    - **细粒度锁定**: 按数据源名称进行锁定，不同数据源可以并发操作。
    - **任务取消**: 支持通过上下文取消正在执行的任务。
- **FR11: 操作原子性 (Atomicity)**: 分组备份(`BackupAll`)和恢复操作（`Restore...`）提供有限的原子性保证。
    - **备份原子性**: 如果任何一个子备份失败，且启用了 `CleanupOnFailure` 选项，系统会根据子备份类型执行清理：
        - 对于**归档全量备份**和**链初始全量备份**：已完成的子备份会被自动清理（删除），以防止产生不完整的备份集。
        - 对于**增量备份 (`incremental`)**：**该策略不适用**。即使失败，已完成的增量备份也**不会**被自动清理，因为删除它们会破坏增量链的完整性，导致数据永久丢失。此时系统会记录警告，但不会执行物理删除。
    - **元数据与任务状态一致性**: 系统必须保证备份元数据（`metadata.json`）的写入**严格发生**在任务状态更新为`Completed`之前，确保不存在没有备份产物的成功任务。
- **FR12: 备份清理 (Lifecycle Management)**:
    - **提供统一的删除接口**以匹配双模备份策略。
    - `DeleteBackup`: 允许安全地删除任何一个独立的归档全量备份。如果备份是增量链的一部分，此操作将**强制**删除整条链以保证数据完整性。
    - **底层安全保障**: 底层存储逻辑**必须**在物理上阻止任何试图单独删除增量链内部备份点的操作，为上层逻辑提供一道额外保险。
- **FR13: 恢复操作原子性 (Restore Atomicity)**: 恢复操作提供有限的原子性保证。
    - **回滚机制**: `RestoreAll` 操作提供了强大的原子性保证，其核心流程如下：
        1.  **预恢复快照创建**：在执行任何实际恢复操作之前，如果 `CreateSnapshot` 选项为 `true`，SDK会为所有涉及的数据源创建临时的“预恢复快照”（类型为归档全量备份）。这些快照是实现回滚的基石，它们捕获了数据源在恢复操作开始前的状态。
        2.  **串行执行恢复**：实际的恢复操作会按请求中数据源的顺序串行执行。这种串行化确保了每一步的顺序性和可控性，便于在失败时进行精确回滚。
        3.  **失败时同步回滚**：如果在恢复过程中任何一个数据源的恢复失败，且 `RollbackOnFailure` 选项为 `true`，系统会立即中断后续恢复。然后，它会利用之前创建的预恢复快照，**同步阻塞地**将所有已成功恢复的数据源回滚到操作开始前的状态，确保整个分组恢复的原子性（要么全部成功，要么全部回滚）。
        4.  **临时快照清理**：无论分组恢复最终成功还是因回滚而结束，系统都会**异步**清理掉所有为实现原子性而创建的临时预恢复快照，以回收存储资源。
    - **双重失败处理**: 如果恢复失败后的自动回滚操作**再次失败**，系统会记录错误并警告，但数据源可能处于不一致状态。需要人工检查和修复。
- **FR14: 任务状态持久化与恢复 (Task Persistence & Recovery)**: 任务状态必须被持久化。
    - **重启恢复**: 在SDK主进程重启后，必须能重新加载任务状态。
    - **中断任务处理**: 任何在中断时处于`Running`状态的任务都必须被自动标记为`Failed`。
    - **补偿清理**: 在将中断任务标记为`Failed`后，系统**必须**自动触发其关联的补偿逻辑（如`BackupAll`的原子性回滚），清理残留的中间产物。
    - **并发安全**: 任务状态的持久化采用深拷贝机制，避免并发修改导致的JSON序列化问题。
- **FR15: 系统状态检查 (System State Check)**: 在执行任何备份或恢复操作之前，SDK会自动检查目标数据源的健康状态，包括：
    - 读取本地锁定状态（status.json），如为锁定则拒绝操作；
    - 主动用配置的账号密码连接目标服务（如MySQL/Elasticsearch），如服务不可用则拒绝操作。
    - 分组操作（BackupAll/RestoreAll）会对所有涉及的数据源分别检查，只要有一个异常则整体拒绝。
    - 单一操作只检查本次sourceType。
    - **特别地，对于分组操作（BackupAllAsync/RestoreAllAsync），会在分派任务前对所有涉及的数据源进行健康检查，只要有一个异常则整体拒绝，以实现快速失败。**

### 非功能性需求 (Non-Functional)

- **NFR1: 容灾设计**: 系统设计必须确保在SDK运行环境被摧毁后，仅通过 `BackupRoot` 目录就能完成所有数据的恢复。
- **NFR2: Go SDK最佳实践**: API设计需遵循Go语言的最佳实践，包括清晰的入口、面向接口编程、上下文（Context）传递、结构化配置和明确的错误处理。
- **NFR3: 易于集成**: SDK应以Go模块形式提供，依赖清晰，第三方应用能够通过简单的 `import` 和配置即可使用。
- **NFR4: 扩展性**: 架构设计应支持在未来轻松添加新的数据源`Provider`，而无需修改核心调度逻辑。
- **NFR5: 可观察性 (Observability)**: SDK应接受一个标准的日志记录器接口，以便将其内部日志集成到宿主应用的日志系统中。
- **NFR6: 任务状态持久化 (Task Persistence)**: 任务状态应支持持久化，确保在SDK主进程重启后，仍能查询到进行中或已完成任务的状态，避免产生孤立的后台进程。
- **NFR7: 任务记录清理 (Task Record Cleanup)**: SDK应提供可配置的机制，自动清理旧的、已完成的任务记录，以防止任务列表无限增长，并有效管理内存和磁盘空间。清理策略应支持基于时间（例如，保留最近N天的任务）和/或基于数量（例如，保留最新的M个任务）的配置。

---

## 4. 架构设计（Architecture Design）

### 4.1 总体架构
- 分层解耦：系统分为 API 层、任务调度层、存储管理层、数据源 Provider 层，各层通过接口解耦，便于扩展和维护。
- 接口驱动：所有核心能力（如备份、恢复、任务、存储）均以接口抽象，支持多实现和单元测试。
- 模块化实现：每种数据源（MySQL、Elasticsearch）独立实现 Provider，未来可平滑扩展新类型。
- 存储抽象：通过 Backend 接口抽象存储层，支持本地存储和多种云存储服务。

```mermaid
flowchart TD
    U["用户/调用方<br/>（CLI/SDK/集成系统）"]
    U --> API["API层<br/>BackupManager"]
    API --> TM["任务调度层<br/>TaskManager"]
    API --> SM["存储管理层<br/>StorageManager"]
    TM --> SM
    TM --> P1["Provider: MySQL"]
    TM --> P2["Provider: Elasticsearch"]
    P1 --> SM
    P2 --> SM
    P1 --> DB["MySQL服务"]
    P2 --> ES["Elasticsearch集群"]
    SM --> BE["Backend接口<br/>(存储抽象层)"]
    BE --> LS["本地存储<br/>(LocalBackend)"]
    BE --> CS["云存储<br/>(CloudBackend)"]
    CS --> S3["AWS S3"]
    CS --> GCS["Google Cloud Storage"]
    CS --> AZ["Azure Blob Storage"]
    
    subgraph "主要分层"
      API
      TM
      SM
      P1
      P2
    end
    
    classDef api fill:#bbf,stroke:#333,stroke-width:1px;
    classDef task fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#bbf,stroke:#333,stroke-width:1px;
    classDef provider fill:#bfb,stroke:#333,stroke-width:1px;
    classDef ext fill:#fff,stroke:#333,stroke-width:1px;
    class API api;
    class TM task;
    class SM storage;
    class P1,P2 provider;
    class DB,ES,FS ext;
```

### 4.2 主要模块
1. **API 层（internal/api/backup_manager.go）**
   - 对外暴露统一的备份/恢复/任务管理接口。
   - 负责参数校验、锁管理、协调并委托具体操作给下层任务调度层（TaskManager）和存储管理层（StorageManager）。
2. **任务调度层（internal/task/manager.go）**
   - 负责所有异步任务的生命周期管理、并发控制、状态持久化。
   - 支持任务取消、进度跟踪、失败补偿、分组原子性。
3. **存储管理层（internal/storage/manager.go）**
   - 负责所有备份产物、元数据、链元数据的目录和文件管理。
   - 提供备份链、归档、查找、删除等能力，严格安全检查。
4. **数据源 Provider 层（internal/provider/）**
   - 每种数据源（MySQL/Elasticsearch）实现统一接口，支持备份/恢复策略。
   - MySQL Provider 使用外部工具实现，Elasticsearch Provider 使用快照API。

### 4.3 并发与安全
- 全局信号量+细粒度 Locker：最大并发数可配置，不同数据源可并发，同一数据源串行，防止资源争抢和链冲突。
- 安全防护：所有删除、恢复等高危操作有多重安全检查，增量链只允许整体删除，防止链断裂。
- 健康检查：所有操作前自动检查本地锁定状态和远端服务可用性，异常时强制拒绝。

### 4.4 扩展性与可维护性
- 新增数据源：只需实现 BackupProvider 接口并注册即可，无需改动主流程。
- 新增存储后端：只需实现 StorageManager 接口即可，主流程无感知。
- 详细注释与分层结构：所有核心方法均有中文注释，便于二次开发和维护。

### 4.5 典型调用链

```mermaid
sequenceDiagram
    participant User
    participant API
    participant TaskMgr
    participant StorageMgr
    participant Provider

    User->>API: Backup/Restore/Query
    API->>TaskMgr: 创建/调度任务
    TaskMgr->>StorageMgr: 创建/查找备份记录
    TaskMgr->>Provider: 调用具体备份/恢复
    Provider->>StorageMgr: 读写元数据/产物
    TaskMgr->>StorageMgr: 更新任务/备份状态
    API->>User: 返回任务ID/结果/状态
```

---

## 5. 模块详细设计 (Go API)

```go
package unibackup

import (
    "context"
    "log/slog"
    "time"
)

// === API设计采用接口模式，遵循Go最佳实践 ===

// BackupManager 是SDK的主入口接口。
// 它定义了所有备份恢复操作的标准契约，支持异步和同步两种操作模式。
// 接口设计便于测试、扩展和依赖注入。
type BackupManager interface {
    // === 异步操作接口（推荐使用） ===

    // BackupAsync 异步备份操作，立即返回任务ID
    BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error)

    // RestoreAsync 异步恢复操作，立即返回任务ID
    RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error)

    // BackupAllAsync 异步分组备份操作，立即返回任务ID
    BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error)

    // RestoreAllAsync 异步分组恢复操作，立即返回任务ID
    RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error)

    // === 任务管理接口 ===

    // GetTask 获取指定任务的详细信息
    GetTask(taskID string) (*types.Task, error)

    // ListTasks 列出所有任务（支持分页和过滤）
    ListTasks() ([]*types.Task, error)

    // CancelTask 取消正在执行的任务
    CancelTask(taskID string) error

    // ClearOldTasks 清理已完成的旧任务记录
    ClearOldTasks() error

    // === 同步操作接口（向后兼容，阻塞执行） ===

    // Backup 同步备份操作，阻塞直到完成
    // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
    Backup(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (*types.BackupRecord, *types.BackupError)

    // Restore 同步恢复操作，阻塞直到完成
    // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
    Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError

    // === 系统管理接口 ===

    // Shutdown 优雅关闭BackupManager，等待所有任务完成
    // Shutdown 优雅关闭
    Shutdown() error

    // === 备份管理接口 ===

    // ListArchivalBackups 列出指定数据源的所有独立归档备份。
    ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError)

    // ListIncrementalChains 列出指定数据源的所有受保护的增量恢复链。
    ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError)

    // DeleteBackup 删除指定的备份。
    // 如果备份是独立的归档备份，则直接删除。
    // 如果备份是增量链的一部分，此操作将强制删除整条链以保证数据完整性。
    DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) *types.BackupError

    // === 统一任务查询接口（重构后）===
    // 注：以下接口经过重构，提供更强大的查询能力和更好的用户体验

    // ListAllBackups 统一的任务查询接口（重构后）
    // 默认查询所有任务类型，支持多值过滤、时间范围查询和搜索功能
    // 新增功能：多值过滤、时间范围查询、按开始时间排序（最新在前）
    ListAllBackups(ctx context.Context, filter types.BackupFilter) (*types.BackupListResult, error)

    // GetBackupDetails 获取备份任务的详细信息。
    // 支持单个备份和分组备份的详情获取，分组备份会递归显示所有子任务。
    GetBackupDetails(ctx context.Context, taskID string) (*types.Task, error)

    // DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型。
    // 重要说明：增量备份删除时会删除整条增量链，保持数据完整性。
    DeleteBackupByTaskID(ctx context.Context, taskID string) error

    // GetBackupDeletionInfo 获取备份删除的影响信息。
    // 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等。
    GetBackupDeletionInfo(ctx context.Context, taskID string) (*types.BackupDeletionInfo, error)

    // === 动态配置管理接口（v2.0新增）===

    // UpdateConfig 支持运行时动态更新配置，无需重启服务
    // 允许在运行时修改数据源配置、并发限制、超时设置等
    UpdateConfig(cfg *types.Config) error

    // GetBackupDeletionInfo 获取备份删除的影响信息
    // 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等
    GetBackupDeletionInfo(taskID string) (*types.BackupDeletionInfo, error)

    // RestoreByTaskID 基于taskID恢复备份到原始位置
    // 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置
    // 自动识别备份类型（归档/增量链）并构建正确的恢复链
    RestoreByTaskID(ctx context.Context, taskID string) (string, error)
}

// NewManager 创建一个新的BackupManager实例。
// cfg: 提供SDK运行所需的所有配置，如备份目录、数据库连接信息等。
// 返回实现了BackupManager接口的具体实例。
func NewManager(cfg *types.Config) (BackupManager, error) { /*...*/ }

// --- 备份操作 ---

// BackupAsync 发起一个异步备份任务，支持三种备份类型。
// ctx: 控制请求生命周期的上下文。
// sourceType: 需要备份的数据源类型 (MySQL, Elasticsearch)。
// sourceName: 数据源的唯一标识名称。
// backupType: 备份类型 (types.BackupTypeArchival, types.BackupTypeChainInitial, types.BackupTypeChainIncremental)。
// description: 对本次备份的文字描述。
// 返回值: 任务ID和可能的错误。
//
// 备份类型说明：
// - types.BackupTypeArchival: 创建独立的、可随时删除的【归档全量备份】
// - types.BackupTypeChainInitial: 创建【受保护增量链】的初始全量备份
// - types.BackupTypeChainIncremental: 向现有增量链添加增量备份点
func (m *Manager) BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (taskID string, err error) { /*...*/ }

// BackupAllAsync 为多个数据源创建【归档全量备份】，并将它们逻辑上归为一个备份集。
// 此操作保证了所有数据源在同一时间点的快照一致性。
// req: 包含多个数据源备份请求的分组备份请求。
func (m *Manager) BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (taskID string, err error) { /*...*/ }

// --- 恢复操作 ---

// RestoreAsync 从指定的备份中恢复数据，自动识别备份类型并执行相应的恢复流程。
// 对于归档备份：直接恢复单个备份点。
// 对于增量链备份：自动构建从链起点到指定备份点的完整恢复链，按顺序执行恢复。
// 恢复成功完成后，如果配置了OnSuccess回调函数，将自动执行回调逻辑。
// config: 包含恢复配置的结构体，包括数据源类型、名称、备份ID和可选的成功回调函数。
func (m *Manager) RestoreAsync(ctx context.Context, config types.RestoreConfig) (taskID string, err error) { /*...*/ }

// RestoreAllAsync 从一个备份集中恢复所有数据源。
// 支持原子性操作：要么全部成功，要么全部回滚。
// 整个分组恢复成功完成后，如果配置了OnSuccess回调函数，将自动执行回调逻辑。
// config: 包含多个数据源恢复请求的分组恢复配置和可选的成功回调函数。
func (m *Manager) RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (taskID string, err error) { /*...*/ }

// RestoreByTaskID 基于taskID恢复备份到原始位置（不带回调）。
// 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置。
func (m *Manager) RestoreByTaskID(ctx context.Context, taskID string, force bool) (string, error) { /*...*/ }

// RestoreByTaskIDWithCallback 基于taskID恢复备份到原始位置（带回调）。
// 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置。
// 自动判断任务类型并设置相应的回调：单个备份任务回调在该数据源恢复完成时触发，分组备份任务回调在整个分组恢复完成时触发。
func (m *Manager) RestoreByTaskIDWithCallback(ctx context.Context, taskID string, force bool, callback types.RestoreSuccessCallback) (string, error) { /*...*/ }

// --- 列表操作 ---

// ListArchivalBackups 列出指定数据源的所有独立归档备份。
func (m *Manager) ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError) { /*...*/ }

// ListIncrementalChains 列出指定数据源的所有受保护的增量恢复链。
func (m *Manager) ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError) { /*...*/ }

// --- 删除操作 ---

// DeleteBackup 删除指定的备份，自动识别备份类型并执行相应的删除逻辑。
// 对于归档备份：直接删除单个备份。
// 对于增量链备份：根据安全策略决定是否允许删除（防止破坏链完整性）。
func (m *Manager) DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) error { /*...*/ }


// --- 任务管理 ---

// GetTask 获取任一任务的当前状态。
func (m *Manager) GetTask(taskID string) (*types.Task, error) { /*...*/ }


// === 数据结构预览 ===

// BackupFilter 备份列表的过滤条件
type BackupFilter struct {
    BackupType   types.BackupType   `json:"backup_type,omitempty"`   // 按备份类型过滤
    Status       types.BackupStatus `json:"status,omitempty"`        // 按状态过滤
    StartTime    *time.Time         `json:"start_time,omitempty"`    // 时间范围开始
    EndTime      *time.Time         `json:"end_time,omitempty"`      // 时间范围结束
    Limit        int                `json:"limit,omitempty"`         // 限制返回数量
}

// BackupAllRequest 分组备份请求
type BackupAllRequest struct {
    Sources           []types.BackupRequest `json:"sources"`             // 要备份的数据源列表
    Description       string                `json:"description"`         // 分组备份描述
    Atomic            bool                  `json:"atomic"`              // 是否需要原子性
    CleanupOnFailure  bool                  `json:"cleanup_on_failure"`  // 失败时是否清理已完成的备份
}

// BackupRequest 单一备份请求
type BackupRequest struct {
    SourceType    types.SourceType `json:"source_type"`              // 要备份的数据源类型
    SourceName    string           `json:"source_name"`              // 要备份的数据源实例名称
    BackupType    types.BackupType `json:"backup_type"`              // 备份类型
    Description   string           `json:"description"`              // 备份描述
    TargetChainID string           `json:"target_chain_id,omitempty"` // 可选：指定要追加到的增量链ID
}

// RestoreConfig 统一的恢复配置。
// 这是重构后的核心恢复配置类型，简化了恢复操作的配置。
//
// 设计理念：
// - 默认安全：CreateSafetyBackup 和 RollbackOnFailure 默认为 true
// - 完全重建：默认使用完全重建策略（MySQL删除重建数据库，ES删除所有索引）
// - 简单易用：大部分情况下使用默认配置即可
// - 事件驱动：支持恢复成功后的自动回调处理
type RestoreConfig struct {
    // 基础信息
    SourceType  SourceType `json:"source_type"`           // 数据源类型（MySQL, Elasticsearch）
    SourceName  string     `json:"source_name"`           // 数据源名称（数据库名/索引名）
    BackupID    string     `json:"backup_id"`             // 备份记录ID
    Description string     `json:"description,omitempty"` // 恢复描述

    // 安全配置（默认启用）
    CreateSafetyBackup bool `json:"create_safety_backup"` // 恢复前创建安全备份（默认 true）
    RollbackOnFailure  bool `json:"rollback_on_failure"`  // 失败时自动回滚（默认 true）

    // 高级选项
    SkipDataCleanup bool `json:"skip_data_cleanup"` // 跳过数据清理，保持旧行为（默认 false）
    Force           bool `json:"force,omitempty"`   // 强制恢复：中断正在运行的备份任务（默认 false）

    // 回调配置
    OnSuccess RestoreSuccessCallback `json:"-"` // 恢复成功时的回调函数，不序列化
}

// BatchRestoreConfig 批量恢复配置。
// 用于同时恢复多个数据源，支持原子性操作。
//
// 与旧版本相比的改进：
// - 每个恢复都有独立的安全配置
// - 简化了全局配置选项
// - 统一了单个恢复和批量恢复的行为
// - 支持分组恢复完成后的统一回调处理
type BatchRestoreConfig struct {
    Restores    []RestoreConfig `json:"restores"`    // 要恢复的数据源列表
    Description string          `json:"description"` // 批量恢复描述
    Atomic      bool            `json:"atomic"`      // 是否原子操作（默认 true）
    Parallel    bool            `json:"parallel"`    // 是否并行执行（仅在非原子时有效，默认 false）
    Force       bool            `json:"force,omitempty"` // 强制恢复：中断正在运行的备份任务（默认 false）

    // 回调配置
    OnSuccess RestoreSuccessCallback `json:"-"` // 分组恢复成功时的回调函数，不序列化
}

// RestoreSuccessCallback 恢复成功完成时的回调函数类型
// 在恢复任务成功完成后自动调用，用于执行后续处理逻辑（如发送通知、更新外部系统等）
// 回调失败不会影响恢复任务的成功状态，错误信息会记录到任务元数据中
type RestoreSuccessCallback func(ctx context.Context) error

// ArchivalBackup 代表一个独立的、自包含的全量备份点。
type ArchivalBackup struct {
    Record *types.BackupRecord `json:"record"`       // 备份记录详情
}

// IncrementalChain 代表一条受保护的、不可分割的恢复链的摘要信息。
type IncrementalChain struct {
    ChainID string                `json:"chain_id"`  // 链的唯一ID
    Backups []*types.BackupRecord `json:"backups"`   // 链内所有备份记录，按时间顺序排列
}

// BackupProvider 定义了数据源提供者必须实现的核心接口
// 该接口将数据源的特定实现细节与上层编排逻辑解耦，支持扩展新的数据源类型
type BackupProvider interface {
    // Backup 执行一次备份操作
    // backupRecord: 当前备份的元数据记录，包含备份类型、路径等信息
    // prevRecord: 上一个备份的元数据记录，用于增量备份的上下文传递（全量备份时为nil）
    // Provider需要根据backupRecord.Type来决定具体的备份行为（归档、链初始、链增量）
    // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
    Backup(ctx context.Context, backupRecord *BackupRecord, prevRecord *BackupRecord) *BackupError

    // Restore 恢复一次独立的备份产物
    // backupRecord: 要恢复的备份记录
    // prevRecord: 恢复链中前一个备份的记录，用于增量恢复的上下文（全量恢复时为nil）
    // 对于增量恢复，Manager会按顺序多次调用此方法
    // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
    Restore(ctx context.Context, backupRecord *BackupRecord, prevRecord *BackupRecord) *BackupError

    // List 返回该数据源所有可用的备份记录元数据
    // 主要用于那些将元数据存储在外部系统（如Elasticsearch快照仓库）的provider
    // 对于将元数据存储在本地文件系统的provider，此方法可能返回空列表
    // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
    List(ctx context.Context) ([]*BackupRecord, *BackupError)

    // Delete 执行特定于提供者的删除操作
    // 例如，在Elasticsearch中删除对应的快照
    // 注意：此方法不应包含业务逻辑（如防止删除增量链的一部分），安全逻辑由上层负责
    // 返回结构化的BackupError而不是标准error，提供更丰富的错误信息
    Delete(ctx context.Context, backupRecord *BackupRecord) *BackupError
}

// Config 是SDK的配置结构体
type Config struct {
    BackupRoot         string        // 备份根目录, 必须
    Logger             *slog.Logger  // 可选的结构化日志记录器
    MySQL              *MySQLConfig  // MySQL配置, 可选
    ES                 *ESConfig     // Elasticsearch配置, 可选
    MaxConcurrentTasks int           // 最大并发任务数, 默认5
    TaskRetentionDays  int           // 任务记录保留天数, 默认365天 (一年)
    MaxTaskHistory     int           // 最大任务历史数量, 默认0 (不限制数量)
    CleanupBackupData  bool          // 是否清理备份数据, 默认false (只清理任务记录)
    BackupTimeout      time.Duration // 备份操作超时, 默认24h
    RestoreTimeout     time.Duration // 恢复操作超时, 默认24h
}

// MySQLConfig 包含了连接MySQL和其工具链所需的所有信息
type MySQLConfig struct {
    Host           string         `json:"host"`
    Port           int            `json:"port"`
    User           string         `json:"user"`
    Password       string         `json:"password"`
    DBName         string         `json:"db_name"`
    // BinlogBasePath 是binlog文件的基础路径，用于增量备份的direct模式
    BinlogBasePath string         `json:"binlog_base_path,omitempty"`
    // ToolsPath 指定MySQL工具的路径，用于备份和恢复操作
    ToolsPath      MySQLToolsPath `json:"tools_path"`
}

// MySQLToolsPath 定义了MySQL命令行工具的路径配置。
// 提取为独立结构体提高可读性和可维护性。
type MySQLToolsPath struct {
    Mysqldump   string `json:"mysqldump,omitempty"`   // mysqldump 用于创建数据库的逻辑备份
    Mysql       string `json:"mysql,omitempty"`       // mysql 命令行客户端，用于执行查询和恢复
    Mysqlbinlog string `json:"mysqlbinlog,omitempty"` // mysqlbinlog 用于处理二进制日志文件（增量备份）
    Mysqladmin  string `json:"mysqladmin,omitempty"`  // mysqladmin 用于执行管理操作，如刷新日志
}

// ESConfig 包含了连接Elasticsearch所需的所有信息
type ESConfig struct {
    Addresses        []string `json:"addresses"`              // 集群节点地址列表
    APIKey           string   `json:"api_key,omitempty"`      // API密钥认证（可选）
    User             string   `json:"user,omitempty"`         // 基本认证用户名（可选）
    Password         string   `json:"password,omitempty"`     // 基本认证密码（可选）
    // 双仓库设计：分别用于归档备份和受保护的增量链
    ArchivalRepoName string   `json:"archival_repo_name"`     // 用于【归档全量备份】的快照仓库名称，必须
    ManagedRepoName  string   `json:"managed_repo_name"`      // 用于【受保护的增量链】的快照仓库名称，必须

    // 仓库管理配置
    AutoCreateRepos  bool     `json:"auto_create_repos"`      // 是否自动创建快照仓库，默认true
    RepoBasePath     string   `json:"repo_base_path,omitempty"` // 快照仓库的基础路径，可选
}

// Task 代表一个异步任务的完整状态信息
type Task struct {
    ID          string                 `json:"id"`                    // 任务的唯一标识符，通常是UUID
    Type        TaskType               `json:"type"`                  // 任务类型：备份、恢复、分组备份等
    Source      SourceType             `json:"source,omitempty"`      // 操作的数据源类型（分组任务可能为空）
    Status      TaskStatus             `json:"status"`                // 任务状态：待执行、运行中、已完成、失败、已取消
    Progress    float64                `json:"progress"`              // 任务进度，0-100
    Error       string                 `json:"error,omitempty"`       // 错误信息摘要（向后兼容）
    ErrorDetail *BackupError          `json:"error_detail,omitempty"` // 详细的结构化错误信息
    StartTime   time.Time              `json:"start_time"`            // 任务开始执行的时间
    EndTime     time.Time              `json:"end_time,omitempty"`    // 任务完成或失败的时间
    SubTaskIDs  []string               `json:"sub_task_ids,omitempty"`// 分组任务包含的子任务ID列表
    Description string                 `json:"description,omitempty"` // 用户提供的任务描述
    Metadata    map[string]interface{} `json:"metadata,omitempty"`    // 任务相关的元数据（如备份记录ID等）
}

// BackupRecord 代表一个物理备份产物的元数据
// 这个结构体会被序列化为metadata.json文件，与备份数据一同存放
type BackupRecord struct {
    // === 基础标识信息 ===
    ID          string    `json:"id"`                        // 备份产物的唯一标识符
    Timestamp   time.Time `json:"timestamp"`       // 备份创建的时间戳
    Source      SourceType `json:"source"`            // 数据源类型 (MySQL, ES)
    SourceName  string    `json:"source_name"`       // 数据源实例名称 (如 db1, db2)
    Type        BackupType `json:"type"`                // 备份的具体类型 (归档, 链初始, 链增量)
    Path        string    `json:"path"`                    // 备份产物在文件系统中的绝对路径
    Description string    `json:"description,omitempty"` // 用户提供的描述

    // === 增量链专用字段 ===
    ChainID string `json:"chain_id,omitempty"`   // 标识此备份属于哪条增量链
    ParentID string `json:"parent_id,omitempty"` // 指向链中的上一个备份记录ID

    // === 状态和执行信息 ===
    Status BackupStatus `json:"status"`          // 备份的最终状态
    Error string `json:"error,omitempty"`        // 错误信息摘要（向后兼容）
    ErrorDetail *BackupError `json:"error_detail,omitempty"` // 详细的结构化错误信息
    Size int64 `json:"size,omitempty"`           // 备份产物的大小（字节）
    ExecutionTime time.Duration `json:"execution_time"` // 备份操作的执行时间

    // === MySQL专用字段 ===
    BinlogFile string `json:"binlog_file,omitempty"` // 全量备份结束时或增量备份的结束点的binlog文件名
    BinlogPos uint32 `json:"binlog_pos,omitempty"`   // 对应的binlog文件中的位置

    // === 扩展元数据 ===
    Extra string `json:"extra,omitempty"`        // 存储任意 provider-specific 的元数据 (e.g., ES snapshot name)
}

// ChainMeta 代表增量备份链的聚合元数据，存储在chain_meta.json中
type ChainMeta struct {
    ChainID   string   `json:"chain_id"`   // 链的唯一标识符
    BackupIDs []string `json:"backup_ids"` // 有序的备份记录ID列表，从全量到最后的增量
}

// IncrementalChain 代表一个完整的增量备份链，包含全量备份和所有后续增量备份
// 这是一个动态结构体，通常在显示或恢复逻辑中临时构建
type IncrementalChain struct {
    ChainID string          `json:"chain_id"` // 链的唯一ID
    Backups []*BackupRecord `json:"backups"`  // 链内所有备份记录，按时间顺序排列
}

// MySQLBackupMeta MySQL特定的备份元数据
type MySQLBackupMeta struct {
    BinlogFile     string `json:"binlog_file,omitempty"`     // binlog文件名
    BinlogPosition uint32 `json:"binlog_position,omitempty"` // binlog位置
    GTID           string `json:"gtid,omitempty"`            // GTID信息（如果启用）
    ServerID       uint32 `json:"server_id,omitempty"`       // MySQL服务器ID
}

// ESBackupMeta Elasticsearch特定的备份元数据
type ESBackupMeta struct {
    SnapshotName string   `json:"snapshot_name,omitempty"` // ES快照名称
    Repository   string   `json:"repository,omitempty"`    // 快照仓库名称
    Indices      []string `json:"indices,omitempty"`       // 包含的索引列表
    UUID         string   `json:"uuid,omitempty"`          // 快照UUID
    State        string   `json:"state,omitempty"`         // 快照状态
}

// === 企业级扩展数据结构 ===

// BinlogPosition MySQL Binlog位置信息（高精度）
type BinlogPosition struct {
    File     string `json:"file"`                // binlog文件名
    Position int64  `json:"position"`            // 位置偏移
    GTID     string `json:"gtid,omitempty"`      // GTID (如果启用)
    ServerID int64  `json:"server_id,omitempty"` // 服务器ID
}

// EncryptionInfo 加密信息
type EncryptionInfo struct {
    Enabled   bool   `json:"enabled"`             // 是否启用加密
    Algorithm string `json:"algorithm,omitempty"` // 加密算法 (AES-256, etc.)
    KeyID     string `json:"key_id,omitempty"`    // 密钥标识符
    IV        string `json:"iv,omitempty"`        // 初始化向量
    Salt      string `json:"salt,omitempty"`      // 盐值
}

// HealthCheckInfo 健康检查信息
type HealthCheckInfo struct {
    Enabled      bool      `json:"enabled"`                // 是否启用健康检查
    CheckTime    time.Time `json:"check_time,omitempty"`   // 检查时间
    Status       string    `json:"status,omitempty"`       // 检查状态 (healthy/warning/error)
    Message      string    `json:"message,omitempty"`      // 检查消息
    Checksum     string    `json:"checksum,omitempty"`     // 文件校验和
    Verified     bool      `json:"verified"`               // 是否已验证
}

// IncrementalInfo 增量备份信息
type IncrementalInfo struct {
    BaseBackupID   string    `json:"base_backup_id"`           // 基础备份ID
    StartPosition  string    `json:"start_position,omitempty"` // 开始位置
    EndPosition    string    `json:"end_position,omitempty"`   // 结束位置
    ChangedBlocks  int64     `json:"changed_blocks,omitempty"` // 变更的数据块数
    CompressionRatio float64 `json:"compression_ratio,omitempty"` // 压缩比
}
```

---

## 6. 数据模型与目录结构

**统一备份目录 (`BackupRoot`) 分层存储架构:**

```
/path/to/backup_root/
├── tasks.json                    // 异步任务状态持久化文件
├── mysql/                        // MySQL数据源根目录
│   ├── archival/                 // 归档备份目录（独立的全量备份）
│   │   └── 20240521100000_arch/
│   │       ├── data.sql.gz
│   │       └── metadata.json
│   ├── chains/                   // 增量链目录（受保护的增量备份链）
│   │   └── chain_20240521110000/
│   │       ├── chain_meta.json   // 增量链聚合元数据
│   │       ├── 20240521110000_initial/
│   │       │   ├── data.sql.gz
│   │       │   └── metadata.json
│   │       └── 20240521120000_inc/
│   │           ├── binlog.sql.gz
│   │           └── metadata.json
│   └── status.json               // 数据源锁定状态文件
└── elasticsearch/                // Elasticsearch数据源根目录
    ├── archival/                 // 归档备份目录
    ├── chains/                   // 增量链目录
    ├── status.json               // 数据源锁定状态文件
    └── .es_repo_cache/           // ES仓库元数据缓存（可选）
```

**存储架构特点:**
- **分层存储**: 归档备份和增量链完全分离，避免混淆和误操作
- **标准化路径**: 统一的路径管理方法，支持自动路径生成和验证
- **双级元数据**: 每个备份都有独立的metadata.json，增量链有聚合的chain_meta.json
- **状态跟踪**: 支持数据源级别的状态锁定，防止并发冲突和手动干预期间的自动操作
- **任务持久化**: 异步任务状态持久化到tasks.json，支持重启恢复和故障排查，采用深拷贝机制确保并发安全
- **存储抽象**: 通过 Backend 接口抽象存储层，支持本地存储和云存储。云存储模式下，`backup_root` 主要用于路径生成，实际数据存储在云端
- **Backend 接口**: 统一的存储抽象接口，支持 Put、Get、NewWriter、List、Delete、Exists、HealthCheck 等操作
- **多存储后端**: LocalBackend（本地文件系统）和 GocloudBackend（云存储），通过工厂模式创建
- **安全保障**: 多重安全检查，防止意外删除和路径注入攻击
- **事务性操作**: 元数据更新采用读取-修改-写回的原子操作，确保一致性
- **错误恢复**: 操作失败时自动回滚，保持存储状态的完整性
- **完全重建策略**: 恢复操作采用完全重建策略，确保数据一致性和清洁状态

**实际存储布局示例（使用ULID格式ID）:**
```
/backup_root/
├── tasks.json                           // 异步任务状态持久化
├── mysql/                               // MySQL数据源根目录
│   ├── archival/                        // 归档备份目录
│   │   ├── 01HF7XQZM8EXAMPLE1/
│   │   │   ├── metadata.json            // 备份元数据
│   │   │   └── data.sql.gz              // 备份数据文件
│   │   └── 01HF7XQZM8EXAMPLE2/
│   │       ├── metadata.json
│   │       └── data.sql.gz
│   ├── chains/                          // 增量链目录
│   │   ├── 01HF7XQZM8CHAIN001/          // 增量链目录（以链ID命名）
│   │   │   ├── chain_meta.json          // 链聚合元数据
│   │   │   ├── 01HF7XQZM8CHAIN001/      // 初始备份（ID与链ID相同）
│   │   │   │   ├── metadata.json
│   │   │   │   └── data.sql.gz
│   │   │   ├── 01HF7XQZM8INC001/        // 第一个增量备份
│   │   │   │   ├── metadata.json
│   │   │   │   └── binlog.gz
│   │   │   └── 01HF7XQZM8INC002/        // 第二个增量备份
│   │   │       ├── metadata.json
│   │   │       └── binlog.gz
│   │   └── 01HF7XQZM8CHAIN002/          // 另一条增量链
│   │       └── ...
│   └── status.json                      // 数据源状态文件
└── elasticsearch/                       // Elasticsearch数据源根目录
    ├── archival/                        // 归档备份目录
    ├── chains/                          // 增量链目录
    ├── status.json                      // 数据源状态文件
    └── .es_repo_cache/                  // ES仓库元数据缓存（可选）

---

## 6.1 企业级扩展数据结构

除了核心的备份记录和任务状态，SDK还定义了一些用于未来扩展或特定高级场景的数据结构。这些结构体目前可能未在所有核心流程中完全使用，但为SDK的未来功能和企业级特性预留了空间。

- **`BackupFilter`**: 用于备份列表的过滤条件，支持按备份类型、状态、时间范围和数量进行筛选，为更灵活的备份查询提供支持。
- **`BinlogPosition`**: 提供了MySQL Binlog的详细位置信息，包括文件名、位置偏移、GTID和服务器ID，为更精细的MySQL增量备份和恢复提供高精度控制。
- **`EncryptionInfo`**: 定义了备份加密相关的信息，包括是否启用加密、加密算法、密钥ID、初始化向量和盐值，为实现备份数据的安全存储预留了接口。
- **`HealthCheckInfo`**: 用于记录健康检查的详细信息，包括检查时间、状态、消息、校验和及验证结果，为未来更完善的系统健康监控和诊断提供数据基础。
- **`IncrementalInfo`**: 提供了增量备份的详细统计信息，如基础备份ID、起止位置、变更数据块数和压缩比，为增量备份的性能分析和优化提供数据支持.
```

**路径管理规范:**
- **数据源路径**: `{BackupRoot}/{source_type}`
- **归档备份路径**: `{BackupRoot}/{source_type}/archival/{backup_id}`
- **增量链路径**: `{BackupRoot}/{source_type}/chains/{chain_id}/{backup_id}`
- **元数据文件路径**: `{backup_path}/metadata.json`
- **链聚合元数据路径**: `{BackupRoot}/{source_type}/chains/{chain_id}/chain_meta.json`
- **数据源状态路径**: `{BackupRoot}/{source_type}/status.json`

## 7. 并发控制架构

**并发控制策略**：

- **全局信号量**：通过信号量（semaphore）限制系统总并发任务数，防止资源过载。最大并发数可配置。
- **细粒度Locker**：按数据源名称加锁，保证同一数据源同一时刻只有一个任务在执行，不同数据源可并发。

### 配置示例

```go
// 并发控制通过TaskManager的maxConcurrent参数配置
// 在NewManager函数中设置，默认值为5
maxConcurrent := 5 // 最大并发任务数
taskManager := task.NewManager(storageManager, providers, tasksFilePath, logger, maxConcurrent)
```

### 并发控制模式

- **全局信号量+细粒度Locker**：
  - 通过信号量控制最大并发任务数（默认5个）
  - 通过细粒度锁按数据源名称串行化同一数据源的操作
  - 不同数据源可并发执行

## 8. 错误处理和异常管理架构

### 8.1 结构化错误类型

- 当前实现支持结构化错误（如BackupError），包含错误代码、消息、组件、时间戳、上下文等。
- 所有核心流程均返回结构化错误，便于日志追踪和问题定位。

### 8.2 错误分类系统

- 错误类型按功能域分类（如连接、权限、存储、数据、备份、系统等），具体见types.go。
- 主要通过BackupError结构体和标准Go error链路进行分类和处理。

### 8.3 错误处理机制

- 所有错误均为结构化错误对象（BackupError），包含错误代码、组件、操作、时间戳等详细信息。
- panic会被捕获并转为结构化错误，所有关键错误均有详细日志。
- 支持错误链（Unwrap方法）和可重试性标识。

### 8.4 错误标准化与日志

- 所有模块均通过结构化日志输出错误，便于统一监控和排查。
- 关键错误均有详细上下文和组件信息，便于定位。

## 9. 核心工作流

### 场景A: 执行MySQL增量备份
1. 用户调用 `manager.Backup(ctx, "mysql", "incremental", "Regular 10-min log backup")`。
2. `BackupManager` 创建`Task`，获取全局锁，并异步调用 `MySQLProvider.Backup()`。
3. **`MySQLProvider.Backup()` 内部逻辑**:
    a. 因为 `backupType` 是 `incremental`，首先调用 `ListBackups()` 找到最新的一个备份记录（无论全量或增量），作为**父备份**。如果找不到任何备份，则返回错误。
    b. **增量备份父链选择逻辑**：
        *   系统会列出所有可用的增量链。
        *   根据链中最后一个备份的时间戳对这些链进行排序，选择最新的增量链。
        *   获取所选增量链的最新记录作为本次增量备份的父记录。
    c. 读取父备份的 `metadata.json`，获取上一次的binlog文件名和结束位置 (`end_pos`)。
    c. 连接到MySQL，执行 `FLUSH LOGS` 刷新日志文件，并查询 `SHOW MASTER STATUS` 获取当前最新的binlog文件名和位置，作为本次增量备份的**结束点**。
    d. 使用 `mysqlbinlog` 工具，从 `start_pos` (上次的结束点) 到 `end_pos` (本次的结束点) 导出增量日志。
    e. 将导出的日志压缩并存入本次备份的新目录中。
    f. 成功后，创建新的 `metadata.json`，记录 `parent_id`（指向父备份）、新的binlog结束点、`type: incremental`等信息。
4. `TaskManager` 更新任务状态为 `Completed`。

### 场景B: 灾难后恢复MySQL（从增量点）
1. 用户选择了一个增量备份点 `inc_backup_3`，调用 `manager.Restore(ctx, "mysql", "inc_backup_3")`。
2. `MySQLProvider.Restore()` 被调用。
3. **`Restore()` 内部逻辑**:
    a. 自动调用 `findRestoreChain()`，通过 `ParentID` 向上追溯，直到找到根部的全量备份。得到恢复链: `[full_backup, inc_backup_1, inc_backup_2, inc_backup_3]`。
    b. **按顺序**执行恢复：
        i.  使用 `gunzip | mysql` 恢复 `full_backup`。
        ii. 使用 `gunzip | mysql` 恢复 `inc_backup_1` 的binlog。
        iii.恢复 `inc_backup_2` 的binlog。
        iv. 恢复 `inc_backup_3` 的binlog。
4. `TaskManager` 更新任务状态。

### 场景C: 分组全量备份 (`BackupAll`)
1. 用户调用 `manager.BackupAll(ctx, "Pre-release v2.0 snapshot")`。
2. `BackupManager` **为每个** `Provider` 调用其 `Backup` 方法，并**强制传递 `backupType: "full"`**。
3. 后续的并发管理、失败回滚等逻辑保持不变。这确保了 `BackupAll` 始终创建的是一组可独立恢复的**全量**备份。

---

## 10. 备份生命周期管理

为了防止备份数据无限增长，SDK提供了手动清理的功能。

- **`DeleteBackup`**: 用于删除单个数据源的某次备份。**警告：删除备份时，用户有责任确保不破坏其他备份的依赖链（例如，不应删除一个被后续增量备份所依赖的全量备份），否则将导致后续所有相关的增量备份失效。**
- **`DeleteSet`**: 用于删除一个完整的备份集。此操作会读取备份集的清单文件，并依次删除其中包含的所有成员备份（MySQL数据和ES快照）。

未来的版本可以考虑引入基于策略的自动清理功能，例如 `Cleanup(policy RetentionPolicy)`，允许用户定义"保留最近7天的备份"或"保留最近5个备份集"这样的规则。

---

## 11. Go SDK 最佳实践遵循
- **清晰的API入口**: `BackupManager`是唯一的交互对象。
- **面向接口编程**: `BackupProvider`接口确保了核心逻辑与具体实现解耦，易于扩展。
- **上下文传递**: 所有耗时API都接受`context.Context`，支持超时和取消。
- **结构化配置**: `Config`结构体清晰地定义了所有配置项。
- **企业级错误处理**: 采用结构化错误管理系统，支持错误分类、聚合、重试和恢复机制。所有错误都包含详细的上下文信息和修复建议。
- **并发安全**: `TaskManager`使用`sync.Map`或带锁的`map`来安全地管理任务状态。
- **零依赖（核心逻辑）**: SDK的核心调度逻辑不依赖任何第三方库。
- **详细的文档**: 所有公开的类型、函数和方法都将附有标准的GoDoc注释。 

### Provider.Delete 设计说明
- 对于 MySQL Provider，Delete 方法为 no-op（无操作），所有物理文件和元数据的删除由 StorageManager 统一管理。
- 仅在如 Elasticsearch 等外部系统需要 API 删除时，Provider.Delete 才有实际操作。

### 分组操作并发/串行策略
- BackupAll/RestoreAll 支持并发与串行两种执行策略，默认串行更安全。
- 用户可通过参数 Parallel 控制是否并发执行。
- Atomic=true 时强制串行，保证原子性；Atomic=false 时可并发。

### 异常恢复与自动修复设计
- 任务持久化文件（tasks.json）损坏时，系统应自动备份原文件（如重命名为 tasks.json.bak），并尝试重建空任务列表，允许服务继续启动，同时在日志中高亮提示管理员手动检查。
- Provider 未注册或不可用时，系统应输出明确的错误信息，提示用户检查配置或依赖。
- 存储异常等其他关键错误场景，建议统一输出结构化错误，并在文档中提供用户指引和排查建议。
- 后续可考虑任务文件增量写入、分片或嵌入式数据库等优化方案，以提升极大任务量下的性能和可靠性。

### 任务持久化文件损坏的自动修复机制
- 当 tasks.json 文件损坏时，系统会自动将原文件重命名为 tasks.json.bak 并重建空任务列表，允许服务继续启动，并在日志中高亮提示管理员手动检查和恢复。

### 极大任务量下的性能优化建议
- 当前任务持久化采用全量写入，极大任务量下建议后续考虑增量写入、任务分片或采用嵌入式数据库（如 bbolt）等优化方案，以提升性能和可靠性。
``` 