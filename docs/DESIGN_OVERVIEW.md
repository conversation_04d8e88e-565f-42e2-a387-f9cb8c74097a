# UniBackup 设计概览

本文档提供 UniBackup 项目的完整设计概览，包括架构设计、实现方案和技术决策。

## 📋 目录

- [项目概述](#项目概述)
- [架构设计](#架构设计)
- [实现设计](#实现设计)
- [存储抽象设计](#存储抽象设计)

---

# 项目概述

## 🎯 项目定位

`UniBackup` 是一个提供统一容灾（Disaster Recovery）能力的企业级 Go 语言 SDK。它采用现代化的接口设计和异步任务模式，为开发者提供对多种数据源（MySQL 和 Elasticsearch）进行高效、可靠的备份与恢复操作。

## 🔑 核心特性

- **接口驱动设计**: 遵循 Go 最佳实践，便于测试和扩展
- **双模式操作**: 支持异步（推荐）和同步两种操作模式
- **智能恢复**: 自动识别备份类型并构建最优恢复策略，采用完全重建策略确保数据一致性
- **并发控制**: 采用全局信号量限制最大并发任务数，并结合细粒度 Locker 实现数据源级别互斥
- **完整任务管理**: 支持任务取消、进度跟踪、状态持久化和清理，具备深拷贝机制防止并发修改
- **强扩展性**: 模块化架构设计，易于添加新的数据源支持
- **原子性保证**: 分组操作支持原子性，包括预恢复快照和失败回滚机制
- **云存储支持**: 支持 AWS S3、Google Cloud Storage、Azure Blob Storage，提供企业级云备份能力

## 📊 需求分析

### 功能性需求

- **FR1: 统一API**: 提供单一、简洁的API入口，用于管理不同数据源的备份与恢复
- **FR2: 数据源支持**: 支持 MySQL 和 Elasticsearch
- **FR3: 备份类型与策略**:
  - **归档全量备份**: 创建完全独立的、自包含的全量数据快照
  - **受保护的增量链**: 创建由"初始全量备份 + N个后续增量备份"组成的严格恢复链
- **FR4: 核心容灾能力**: 系统必须能在原始环境完全损坏的情况下，仅通过备份目录就能完成所有数据恢复
- **FR5: 易用性与自动化**: 操作需非阻塞，支持任务状态查询，并能自动处理复杂的恢复流程

### 非功能性需求

- **NFR1: 性能**: 支持大规模数据备份，优化内存和磁盘使用
- **NFR2: 可靠性**: 提供完整的错误处理和重试机制
- **NFR3: 可扩展性**: 模块化设计，易于添加新的数据源
- **NFR4: 可维护性**: 清晰的代码结构和完整的文档
- **NFR5: 安全性**: 支持加密传输和存储

## 🎯 设计目标

1. **统一性**: 为不同数据源提供一致的操作接口
2. **可靠性**: 确保备份和恢复操作的数据完整性
3. **高性能**: 优化大规模数据处理能力
4. **易用性**: 简化复杂的备份恢复流程
5. **可扩展性**: 支持未来新数据源的快速集成

---

# 架构设计

## 🏗️ 整体架构

UniBackup 采用分层架构设计，从上到下分为：

```
┌─────────────────────────────────────────┐
│              用户应用层                    │
├─────────────────────────────────────────┤
│              SDK 接口层                   │
│  ┌─────────────────────────────────────┐ │
│  │         BackupManager              │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│              业务逻辑层                    │
│  ┌──────────┐  ┌──────────┐  ┌────────┐ │
│  │TaskManager│  │Scheduler │  │Locker  │ │
│  └──────────┘  └──────────┘  └────────┘ │
├─────────────────────────────────────────┤
│              数据源适配层                  │
│  ┌──────────┐  ┌──────────┐             │
│  │MySQL     │  │Elasticsearch          │
│  │Provider  │  │Provider  │             │
│  └──────────┘  └──────────┘             │
├─────────────────────────────────────────┤
│              存储抽象层                    │
│  ┌─────────────────────────────────────┐ │
│  │         StorageManager             │ │
│  │  ┌─────────────┐  ┌──────────────┐ │ │
│  │  │LocalBackend │  │CloudBackend  │ │ │
│  │  └─────────────┘  └──────────────┘ │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│              基础设施层                    │
│  ┌──────────┐  ┌──────────┐  ┌────────┐ │
│  │本地文件系统│  │云存储服务  │  │数据库   │ │
│  └──────────┘  └──────────┘  └────────┘ │
└─────────────────────────────────────────┘
```

## 🔧 核心组件

### BackupManager

**职责**: 提供统一的备份管理接口

```go
type BackupManager interface {
    // 异步操作（推荐）
    BackupAsync(ctx context.Context, sourceType SourceType, sourceName string, backupType BackupType, description string) (string, error)
    RestoreAsync(ctx context.Context, config *RestoreConfig) (string, error)
    BackupAllAsync(ctx context.Context, request *BackupAllRequest) (string, error)
    RestoreAllAsync(ctx context.Context, config *RestoreAllConfig) (string, error)
    
    // 任务管理
    GetTask(taskID string) (*Task, error)
    ListTasks(ctx context.Context, filter TaskFilter) ([]*Task, error)
    CancelTask(taskID string) error
    
    // 备份管理（已废弃，使用统一查询接口）
    // ListArchivalBackups(ctx context.Context, sourceType SourceType) ([]*BackupRecord, error) - 已废弃，使用ListAllBackups
    // ListChainBackups(ctx context.Context, sourceType SourceType) ([]*ChainInfo, error) - 已废弃，使用ListAllBackups
    // DeleteBackup(ctx context.Context, sourceType SourceType, backupID string) error - 已废弃，使用DeleteBackupByTaskID
    
    // 统一查询接口
    ListAllBackups(ctx context.Context, filter BackupFilter) (*BackupQueryResult, error)
    
    // 生命周期管理
    Shutdown() error
}
```

### TaskManager

**职责**: 管理异步任务的生命周期

- **任务调度**: 控制并发任务数量
- **状态管理**: 跟踪任务执行状态
- **持久化**: 任务状态持久化存储
- **清理机制**: 自动清理过期任务

### Provider 接口

**职责**: 定义数据源操作的标准接口

```go
type Provider interface {
    // 归档备份
    CreateArchivalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error)
    
    // 增量链备份
    CreateChainInitialBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error)
    CreateChainIncrementalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error)
    
    // 恢复操作
    RestoreArchivalBackup(ctx context.Context, backupID string, config *RestoreConfig) error
    RestoreChainBackup(ctx context.Context, chainID string, pointInTime *time.Time, config *RestoreConfig) error
    
    // 查询操作（统一接口）
    ListAllBackups(ctx context.Context, filter BackupFilter) (*BackupQueryResult, error)
    
    // 管理操作
    DeleteBackupByTaskID(ctx context.Context, taskID string) error
    ValidateBackup(ctx context.Context, backupID string) error
    
    // 生命周期
    Initialize(ctx context.Context) error
    Shutdown() error
}
```

### StorageManager

**职责**: 提供统一的存储抽象

- **存储抽象**: 统一本地和云存储操作
- **元数据管理**: 管理备份元数据
- **完整性检查**: 验证备份文件完整性
- **生命周期管理**: 管理备份文件的生命周期

## 🔄 数据流设计

### 备份流程

```mermaid
sequenceDiagram
    participant User
    participant BackupManager
    participant TaskManager
    participant Provider
    participant StorageManager
    participant Backend

    User->>BackupManager: BackupAsync()
    BackupManager->>TaskManager: CreateTask()
    TaskManager-->>BackupManager: TaskID
    BackupManager-->>User: TaskID
    
    TaskManager->>Provider: CreateBackup()
    Provider->>StorageManager: GetBackupWriter()
    StorageManager->>Backend: NewWriter()
    Backend-->>StorageManager: Writer
    StorageManager-->>Provider: Writer
    
    Provider->>Provider: ExecuteBackup()
    Provider->>StorageManager: SaveMetadata()
    StorageManager->>Backend: Put()
    
    Provider-->>TaskManager: BackupResult
    TaskManager->>TaskManager: UpdateTaskStatus()
```

### 恢复流程

```mermaid
sequenceDiagram
    participant User
    participant BackupManager
    participant TaskManager
    participant Provider
    participant StorageManager
    participant Backend

    User->>BackupManager: RestoreAsync()
    BackupManager->>TaskManager: CreateTask()
    TaskManager-->>BackupManager: TaskID
    BackupManager-->>User: TaskID
    
    TaskManager->>Provider: RestoreBackup()
    Provider->>StorageManager: GetBackupReader()
    StorageManager->>Backend: NewReader()
    Backend-->>StorageManager: Reader
    StorageManager-->>Provider: Reader
    
    Provider->>Provider: ExecuteRestore()
    Provider-->>TaskManager: RestoreResult
    TaskManager->>TaskManager: UpdateTaskStatus()
```

## 🔐 并发控制设计

### 全局并发控制

```go
type ConcurrencyController struct {
    semaphore chan struct{}
    maxTasks  int
}

func (c *ConcurrencyController) Acquire() {
    c.semaphore <- struct{}{}
}

func (c *ConcurrencyController) Release() {
    <-c.semaphore
}
```

### 数据源级别互斥

```go
type SourceLocker struct {
    locks map[string]*sync.Mutex
    mutex sync.RWMutex
}

func (l *SourceLocker) Lock(sourceKey string) {
    l.mutex.Lock()
    if _, exists := l.locks[sourceKey]; !exists {
        l.locks[sourceKey] = &sync.Mutex{}
    }
    lock := l.locks[sourceKey]
    l.mutex.Unlock()
    
    lock.Lock()
}
```

## 📊 状态管理设计

### 任务状态机

```
┌─────────┐    ┌─────────┐    ┌───────────┐
│ Pending │───▶│ Running │───▶│ Completed │
└─────────┘    └─────────┘    └───────────┘
                    │              ▲
                    ▼              │
               ┌─────────┐    ┌─────────┐
               │ Failed  │    │Cancelled│
               └─────────┘    └─────────┘
```

### 状态持久化

```go
type TaskPersistence struct {
    filePath string
    mutex    sync.RWMutex
}

func (p *TaskPersistence) SaveTask(task *Task) error {
    p.mutex.Lock()
    defer p.mutex.Unlock()
    
    // 读取现有任务
    tasks, err := p.loadTasks()
    if err != nil {
        return err
    }
    
    // 更新或添加任务
    tasks[task.ID] = task
    
    // 保存到文件
    return p.saveTasks(tasks)
}
```

---

# 实现设计

## 🔧 MySQL Provider 实现

### 备份实现

#### 归档全量备份

```go
func (p *MySQLProvider) CreateArchivalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error) {
    // 1. 创建备份记录
    record := &BackupRecord{
        ID:          generateBackupID(),
        SourceType:  MySQL,
        SourceName:  sourceName,
        BackupType:  BackupTypeArchival,
        Description: description,
        CreatedAt:   time.Now(),
    }
    
    // 2. 获取存储写入器
    writer, err := p.storageManager.GetBackupWriter(record, "data.sql.gz")
    if err != nil {
        return nil, err
    }
    defer writer.Close()
    
    // 3. 执行 mysqldump
    cmd := exec.CommandContext(ctx, "mysqldump",
        "--host", p.config.Host,
        "--port", strconv.Itoa(p.config.Port),
        "--user", p.config.User,
        "--password", p.config.Password,
        "--single-transaction",
        "--routines",
        "--triggers",
        sourceName,
    )
    
    // 4. 压缩并写入存储
    gzWriter := gzip.NewWriter(writer)
    defer gzWriter.Close()
    
    cmd.Stdout = gzWriter
    
    if err := cmd.Run(); err != nil {
        return nil, fmt.Errorf("mysqldump failed: %w", err)
    }
    
    // 5. 保存元数据
    if err := p.storageManager.SaveBackupMetadata(record); err != nil {
        return nil, err
    }
    
    return record, nil
}
```

#### 增量链备份

```go
func (p *MySQLProvider) CreateChainIncrementalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error) {
    // 1. 获取最新的链信息
    chain, err := p.getLatestChain(sourceName)
    if err != nil {
        return nil, err
    }
    
    // 2. 获取起始 binlog 位置
    startPos, err := p.getLastBinlogPosition(chain.ID)
    if err != nil {
        return nil, err
    }
    
    // 3. 获取当前 binlog 位置
    currentPos, err := p.getCurrentBinlogPosition()
    if err != nil {
        return nil, err
    }
    
    // 4. 导出增量 binlog
    record := &BackupRecord{
        ID:          generateBackupID(),
        SourceType:  MySQL,
        SourceName:  sourceName,
        BackupType:  BackupTypeChainIncremental,
        Description: description,
        CreatedAt:   time.Now(),
        ChainID:     chain.ID,
    }
    
    writer, err := p.storageManager.GetBackupWriter(record, "incremental.binlog")
    if err != nil {
        return nil, err
    }
    defer writer.Close()
    
    // 5. 使用 mysqlbinlog 导出
    cmd := exec.CommandContext(ctx, "mysqlbinlog",
        "--read-from-remote-server",
        "--host", p.config.Host,
        "--port", strconv.Itoa(p.config.Port),
        "--user", p.config.User,
        "--password", p.config.Password,
        "--start-position", strconv.FormatUint(startPos.Position, 10),
        "--stop-position", strconv.FormatUint(currentPos.Position, 10),
        startPos.File,
    )
    
    cmd.Stdout = writer
    
    if err := cmd.Run(); err != nil {
        return nil, fmt.Errorf("mysqlbinlog failed: %w", err)
    }
    
    // 6. 更新链信息
    chain.LastBackupID = record.ID
    chain.LastPosition = currentPos
    if err := p.storageManager.SaveChainMetadata(chain); err != nil {
        return nil, err
    }
    
    // 7. 保存备份元数据
    if err := p.storageManager.SaveBackupMetadata(record); err != nil {
        return nil, err
    }
    
    return record, nil
}
```

### 恢复实现

#### 智能恢复策略

```go
func (p *MySQLProvider) RestoreBackup(ctx context.Context, config *RestoreConfig) error {
    if config.BackupID != "" {
        // 归档备份恢复
        return p.restoreArchivalBackup(ctx, config)
    } else if config.ChainID != "" {
        // 增量链恢复
        return p.restoreChainBackup(ctx, config)
    }
    
    return fmt.Errorf("invalid restore config")
}

func (p *MySQLProvider) restoreArchivalBackup(ctx context.Context, config *RestoreConfig) error {
    // 1. 获取备份文件
    reader, err := p.storageManager.GetBackupReader(config.BackupID, "data.sql.gz")
    if err != nil {
        return err
    }
    defer reader.Close()
    
    // 2. 解压缩
    gzReader, err := gzip.NewReader(reader)
    if err != nil {
        return err
    }
    defer gzReader.Close()
    
    // 3. 执行恢复
    cmd := exec.CommandContext(ctx, "mysql",
        "--host", p.config.Host,
        "--port", strconv.Itoa(p.config.Port),
        "--user", p.config.User,
        "--password", p.config.Password,
        config.TargetName,
    )
    
    cmd.Stdin = gzReader
    
    return cmd.Run()
}
```

## 🔍 Elasticsearch Provider 实现

### 快照仓库管理

```go
func (p *ESProvider) ensureRepositories(ctx context.Context) error {
    if p.config.AutoCreateRepos {
        // 创建归档仓库
        if err := p.createRepository(ctx, p.config.ArchivalRepoName, "archival"); err != nil {
            return err
        }
        
        // 创建管理仓库
        if err := p.createRepository(ctx, p.config.ManagedRepoName, "managed"); err != nil {
            return err
        }
    }
    
    return nil
}

func (p *ESProvider) createRepository(ctx context.Context, repoName, repoType string) error {
    settings := map[string]interface{}{
        "type": "fs",
        "settings": map[string]interface{}{
            "location": filepath.Join(p.config.RepoBasePath, repoName),
            "compress": true,
        },
    }
    
    // 如果启用云存储，使用云存储仓库
    if p.cloudConfig != nil && p.cloudConfig.Enabled {
        settings = p.buildCloudRepositorySettings(repoName, repoType)
    }
    
    req := esapi.SnapshotCreateRepositoryRequest{
        Repository: repoName,
        Body:       esutil.NewJSONReader(settings),
    }
    
    res, err := req.Do(ctx, p.client)
    if err != nil {
        return err
    }
    defer res.Body.Close()
    
    if res.IsError() {
        return fmt.Errorf("failed to create repository: %s", res.String())
    }
    
    return nil
}
```

### 快照操作

```go
func (p *ESProvider) CreateArchivalBackup(ctx context.Context, sourceName string, description string) (*BackupRecord, error) {
    record := &BackupRecord{
        ID:          generateBackupID(),
        SourceType:  Elasticsearch,
        SourceName:  sourceName,
        BackupType:  BackupTypeArchival,
        Description: description,
        CreatedAt:   time.Now(),
    }
    
    // 创建快照
    snapshotName := fmt.Sprintf("snapshot-%s", record.ID)
    
    req := esapi.SnapshotCreateRequest{
        Repository: p.config.ArchivalRepoName,
        Snapshot:   snapshotName,
        Body: esutil.NewJSONReader(map[string]interface{}{
            "indices":              "*",
            "ignore_unavailable":   true,
            "include_global_state": true,
            "metadata": map[string]interface{}{
                "description": description,
                "source":      sourceName,
                "backup_id":   record.ID,
            },
        }),
    }
    
    res, err := req.Do(ctx, p.client)
    if err != nil {
        return nil, err
    }
    defer res.Body.Close()
    
    if res.IsError() {
        return nil, fmt.Errorf("failed to create snapshot: %s", res.String())
    }
    
    // 等待快照完成
    if err := p.waitForSnapshotCompletion(ctx, p.config.ArchivalRepoName, snapshotName); err != nil {
        return nil, err
    }
    
    // 保存元数据
    if err := p.storageManager.SaveBackupMetadata(record); err != nil {
        return nil, err
    }
    
    return record, nil
}
```

## 📦 任务管理实现

### 任务调度器

```go
type TaskScheduler struct {
    concurrencyController *ConcurrencyController
    sourceLocker         *SourceLocker
    taskManager          *TaskManager
    providers            map[SourceType]Provider
}

func (s *TaskScheduler) ScheduleBackup(ctx context.Context, request *BackupRequest) (string, error) {
    // 1. 创建任务
    task := &Task{
        ID:        generateTaskID(),
        Type:      TaskTypeBackup,
        Status:    TaskStatusPending,
        CreatedAt: time.Now(),
        Metadata: map[string]interface{}{
            "source_type": request.SourceType,
            "source_name": request.SourceName,
            "backup_type": request.BackupType,
        },
    }
    
    // 2. 保存任务
    if err := s.taskManager.SaveTask(task); err != nil {
        return "", err
    }
    
    // 3. 异步执行
    go s.executeBackupTask(ctx, task, request)
    
    return task.ID, nil
}

func (s *TaskScheduler) executeBackupTask(ctx context.Context, task *Task, request *BackupRequest) {
    // 1. 获取并发控制
    s.concurrencyController.Acquire()
    defer s.concurrencyController.Release()
    
    // 2. 获取数据源锁
    sourceKey := fmt.Sprintf("%s:%s", request.SourceType, request.SourceName)
    s.sourceLocker.Lock(sourceKey)
    defer s.sourceLocker.Unlock(sourceKey)
    
    // 3. 更新任务状态
    task.Status = TaskStatusRunning
    task.StartedAt = &time.Now()
    s.taskManager.SaveTask(task)
    
    // 4. 执行备份
    provider := s.providers[request.SourceType]
    var record *BackupRecord
    var err error
    
    switch request.BackupType {
    case BackupTypeArchival:
        record, err = provider.CreateArchivalBackup(ctx, request.SourceName, request.Description)
    case BackupTypeChainInitial:
        record, err = provider.CreateChainInitialBackup(ctx, request.SourceName, request.Description)
    case BackupTypeChainIncremental:
        record, err = provider.CreateChainIncrementalBackup(ctx, request.SourceName, request.Description)
    }
    
    // 5. 更新任务结果
    completedAt := time.Now()
    task.CompletedAt = &completedAt
    
    if err != nil {
        task.Status = TaskStatusFailed
        task.Error = err.Error()
    } else {
        task.Status = TaskStatusCompleted
        task.BackupID = record.ID
    }
    
    s.taskManager.SaveTask(task)
}
```

---

# 存储抽象设计

## 🏗️ 存储架构概览

UniBackup 采用分层存储架构，通过 StorageManager 和 Backend 接口实现存储抽象，支持本地文件系统和多种云存储服务。

### 架构层次

```
UniBackup 应用层
    ↓
StorageManager (存储管理器)
    ↓
Backend 接口 (存储抽象)
    ↓
LocalBackend / GocloudBackend (具体实现)
    ↓
本地文件系统 / 云存储服务
```

## 🔧 核心接口设计

### Backend 接口

```go
// Backend 定义了统一的存储操作接口
type Backend interface {
    // 基础操作
    Put(ctx context.Context, key string, data io.Reader) error
    Get(ctx context.Context, key string) (io.ReadCloser, error)
    Delete(ctx context.Context, key string) error
    List(ctx context.Context, prefix string) ([]string, error)
    Exists(ctx context.Context, key string) (bool, error)
    
    // 流式操作
    NewWriter(ctx context.Context, key string) (io.WriteCloser, error)
    NewReader(ctx context.Context, key string) (io.ReadCloser, error)
    
    // 元数据操作
    GetMetadata(ctx context.Context, key string) (*ObjectMetadata, error)
    SetMetadata(ctx context.Context, key string, metadata *ObjectMetadata) error
    
    // 健康检查
    HealthCheck(ctx context.Context) error
    
    // 生命周期
    Close() error
}

// ObjectMetadata 对象元数据
type ObjectMetadata struct {
    Size         int64             `json:"size"`
    LastModified time.Time         `json:"last_modified"`
    ContentType  string            `json:"content_type"`
    ETag         string            `json:"etag"`
    Custom       map[string]string `json:"custom"`
}
```

### StorageManager 接口

```go
// StorageManager 提供高级存储管理功能
type StorageManager interface {
    // 备份文件操作
    GetBackupWriter(record *BackupRecord, filename string) (io.WriteCloser, error)
    GetBackupReader(backupID string, filename string) (io.ReadCloser, error)
    DeleteBackup(backupID string) error
    
    // 元数据管理
    SaveBackupMetadata(record *BackupRecord) error
    LoadBackupMetadata(backupID string) (*BackupRecord, error)
    ListBackupMetadata(filter *BackupFilter) ([]*BackupRecord, error)
    
    // 链管理
    SaveChainMetadata(chain *ChainInfo) error
    LoadChainMetadata(chainID string) (*ChainInfo, error)
    ListChainMetadata(sourceType SourceType) ([]*ChainInfo, error)
    
    // 完整性检查
    ValidateBackup(backupID string) error
    
    // 统计信息
    GetStorageStats() (*StorageStats, error)
    
    // 生命周期
    Initialize(ctx context.Context) error
    Shutdown() error
}
```

## 💾 本地存储实现

### LocalBackend 实现

```go
type LocalBackend struct {
    basePath string
    mutex    sync.RWMutex
}

func NewLocalBackend(basePath string) (*LocalBackend, error) {
    if err := os.MkdirAll(basePath, 0755); err != nil {
        return nil, fmt.Errorf("failed to create base directory: %w", err)
    }
    
    return &LocalBackend{
        basePath: basePath,
    }, nil
}

func (b *LocalBackend) Put(ctx context.Context, key string, data io.Reader) error {
    filePath := filepath.Join(b.basePath, key)
    
    // 确保目录存在
    if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
        return fmt.Errorf("failed to create directory: %w", err)
    }
    
    // 创建临时文件
    tempFile := filePath + ".tmp"
    file, err := os.Create(tempFile)
    if err != nil {
        return fmt.Errorf("failed to create temp file: %w", err)
    }
    defer file.Close()
    
    // 写入数据
    if _, err := io.Copy(file, data); err != nil {
        os.Remove(tempFile)
        return fmt.Errorf("failed to write data: %w", err)
    }
    
    // 原子性重命名
    if err := os.Rename(tempFile, filePath); err != nil {
        os.Remove(tempFile)
        return fmt.Errorf("failed to rename file: %w", err)
    }
    
    return nil
}

func (b *LocalBackend) Get(ctx context.Context, key string) (io.ReadCloser, error) {
    filePath := filepath.Join(b.basePath, key)
    
    file, err := os.Open(filePath)
    if err != nil {
        if os.IsNotExist(err) {
            return nil, ErrObjectNotFound
        }
        return nil, fmt.Errorf("failed to open file: %w", err)
    }
    
    return file, nil
}

func (b *LocalBackend) NewWriter(ctx context.Context, key string) (io.WriteCloser, error) {
    filePath := filepath.Join(b.basePath, key)
    
    // 确保目录存在
    if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
        return nil, fmt.Errorf("failed to create directory: %w", err)
    }
    
    file, err := os.Create(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to create file: %w", err)
    }
    
    return file, nil
}
```

## ☁️ 云存储实现

### GocloudBackend 实现

```go
type GocloudBackend struct {
    bucket *blob.Bucket
    config *CloudStorageConfig
}

func NewGocloudBackend(config *CloudStorageConfig) (*GocloudBackend, error) {
    ctx := context.Background()
    var bucket *blob.Bucket
    var err error
    
    switch config.Type {
    case "s3":
        bucket, err = s3blob.OpenBucket(ctx, sess, config.Bucket, nil)
    case "gcs":
        bucket, err = gcsblob.OpenBucket(ctx, client, config.Bucket, nil)
    case "azure":
        bucket, err = azureblob.OpenBucket(ctx, pipeline, config.Container, nil)
    default:
        return nil, fmt.Errorf("unsupported storage type: %s", config.Type)
    }
    
    if err != nil {
        return nil, fmt.Errorf("failed to open bucket: %w", err)
    }
    
    return &GocloudBackend{
        bucket: bucket,
        config: config,
    }, nil
}

func (b *GocloudBackend) Put(ctx context.Context, key string, data io.Reader) error {
    writer, err := b.bucket.NewWriter(ctx, key, nil)
    if err != nil {
        return fmt.Errorf("failed to create writer: %w", err)
    }
    defer writer.Close()
    
    if _, err := io.Copy(writer, data); err != nil {
        return fmt.Errorf("failed to write data: %w", err)
    }
    
    return nil
}

func (b *GocloudBackend) Get(ctx context.Context, key string) (io.ReadCloser, error) {
    reader, err := b.bucket.NewReader(ctx, key, nil)
    if err != nil {
        if gcerrors.Code(err) == gcerrors.NotFound {
            return nil, ErrObjectNotFound
        }
        return nil, fmt.Errorf("failed to create reader: %w", err)
    }
    
    return reader, nil
}

func (b *GocloudBackend) NewWriter(ctx context.Context, key string) (io.WriteCloser, error) {
    writer, err := b.bucket.NewWriter(ctx, key, &blob.WriterOptions{
        ContentType: "application/octet-stream",
        Metadata: map[string]string{
            "created_by": "unibackup",
            "created_at": time.Now().Format(time.RFC3339),
        },
    })
    if err != nil {
        return nil, fmt.Errorf("failed to create writer: %w", err)
    }
    
    return writer, nil
}
```

## 📁 目录结构设计

### 存储目录布局

```
{BackupRoot}/
├── tasks.json                    # 任务状态持久化
├── mysql/                        # MySQL 备份
│   ├── archival/                 # 归档备份
│   │   └── {backup_id}/
│   │       ├── metadata.json
│   │       └── data.sql.gz
│   ├── chains/                   # 增量链
│   │   └── {chain_id}/
│   │       ├── chain_meta.json
│   │       ├── initial/          # 初始全量备份
│   │       │   ├── metadata.json
│   │       │   └── data.sql.gz
│   │       └── incremental/      # 增量备份
│   │           └── {backup_id}/
│   │               ├── metadata.json
│   │               └── incremental.binlog
│   └── status.json               # 数据源状态
└── elasticsearch/                # ES 备份
    ├── archival/
    │   └── {backup_id}/
    │       └── metadata.json
    ├── chains/
    │   └── {chain_id}/
    │       ├── chain_meta.json
    │       └── snapshots/
    │           └── {backup_id}/
    │               └── metadata.json
    └── status.json
```

### 路径生成策略

```go
type PathGenerator struct {
    backupRoot string
}

func (g *PathGenerator) GetBackupPath(record *BackupRecord, filename string) string {
    switch record.BackupType {
    case BackupTypeArchival:
        return filepath.Join(g.backupRoot, 
            strings.ToLower(string(record.SourceType)),
            "archival",
            record.ID,
            filename)
    case BackupTypeChainInitial:
        return filepath.Join(g.backupRoot,
            strings.ToLower(string(record.SourceType)),
            "chains",
            record.ChainID,
            "initial",
            filename)
    case BackupTypeChainIncremental:
        return filepath.Join(g.backupRoot,
            strings.ToLower(string(record.SourceType)),
            "chains",
            record.ChainID,
            "incremental",
            record.ID,
            filename)
    default:
        return ""
    }
}

func (g *PathGenerator) GetMetadataPath(record *BackupRecord) string {
    return g.GetBackupPath(record, "metadata.json")
}

func (g *PathGenerator) GetChainMetadataPath(sourceType SourceType, chainID string) string {
    return filepath.Join(g.backupRoot,
        strings.ToLower(string(sourceType)),
        "chains",
        chainID,
        "chain_meta.json")
}
```

## 🔄 数据一致性保证

### 原子性操作

```go
type AtomicOperation struct {
    backend Backend
    tempKeys []string
    finalKeys []string
}

func (op *AtomicOperation) AddOperation(tempKey, finalKey string) {
    op.tempKeys = append(op.tempKeys, tempKey)
    op.finalKeys = append(op.finalKeys, finalKey)
}

func (op *AtomicOperation) Commit(ctx context.Context) error {
    // 1. 验证所有临时文件存在
    for _, tempKey := range op.tempKeys {
        exists, err := op.backend.Exists(ctx, tempKey)
        if err != nil {
            return err
        }
        if !exists {
            return fmt.Errorf("temp file not found: %s", tempKey)
        }
    }
    
    // 2. 原子性重命名
    for i, tempKey := range op.tempKeys {
        finalKey := op.finalKeys[i]
        
        // 读取临时文件
        reader, err := op.backend.Get(ctx, tempKey)
        if err != nil {
            return err
        }
        
        // 写入最终位置
        if err := op.backend.Put(ctx, finalKey, reader); err != nil {
            reader.Close()
            return err
        }
        reader.Close()
        
        // 删除临时文件
        op.backend.Delete(ctx, tempKey)
    }
    
    return nil
}

func (op *AtomicOperation) Rollback(ctx context.Context) error {
    // 清理所有临时文件
    for _, tempKey := range op.tempKeys {
        op.backend.Delete(ctx, tempKey)
    }
    return nil
}
```

### 完整性检查

```go
type IntegrityChecker struct {
    backend Backend
}

func (c *IntegrityChecker) ValidateBackup(ctx context.Context, backupID string) error {
    // 1. 检查元数据文件
    metadataKey := fmt.Sprintf("metadata/%s.json", backupID)
    exists, err := c.backend.Exists(ctx, metadataKey)
    if err != nil {
        return err
    }
    if !exists {
        return fmt.Errorf("metadata file not found for backup: %s", backupID)
    }
    
    // 2. 读取元数据
    reader, err := c.backend.Get(ctx, metadataKey)
    if err != nil {
        return err
    }
    defer reader.Close()
    
    var metadata BackupRecord
    if err := json.NewDecoder(reader).Decode(&metadata); err != nil {
        return fmt.Errorf("failed to decode metadata: %w", err)
    }
    
    // 3. 验证数据文件
    for _, file := range metadata.Files {
        exists, err := c.backend.Exists(ctx, file.Key)
        if err != nil {
            return err
        }
        if !exists {
            return fmt.Errorf("data file not found: %s", file.Key)
        }
        
        // 验证文件大小
        objMetadata, err := c.backend.GetMetadata(ctx, file.Key)
        if err != nil {
            return err
        }
        
        if objMetadata.Size != file.Size {
            return fmt.Errorf("file size mismatch for %s: expected %d, got %d", 
                file.Key, file.Size, objMetadata.Size)
        }
    }
    
    return nil
}
```

## 📊 性能优化

### 并发上传

```go
type ConcurrentUploader struct {
    backend     Backend
    concurrency int
    chunkSize   int64
}

func (u *ConcurrentUploader) Upload(ctx context.Context, key string, data io.Reader, size int64) error {
    if size <= u.chunkSize {
        // 小文件直接上传
        return u.backend.Put(ctx, key, data)
    }
    
    // 大文件分块上传
    return u.uploadInChunks(ctx, key, data, size)
}

func (u *ConcurrentUploader) uploadInChunks(ctx context.Context, key string, data io.Reader, size int64) error {
    chunks := int(math.Ceil(float64(size) / float64(u.chunkSize)))
    
    // 创建工作池
    semaphore := make(chan struct{}, u.concurrency)
    errChan := make(chan error, chunks)
    
    for i := 0; i < chunks; i++ {
        go func(chunkIndex int) {
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            // 上传分块
            chunkKey := fmt.Sprintf("%s.chunk.%d", key, chunkIndex)
            chunkData := io.LimitReader(data, u.chunkSize)
            
            if err := u.backend.Put(ctx, chunkKey, chunkData); err != nil {
                errChan <- err
                return
            }
            
            errChan <- nil
        }(i)
    }
    
    // 等待所有分块完成
    for i := 0; i < chunks; i++ {
        if err := <-errChan; err != nil {
            return err
        }
    }
    
    // 合并分块
    return u.mergeChunks(ctx, key, chunks)
}
```

### 缓存机制

```go
type CachedBackend struct {
    backend Backend
    cache   *lru.Cache
    ttl     time.Duration
}

type CacheEntry struct {
    Data      []byte
    CreatedAt time.Time
}

func (b *CachedBackend) Get(ctx context.Context, key string) (io.ReadCloser, error) {
    // 检查缓存
    if entry, ok := b.cache.Get(key); ok {
        cacheEntry := entry.(*CacheEntry)
        if time.Since(cacheEntry.CreatedAt) < b.ttl {
            return io.NopCloser(bytes.NewReader(cacheEntry.Data)), nil
        }
        b.cache.Remove(key)
    }
    
    // 从后端读取
    reader, err := b.backend.Get(ctx, key)
    if err != nil {
        return nil, err
    }
    
    // 读取数据并缓存
    data, err := io.ReadAll(reader)
    reader.Close()
    if err != nil {
        return nil, err
    }
    
    // 只缓存小文件
    if len(data) <= 1024*1024 { // 1MB
        b.cache.Add(key, &CacheEntry{
            Data:      data,
            CreatedAt: time.Now(),
        })
    }
    
    return io.NopCloser(bytes.NewReader(data)), nil
}
```

---

> 💡 **提示**: 本设计文档持续更新，反映项目的最新架构和实现方案。
> 
> 🔧 **参考**: 详细的实现代码请参考项目源码和开发指南。
