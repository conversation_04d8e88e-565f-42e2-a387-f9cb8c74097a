# UniBackup 快速开始指南

本指南帮助您在 5 分钟内快速上手 UniBackup，体验基本的备份和恢复功能。

## 🚀 快速安装

### 方式一：Go 模块安装（推荐）

```bash
# 初始化 Go 模块
go mod init your-project

# 安装 UniBackup
go get git.gobies.org/fobrain/unibackup
```

### 方式二：源码编译

```bash
# 克隆仓库
git clone https://git.gobies.org/fobrain/unibackup.git
cd unibackup

# 编译
make build

# 安装到系统路径
make install
```

## ⚡ 最简示例

### 1. 基础配置

创建 `main.go` 文件：

```go
package main

import (
    "context"
    "log/slog"
    "os"

    "git.gobies.org/fobrain/unibackup/pkg/unibackup"
    "git.gobies.org/fobrain/unibackup/pkg/types"
)

// 辅助函数：返回指针
func ptr[T any](v T) *T {
    return &v
}

func main() {
    // 最小配置
    cfg := &types.Config{
        BackupRoot: "/tmp/unibackup-demo",  // 备份存储目录
        Logger:     slog.Default(),
        
        // MySQL 配置（可选）
        MySQL: &types.MySQLConfig{
            Host:     "localhost",
            Port:     3306,
            User:     "backup_user",
            Password: os.Getenv("MYSQL_PASSWORD"),
            DBName:   "test_db",
        },
    }

    // 创建备份管理器
    manager, err := unibackup.NewManager(cfg)
    if err != nil {
        panic(err)
    }
    defer manager.Shutdown()

    ctx := context.Background()

    // 执行备份
    taskID, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeArchival, "快速开始示例")
    if err != nil {
        panic(err)
    }

    // 查询任务状态
    task, err := manager.GetTask(taskID)
    if err != nil {
        panic(err)
    }
    
    println("✅ 备份任务已启动，任务ID:", taskID)
    println("📊 任务状态:", string(task.Status))
}
```

### 2. 运行示例

```bash
# 设置 MySQL 密码（如果使用 MySQL）
export MYSQL_PASSWORD="your_password"

# 运行示例
go run main.go
```

## 🔧 环境准备

### MySQL 环境（可选）

如果要测试 MySQL 备份功能：

```bash
# 使用 Docker 快速启动 MySQL
docker run -d \
  --name mysql-test \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=test_db \
  -e MYSQL_USER=backup_user \
  -e MYSQL_PASSWORD=backup_pass \
  -p 3306:3306 \
  mysql:8.0

# 等待 MySQL 启动
sleep 30

# 设置环境变量
export MYSQL_PASSWORD="backup_pass"
```

### Elasticsearch 环境（可选）

如果要测试 ES 备份功能：

```bash
# 使用 Docker 启动 Elasticsearch
docker run -d \
  --name es-test \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  elasticsearch:8.0.0

# 等待 ES 启动
sleep 60
```

## 📝 配置示例

### 仅本地存储

```go
cfg := &types.Config{
    BackupRoot: "/data/backups",
    Logger:     slog.Default(),
    MySQL: &types.MySQLConfig{
        Host:     "localhost",
        Port:     3306,
        User:     "backup_user",
        Password: os.Getenv("MYSQL_PASSWORD"),
        DBName:   "production_db",
    },
}
```

### 云存储配置

```go
cfg := &types.Config{
    BackupRoot: "/data/backups",
    Logger:     slog.Default(),
    
    // 启用云存储
    CloudStorage: &types.CloudStorageConfig{
        Enabled: true,
        Type:    "s3",
        Bucket:  "my-backup-bucket",
        Region:  "us-west-2",
        // 认证信息通过环境变量提供
    },
    
    MySQL: &types.MySQLConfig{
        Host:     "localhost",
        Port:     3306,
        User:     "backup_user",
        Password: os.Getenv("MYSQL_PASSWORD"),
        DBName:   "production_db",
    },
}
```

## 🎯 核心操作

### 备份操作

```go
// 异步备份（推荐）
taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "描述")

// 同步备份
err := manager.Backup(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "描述")
```

### 恢复操作

```go
// 列出可用备份
filter := types.BackupFilter{
    SourceTypes: []types.SourceType{types.MySQL},
    Statuses:    []types.TaskStatus{types.TaskStatusCompleted},
    Limit:       10,
}

result, err := manager.ListAllBackups(ctx, filter)
if err != nil {
    panic(err)
}

if len(result.Tasks) == 0 {
    panic("没有找到可用的备份")
}

// 使用任务ID进行恢复（推荐方式）
taskID, err := manager.RestoreByTaskID(ctx, result.Tasks[0].ID, false)
if err != nil {
    panic(err)
}

fmt.Printf("恢复任务已启动，任务ID: %s\n", taskID)

// 带回调的恢复示例
taskID, err = manager.RestoreByTaskIDWithCallback(ctx, result.Tasks[0].ID, false,
    func(ctx context.Context) error {
        fmt.Printf("基于任务ID的恢复成功完成！\n")
        return nil
    })
if err != nil {
    panic(err)
}

fmt.Printf("带回调的恢复任务已启动，任务ID: %s\n", taskID)

// 强制恢复示例
taskID, err = manager.RestoreByTaskID(ctx, result.Tasks[0].ID, true)
if err != nil {
    panic(err)
}

fmt.Printf("强制恢复任务已启动，任务ID: %s\n", taskID)
```

### 任务管理

```go
// 查询任务状态
task, err := manager.GetTask(taskID)
if err != nil {
    panic(err)
}

fmt.Printf("任务状态: %s\n", task.Status)

// 列出所有任务
tasks, err := manager.ListTasks()
if err != nil {
    panic(err)
}

fmt.Printf("总共有 %d 个任务\n", len(tasks))

// 取消任务
err = manager.CancelTask(taskID)
if err != nil {
    fmt.Printf("取消任务失败: %v\n", err)
} else {
    fmt.Println("任务已取消")
}

// 清理旧任务
err = manager.ClearOldTasks()
if err != nil {
    fmt.Printf("清理旧任务失败: %v\n", err)
} else {
    fmt.Println("旧任务清理完成")
}
```

## 🚨 常见问题

### 1. MySQL 工具未找到

```bash
# 安装 MySQL 客户端工具
# Ubuntu/Debian
sudo apt-get install mysql-client

# CentOS/RHEL
sudo yum install mysql

# macOS
brew install mysql-client
```

### 2. 权限错误

```bash
# 确保备份目录可写
sudo mkdir -p /data/backups
sudo chown $USER:$USER /data/backups
chmod 755 /data/backups
```

### 3. 连接失败

- 检查数据库服务是否运行
- 验证连接参数（主机、端口、用户名、密码）
- 确认网络连接和防火墙设置

## 📚 下一步

快速开始后，您可以：

1. **[完整配置指南](usage/CONFIGURATION_GUIDE.md)** - 了解所有配置选项
2. **[云存储指南](usage/CLOUD_STORAGE_GUIDE.md)** - 配置云存储备份
3. **[SDK 使用指南](usage/SDK_USAGE.md)** - 深入了解 API 使用
4. **[示例代码](../examples/)** - 查看更多实际使用示例
5. **[部署指南](deployment/)** - 生产环境部署

## 💡 提示

- 生产环境建议使用云存储
- 定期测试备份恢复流程
- 监控备份任务状态和存储空间
- 设置合理的备份保留策略
