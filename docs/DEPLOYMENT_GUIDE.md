# UniBackup 部署指南

本指南详细说明如何在不同环境中部署 UniBackup，包括 Docker 部署和 Elasticsearch 云存储部署。

## 📋 目录

- [Docker 部署](#docker-部署)
- [Elasticsearch 云存储部署](#elasticsearch-云存储部署)

---

# Docker 部署

## 📋 概述

UniBackup 在 Docker 环境中提供了完整的备份解决方案，支持：

- **MySQL 智能双模式备份**：直接文件访问模式（推荐）和网络流式模式（降级方案）
- **Elasticsearch 快照备份**：自动仓库管理和快照存储
- **统一配置管理**：一致的配置体验和环境变量支持
- **容器化部署**：完整的 Docker Compose 配置和最佳实践

系统会自动检测环境并选择最佳备份模式，无需手动配置。

## ⚠️ 重要：MySQL Binlog保留策略配置

**增量备份依赖binlog文件的连续性**，在Docker环境中必须正确配置binlog保留策略，否则会导致增量备份失败。

### 必需配置

在MySQL容器的启动命令中添加binlog保留参数：

```yaml
mysql:
  command: >
    --log-bin=/var/lib/mysql-binlogs/mysql-bin
    --binlog-format=ROW
    --server-id=1
    --expire-logs-days=7                    # 保留7天binlog
    --binlog-expire-logs-seconds=604800     # 或使用秒数（MySQL 8.0+）
    --max-binlog-size=1G                    # 控制单个binlog文件大小
```

### 保留策略建议

- **每日增量备份**：`--expire-logs-days=7`（保留7天）
- **每小时增量备份**：`--expire-logs-days=3`（保留3天）
- **高频增量备份**：`--expire-logs-days=1`（保留1天）
- **生产环境**：`--expire-logs-days=14`（保留14天，确保故障恢复时间）

### 风险提醒

❌ **如果binlog保留时间过短**：
- 增量备份会失败并报错："未找到起始binlog文件"
- 备份链会断裂，需要重新执行全量备份
- 可能导致数据恢复点丢失

✅ **正确配置后**：
- 增量备份稳定运行
- 备份链保持连续性
- 支持任意时间点恢复

## 🏗️ 架构说明

### 模式检测流程

1. **检查工具可用性**：验证mysqlbinlog工具是否配置且可执行
2. **检测文件访问权限**：尝试访问常见的binlog路径
3. **自动降级机制**：如果直接访问失败，自动切换到网络流式模式

### 性能对比

| 模式 | 性能 | 数据库负载 | 网络开销 | 兼容性 |
|------|------|------------|----------|--------|
| 直接文件访问 | 高 (50-80%提升) | 极低 | 无 | 需要文件挂载 |
| 网络流式 | 中等 | 中等 | 中等 | 通用兼容 |

## 🚀 部署方案

### 方案一：MySQL 直接文件访问模式（推荐）

#### Docker Compose配置

```yaml
version: '3.8'

services:
  unibackup:
    image: unibackup:latest
    container_name: unibackup
    volumes:
      - ./config.json:/app/config.json:ro
      - backup_data:/app/backup
      - mysql_binlogs:/var/lib/mysql-binlogs:ro  # 关键：只读挂载binlog
    environment:
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - ES_PASSWORD=${ES_PASSWORD}
    depends_on:
      - mysql
      - elasticsearch
    networks:
      - backup_network

  mysql:
    image: mysql:8.0
    container_name: mysql_server
    command: >
      --log-bin=/var/lib/mysql-binlogs/mysql-bin
      --binlog-format=ROW
      --server-id=1
      --expire-logs-days=7
      --max-binlog-size=1G
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_binlogs:/var/lib/mysql-binlogs  # 关键：binlog专用卷
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=production_db
      - MYSQL_USER=backup_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    networks:
      - backup_network

  elasticsearch:
    image: elasticsearch:8.0.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
    volumes:
      - es_data:/usr/share/elasticsearch/data
      - es_snapshots:/usr/share/elasticsearch/snapshots  # ES快照目录
    ports:
      - "9200:9200"
    networks:
      - backup_network

volumes:
  backup_data:
    driver: local
  mysql_data:
    driver: local
  mysql_binlogs:
    driver: local
  es_data:
    driver: local
  es_snapshots:
    driver: local

networks:
  backup_network:
    driver: bridge
```

#### 配置文件示例

```json
{
  "backup_root": "/app/backup",
  "max_concurrent_tasks": 3,
  "task_retention_days": 30,
  "mysql": {
    "host": "mysql_server",
    "port": 3306,
    "user": "backup_user",
    "db_name": "production_db",
    "binlog_base_path": "/var/lib/mysql-binlogs",
    "tools_path": {
      "mysqldump": "/usr/bin/mysqldump",
      "mysql": "/usr/bin/mysql",
      "mysqlbinlog": "/usr/bin/mysqlbinlog",
      "mysqladmin": "/usr/bin/mysqladmin"
    }
  },
  "es": {
    "addresses": ["http://elasticsearch:9200"],
    "archival_repo_name": "docker-archival",
    "managed_repo_name": "docker-managed",
    "auto_create_repos": true,
    "repo_base_path": "/usr/share/elasticsearch/snapshots"
  }
}
```

### 方案二：网络流式模式（兼容性方案）

当无法挂载binlog文件时，系统自动使用网络流式模式：

```yaml
version: '3.8'

services:
  unibackup:
    image: unibackup:latest
    container_name: unibackup
    volumes:
      - ./config.json:/app/config.json:ro
      - backup_data:/app/backup
      # 注意：不挂载binlog目录
    environment:
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - ES_PASSWORD=${ES_PASSWORD}
    depends_on:
      - mysql
      - elasticsearch
    networks:
      - backup_network

  mysql:
    image: mysql:8.0
    container_name: mysql_server
    command: >
      --log-bin=mysql-bin
      --binlog-format=ROW
      --server-id=1
      --expire-logs-days=7
    volumes:
      - mysql_data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=production_db
      - MYSQL_USER=backup_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    networks:
      - backup_network

  # elasticsearch 配置同上...
```

#### 网络流式模式配置

```json
{
  "backup_root": "/app/backup",
  "mysql": {
    "host": "mysql_server",
    "port": 3306,
    "user": "backup_user",
    "db_name": "production_db"
    // 注意：不配置 binlog_base_path，系统自动使用网络模式
  },
  "es": {
    "addresses": ["http://elasticsearch:9200"],
    "archival_repo_name": "docker-archival",
    "managed_repo_name": "docker-managed",
    "auto_create_repos": true
  }
}
```

## 🔧 环境变量配置

### 必需环境变量

```bash
# MySQL 认证
export MYSQL_ROOT_PASSWORD="secure_root_password"
export MYSQL_PASSWORD="secure_backup_password"

# Elasticsearch 认证（如启用安全）
export ES_PASSWORD="secure_es_password"

# 云存储认证（如使用云存储）
export AWS_ACCESS_KEY_ID="your_access_key"
export AWS_SECRET_ACCESS_KEY="your_secret_key"
```

### 可选环境变量

```bash
# UniBackup 配置
export UNIBACKUP_BACKUP_ROOT="/app/backup"
export UNIBACKUP_MAX_CONCURRENT_TASKS="3"
export UNIBACKUP_LOG_LEVEL="info"

# MySQL 配置
export UNIBACKUP_MYSQL_HOST="mysql_server"
export UNIBACKUP_MYSQL_PORT="3306"
export UNIBACKUP_MYSQL_USER="backup_user"
export UNIBACKUP_MYSQL_DB_NAME="production_db"

# ES 配置
export UNIBACKUP_ES_ADDRESSES="http://elasticsearch:9200"
```

## 🚀 启动和运行

### 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f unibackup
```

### 验证部署

```bash
# 检查 UniBackup 状态
docker exec unibackup unibackup status

# 检查 MySQL 连接
docker exec unibackup unibackup status --check-mysql

# 检查 ES 连接
docker exec unibackup unibackup status --check-es

# 执行测试备份
docker exec unibackup unibackup backup mysql --source production_db --type archival --description "部署测试"
```

## 📊 监控和维护

### 健康检查

```yaml
# 在 docker-compose.yml 中添加健康检查
services:
  unibackup:
    # ... 其他配置
    healthcheck:
      test: ["CMD", "unibackup", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mysql:
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  elasticsearch:
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 日志管理

```yaml
# 配置日志驱动
services:
  unibackup:
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
```

### 备份数据管理

```bash
# 备份 Docker 卷
docker run --rm -v backup_data:/data -v $(pwd):/backup alpine tar czf /backup/backup_data.tar.gz -C /data .

# 恢复 Docker 卷
docker run --rm -v backup_data:/data -v $(pwd):/backup alpine tar xzf /backup/backup_data.tar.gz -C /data
```

## 🚨 故障排除

### 常见问题

#### 1. Binlog 访问权限问题

**错误信息**：
```
错误：无法访问binlog文件: permission denied
```

**解决方案**：
```bash
# 检查卷挂载
docker inspect mysql_server | grep -A 10 "Mounts"

# 检查文件权限
docker exec mysql_server ls -la /var/lib/mysql-binlogs/

# 修复权限（如需要）
docker exec mysql_server chown -R mysql:mysql /var/lib/mysql-binlogs/
```

#### 2. 网络连接问题

**错误信息**：
```
错误：连接MySQL失败: dial tcp: lookup mysql_server
```

**解决方案**：
```bash
# 检查网络配置
docker network ls
docker network inspect backup_network

# 检查服务名解析
docker exec unibackup nslookup mysql_server
```

#### 3. ES 仓库创建失败

**错误信息**：
```
错误：创建ES仓库失败: repository verification failed
```

**解决方案**：
```bash
# 检查ES快照目录权限
docker exec elasticsearch ls -la /usr/share/elasticsearch/snapshots/

# 修复权限
docker exec elasticsearch chown -R elasticsearch:elasticsearch /usr/share/elasticsearch/snapshots/
```

### 调试模式

```bash
# 启用调试日志
docker-compose down
export UNIBACKUP_LOG_LEVEL="debug"
docker-compose up -d

# 查看详细日志
docker-compose logs -f unibackup
```

---

# Elasticsearch 云存储部署

## 📋 概述

ES 云存储功能允许 UniBackup 将 Elasticsearch 快照直接存储到云存储服务（S3、GCS、Azure），提供企业级的备份存储解决方案。

### 核心特性
- **自动仓库管理**：自动创建和配置 ES 快照仓库
- **双重认证支持**：环境变量和 keystore 两种认证方式
- **配置验证**：自动验证 ES 插件、认证和连接性
- **故障自诊断**：详细的错误信息和解决方案指导

## 🚀 生产环境部署流程

### 第一步：ES 集群准备

#### 1.1 安装云存储插件

根据您使用的云存储类型，安装相应的ES插件：

**AWS S3 和 S3 兼容存储（MinIO）：**
```bash
# 在每个 ES 节点上执行
cd /usr/share/elasticsearch
sudo bin/elasticsearch-plugin install repository-s3

# 重启 ES 集群
sudo systemctl restart elasticsearch
```

**Google Cloud Storage：**
```bash
# 在每个 ES 节点上执行
cd /usr/share/elasticsearch
sudo bin/elasticsearch-plugin install repository-gcs

# 重启 ES 集群
sudo systemctl restart elasticsearch
```

**Azure Blob Storage：**
```bash
# 在每个 ES 节点上执行
cd /usr/share/elasticsearch
sudo bin/elasticsearch-plugin install repository-azure

# 重启 ES 集群
sudo systemctl restart elasticsearch
```

#### 1.2 验证插件安装

```bash
# 检查已安装的插件
curl -X GET "localhost:9200/_cat/plugins?v"

# 应该看到类似输出：
# name    component      version
# node-1  repository-s3  8.0.0
```

### 第二步：配置云存储认证

#### 2.1 AWS S3 认证配置

**方式一：环境变量（推荐用于容器部署）**
```bash
# 设置环境变量
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

**方式二：ES Keystore（推荐用于生产环境）**
```bash
# 在每个 ES 节点上执行
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.default.access_key
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.default.secret_key

# 重启 ES 集群
sudo systemctl restart elasticsearch
```

**方式三：IAM 角色（推荐用于 EC2 部署）**
```bash
# 无需额外配置，ES 会自动使用 EC2 实例的 IAM 角色
# 确保 IAM 角色有 S3 访问权限
```

#### 2.2 Google Cloud Storage 认证配置

**服务账户认证：**
```bash
# 将服务账户密钥添加到 keystore
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add-file gcs.client.default.credentials_file /path/to/service-account.json

# 重启 ES 集群
sudo systemctl restart elasticsearch
```

#### 2.3 Azure Blob Storage 认证配置

**账户密钥认证：**
```bash
# 添加 Azure 存储账户信息到 keystore
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add azure.client.default.account
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add azure.client.default.key

# 重启 ES 集群
sudo systemctl restart elasticsearch
```

### 第三步：UniBackup 配置

#### 3.1 配置文件示例

**AWS S3 配置：**
```json
{
  "backup_root": "/var/lib/unibackup",
  "cloud_storage": {
    "enabled": true,
    "type": "s3",
    "bucket": "my-es-backup-bucket",
    "region": "us-east-1"
  },
  "es": {
    "addresses": ["http://localhost:9200"],
    "archival_repo_name": "s3-archival-repo",
    "managed_repo_name": "s3-managed-repo",
    "auto_create_repos": true
  }
}
```

**Google Cloud Storage 配置：**
```json
{
  "backup_root": "/var/lib/unibackup",
  "cloud_storage": {
    "enabled": true,
    "type": "gcs",
    "bucket": "my-gcs-backup-bucket",
    "project_id": "my-gcp-project"
  },
  "es": {
    "addresses": ["http://localhost:9200"],
    "archival_repo_name": "gcs-archival-repo",
    "managed_repo_name": "gcs-managed-repo",
    "auto_create_repos": true
  }
}
```

#### 3.2 自动仓库创建

UniBackup 会自动创建和配置 ES 仓库：

```bash
# 启动 UniBackup 时会自动执行：
# 1. 检查 ES 连接
# 2. 验证云存储插件
# 3. 测试云存储连接
# 4. 创建快照仓库
# 5. 验证仓库功能
```

### 第四步：验证部署

#### 4.1 检查仓库状态

```bash
# 查看已创建的仓库
curl -X GET "localhost:9200/_snapshot"

# 验证仓库连接
curl -X POST "localhost:9200/_snapshot/s3-archival-repo/_verify"
```

#### 4.2 执行测试备份

```bash
# 使用 UniBackup 执行测试备份
unibackup backup elasticsearch --source my_cluster --type archival --description "部署测试备份"

# 查看备份状态
unibackup list elasticsearch --type archival
```

## 🔧 高级配置

### 自定义仓库设置

```json
{
  "es": {
    "addresses": ["http://localhost:9200"],
    "archival_repo_name": "production-archival",
    "managed_repo_name": "production-managed",
    "auto_create_repos": true,
    "repo_settings": {
      "compress": true,
      "chunk_size": "1gb",
      "max_restore_bytes_per_sec": "40mb",
      "max_snapshot_bytes_per_sec": "40mb"
    }
  }
}
```

### 多客户端配置

```bash
# 为不同环境配置不同的 S3 客户端
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.production.access_key
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.production.secret_key
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.staging.access_key
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.staging.secret_key
```

### 网络和安全配置

```yaml
# elasticsearch.yml 配置
network.host: 0.0.0.0
discovery.type: single-node

# 启用安全（可选）
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true

# S3 代理配置（如需要）
s3.client.default.proxy.host: proxy.company.com
s3.client.default.proxy.port: 8080
```

## 🚨 故障排除

### 常见问题

#### 1. 插件安装失败

**错误信息**：
```
ERROR: Plugin repository-s3 not found
```

**解决方案**：
```bash
# 检查 ES 版本兼容性
/usr/share/elasticsearch/bin/elasticsearch --version

# 手动下载插件
wget https://artifacts.elastic.co/downloads/elasticsearch-plugins/repository-s3/repository-s3-8.0.0.zip
sudo /usr/share/elasticsearch/bin/elasticsearch-plugin install file:///path/to/repository-s3-8.0.0.zip
```

#### 2. 认证失败

**错误信息**：
```
repository verification failed: [s3-repo] The AWS Access Key Id you provided does not exist
```

**解决方案**：
```bash
# 验证认证信息
aws s3 ls s3://your-bucket-name

# 检查 keystore 内容
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore list

# 重新设置认证信息
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore remove s3.client.default.access_key
sudo -u elasticsearch /usr/share/elasticsearch/bin/elasticsearch-keystore add s3.client.default.access_key
```

#### 3. 权限不足

**错误信息**：
```
Access Denied (Service: Amazon S3; Status Code: 403)
```

**解决方案**：
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket",
        "s3:GetBucketLocation"
      ],
      "Resource": "arn:aws:s3:::your-backup-bucket"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::your-backup-bucket/*"
    }
  ]
}
```

#### 4. 网络连接问题

**错误信息**：
```
Unable to execute HTTP request: Connect to s3.amazonaws.com:443
```

**解决方案**：
```bash
# 检查网络连接
curl -I https://s3.amazonaws.com

# 检查防火墙设置
sudo iptables -L

# 配置代理（如需要）
echo 's3.client.default.proxy.host: proxy.company.com' >> /etc/elasticsearch/elasticsearch.yml
echo 's3.client.default.proxy.port: 8080' >> /etc/elasticsearch/elasticsearch.yml
```

### 调试和监控

#### 启用详细日志

```yaml
# 在 elasticsearch.yml 中添加
logger.org.elasticsearch.repositories.s3: DEBUG
logger.com.amazonaws: DEBUG
```

#### 监控仓库健康

```bash
# 定期检查仓库状态
curl -X GET "localhost:9200/_snapshot/_status"

# 监控快照进度
curl -X GET "localhost:9200/_snapshot/s3-archival-repo/_current"

# 检查集群健康
curl -X GET "localhost:9200/_cluster/health"
```

## 📚 相关文档

- [用户指南 - 云存储指南](#云存储指南) - 云存储配置详解
- [用户指南 - 配置指南](#配置指南) - 完整配置说明
- [故障排除指南](TROUBLESHOOTING.md) - 常见问题解决

---

> 💡 **提示**: 生产环境建议使用 IAM 角色或 keystore 进行认证，避免在配置文件中硬编码密钥。
> 
> 🔧 **工具**: 使用 `unibackup status --check-es` 可以验证 ES 云存储配置的正确性。
