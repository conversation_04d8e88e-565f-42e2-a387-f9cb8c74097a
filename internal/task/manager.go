package task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	configpkg "git.gobies.org/fobrain/unibackup/internal/config"
	berrors "git.gobies.org/fobrain/unibackup/internal/errors"
	"git.gobies.org/fobrain/unibackup/internal/provider"
	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// 移除硬编码的超时常量，改为使用配置中的值

// Package task 实现了执行和管理备份/恢复任务的核心逻辑。
//
// TaskManager 是 UniBackup 的"引擎室"，它负责将上层API（如 BackupManager）的请求
// 转化为具体的、可执行的、可跟踪的后台任务。它的职责包括：
//
// 1. **异步任务抽象**: 将备份、恢复等长时间操作封装为异步任务（`types.Task`），
//    并立即返回一个任务ID，允许客户端轮询状态。这是所有 `...Async` 方法的核心。
//
// 2. **生命周期管理**: 管理每个任务从创建、排队、运行、到最终状态（完成、失败、取消）
//    的整个生命周期。
//
// 3. **持久化与故障恢复**: 将所有任务的状态实时持久化到 `tasks.json` 文件。在服务重启时，
//    它能从该文件加载任务，并将任何在重启前处于"运行中"状态的任务标记为"失败"，
//    从而保证系统状态的一致性。
//
// 4. **并发控制**: 通过信号量（Semaphore）机制，限制了可以同时运行的后台任务数量，
//    防止因大量并发请求而耗尽系统资源。
//
// 5. **分组与原子性操作**: 实现了项目中最复杂的 `BackupAll` 和 `RestoreAll` 逻辑。
//    - 对于 `BackupAll`，它支持并行备份，并提供了"失败时清理"的选项，以删除部分成功的备份，
//      保证备份组的逻辑一致性。
//    - 对于 `RestoreAll`，它提供了真正的原子性保证。通过"预恢复快照"和"失败时回滚"机制，
//      确保分组恢复要么全部成功，要么全部回滚到操作开始前的状态。
//
// 这个模块是整个系统中最复杂的部分，它通过组合使用 Go 的并发原语（goroutine, channel, sync.Mutex, sync.WaitGroup）
// 和严谨的状态管理，构建了一个健壮、可靠的后台任务执行框架。

// TaskManager 定义了执行底层备份和恢复任务的接口。
// 使用完全重建策略确保数据一致性。
type TaskManager interface {
	// === 异步操作接口 ===

	// BackupAsync 异步地启动一个备份任务。
	BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error)

	// RestoreAsync 异步地启动一个恢复任务（使用完全重建策略）。
	RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error)

	// BackupAllAsync 异步地启动一个分组备份任务，可同时备份多个数据源。
	BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error)

	// RestoreAllAsync 异步地启动一个分组恢复任务，支持原子性操作（使用完全重建策略）。
	RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error)

	// === 任务管理接口 ===

	// GetTask 通过任务ID获取任务的详细信息和当前状态。
	GetTask(taskID string) (*types.Task, error)

	// ListTasks 列出最近的任务历史记录。
	ListTasks() ([]*types.Task, error)

	// CancelTask 尝试取消一个正在运行或排队的任务。
	CancelTask(taskID string) error

	// ClearOldTasks 手动触发清理，移除旧的、已完成的任务记录。
	ClearOldTasks() error

	// DeleteTask 删除指定的任务记录。
	DeleteTask(taskID string) error

	// Shutdown 优雅地关闭任务管理器，会尝试取消所有正在运行的任务并保存最终状态。
	Shutdown() error

	// === 同步操作接口（简化版本） ===

	// RunBackup 同步地执行一个具体的备份操作。
	RunBackup(ctx context.Context, record *types.BackupRecord) *types.BackupError

	// Restore 同步地执行一个恢复操作（使用完全重建策略）。
	Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError

	// DeleteBackup 同步地删除一个备份，协调 provider 和 storage 的操作。
	DeleteBackup(ctx context.Context, source types.SourceType, backupID string) *types.BackupError

	// UpdateConfig 动态更新配置和Providers，支持运行时配置变更
	UpdateConfig(cfg *types.Config, providers map[types.SourceType]provider.BackupProvider) error

	// SetStorageManager 设置存储管理器，用于解决循环依赖问题
	SetStorageManager(storageMgr storage.StorageManager) error
}

// Manager 是 TaskManager 接口的具体实现。
// 这个结构体实际上承担了两种职责：
//  1. 实现 TaskManager 接口，负责执行底层的备份/恢复流程。
//  2. 管理异步任务（types.Task）的生命周期，包括创建、状态更新和持久化到文件（tasks.json）。
//     这部分功能没有通过接口暴露，而是由更高层的 BackupManager 直接调用。
type Manager struct {
	storageMgr storage.StorageManager
	providers  map[types.SourceType]provider.BackupProvider
	logger     *zap.Logger
	cfg        *types.Config // 添加配置引用以获取超时值

	// --- 异步任务生命周期管理相关字段 ---
	tasksFilepath   string     // tasks.json 文件的完整路径
	tasks           sync.Map   // 并发安全的Map，用于在内存中缓存所有任务，以 taskID 为键
	mu              sync.Mutex // 用于保护对 tasks.json 文件的写操作，确保原子性
	taskMutexes     sync.Map   // 任务级别的互斥锁，防止同一任务的并发状态更新
	taskUpdateMutex sync.Mutex // 全局任务更新锁，防止并发状态更新冲突

	// --- 并发控制字段 ---
	maxConcurrent int           // 最大并发任务数
	semaphore     chan struct{} // 并发控制信号量，其缓冲区大小等于maxConcurrent

	// --- Context管理字段 ---
	taskContexts sync.Map // 用于存储每个正在运行任务的 `context.CancelFunc`，以便能够取消它们

	// --- 清理管理字段 ---
	taskRetentionDays int           // 任务记录的保留天数
	maxTaskHistory    int           // 内存和文件中保留的最大任务历史数量
	cleanupBackupData bool          // 是否在清理任务记录时同时清理对应的备份数据
	cleanupTicker     *time.Ticker  // 定期清理旧任务的定时器
	shutdownChan      chan struct{} // 用于通知清理goroutine退出的关闭信号
}

// 编译时检查，确保 *Manager 实现了 TaskManager 接口。
var _ TaskManager = (*Manager)(nil)

// NewManager 创建一个新的任务管理器实例。
// storageMgr: 存储管理器，用于与文件系统交互。
// providers: 一个从数据源类型到其具体Provider实现的映射。
// tasksFilepath: 用于持久化任务状态的 `tasks.json` 文件的路径。
// logger: 日志记录器。
// maxConcurrent: 最大并发任务数，如果<=0则默认为5。
// 返回新的任务管理器实例或错误。
func NewManager(cfg *types.Config, storageMgr storage.StorageManager, providers map[types.SourceType]provider.BackupProvider, tasksFilepath string, logger *zap.Logger, maxConcurrent int, taskRetentionDays int, maxTaskHistory int) (*Manager, error) {
	if maxConcurrent <= 0 {
		maxConcurrent = 5 // 默认最多5个并发任务
	}

	if taskRetentionDays <= 0 {
		taskRetentionDays = 365 // 默认保留一年
	}
	if maxTaskHistory < 0 {
		maxTaskHistory = 0 // 默认不限制数量，只按时间清理
	}

	m := &Manager{
		cfg:               cfg,
		storageMgr:        storageMgr,
		providers:         providers,
		logger:            logger.With(zap.String("component", "task_manager")),
		tasksFilepath:     tasksFilepath,
		maxConcurrent:     maxConcurrent,
		semaphore:         make(chan struct{}, maxConcurrent),
		taskRetentionDays: taskRetentionDays,
		maxTaskHistory:    maxTaskHistory,
		cleanupBackupData: cfg.CleanupBackupData,
		shutdownChan:      make(chan struct{}),
	}

	// 只有在 storageManager 不为 nil 时才加载现有任务
	// 这是为了解决循环依赖问题：storageManager 需要 taskManager，但 taskManager 初始化时需要 storageManager
	if storageMgr != nil {
		if err := m.loadTasksFromFile(); err != nil {
			// 优雅地处理错误，而不是panic
			logger.Error("从文件加载任务失败", zap.Error(err))
			return nil, fmt.Errorf("从文件加载任务失败: %w", err)
		}
	}

	// 启动定期清理（每小时一次）
	m.cleanupTicker = time.NewTicker(1 * time.Hour)
	go m.periodicCleanup()

	return m, nil
}

// SetStorageManager 设置存储管理器并加载任务文件。
// 这个方法用于解决循环依赖问题：在创建 TaskManager 时 storageManager 可能还没有初始化。
func (m *Manager) SetStorageManager(storageMgr storage.StorageManager) error {
	m.storageMgr = storageMgr

	// 现在可以安全地加载任务文件了
	if err := m.loadTasksFromFile(); err != nil {
		m.logger.Error("设置存储管理器后加载任务文件失败", zap.Error(err))
		return fmt.Errorf("加载任务文件失败: %w", err)
	}

	return nil
}

// UpdateConfig 动态更新配置和Providers，支持运行时配置变更
func (m *Manager) UpdateConfig(cfg *types.Config, providers map[types.SourceType]provider.BackupProvider) error {
	// 更新配置引用
	m.cfg = cfg

	// 更新任务文件路径
	newTasksPath := filepath.Join(cfg.BackupRoot, "tasks.json")
	pathChanged := m.tasksFilepath != newTasksPath
	if pathChanged {
		m.logger.Info("任务文件路径已更新",
			zap.String("old", m.tasksFilepath),
			zap.String("new", newTasksPath))
		m.tasksFilepath = newTasksPath
	}

	// 更新清理配置
	m.cleanupBackupData = cfg.CleanupBackupData

	// 更新providers
	m.providers = providers

	// 检查存储后端是否发生变化（本地存储 <-> 云存储）
	storageTypeChanged := m.hasStorageTypeChanged(cfg)

	// 如果存储后端类型或路径发生变化，需要重新加载任务记录
	if (pathChanged || storageTypeChanged) && m.storageMgr != nil {
		m.logger.Info("存储后端已切换，重新加载任务记录",
			zap.String("new_tasks_path", newTasksPath),
			zap.Bool("path_changed", pathChanged),
			zap.Bool("storage_type_changed", storageTypeChanged))

		// 清空当前内存中的任务记录
		m.tasks.Range(func(key, value interface{}) bool {
			m.tasks.Delete(key)
			return true
		})

		// 从新的存储后端加载任务记录
		if err := m.loadTasksFromFile(); err != nil {
			m.logger.Warn("从新存储后端加载任务记录失败，继续使用空任务列表",
				zap.Error(err))
			// 不返回错误，允许系统继续运行
		}
	}

	m.logger.Info("TaskManager配置和Providers更新完成",
		zap.String("backup_root", cfg.BackupRoot),
		zap.String("tasks_file", newTasksPath),
		zap.Int("provider_count", len(providers)))

	return nil
}

// hasStorageTypeChanged 检查存储后端类型是否发生变化
func (m *Manager) hasStorageTypeChanged(newCfg *types.Config) bool {
	// 检查当前配置的存储类型
	currentIsCloud := m.cfg.CloudStorage != nil && m.cfg.CloudStorage.Enabled
	newIsCloud := newCfg.CloudStorage != nil && newCfg.CloudStorage.Enabled

	// 如果存储类型发生变化（本地<->云存储）
	if currentIsCloud != newIsCloud {
		return true
	}

	// 如果都是云存储，检查云存储类型是否变化
	if currentIsCloud && newIsCloud {
		currentType := m.cfg.CloudStorage.Type
		newType := newCfg.CloudStorage.Type
		if currentType != newType {
			return true
		}

		// 检查关键配置是否变化（存储桶、端点等）
		if m.cfg.CloudStorage.Bucket != newCfg.CloudStorage.Bucket ||
			m.cfg.CloudStorage.Endpoint != newCfg.CloudStorage.Endpoint ||
			m.cfg.CloudStorage.Container != newCfg.CloudStorage.Container {
			return true
		}
	}

	return false
}

// === 异步操作实现 ===

// BackupAsync 异步地启动一个备份任务。
func (m *Manager) BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error) {
	// 检查并发冲突（在获取信号量之前）
	sourceKey := fmt.Sprintf("%s-%s", sourceType, sourceName)
	if err := m.checkConcurrencyConflict(types.BackupTask, []string{sourceKey}, backupType); err != nil {
		return "", err
	}

	// 增量备份智能处理：如果是增量备份但没有可用的增量链，自动转换为初始备份
	if backupType == types.BackupTypeChainIncremental {
		chains, err := m.storageMgr.ListIncrementalChains(sourceType, sourceName)
		if err != nil {
			return "", fmt.Errorf("检查增量链失败: %w", err)
		}

		if len(chains) == 0 {
			m.logger.Info("未找到增量链，自动转换为链初始备份",
				zap.String("source", sourceKey),
				zap.String("requested_type", string(types.BackupTypeChainIncremental)),
				zap.String("actual_type", string(types.BackupTypeChainInitial)))
			backupType = types.BackupTypeChainInitial
		}
	}

	// 备份任务不需要信号量限制（备份之间可以并发）

	// 创建任务对象
	task := &types.Task{
		ID:          uuid.NewString(),
		Type:        types.BackupTask,
		Source:      sourceType,
		Status:      types.TaskStatusPending,
		Progress:    0,
		StartTime:   time.Now(),
		Description: description,
		Metadata: map[string]interface{}{
			"source_name": sourceName,
			"backup_type": string(backupType),
			"backup_name": types.GenerateBackupName(sourceType, sourceName, backupType, time.Now()),
		},
	}

	// 为该任务创建一个带超时的上下文
	timeout := m.cfg.BackupTimeout.ToDuration()
	if timeout <= 0 {
		timeout = 24 * time.Hour // 默认24小时
	}
	taskCtx, cancel := context.WithTimeout(ctx, timeout)
	m.taskContexts.Store(task.ID, cancel)

	// 将任务存入内存并立即持久化到文件
	m.tasks.Store(task.ID, task)
	if err := m.writeTasksToFile(); err != nil {
		// 备份任务失败时不需要释放信号量
		cancel()
		m.taskContexts.Delete(task.ID)
		return "", fmt.Errorf("持久化新任务失败: %w", err)
	}

	// 启动一个 goroutine 在后台异步执行实际的备份逻辑
	go func() {
		defer func() {
			if r := recover(); r != nil {
				m.logger.Error("备份任务发生panic，正在清理资源",
					zap.String("task_id", task.ID),
					zap.Any("panic", r))
				task.Status = types.TaskStatusFailed
				task.Error = fmt.Sprintf("任务执行时发生panic: %v", r)
				m.updateTask(task)
			}
			// 备份任务不需要释放信号量
			cancel()
			m.taskContexts.Delete(task.ID)
		}()
		m.executeBackupWithContext(taskCtx, task, sourceType, sourceName, backupType)
	}()

	m.logger.Info("异步备份任务已成功启动",
		zap.String("task_id", task.ID),
		zap.String("source", sourceName))
	return task.ID, nil
}

// RestoreAsync 异步地启动一个恢复任务（使用完全重建策略）。
func (m *Manager) RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error) {
	sourceKey := fmt.Sprintf("%s-%s", config.SourceType, config.SourceName)

	// 如果启用强制恢复，先中断备份任务
	if config.Force {
		if err := m.forceInterruptBackupTasks([]string{sourceKey}); err != nil {
			return "", fmt.Errorf("强制中断备份任务失败: %w", err)
		}
	}

	// 检查并发冲突（恢复任务不需要备份类型参数，传入默认值）
	if err := m.checkConcurrencyConflict(types.RestoreTask, []string{sourceKey}, types.BackupTypeArchival); err != nil {
		if config.Force {
			return "", fmt.Errorf("强制恢复失败，仍有任务冲突: %w", err)
		}
		return "", err
	}

	// 恢复任务需要获取信号量（恢复任务不能并发）
	select {
	case m.semaphore <- struct{}{}:
		// 获得许可，可以执行
	default:
		return "", fmt.Errorf("系统繁忙，恢复任务已达上限(%d)，请稍后重试", m.maxConcurrent)
	}

	// 创建任务对象
	task := &types.Task{
		ID:          uuid.NewString(),
		Type:        types.RestoreTask,
		Source:      config.SourceType,
		Status:      types.TaskStatusPending,
		Progress:    0,
		StartTime:   time.Now(),
		Description: config.Description,
		Metadata: map[string]interface{}{
			"source_name":          config.SourceName,
			"backup_id":            config.BackupID,
			"create_safety_backup": config.CreateSafetyBackup,
			"rollback_on_failure":  config.RollbackOnFailure,
			"skip_data_cleanup":    config.SkipDataCleanup,
			"force":                config.Force,
		},
	}

	// 创建带超时的上下文
	timeout := m.cfg.RestoreTimeout.ToDuration()
	if timeout <= 0 {
		timeout = 24 * time.Hour // 默认24小时
	}
	taskCtx, cancel := context.WithTimeout(ctx, timeout)
	m.taskContexts.Store(task.ID, cancel)

	// 存储任务并持久化
	m.tasks.Store(task.ID, task)
	if err := m.writeTasksToFile(); err != nil {
		<-m.semaphore
		cancel()
		m.taskContexts.Delete(task.ID)
		return "", fmt.Errorf("持久化新任务失败: %w", err)
	}

	// 异步执行
	go func() {
		defer func() {
			// 使用 recover 捕获可能的 panic，确保资源始终被释放
			if r := recover(); r != nil {
				m.logger.Error("恢复任务发生panic，正在清理资源",
					zap.String("task_id", task.ID),
					zap.Any("panic", r))
				// 更新任务状态为失败
				task.Status = types.TaskStatusFailed
				task.Error = fmt.Sprintf("任务执行时发生panic: %v", r)
				m.updateTask(task)
			}
			<-m.semaphore                  // 释放许可
			cancel()                       // 确保取消函数被调用
			m.taskContexts.Delete(task.ID) // 清理context
		}()
		m.executeRestoreWithContext(taskCtx, task, config)
	}()

	m.logger.Info("异步恢复任务已成功启动",
		zap.String("task_id", task.ID),
		zap.String("source", config.SourceName),
		zap.String("backup_id", config.BackupID))
	return task.ID, nil
}

// BackupAllAsync 异步地启动一个分组备份任务。
func (m *Manager) BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error) {
	// 检查请求有效性
	if len(req.Sources) == 0 {
		return "", fmt.Errorf("备份请求列表不能为空")
	}

	// 检查每个数据源的并发冲突（按备份类型分别检查）
	for _, source := range req.Sources {
		sourceKey := fmt.Sprintf("%s-%s", source.SourceType, source.SourceName)
		if err := m.checkConcurrencyConflict(types.BackupAllTask, []string{sourceKey}, source.BackupType); err != nil {
			return "", err
		}
	}

	// 分组备份智能处理：将需要的增量备份转换为初始备份
	processedSources := make([]types.BackupRequest, 0, len(req.Sources))
	for _, source := range req.Sources {
		if source.BackupType == types.BackupTypeChainIncremental {
			hasBackups, err := m.storageMgr.HasAnyBackups(source.SourceType, source.SourceName)
			if err != nil {
				return "", fmt.Errorf("检查数据源 %s-%s 备份历史失败: %w",
					source.SourceType, source.SourceName, err)
			}

			if !hasBackups {
				// 转换为初始备份
				modifiedSource := source
				modifiedSource.BackupType = types.BackupTypeChainInitial
				modifiedSource.Description = fmt.Sprintf("自动创建增量链初始备份 - %s", source.Description)
				processedSources = append(processedSources, modifiedSource)

				m.logger.Info("分组备份中自动转换为链初始备份",
					zap.String("source", fmt.Sprintf("%s-%s", source.SourceType, source.SourceName)),
					zap.String("requested_type", string(types.BackupTypeChainIncremental)),
					zap.String("actual_type", string(types.BackupTypeChainInitial)))
			} else {
				processedSources = append(processedSources, source)
			}
		} else {
			processedSources = append(processedSources, source)
		}
	}

	// 分组备份不需要预先获取信号量

	// 创建主任务
	mainTask := &types.Task{
		ID:          uuid.NewString(),
		Type:        types.BackupAllTask,
		Status:      types.TaskStatusPending,
		Progress:    0,
		StartTime:   time.Now(),
		Description: req.Description,
		Metadata: map[string]interface{}{
			"sources":            processedSources, // 使用处理后的源信息
			"original_sources":   req.Sources,      // 保存原始请求用于记录
			"atomic":             req.Atomic,
			"cleanup_on_failure": req.CleanupOnFailure,
			"source_count":       len(processedSources),
			"completed_count":    0,
			"failed_count":       0,
			"total_size":         "0B",
			"backup_name":        types.GenerateGroupBackupName(len(processedSources), time.Now()),
		},
	}

	// 为每个子备份创建一个唯一的ID
	var subTaskIDs []string
	for range processedSources {
		subTaskIDs = append(subTaskIDs, uuid.NewString())
	}
	mainTask.SubTaskIDs = subTaskIDs

	// 存储主任务并持久化
	m.tasks.Store(mainTask.ID, mainTask)
	if err := m.writeTasksToFile(); err != nil {
		return "", fmt.Errorf("持久化主任务失败: %w", err)
	}

	// 创建处理后的请求
	processedReq := req
	processedReq.Sources = processedSources

	// 异步执行（添加超时控制）
	go func() {
		// 为分组任务创建带超时的上下文
		timeout := m.cfg.BackupTimeout.ToDuration()
		if timeout <= 0 {
			timeout = 24 * time.Hour
		}
		// 分组任务可能需要更长时间，使用2倍超时
		groupCtx, cancel := context.WithTimeout(ctx, timeout*2)
		defer cancel()

		m.executeBackupAll(groupCtx, mainTask, processedReq)
	}()

	m.logger.Info("异步分组备份任务已成功启动",
		zap.String("task_id", mainTask.ID),
		zap.Int("source_count", len(processedSources)))
	return mainTask.ID, nil
}

// RestoreAllAsync 异步地启动一个分组恢复任务（使用完全重建策略）。
func (m *Manager) RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error) {
	// 检查请求有效性
	if len(config.Restores) == 0 {
		return "", fmt.Errorf("恢复请求列表不能为空")
	}

	// 构建涉及的数据源键列表
	var sourceKeys []string
	for _, restore := range config.Restores {
		sourceKey := fmt.Sprintf("%s-%s", restore.SourceType, restore.SourceName)
		sourceKeys = append(sourceKeys, sourceKey)
	}

	// 如果启用强制恢复，先中断备份任务
	if config.Force {
		if err := m.forceInterruptBackupTasks(sourceKeys); err != nil {
			return "", fmt.Errorf("强制中断备份任务失败: %w", err)
		}
	}

	// 检查并发冲突（恢复任务不需要备份类型参数，传入默认值）
	if err := m.checkConcurrencyConflict(types.RestoreAllTask, sourceKeys, types.BackupTypeArchival); err != nil {
		if config.Force {
			return "", fmt.Errorf("强制恢复失败，仍有任务冲突: %w", err)
		}
		return "", err
	}

	// 根据恢复模式获取信号量：串行恢复只需1个，并行恢复需要所有
	var requiredSlots int
	if config.Atomic {
		// 串行恢复：只需要1个信号量
		requiredSlots = 1
		m.logger.Debug("串行恢复模式，获取1个信号量", zap.String("restore_count", fmt.Sprintf("%d", len(config.Restores))))
	} else {
		// 并行恢复：需要每个恢复任务1个信号量
		requiredSlots = len(config.Restores)
		if requiredSlots > m.maxConcurrent {
			return "", fmt.Errorf("请求的恢复数量(%d)超过最大并发限制(%d)", requiredSlots, m.maxConcurrent)
		}
		m.logger.Debug("并行恢复模式，获取多个信号量", zap.Int("required_slots", requiredSlots))
	}

	// 尝试获取所需的信号量
	for i := 0; i < requiredSlots; i++ {
		select {
		case m.semaphore <- struct{}{}:
			// 成功获取一个信号量
		default:
			// 如果无法获取足够的信号量，释放已获取的
			for j := 0; j < i; j++ {
				<-m.semaphore
			}
			if config.Atomic {
				return "", fmt.Errorf("系统繁忙，无法获取信号量进行串行恢复")
			} else {
				return "", fmt.Errorf("系统繁忙，无法获取足够的并发槽位进行并行恢复")
			}
		}
	}

	// 启动前验证所有备份ID是否存在，这是一个快速失败的检查
	for i, restoreConfig := range config.Restores {
		_, err := m.storageMgr.FindBackupRecord(restoreConfig.SourceType, restoreConfig.BackupID)
		if err != nil {
			return "", fmt.Errorf("第%d个恢复请求的备份ID '%s' 无效: %w",
				i+1, restoreConfig.BackupID, err)
		}
	}

	// 检查是否有任何配置启用了快照和回滚
	createSnapshot := false
	rollbackOnFailure := false
	for _, restoreConfig := range config.Restores {
		if restoreConfig.CreateSafetyBackup {
			createSnapshot = true
		}
		if restoreConfig.RollbackOnFailure {
			rollbackOnFailure = true
		}
	}

	// 创建主任务
	mainTask := &types.Task{
		ID:          uuid.NewString(),
		Type:        types.RestoreAllTask,
		Status:      types.TaskStatusPending,
		Progress:    0,
		StartTime:   time.Now(),
		Description: config.Description,
		Metadata: map[string]interface{}{
			"restores":            config.Restores, // 保存恢复信息用于并发检查
			"atomic":              config.Atomic,
			"parallel":            config.Parallel,
			"source_count":        len(config.Restores),
			"create_snapshot":     createSnapshot,
			"rollback_on_failure": rollbackOnFailure,
		},
	}

	// 创建子任务ID列表
	var subTaskIDs []string
	for range config.Restores {
		subTaskIDs = append(subTaskIDs, uuid.NewString())
	}
	mainTask.SubTaskIDs = subTaskIDs

	// 存储主任务并持久化
	m.tasks.Store(mainTask.ID, mainTask)
	if err := m.writeTasksToFile(); err != nil {
		return "", fmt.Errorf("持久化主任务失败: %w", err)
	}

	// 异步执行
	go func() {
		defer func() {
			// 安全地释放所有获取的信号量
			if r := recover(); r != nil {
				m.logger.Error("分组恢复任务发生panic，正在清理资源",
					zap.String("task_id", mainTask.ID),
					zap.Any("panic", r))
				mainTask.Status = types.TaskStatusFailed
				mainTask.Error = fmt.Sprintf("分组恢复任务执行时发生panic: %v", r)
				m.updateTask(mainTask)
			}
			// 释放所有获取的信号量
			for i := 0; i < requiredSlots; i++ {
				<-m.semaphore
			}
		}()

		// 为分组任务创建带超时的上下文
		timeout := m.cfg.RestoreTimeout.ToDuration()
		if timeout <= 0 {
			timeout = 24 * time.Hour
		}
		// 分组任务可能需要更长时间，使用2倍超时
		groupCtx, cancel := context.WithTimeout(ctx, timeout*2)
		defer cancel()

		m.executeRestoreAll(groupCtx, mainTask, config)
	}()

	m.logger.Info("异步分组恢复任务已成功启动",
		zap.String("task_id", mainTask.ID),
		zap.Int("source_count", len(config.Restores)))
	return mainTask.ID, nil
}

// === 任务管理实现 ===

// GetTask 通过任务ID获取任务的详细信息和当前状态。
func (m *Manager) GetTask(taskID string) (*types.Task, error) {
	task, ok := m.tasks.Load(taskID)
	if !ok {
		return nil, fmt.Errorf("未找到ID为 '%s' 的任务", taskID)
	}
	return task.(*types.Task), nil
}

// ListTasks 列出所有任务，并按开始时间倒序排列。
func (m *Manager) ListTasks() ([]*types.Task, error) {
	var tasks []*types.Task

	m.tasks.Range(func(key, value interface{}) bool {
		tasks = append(tasks, value.(*types.Task))
		return true
	})

	// 按开始时间倒序排列，最新的任务在前面
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].StartTime.After(tasks[j].StartTime)
	})

	return tasks, nil
}

// CancelTask 尝试取消一个正在运行或排队的任务。
func (m *Manager) CancelTask(taskID string) error {
	// 使用全局锁防止并发状态检查和取消操作的竞态条件
	m.taskUpdateMutex.Lock()
	defer m.taskUpdateMutex.Unlock()

	// 检查任务是否存在
	taskInterface, exists := m.tasks.Load(taskID)
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	task := taskInterface.(*types.Task)
	if task.Status.IsTerminal() {
		return fmt.Errorf("任务无法取消，当前状态已经是终态: %s", task.Status)
	}

	// 通过调用存储的 cancel() 函数来取消与任务关联的上下文
	if cancelFunc, exists := m.taskContexts.LoadAndDelete(taskID); exists {
		cancelFunc.(context.CancelFunc)()
		m.logger.Info("任务取消请求已发送", zap.String("task_id", taskID))
		return nil
	}

	// 如果找不到 cancel 函数，说明是僵尸任务（执行上下文已丢失）
	// 正在执行的任务应该有对应的执行上下文，没有上下文就是僵尸任务
	m.logger.Warn("检测到僵尸任务，直接标记为已取消",
		zap.String("task_id", taskID),
		zap.String("status", string(task.Status)),
		zap.Duration("running_duration", time.Since(task.StartTime)))

	// 直接标记为已取消
	task.Status = types.TaskStatusCancelled
	task.Error = "僵尸任务被强制取消（执行上下文已丢失）"
	task.EndTime = time.Now()
	m.updateTask(task)
	return nil
}

// ClearOldTasks 手动触发清理，移除旧的、已完成的任务记录。
// 这是一个内部维护函数，也可以通过API暴露给用户。
func (m *Manager) ClearOldTasks() error {
	m.cleanupOldTasks()
	return nil
}

// Shutdown 优雅地关闭任务管理器。
func (m *Manager) Shutdown() error {
	m.logger.Info("开始关闭任务管理器")

	// 1. 停止定期清理的 goroutine
	if m.cleanupTicker != nil {
		m.cleanupTicker.Stop()
	}

	// 2. 发送关闭信号，让其他可能存在的后台 goroutine 退出
	close(m.shutdownChan)

	// 3. 取消所有仍在运行中的任务的上下文
	m.taskContexts.Range(func(key, value interface{}) bool {
		m.logger.Info("正在取消运行中的任务", zap.Any("task_id", key))
		value.(context.CancelFunc)()
		return true
	})

	// 等待一小段时间，让任务有机会处理取消信号并退出
	// 在测试环境中使用较短的等待时间
	waitTime := configpkg.DefaultShutdownWaitTime
	if m.cfg != nil && m.cfg.BackupTimeout > 0 && m.cfg.BackupTimeout.ToDuration() <= configpkg.TestEnvironmentThreshold {
		// 如果配置的超时时间很短（<=100ms），说明是测试环境，使用较短等待时间
		waitTime = configpkg.TestEnvironmentWaitTime
	}
	time.Sleep(waitTime)

	// 4. 最后再执行一次状态持久化，以保存所有任务的最终状态（可能是Canceled或Failed）
	if err := m.writeTasksToFile(); err != nil {
		m.logger.Error("关闭时持久化任务失败", zap.Error(err))
		return err
	}

	m.logger.Info("任务管理器已成功关闭")
	return nil
}

// updateTask 是一个内部辅助函数，用于更新内存中任务的状态，并在必要时将其持久化。
// 实现增量写入机制：只在重要状态变化时才写入磁盘，减少I/O操作。
func (m *Manager) updateTask(task *types.Task) {
	// 使用全局锁防止并发状态更新冲突
	m.taskUpdateMutex.Lock()
	defer m.taskUpdateMutex.Unlock()

	// 获取任务专用锁，防止同一任务的并发状态更新
	mutexInterface, _ := m.taskMutexes.LoadOrStore(task.ID, &sync.Mutex{})
	taskMutex := mutexInterface.(*sync.Mutex)

	taskMutex.Lock()
	defer taskMutex.Unlock()

	// 记录之前的状态，用于判断是否需要持久化
	var previousStatus types.TaskStatus
	if prevTaskInterface, exists := m.tasks.Load(task.ID); exists {
		previousStatus = prevTaskInterface.(*types.Task).Status
	}

	// 原子性更新任务状态
	if task.Status.IsTerminal() && task.EndTime.IsZero() {
		task.EndTime = time.Now() // 使用本地时区，与StartTime保持一致
	}
	m.tasks.Store(task.ID, task)

	// 增量写入策略：只在重要状态变化时才写入磁盘
	shouldPersist := false

	// 状态变化时需要持久化
	if previousStatus != task.Status {
		shouldPersist = true
	}

	// 终态任务总是需要持久化（确保最终状态被保存）
	if task.Status.IsTerminal() {
		shouldPersist = true
	}

	// 如果是第一次创建任务也需要持久化
	if previousStatus == "" {
		shouldPersist = true
	}

	if shouldPersist {
		if err := m.writeTasksToFile(); err != nil {
			m.logger.Error("更新并持久化任务状态失败",
				zap.String("taskID", task.ID),
				zap.String("status", string(task.Status)),
				zap.Error(err))
		}
	}

	// 清理已完成任务的锁，防止内存泄漏
	if task.Status.IsTerminal() {
		// 异步清理资源，避免阻塞主流程
		go m.cleanupTaskResources(task.ID, taskMutex)
	}
}

// getSemaphoreStatus 获取信号量状态用于调试
func (m *Manager) getSemaphoreStatus() (capacity, current int) {
	return cap(m.semaphore), len(m.semaphore)
}

// forceClearSemaphore 强制清理信号量（紧急情况使用）
func (m *Manager) forceClearSemaphore() {
	capacity, current := m.getSemaphoreStatus()
	m.logger.Warn("强制清理信号量",
		zap.Int("capacity", capacity),
		zap.Int("current_before", current))

	// 清空信号量
	for len(m.semaphore) > 0 {
		<-m.semaphore
	}

	_, currentAfter := m.getSemaphoreStatus()
	m.logger.Info("信号量强制清理完成",
		zap.Int("capacity", capacity),
		zap.Int("current_after", currentAfter))
}

// cleanupTaskResources 安全地清理任务相关资源，防止内存泄漏
func (m *Manager) cleanupTaskResources(taskID string, taskMutex *sync.Mutex) {
	// 使用超时防止无限等待
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 清理任务上下文
	if cancelFunc, exists := m.taskContexts.LoadAndDelete(taskID); exists {
		cancelFunc.(context.CancelFunc)()
	}

	// 安全清理任务锁（改进版本）
	done := make(chan struct{})
	go func() {
		defer func() {
			if r := recover(); r != nil {
				m.logger.Error("清理任务锁时发生panic",
					zap.String("task_id", taskID),
					zap.Any("panic", r))
			}
			close(done)
		}()

		// 使用带超时的锁获取，防止永久阻塞
		lockCtx, lockCancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer lockCancel()

		// 尝试获取锁，如果超时则放弃
		lockDone := make(chan struct{})
		go func() {
			defer close(lockDone)
			taskMutex.Lock()
			defer taskMutex.Unlock()
			m.taskMutexes.Delete(taskID)
		}()

		select {
		case <-lockDone:
			// 锁获取成功，清理完成
		case <-lockCtx.Done():
			// 锁获取超时，记录警告但不阻塞
			m.logger.Warn("获取任务锁超时，跳过锁清理",
				zap.String("task_id", taskID))
		}
	}()

	// 等待清理完成或超时
	select {
	case <-done:
		m.logger.Debug("任务资源清理完成", zap.String("task_id", taskID))
	case <-ctx.Done():
		m.logger.Warn("任务资源清理超时", zap.String("task_id", taskID))
		// 注意：即使超时，goroutine 也会在后台完成或超时退出
	}
}

// failTask 是一个辅助函数，用于将任务标记为失败状态并更新。
func (m *Manager) failTask(task *types.Task, err *types.BackupError) {
	task.Status = types.TaskStatusFailed
	if err != nil {
		task.Error = err.Message
		task.ErrorDetail = err
	} else {
		task.Error = "未知错误"
	}
	task.EndTime = time.Now()
	m.updateTask(task)

	// 确保失败的任务释放资源
	// 获取任务的 mutex（如果存在）
	if mutexInterface, exists := m.taskMutexes.Load(task.ID); exists {
		if taskMutex, ok := mutexInterface.(*sync.Mutex); ok {
			m.cleanupTaskResources(task.ID, taskMutex)
		}
	} else {
		// 如果没有 mutex，只清理上下文
		if cancelFunc, exists := m.taskContexts.LoadAndDelete(task.ID); exists {
			cancelFunc.(context.CancelFunc)()
			m.logger.Debug("清理失败任务的执行上下文", zap.String("task_id", task.ID))
		}
	}
}

// cancelTask 是一个辅助函数，用于将任务标记为已取消状态并更新。
func (m *Manager) cancelTask(task *types.Task) {
	task.Status = types.TaskStatusCancelled
	task.Error = "任务被用户或系统取消"
	m.updateTask(task)
}

// executeRestoreCallback 执行恢复成功回调，不设置超时控制
func (m *Manager) executeRestoreCallback(ctx context.Context, callback types.RestoreSuccessCallback, task *types.Task) {
	if callback == nil {
		return
	}

	// 直接使用传入的context，不额外设置超时
	if err := callback(ctx); err != nil {
		// 回调失败不影响恢复成功，记录到元数据
		task.Metadata["callback_error"] = err.Error()
		task.Metadata["callback_status"] = "failed"
		m.logger.Warn("恢复成功回调执行失败",
			zap.String("task_id", task.ID),
			zap.Error(err))
	} else {
		task.Metadata["callback_status"] = "success"
		m.logger.Info("恢复成功回调执行完成", zap.String("task_id", task.ID))
	}
}

// isTaskCancelled 是一个辅助函数，用于在任务的关键步骤之间检查上下文是否已被取消。
// 如果已取消，它会立即将任务状态更新为 "Canceled" 并返回 true。
func (m *Manager) isTaskCancelled(ctx context.Context, task *types.Task) bool {
	select {
	case <-ctx.Done():
		m.logger.Info("检测到任务上下文已取消", zap.String("task_id", task.ID))
		m.cancelTask(task)
		return true
	default:
		return false
	}
}

// writeTasksToFile 将内存中的所有任务原子地写入到持久化文件（tasks.json）中。
// 使用互斥锁来防止并发写操作导致文件损坏。
func (m *Manager) writeTasksToFile() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	tasksToSave := make(map[string]*types.Task)
	m.tasks.Range(func(key, value interface{}) bool {
		task := value.(*types.Task)
		// 创建任务的深拷贝以避免并发修改导致的JSON序列化问题
		taskCopy := m.deepCopyTask(task)
		tasksToSave[key.(string)] = taskCopy
		return true
	})

	data, err := json.MarshalIndent(tasksToSave, "", "  ")
	if err != nil {
		// 序列化失败是一个严重的编程错误，应立即报告。
		return fmt.Errorf("!!! 关键错误：序列化任务失败: %w", err)
	}

	// 阶段二改造：使用 StorageManager.WriteSystemFile 替代直接文件操作
	// 获取相对于备份根目录的路径
	relPath, err := filepath.Rel(m.storageMgr.GetBackupRoot(), m.tasksFilepath)
	if err != nil {
		// 如果无法获取相对路径，使用文件名
		relPath = filepath.Base(m.tasksFilepath)
	}

	return m.storageMgr.WriteSystemFile(relPath, data)
}

// deepCopyTask 创建任务的深拷贝，避免并发修改导致的JSON序列化问题
// 注意：此方法假设调用者已经持有适当的锁
func (m *Manager) deepCopyTask(task *types.Task) *types.Task {
	taskCopy := &types.Task{
		ID:          task.ID,
		Type:        task.Type,
		Source:      task.Source,
		Status:      task.Status,
		Progress:    task.Progress,
		Error:       task.Error,
		ErrorDetail: task.ErrorDetail, // BackupError 是不可变的，可以共享
		StartTime:   task.StartTime,
		EndTime:     task.EndTime,
		Description: task.Description,
	}

	// 深拷贝 SubTaskIDs slice
	if task.SubTaskIDs != nil {
		taskCopy.SubTaskIDs = make([]string, len(task.SubTaskIDs))
		copy(taskCopy.SubTaskIDs, task.SubTaskIDs)
	}

	// 通过手动拷贝实现Metadata的深拷贝，避免JSON序列化的并发问题
	if task.Metadata != nil {
		taskCopy.Metadata = make(map[string]interface{})
		// 手动拷贝每个键值对，避免并发修改导致的问题
		for k, v := range task.Metadata {
			// 对于基本类型，直接拷贝值
			// 对于复杂类型，这里做浅拷贝，因为在当前使用场景中metadata主要存储基本类型
			taskCopy.Metadata[k] = v
		}
	}

	return taskCopy
}

// loadTasksFromFile 从持久化文件中加载任务，并执行关键的故障恢复逻辑。
// 此方法应在SDK初始化时调用一次，以确保系统从任何意外中断中恢复到一致状态。
func (m *Manager) loadTasksFromFile() error {
	// 阶段二改造：使用 StorageManager.EnsureSystemDir 和 ReadSystemFile
	// 获取相对于备份根目录的路径
	relPath, err := filepath.Rel(m.storageMgr.GetBackupRoot(), m.tasksFilepath)
	if err != nil {
		// 如果无法获取相对路径，使用文件名
		relPath = filepath.Base(m.tasksFilepath)
	}

	// 确保任务文件目录存在
	if err := m.storageMgr.EnsureSystemDir(filepath.Dir(relPath)); err != nil {
		return fmt.Errorf("创建任务文件目录失败: %w", err)
	}

	data, err := m.storageMgr.ReadSystemFile(relPath)
	if err != nil {
		// 如果文件不存在，说明是首次启动，这不是一个错误。
		if os.IsNotExist(err) {
			// 清空任务列表，保证无任务（并发安全的方式）
			m.tasks.Range(func(key, value interface{}) bool {
				m.tasks.Delete(key)
				return true
			})
			m.logger.Info("任务文件未找到，全新启动", zap.String("path", m.tasksFilepath))
			return nil
		}
		return fmt.Errorf("读取任务文件失败: %w", err)
	}
	// 如果文件为空，也视为正常情况
	if len(data) == 0 {
		// 清空任务列表，保证无任务（并发安全的方式）
		m.tasks.Range(func(key, value interface{}) bool {
			m.tasks.Delete(key)
			return true
		})
		m.logger.Info("任务文件为空，清空任务列表", zap.String("path", m.tasksFilepath))
		return nil
	}

	var tasksOnDisk map[string]*types.Task
	if err := json.Unmarshal(data, &tasksOnDisk); err != nil {
		// 任务文件损坏时的自动修复机制
		m.logger.Error("任务文件损坏，正在执行自动修复",
			zap.Error(err),
			zap.String("file", m.tasksFilepath))

		// 1. 备份损坏的文件
		// 阶段二改造：使用 StorageManager.RenameSystemFile
		backupPath := m.tasksFilepath + ".corrupted." + time.Now().Format("20060102-150405")
		backupRelPath, _ := filepath.Rel(m.storageMgr.GetBackupRoot(), backupPath)
		if backupRelPath == "" {
			backupRelPath = filepath.Base(backupPath)
		}

		if backupErr := m.storageMgr.RenameSystemFile(relPath, backupRelPath); backupErr != nil {
			m.logger.Warn("备份损坏文件失败", zap.Error(backupErr))
		} else {
			m.logger.Info("已备份损坏的任务文件", zap.String("backup_path", backupPath))
		}

		// 2. 创建空的任务列表
		tasksOnDisk = make(map[string]*types.Task)

		// 3. 写入新的空任务文件
		if writeErr := m.writeTasksToFile(); writeErr != nil {
			return fmt.Errorf("创建新任务文件失败: %w", writeErr)
		}

		m.logger.Warn("任务文件已重建，服务将以空任务列表启动。请检查备份文件以恢复历史任务",
			zap.String("backup_file", backupPath))
	}

	// **故障恢复与补偿逻辑**
	for id, task := range tasksOnDisk {
		// 如果任务在程序崩溃时处于"正在运行"或"排队中"的状态...
		if task.Status == types.TaskStatusRunning || task.Status == types.TaskStatusPending {
			m.logger.Warn("发现一个被中断的任务，正在标记为失败以进行补偿",
				zap.String("taskID", task.ID),
				zap.String("type", string(task.Type)),
				zap.String("status", string(task.Status)))
			// 1. 将其状态标记为失败。
			task.Status = types.TaskStatusFailed
			task.Error = "任务因程序意外关闭或重启而失败"
			// 注意：这里不需要调用 m.updateTask()，因为我们会在循环结束后一次性写回所有更改。
		}
		m.tasks.Store(id, task)
	}

	m.logger.Info("成功从磁盘加载并协调任务", zap.Int("count", len(tasksOnDisk)))
	// 将协调后的任务状态写回文件
	return m.writeTasksToFile()
}

// === 同步操作接口实现 ===

// RunBackup 是执行备份的核心方法。
// 它被更高层的管理器调用，此时所有编排工作（如记录创建、锁获取）都已完成。
// 它的唯一职责是找到正确的提供者并执行备份。
func (m *Manager) RunBackup(ctx context.Context, record *types.BackupRecord) *types.BackupError {
	m.logger.Info("正在执行同步备份任务",
		zap.String("source", string(record.Source)),
		zap.String("type", string(record.Type)),
		zap.String("recordID", record.ID))
	return m.executeCoreBackup(ctx, record)
}

// executeCoreBackup 是同步和异步备份的统一核心实现。
// 它负责调用provider执行备份并处理结果，但不处理任务状态更新。
func (m *Manager) executeCoreBackup(ctx context.Context, record *types.BackupRecord) *types.BackupError {
	// 1. 获取对应数据源的提供者。
	prov, ok := m.providers[record.Source]
	if !ok {
		return berrors.NewTaskManagerError("PROVIDER_NOT_FOUND", "executeCoreBackup", fmt.Errorf("未找到数据源类型 %s 的提供者", record.Source), false)
	}

	// 2. 如果是增量备份，获取其父备份记录。
	var prevRecord *types.BackupRecord
	if record.Type == types.BackupTypeChainIncremental {
		if record.ParentID == "" {
			return berrors.NewTaskManagerError("INVALID_ARGUMENT", "executeCoreBackup", fmt.Errorf("增量备份记录 %s 缺少父备份ID", record.ID), false)
		}
		var err *types.BackupError
		prevRecord, err = m.storageMgr.GetChainBackupRecord(record.Source, record.ChainID, record.ParentID)
		if err != nil {
			return berrors.NewTaskManagerError("STORAGE_ERROR", "executeCoreBackup", fmt.Errorf("为增量备份获取父备份记录 %s 失败: %w", record.ParentID, err), false)
		}
	}

	// 3. 执行实际的备份。
	if err := prov.Backup(ctx, record, prevRecord); err != nil {
		m.logger.Error("备份提供者执行失败",
			zap.String("recordID", record.ID),
			zap.Error(err))

		// 尝试清理可能的残留数据（主要是Provider层数据，如ES快照）
		m.logger.Info("开始清理失败备份的残留数据", zap.String("record_id", record.ID))

		// 清理Provider层的数据（如ES快照、MySQL文件等）
		// 注意：不清理StorageManager中的元数据，保留失败记录用于调试
		// 恢复时会自动跳过失败的备份
		if cleanupErr := prov.Delete(ctx, record); cleanupErr != nil {
			m.logger.Warn("清理失败备份的Provider数据时出错",
				zap.String("record_id", record.ID),
				zap.Error(cleanupErr))
		} else {
			m.logger.Info("已清理失败备份的Provider数据", zap.String("record_id", record.ID))
		}

		return berrors.NewTaskManagerError("PROVIDER_EXECUTION_FAILED", "executeCoreBackup", fmt.Errorf("备份提供者为 %s 执行失败: %w", record.ID, err), false)
	}

	m.logger.Info("核心备份逻辑成功完成", zap.String("recordID", record.ID))
	return nil
}

// Restore 是执行恢复的核心方法（使用完全重建策略）。
func (m *Manager) Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError {
	m.logger.Info("正在启动同步恢复流程",
		zap.String("source", string(config.SourceType)),
		zap.String("backup_id", config.BackupID))

	// 步骤 1: 获取提供者。
	prov, ok := m.providers[config.SourceType]
	if !ok {
		return berrors.NewTaskManagerError("PROVIDER_NOT_FOUND", "Restore", fmt.Errorf("未找到数据源类型 %s 的提供者", config.SourceType), false)
	}

	// 步骤 2: 如果需要，创建预恢复快照
	var preRestoreSnapshot string
	if config.CreateSafetyBackup {
		m.logger.Info("开始为同步恢复创建预恢复快照",
			zap.String("source_name", config.SourceName))

		snapshot, err := m.createSinglePreRestoreSnapshot(ctx, "sync-restore", config.SourceType, config.SourceName)
		if err != nil {
			return berrors.NewTaskManagerError("PRE_RESTORE_SNAPSHOT_FAILED", "Restore",
				fmt.Errorf("创建预恢复快照失败: %w", err), false)
		}
		preRestoreSnapshot = snapshot
		m.logger.Info("成功创建预恢复快照",
			zap.String("snapshot_id", preRestoreSnapshot))
	}

	// 步骤 3: 从存储层获取要恢复的完整备份记录链。
	// 对于归档备份，这会返回一个只包含单个记录的切片。
	// 对于增量备份，这会返回从链的初始备份到指定备份点的所有记录，并按正确顺序排列。
	recordsToRestore, err := m.storageMgr.GetRestorationChain(config.SourceType, config.BackupID)
	if err != nil {
		// 如果创建了快照，需要清理
		if preRestoreSnapshot != "" {
			m.cleanupSinglePreRestoreSnapshot(context.Background(), "sync-restore", preRestoreSnapshot)
		}
		return berrors.NewTaskManagerError("STORAGE_ERROR", "Restore", fmt.Errorf("为备份 %s 获取恢复链失败: %w", config.BackupID, err), false)
	}

	if len(recordsToRestore) == 0 {
		// 如果创建了快照，需要清理
		if preRestoreSnapshot != "" {
			m.cleanupSinglePreRestoreSnapshot(context.Background(), "sync-restore", preRestoreSnapshot)
		}
		return berrors.NewTaskManagerError("BACKUP_NOT_FOUND", "Restore", fmt.Errorf("未找到用于恢复的备份记录，ID: %s", config.BackupID), false)
	}
	m.logger.Info("找到用于恢复的记录",
		zap.Int("count", len(recordsToRestore)),
		zap.String("backup_id", config.BackupID))

	// 步骤 4: 按顺序循环执行恢复。
	// 对于增量链，这将是：全量 -> 增量1 -> 增量2 ...
	var prevRecord *types.BackupRecord
	var restoreError error

	for _, record := range recordsToRestore {
		m.logger.Info("正在恢复链中的记录",
			zap.String("id", record.ID),
			zap.String("type", string(record.Type)))
		if err := prov.Restore(ctx, record, prevRecord); err != nil {
			m.logger.Error("在记录上恢复失败",
				zap.String("id", record.ID),
				zap.Error(err))
			restoreError = err
			break
		}
		prevRecord = record // 将当前记录作为下一次循环的父记录
	}

	// 步骤 5: 处理恢复结果
	if restoreError != nil {
		// 恢复失败，检查是否需要回滚
		if config.RollbackOnFailure && preRestoreSnapshot != "" {
			m.logger.Warn("同步恢复失败，开始回滚到预恢复快照",
				zap.String("snapshot_id", preRestoreSnapshot),
				zap.Error(restoreError))

			// 执行回滚
			if rollbackErr := m.rollbackSingleRestore(ctx, "sync-restore", config.SourceType, config.SourceName, preRestoreSnapshot); rollbackErr != nil {
				m.logger.Error("回滚失败！数据源可能处于不一致状态，需要人工干预！",
					zap.String("source_name", config.SourceName),
					zap.String("snapshot_id", preRestoreSnapshot),
					zap.Error(rollbackErr))
				// 清理快照
				if preRestoreSnapshot != "" {
					m.cleanupSinglePreRestoreSnapshot(context.Background(), "sync-restore", preRestoreSnapshot)
				}
				return berrors.NewTaskManagerError("RESTORE_AND_ROLLBACK_FAILED", "Restore",
					fmt.Errorf("恢复失败且回滚失败: %w. 回滚错误: %s", restoreError, rollbackErr.Error()), false)
			} else {
				m.logger.Info("成功回滚到预恢复快照",
					zap.String("source_name", config.SourceName))
			}
		}

		// 清理预恢复快照
		if preRestoreSnapshot != "" {
			m.cleanupSinglePreRestoreSnapshot(context.Background(), "sync-restore", preRestoreSnapshot)
		}

		return berrors.NewTaskManagerError("PROVIDER_EXECUTION_FAILED", "Restore", restoreError, false)
	}

	// 步骤 6: 恢复成功，清理预恢复快照
	if preRestoreSnapshot != "" {
		m.logger.Info("同步恢复成功，清理预恢复快照",
			zap.String("snapshot_id", preRestoreSnapshot))
		go m.cleanupSinglePreRestoreSnapshot(context.Background(), "sync-restore", preRestoreSnapshot)
	}

	m.logger.Info("同步恢复流程成功完成",
		zap.String("source", string(config.SourceType)),
		zap.String("backup_id", config.BackupID))
	return nil
}

// DeleteBackup 删除一个备份，可以是独立的归档备份，也可以是一整条增量链。
//
// 执行步骤：
// 1. 查找备份记录以确定其类型和元数据
// 2. 获取对应的数据源提供者
// 3. 调用提供者的Delete方法清理外部资源（如ES快照、数据库特定资源）
// 4. 根据备份类型执行相应的本地文件系统删除策略
//   - 归档备份：直接删除单个备份目录
//   - 增量链备份：删除整条链以保持数据完整性
//
// 注意：此方法确保外部资源和本地文件的一致性删除，避免资源泄露。
func (m *Manager) DeleteBackup(ctx context.Context, source types.SourceType, backupID string) *types.BackupError {
	m.logger.Info("请求删除备份",
		zap.String("source", string(source)),
		zap.String("backup_id", backupID))

	// 步骤1: 查找备份记录以确定其类型和元数据
	record, err := m.storageMgr.FindBackupRecord(source, backupID)
	if err != nil {
		return berrors.NewTaskManagerError("STORAGE_ERROR", "FindBackupRecord", fmt.Errorf("删除前查找备份 %s 失败: %w", backupID, err), false)
	}

	// 步骤2: 获取对应的数据源提供者
	prov, ok := m.providers[record.Source]
	if !ok {
		return berrors.NewTaskManagerError("PROVIDER_NOT_FOUND", "DeleteBackup", fmt.Errorf("未找到数据源类型 %s 的提供者", record.Source), false)
	}

	// 步骤3: 调用提供者的Delete方法清理外部资源
	// - 对于Elasticsearch：删除集群中的快照
	// - 对于MySQL：空操作，因为所有数据都在本地文件系统中
	// 即使提供者删除失败，也会继续尝试删除本地文件，确保不留下孤儿文件
	if err := prov.Delete(ctx, record); err != nil {
		m.logger.Warn("提供者删除操作失败，将继续尝试删除文件系统记录",
			zap.String("recordID", record.ID),
			zap.Error(err))
	}

	// 步骤4: 根据备份类型执行相应的本地文件系统删除策略
	if record.IsPartOfChain() {
		// 增量链备份：必须删除整条链以保持数据完整性
		// 删除单个增量备份会破坏链的连续性，导致后续备份无法使用
		m.logger.Warn("备份属于增量链，将删除整条链", zap.String("chainID", record.ChainID))
		if err := m.storageMgr.DeleteIncrementalChain(source, record.ChainID); err != nil {
			return berrors.NewTaskManagerError("STORAGE_ERROR", "DeleteIncrementalChain", fmt.Errorf("删除增量链失败: %w", err), false)
		}
	} else {
		// 归档备份：可以安全地单独删除，不影响其他备份
		if err := m.storageMgr.DeleteBackupRecord(record); err != nil {
			return berrors.NewTaskManagerError("STORAGE_ERROR", "DeleteBackupRecord", fmt.Errorf("删除归档备份失败: %w", err), false)
		}
	}

	m.logger.Info("成功删除备份", zap.String("backup_id", backupID))
	return (*types.BackupError)(nil)
}

// === 异步任务执行体 ===

// executeBackupWithContext 是单个异步备份任务的实际执行体。
func (m *Manager) executeBackupWithContext(ctx context.Context, task *types.Task, sourceType types.SourceType, sourceName string, backupType types.BackupType) {
	// 更新状态为运行中
	task.Status = types.TaskStatusRunning
	task.Progress = 5
	m.updateTask(task)

	// 1. 对于增量备份，先确定目标链ID
	var targetChainID string
	if backupType == types.BackupTypeChainIncremental {
		// 检查是否指定了目标链ID
		if task.Metadata != nil {
			if chainID, ok := task.Metadata["target_chain_id"].(string); ok && chainID != "" {
				targetChainID = chainID
			}
		}

		if targetChainID == "" {
			// 如果没有指定目标链，使用最新的链
			chains, err := m.storageMgr.ListIncrementalChains(sourceType, sourceName)
			m.logger.Info("任务管理器内部链查找调试",
				zap.String("source_type", string(sourceType)),
				zap.String("source_name", sourceName),
				zap.Int("chains_count", len(chains)),
				zap.Error(err))
			if err != nil {
				m.logger.Error("任务管理器内部链查找失败",
					zap.String("source_type", string(sourceType)),
					zap.String("source_name", sourceName),
					zap.Error(err))
				m.failTask(task, berrors.NewTaskManagerError("CHAIN_LOOKUP_ERROR", "executeBackup", fmt.Errorf("查找增量链时出错: %w", err), false))
				return
			}
			if len(chains) == 0 {
				// 智能处理：自动转换为链初始备份
				sourceKey := fmt.Sprintf("%s-%s", sourceType, sourceName)
				m.logger.Info("未找到增量链，自动转换为链初始备份",
					zap.String("source", sourceKey),
					zap.String("requested_type", string(types.BackupTypeChainIncremental)),
					zap.String("actual_type", string(types.BackupTypeChainInitial)))
				backupType = types.BackupTypeChainInitial
				// 更新任务元数据
				task.Metadata["backup_type"] = string(backupType)
				m.updateTask(task)
			} else {
				// 按时间戳对链进行排序，找到最新的链
				sort.Slice(chains, func(i, j int) bool {
					// 检查备份数组是否为空，避免索引越界
					if len(chains[i].Backups) == 0 || len(chains[j].Backups) == 0 {
						return len(chains[i].Backups) > len(chains[j].Backups)
					}
					// 比较链中最后一个备份的时间戳
					iLast := len(chains[i].Backups) - 1
					jLast := len(chains[j].Backups) - 1
					return chains[i].Backups[iLast].Timestamp.After(chains[j].Backups[jLast].Timestamp)
				})
				targetChainID = chains[0].ChainID
			}
		}
	}

	// 2. 创建备份记录元数据
	record, err := m.storageMgr.CreateBackupRecord(sourceType, sourceName, backupType, targetChainID, task.Description)
	if err != nil {
		m.failTask(task, err)
		return
	}
	task.Metadata["backup_record_path"] = record.Path // 记录路径，便于调试

	task.Progress = 20
	m.updateTask(task)

	// 2. 检查任务是否在创建记录后被取消
	if m.isTaskCancelled(ctx, task) {
		// 如果任务被取消，需要清理已创建的空备份目录和元数据
		m.logger.Warn("任务在Provider执行前被取消，正在清理资源",
			zap.String("task_id", task.ID),
			zap.String("record_id", record.ID))
		if delErr := m.storageMgr.DeleteBackupRecord(record); delErr != nil {
			m.logger.Error("清理被取消任务的资源时失败",
				zap.String("record_id", record.ID),
				zap.Error(delErr))
		}
		return
	}

	// 3. 执行备份
	providerErr := m.executeCoreBackup(ctx, record)
	if providerErr != nil {
		if errors.Is(providerErr, context.Canceled) {
			m.cancelTask(task)
		} else {
			m.failTask(task, providerErr)
		}
		// 备份失败后，保存失败的记录状态（使用原子性方法）
		record.Status = types.BackupStatusFailed
		record.Error = providerErr.Message
		record.ErrorDetail = providerErr
		if saveErr := m.storageMgr.SaveBackupRecordAtomic(record); saveErr != nil {
			m.logger.Error("保存失败的备份记录时出错",
				zap.String("recordID", record.ID),
				zap.Error(saveErr))
		}
		return
	}

	task.Progress = 90
	m.updateTask(task)

	// 4. 保存成功的备份记录（使用原子性方法）
	record.Status = types.BackupStatusCompleted
	if err := m.storageMgr.SaveBackupRecordAtomic(record); err != nil {
		m.failTask(task, err)
		return
	}

	// 5. 将备份记录信息保存到任务元数据中，以便API查询
	task.Metadata["backup_record_id"] = record.ID
	task.Metadata["backup_size"] = types.FormatBackupSize(record.Size)

	// 6. 完成任务
	task.Status = types.TaskStatusCompleted
	task.Progress = 100
	m.updateTask(task)

	m.logger.Info("异步备份任务执行完成",
		zap.String("task_id", task.ID),
		zap.String("backup_id", record.ID))
}

// executeRestoreWithContext 是单个异步恢复任务的实际执行体（使用完全重建策略）。
func (m *Manager) executeRestoreWithContext(ctx context.Context, task *types.Task, config types.RestoreConfig) {
	// 更新状态为运行中
	task.Status = types.TaskStatusRunning
	task.Progress = 5
	m.updateTask(task)

	// 1. 如果需要，创建预恢复快照
	var preRestoreSnapshot string
	if config.CreateSafetyBackup {
		m.logger.Info("开始为单个恢复任务创建预恢复快照",
			zap.String("task_id", task.ID),
			zap.String("source_name", config.SourceName))

		snapshot, err := m.createSinglePreRestoreSnapshot(ctx, task.ID, config.SourceType, config.SourceName)
		if err != nil {
			m.failTask(task, berrors.NewTaskManagerError("PRE_RESTORE_SNAPSHOT_FAILED", "executeRestoreWithContext",
				fmt.Errorf("创建预恢复快照失败: %w", err), false))
			return
		}
		preRestoreSnapshot = snapshot
		m.logger.Info("成功创建预恢复快照",
			zap.String("task_id", task.ID),
			zap.String("snapshot_id", preRestoreSnapshot))
	}

	// 2. 获取恢复链
	restoreChain, err := m.storageMgr.GetRestorationChain(config.SourceType, config.BackupID)
	if err != nil {
		// 如果创建了快照，需要清理
		if preRestoreSnapshot != "" {
			m.cleanupSinglePreRestoreSnapshot(context.Background(), task.ID, preRestoreSnapshot)
		}
		m.failTask(task, err)
		return
	}
	if len(restoreChain) == 0 {
		// 如果创建了快照，需要清理
		if preRestoreSnapshot != "" {
			m.cleanupSinglePreRestoreSnapshot(context.Background(), task.ID, preRestoreSnapshot)
		}
		m.failTask(task, berrors.NewTaskManagerError("BACKUP_NOT_FOUND", "executeRestore", fmt.Errorf("未找到备份ID %s", config.BackupID), false))
		return
	}

	task.Progress = 15
	m.updateTask(task)

	// 3. 按顺序恢复每个备份点
	provider := m.providers[config.SourceType]
	var prevRecord *types.BackupRecord
	var restoreError error

	for i, record := range restoreChain {
		if m.isTaskCancelled(ctx, task) {
			// 如果创建了快照，需要清理
			if preRestoreSnapshot != "" {
				m.cleanupSinglePreRestoreSnapshot(context.Background(), task.ID, preRestoreSnapshot)
			}
			return
		}

		// 更新详细进度
		task.Progress = 15 + float64(i)/float64(len(restoreChain))*75
		task.Metadata["restoring_record_id"] = record.ID
		m.updateTask(task)

		if err := provider.Restore(ctx, record, prevRecord); err != nil {
			restoreError = err
			break
		}
		prevRecord = record
	}

	// 4. 处理恢复结果
	if restoreError != nil {
		// 恢复失败，检查是否需要回滚
		if config.RollbackOnFailure && preRestoreSnapshot != "" {
			m.logger.Warn("恢复失败，开始回滚到预恢复快照",
				zap.String("task_id", task.ID),
				zap.String("snapshot_id", preRestoreSnapshot),
				zap.Error(restoreError))

			// 执行回滚
			if rollbackErr := m.rollbackSingleRestore(ctx, task.ID, config.SourceType, config.SourceName, preRestoreSnapshot); rollbackErr != nil {
				m.logger.Error("回滚失败！数据源可能处于不一致状态，需要人工干预！",
					zap.String("task_id", task.ID),
					zap.String("source_name", config.SourceName),
					zap.String("snapshot_id", preRestoreSnapshot),
					zap.Error(rollbackErr))
				task.Error = fmt.Sprintf("恢复失败且回滚失败: %s. 数据可能不一致，需要人工检查. 回滚错误: %s", restoreError.Error(), rollbackErr.Error())
			} else {
				m.logger.Info("成功回滚到预恢复快照",
					zap.String("task_id", task.ID),
					zap.String("source_name", config.SourceName))
				task.Error = fmt.Sprintf("恢复失败，已回滚到原始状态: %s", restoreError.Error())
			}
		}

		// 清理预恢复快照
		if preRestoreSnapshot != "" {
			m.cleanupSinglePreRestoreSnapshot(context.Background(), task.ID, preRestoreSnapshot)
		}

		// 处理不同类型的错误
		if errors.Is(restoreError, context.Canceled) {
			m.cancelTask(task)
		} else {
			// 将普通错误转换为 BackupError
			var backupErr *types.BackupError
			if errors.As(restoreError, &backupErr) {
				m.failTask(task, backupErr)
			} else {
				m.failTask(task, berrors.NewTaskManagerError("RESTORE_FAILED", "executeRestoreWithContext", restoreError, false))
			}
		}
		return
	}

	// 5. 恢复成功，完成任务
	task.Status = types.TaskStatusCompleted
	task.Progress = 100

	// 执行成功回调
	m.executeRestoreCallback(ctx, config.OnSuccess, task)

	// 清理预恢复快照（成功后清理）
	if preRestoreSnapshot != "" {
		m.logger.Info("恢复成功，清理预恢复快照",
			zap.String("task_id", task.ID),
			zap.String("snapshot_id", preRestoreSnapshot))
		go m.cleanupSinglePreRestoreSnapshot(context.Background(), task.ID, preRestoreSnapshot)
	}

	m.updateTask(task)

	m.logger.Info("异步恢复任务执行完成",
		zap.String("task_id", task.ID),
		zap.String("backup_id", config.BackupID))
}

// executeBackupAll 是分组备份任务的调度器。
func (m *Manager) executeBackupAll(ctx context.Context, mainTask *types.Task, req types.BackupAllRequest) {
	mainTask.Status = types.TaskStatusRunning
	m.updateTask(mainTask)

	// 当前版本只实现了并行的非原子性备份。
	// 一个完整的实现会在这里根据 req.Atomic 的值来选择不同的执行策略（例如串行执行）。
	m.executeParallelBackupAll(ctx, mainTask, req)
}

// executeParallelBackupAll 并行地执行所有分组备份中的子任务。
func (m *Manager) executeParallelBackupAll(ctx context.Context, mainTask *types.Task, req types.BackupAllRequest) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	completedCount := 0
	failedCount := 0
	var completedBackupIDs []string        // 记录已完成的备份ID，用于可能的清理
	var subTaskErrors []*types.BackupError // 用于聚合所有失败子任务的详细错误

	m.logger.Info("开始并行执行分组备份",
		zap.String("task_id", mainTask.ID),
		zap.Int("source_count", len(req.Sources)),
		zap.Bool("cleanup_on_failure", req.CleanupOnFailure))

	// 为每个子备份启动一个goroutine
	for i, source := range req.Sources {
		wg.Add(1)
		go func(index int, src types.BackupRequest) {
			defer wg.Done()

			// 移除信号量获取逻辑：
			// 备份任务之间可以并发，不需要信号量限制

			// 检查主任务是否在排队时被取消
			if m.isTaskCancelled(ctx, mainTask) {
				return
			}

			// 创建子任务
			subTask := &types.Task{
				ID:          mainTask.SubTaskIDs[index],
				Type:        types.BackupTask,
				Source:      src.SourceType,
				Status:      types.TaskStatusRunning,
				Progress:    0,
				StartTime:   time.Now(),
				Description: src.Description,
				Metadata: map[string]interface{}{
					"parent_task_id": mainTask.ID,
					"source_name":    src.SourceName,
					"backup_type":    string(src.BackupType),
					"source_type":    string(src.SourceType), // 修复：显式保存source_type
					"backup_name":    types.GenerateBackupName(src.SourceType, src.SourceName, src.BackupType, time.Now()),
				},
			}
			m.updateTask(subTask) // 创建后立即更新一次状态
			m.logger.Info("分组备份的子任务开始执行",
				zap.String("sub_task_id", subTask.ID),
				zap.String("source", src.SourceName))

			// 执行单个备份的核心逻辑
			var backupRecordID string
			err := m.executeSubBackup(ctx, subTask, src, &backupRecordID)

			// 更新子任务的最终状态
			if err != nil {
				// 使用 failTask 来标准化失败处理
				m.failTask(subTask, berrors.NewTaskManagerError("SUB_TASK_FAILED", "executeSubBackup", err, false))
				m.logger.Error("分组备份的子任务执行失败",
					zap.String("sub_task_id", subTask.ID),
					zap.Error(err))
			} else {
				subTask.Status = types.TaskStatusCompleted
				subTask.Progress = 100
				subTask.Metadata["backup_record_id"] = backupRecordID
				m.updateTask(subTask)
				m.logger.Info("分组备份的子任务执行成功",
					zap.String("sub_task_id", subTask.ID),
					zap.String("backup_id", backupRecordID))
			}

			// 更新主任务进度和统计信息（使用互斥锁保护）
			mu.Lock()
			if err != nil {
				failedCount++
				// 聚合详细错误信息
				if subTask.ErrorDetail != nil {
					subTaskErrors = append(subTaskErrors, subTask.ErrorDetail)
				}
			} else {
				completedCount++
				if backupRecordID != "" {
					completedBackupIDs = append(completedBackupIDs, backupRecordID)
				}
			}

			// 更新主任务的汇总信息
			// 创建主任务的深拷贝以避免并发修改竞争条件
			taskCopy := m.deepCopyTask(mainTask)
			taskCopy.Metadata["completed_count"] = completedCount
			taskCopy.Metadata["failed_count"] = failedCount

			// 计算总大小（需要从所有已完成的子任务中获取）
			var totalSizeBytes int64
			for _, subTaskID := range mainTask.SubTaskIDs {
				if subTask, err := m.GetTask(subTaskID); err == nil && subTask.Status == types.TaskStatusCompleted {
					if recordID, ok := subTask.Metadata["backup_record_id"].(string); ok {
						if record, err := m.storageMgr.GetBackupRecord(subTask.Source, recordID); err == nil {
							totalSizeBytes += record.Size
						}
					}
				}
			}
			taskCopy.Metadata["total_size"] = types.FormatBackupSize(totalSizeBytes)

			progress := float64(completedCount+failedCount) / float64(len(req.Sources)) * 100
			taskCopy.Progress = progress
			m.updateTask(taskCopy)
			mu.Unlock()

		}(i, source)
	}

	// 等待所有子任务的goroutine完成
	wg.Wait()

	// 更新主任务的最终状态
	if failedCount == 0 {
		// 全部成功
		mainTask.Status = types.TaskStatusCompleted
		m.logger.Info("分组备份全部成功",
			zap.String("task_id", mainTask.ID),
			zap.Int("completed", completedCount))
	} else {
		// 如果有任何失败，将聚合的错误信息存入主任务
		if len(subTaskErrors) > 0 {
			mainTask.Metadata["sub_task_errors"] = subTaskErrors
		}

		if req.CleanupOnFailure {
			// 任何失败都会触发清理
			mainTask.Status = types.TaskStatusFailed
			mainTask.Error = fmt.Sprintf("检测到%d个失败，已启动对%d个成功备份的清理", failedCount, len(completedBackupIDs))
			m.logger.Warn("分组备份失败，开始清理已完成的备份",
				zap.String("task_id", mainTask.ID),
				zap.Int("failed_count", failedCount),
				zap.Int("cleanup_count", len(completedBackupIDs)))
			// 同步地启动清理流程，确保原子性
			m.cleanupCompletedBackups(ctx, mainTask, completedBackupIDs)
		} else {
			// 未启用失败清理：标记为失败并提供错误计数
			mainTask.Status = types.TaskStatusFailed
			mainTask.Error = fmt.Sprintf("%d个成功，%d个失败", completedCount, failedCount)
			m.logger.Warn("分组备份部分成功但有失败",
				zap.String("task_id", mainTask.ID),
				zap.Int("completed_count", completedCount),
				zap.Int("failed_count", failedCount))
		}
	}

	mainTask.Progress = 100
	m.updateTask(mainTask)
	m.logger.Info("分组备份任务完成",
		zap.String("task_id", mainTask.ID),
		zap.String("final_status", string(mainTask.Status)))
}

// executeSubBackup 是执行单个子备份任务的核心逻辑。
// 它现在复用 executeBackupWithContext 的功能。
func (m *Manager) executeSubBackup(ctx context.Context, subTask *types.Task, req types.BackupRequest, backupRecordID *string) error {
	// 如果指定了TargetChainID，存储到任务元数据中
	if req.TargetChainID != "" {
		if subTask.Metadata == nil {
			subTask.Metadata = make(map[string]interface{})
		}
		subTask.Metadata["target_chain_id"] = req.TargetChainID
	}

	// executeBackupWithContext 已经包含了创建备份记录、调用 Provider、保存记录和更新任务状态的逻辑。
	// 它会直接修改传入的 subTask 对象。
	m.executeBackupWithContext(ctx, subTask, req.SourceType, req.SourceName, req.BackupType)

	// 检查 subTask 的最终状态来判断是否成功
	if subTask.Status == types.TaskStatusCompleted {
		// 从 subTask 的 Metadata 中获取 backup_record_id
		if id, ok := subTask.Metadata["backup_record_id"].(string); ok {
			*backupRecordID = id
		} else {
			// 理论上不应该发生，因为 executeBackupWithContext 成功时会设置此元数据
			return fmt.Errorf("executeBackupWithContext 成功但未设置 backup_record_id")
		}
		return nil
	} else {
		// 如果 subTask 未完成，则返回其错误信息
		if subTask.ErrorDetail != nil {
			return subTask.ErrorDetail
		}
		return fmt.Errorf("子备份任务未成功完成: %s", subTask.Error)
	}
}

// cleanupCompletedBackups 在分组备份失败且开启了 CleanupOnFailure 时，同步地清理掉已经成功完成的备份。
func (m *Manager) cleanupCompletedBackups(ctx context.Context, mainTask *types.Task, completedBackupIDs []string) {
	m.logger.Info("开始清理分组备份中已完成的部分",
		zap.String("main_task_id", mainTask.ID),
		zap.Int("backup_count", len(completedBackupIDs)))

	cleanedCount := 0

	// 遍历所有已完成的备份ID进行清理
	for _, backupID := range completedBackupIDs {
		var subTask *types.Task
		var found bool

		// 在主任务的子任务列表中查找对应的子任务
		for _, subTaskID := range mainTask.SubTaskIDs {
			task, err := m.GetTask(subTaskID)
			if err != nil {
				continue // 如果找不到任务，跳过
			}
			if bid, ok := task.Metadata["backup_record_id"]; ok && bid == backupID {
				subTask = task
				found = true
				break
			}
		}

		if !found {
			m.logger.Error("无法找到备份ID对应的子任务，跳过清理", zap.String("backup_id", backupID))
			continue
		}

		// 从子任务的元数据中安全地获取 sourceType 和 backupType
		sourceTypeStr, ok := subTask.Metadata["source_type"].(string)
		if !ok {
			m.logger.Error("子任务元数据中缺少或无效的source_type，跳过清理", zap.String("sub_task_id", subTask.ID))
			continue
		}
		sourceType := types.SourceType(sourceTypeStr)

		backupTypeStr, ok := subTask.Metadata["backup_type"].(string)
		if !ok {
			m.logger.Error("子任务元数据中缺少或无效的backup_type，跳过清理", zap.String("sub_task_id", subTask.ID))
			continue
		}
		backupType := types.BackupType(backupTypeStr)

		// 关键逻辑：对于增量备份的清理策略
		if backupType == types.BackupTypeChainIncremental {
			// 增量备份不能单独清理，会破坏链完整性
			m.logger.Warn("跳过清理增量备份，因为自动清理会破坏增量链完整性",
				zap.String("backup_id", backupID),
				zap.String("source_type", string(sourceType)),
				zap.String("backup_type", string(backupType)))
			continue
		} else if backupType == types.BackupTypeChainInitial {
			// ChainInitial 可以安全清理，因为它是链的起点，清理它会清理整条链
			m.logger.Info("清理初始链备份，这将删除整条增量链",
				zap.String("backup_id", backupID),
				zap.String("source_type", string(sourceType)),
				zap.String("backup_type", string(backupType)))
			// 继续执行清理逻辑
		}

		// 对于归档备份，直接调用 DeleteBackup 进行清理
		if err := m.DeleteBackup(context.Background(), sourceType, backupID); err != nil {
			m.logger.Error("清理备份失败",
				zap.String("backup_id", backupID),
				zap.String("source_type", string(sourceType)),
				zap.Error(err))
		} else {
			cleanedCount++
			m.logger.Info("成功清理备份",
				zap.String("backup_id", backupID),
				zap.String("source_type", string(sourceType)))
		}
	}

	m.logger.Info("分组备份清理完成",
		zap.String("main_task_id", mainTask.ID),
		zap.Int("total_to_clean", len(completedBackupIDs)),
		zap.Int("cleaned_count", cleanedCount))
}

// executeRestoreAll 是分组恢复任务的调度器（使用完全重建策略）。
// 这是整个系统中最复杂的函数之一，因为它需要处理原子性、预恢复快照和失败回滚。
func (m *Manager) executeRestoreAll(ctx context.Context, mainTask *types.Task, config types.BatchRestoreConfig) {
	mainTask.Status = types.TaskStatusRunning
	mainTask.Progress = 5
	mainTask.Metadata["phase"] = "初始化中"
	m.updateTask(mainTask)

	var preRestoreSnapshots map[string]string
	// 步骤 1: 如果需要，为所有目标数据源创建预恢复快照。
	shouldCreateSnapshots := false // 根据每个restore config决定
	for _, restoreConfig := range config.Restores {
		if restoreConfig.CreateSafetyBackup {
			shouldCreateSnapshots = true
			break
		}
	}

	if shouldCreateSnapshots {
		mainTask.Progress = 10
		mainTask.Metadata["phase"] = "创建预恢复快照"
		m.updateTask(mainTask)
		// 转换为RestoreRequest格式以复用现有的快照创建逻辑
		var snapshotSources []types.RestoreRequest
		for _, restoreConfig := range config.Restores {
			if restoreConfig.CreateSafetyBackup {
				snapshotSources = append(snapshotSources, types.RestoreRequest{
					SourceType:  restoreConfig.SourceType,
					SourceName:  restoreConfig.SourceName,
					BackupID:    restoreConfig.BackupID,
					Description: restoreConfig.Description,
				})
			}
		}
		preRestoreSnapshots = m.createPreRestoreSnapshots(ctx, mainTask, snapshotSources)
		// 如果创建快照失败，且要求原子性，则整个任务失败。
		if len(preRestoreSnapshots) != len(snapshotSources) && config.Atomic {
			m.failTask(mainTask, berrors.NewTaskManagerError("PRE_RESTORE_SNAPSHOT_FAILED", "executeRestoreAll",
				fmt.Errorf("未能为所有数据源创建预恢复快照（需要 %d，实际 %d），原子恢复中止", len(snapshotSources), len(preRestoreSnapshots)), false))
			// 清理已创建的快照
			if len(preRestoreSnapshots) > 0 {
				m.logger.Info("清理部分创建的预恢复快照",
					zap.String("task_id", mainTask.ID),
					zap.Int("snapshot_count", len(preRestoreSnapshots)))
				m.cleanupPreRestoreSnapshots(ctx, mainTask.ID, preRestoreSnapshots)
			}
			return
		}
		mainTask.Progress = 20
		mainTask.Metadata["phase"] = "预恢复快照创建完成"
		m.updateTask(mainTask)
	}

	// 步骤 2: 根据原子性要求，选择串行或并行执行策略。
	if config.Atomic {
		mainTask.Metadata["phase"] = "串行恢复（原子模式）"
		m.updateTask(mainTask)
		m.executeSerialRestoreAll(ctx, mainTask, config, preRestoreSnapshots)
	} else {
		mainTask.Metadata["phase"] = "并行恢复（非原子模式）"
		m.updateTask(mainTask)
		m.executeParallelRestoreAll(ctx, mainTask, config, preRestoreSnapshots)
	}
}

// executeParallelRestoreAll 并行地执行所有分组恢复中的子任务（非原子性）。
func (m *Manager) executeParallelRestoreAll(ctx context.Context, mainTask *types.Task, config types.BatchRestoreConfig, snapshots map[string]string) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	completedCount := 0
	failedCount := 0
	var completedSources []types.RestoreRequest // 跟踪已完成的恢复，用于回滚

	m.logger.Info("开始并行执行分组恢复",
		zap.String("task_id", mainTask.ID),
		zap.Int("source_count", len(config.Restores)))

	for i, restoreConfig := range config.Restores {
		wg.Add(1)
		go func(index int, cfg types.RestoreConfig) {
			defer wg.Done()

			// 注意：信号量已在RestoreAllAsync中预获取，这里不需要再次获取
			if m.isTaskCancelled(ctx, mainTask) {
				return
			}

			// 创建子任务
			subTask := &types.Task{
				ID:          mainTask.SubTaskIDs[index],
				Type:        types.RestoreTask,
				Source:      cfg.SourceType,
				Status:      types.TaskStatusRunning,
				StartTime:   time.Now(),
				Description: cfg.Description,
				Metadata: map[string]interface{}{
					"parent_task_id": mainTask.ID,
					"restore_config": cfg,
				},
			}
			m.updateTask(subTask)

			// 执行恢复
			err := m.executeSubRestore(ctx, subTask, cfg)

			// 更新子任务状态
			if err != nil {
				m.failTask(subTask, berrors.NewTaskManagerError("SUB_TASK_FAILED", "executeSubRestore", err, false))
			} else {
				subTask.Status = types.TaskStatusCompleted
				subTask.Progress = 100
				m.updateTask(subTask)
			}

			// 更新主任务进度和跟踪已完成的恢复
			mu.Lock()
			if err != nil {
				failedCount++
			} else {
				completedCount++
				// 跟踪已完成的恢复，用于可能的回滚
				completedSources = append(completedSources, types.RestoreRequest{
					SourceType:  cfg.SourceType,
					SourceName:  cfg.SourceName,
					BackupID:    cfg.BackupID,
					Description: cfg.Description,
				})
			}
			progress := 20 + float64(completedCount+failedCount)/float64(len(config.Restores))*75
			mainTask.Progress = progress
			mainTask.Metadata["phase"] = fmt.Sprintf("并行恢复中 (%d/%d完成)",
				completedCount+failedCount, len(config.Restores))
			m.updateTask(mainTask)
			mu.Unlock()
		}(i, restoreConfig)
	}

	wg.Wait()

	// 更新主任务最终状态
	if failedCount > 0 {
		mainTask.Status = types.TaskStatusFailed
		mainTask.Error = fmt.Sprintf("%d个成功，%d个失败", completedCount, failedCount)

		// 检查是否需要回滚已完成的恢复
		shouldRollback := false
		for _, restoreConfig := range config.Restores {
			if restoreConfig.RollbackOnFailure {
				shouldRollback = true
				break
			}
		}

		if shouldRollback && len(completedSources) > 0 && len(snapshots) > 0 {
			mainTask.Metadata["phase"] = "执行回滚"
			m.updateTask(mainTask)
			m.logger.Warn("并行恢复失败，开始回滚已完成的部分",
				zap.String("task_id", mainTask.ID),
				zap.Int("rollback_count", len(completedSources)))
			// 同步执行回滚
			m.rollbackCompletedRestores(mainTask.ID, completedSources, snapshots)
			// 回滚后，也清理快照
			if len(snapshots) > 0 {
				m.logger.Info("回滚操作后，开始清理预恢复快照", zap.Int("count", len(snapshots)))
				go m.cleanupPreRestoreSnapshots(context.Background(), mainTask.ID, snapshots)
			}
		} else {
			// 如果不需要回滚，直接清理预恢复快照
			if len(snapshots) > 0 {
				m.logger.Info("并行恢复失败，将清理临时快照", zap.Int("count", len(snapshots)))
				go m.cleanupPreRestoreSnapshots(context.Background(), mainTask.ID, snapshots)
			}
		}
	} else {
		mainTask.Status = types.TaskStatusCompleted
		// 执行分组恢复成功回调
		m.executeRestoreCallback(ctx, config.OnSuccess, mainTask)

		// 成功时清理预恢复快照
		if len(snapshots) > 0 {
			m.logger.Info("并行恢复成功，将清理临时快照", zap.Int("count", len(snapshots)))
			go m.cleanupPreRestoreSnapshots(context.Background(), mainTask.ID, snapshots)
		}
	}
	mainTask.Progress = 100
	m.updateTask(mainTask)

	m.logger.Info("并行分组恢复完成",
		zap.String("task_id", mainTask.ID),
		zap.String("status", string(mainTask.Status)))
}

// executeSubRestore 是执行单个子恢复任务的核心逻辑。
func (m *Manager) executeSubRestore(ctx context.Context, subTask *types.Task, config types.RestoreConfig) error {
	if m.isTaskCancelled(ctx, subTask) {
		return ctx.Err()
	}

	// 获取恢复链
	restoreChain, err := m.storageMgr.GetRestorationChain(config.SourceType, config.BackupID)
	if err != nil {
		return fmt.Errorf("获取恢复链失败: %w", err)
	}

	subTask.Progress = 10
	m.updateTask(subTask)

	// 按顺序恢复每个备份点
	provider, _ := m.providers[config.SourceType]
	var prevRecord *types.BackupRecord
	for i, record := range restoreChain {
		if m.isTaskCancelled(ctx, subTask) {
			return ctx.Err()
		}

		subTask.Progress = 10 + float64(i)/float64(len(restoreChain))*80
		subTask.Metadata["restoring_record_id"] = record.ID
		m.updateTask(subTask)

		if err := provider.Restore(ctx, record, prevRecord); err != nil {
			return fmt.Errorf("在记录 %s 上恢复失败: %w", record.ID, err)
		}
		prevRecord = record
	}
	return nil
}

// createPreRestoreSnapshots 是一个关键的安全函数，它在执行原子恢复前为所有涉及的数据源创建一个临时快照。
// 这些快照是回滚的基石。
// 返回一个从 sourceName 到 snapshotID 的映射。
// 注意：此函数不获取信号量，因为调用方（如串行恢复）已经获取了信号量
func (m *Manager) createPreRestoreSnapshots(ctx context.Context, mainTask *types.Task, sources []types.RestoreRequest) map[string]string {
	successfulSnapshots := make(map[string]string)
	failedSnapshots := make([]string, 0) // 记录失败的快照，用于清理
	var mu sync.Mutex
	var wg sync.WaitGroup

	m.logger.Info("开始创建预恢复快照",
		zap.String("task_id", mainTask.ID),
		zap.Int("source_count", len(sources)))

	for _, source := range sources {
		wg.Add(1)
		go func(src types.RestoreRequest) {
			defer wg.Done()

			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				m.logger.Warn("预恢复快照创建被取消",
					zap.String("source", src.SourceName),
					zap.Error(ctx.Err()))
				return
			default:
				// 继续执行
			}

			// 快照描述应包含主任务ID，便于追踪
			desc := fmt.Sprintf("预恢复快照，用于原子恢复任务 %s，针对数据源 %s", mainTask.ID, src.SourceName)

			m.logger.Info("开始为数据源创建预恢复快照",
				zap.String("source", src.SourceName),
				zap.String("task_id", mainTask.ID))

			// 创建一个一次性的归档备份作为快照
			record, err := m.storageMgr.CreateBackupRecord(src.SourceType, src.SourceName, types.BackupTypeArchival, "", desc)
			if err != nil {
				m.logger.Error("为预恢复快照创建记录失败",
					zap.String("source", src.SourceName),
					zap.String("task_id", mainTask.ID),
					zap.Error(err))
				mu.Lock()
				failedSnapshots = append(failedSnapshots, src.SourceName)
				mu.Unlock()
				return
			}

			m.logger.Info("预恢复快照记录已创建，开始执行备份",
				zap.String("source", src.SourceName),
				zap.String("record_id", record.ID))

			provider := m.providers[src.SourceType]
			if err := provider.Backup(ctx, record, nil); err != nil {
				m.logger.Error("创建预恢复快照失败",
					zap.String("source", src.SourceName),
					zap.String("record_id", record.ID),
					zap.String("task_id", mainTask.ID),
					zap.Error(err))
				// 清理已创建的记录
				if delErr := m.storageMgr.DeleteBackupRecord(record); delErr != nil {
					m.logger.Error("清理失败快照记录时出错",
						zap.String("source", src.SourceName),
						zap.String("record_id", record.ID),
						zap.Error(delErr))
				}
				mu.Lock()
				failedSnapshots = append(failedSnapshots, src.SourceName)
				mu.Unlock()
			} else {
				mu.Lock()
				successfulSnapshots[src.SourceName] = record.ID
				mu.Unlock()
				m.logger.Info("成功创建预恢复快照",
					zap.String("source", src.SourceName),
					zap.String("snapshot_id", record.ID),
					zap.String("task_id", mainTask.ID))
			}
		}(source)
	}

	wg.Wait()

	// 如果有失败的快照，记录详细信息
	if len(failedSnapshots) > 0 {
		m.logger.Warn("部分预恢复快照创建失败",
			zap.String("task_id", mainTask.ID),
			zap.Strings("failed_sources", failedSnapshots),
			zap.Int("successful_count", len(successfulSnapshots)),
			zap.Int("total_count", len(sources)))
	}

	return successfulSnapshots
}

// executeSerialRestoreAll 串行地执行所有恢复任务，这是实现原子性的关键。
func (m *Manager) executeSerialRestoreAll(ctx context.Context, mainTask *types.Task, config types.BatchRestoreConfig, snapshots map[string]string) {
	var completedSources []types.RestoreRequest
	var finalError *types.BackupError

	m.logger.Info("开始串行执行原子恢复",
		zap.String("task_id", mainTask.ID),
		zap.Int("restore_count", len(config.Restores)),
		zap.Int("snapshot_count", len(snapshots)))

	// 注意：信号量已在RestoreAllAsync中预获取，这里不需要再次获取
	// 串行恢复使用预获取的信号量，避免重复获取导致死锁

	m.logger.Debug("串行恢复开始执行（使用预获取的信号量）",
		zap.String("task_id", mainTask.ID),
		zap.Int("restore_count", len(config.Restores)))

	for i, restoreConfig := range config.Restores {
		// 每次循环前都检查是否已接收到取消信号
		if m.isTaskCancelled(ctx, mainTask) {
			finalError = berrors.NewTaskManagerError("RESTORE_CANCELLED", "executeSerialRestoreAll", errors.New("原子恢复被取消"), false)
			break // 中断循环
		}

		// 更新主任务进度
		mainTask.Progress = 20 + float64(i+1)/float64(len(config.Restores))*75
		mainTask.Metadata["phase"] = fmt.Sprintf("正在恢复 %s (%d/%d)",
			restoreConfig.SourceName, i+1, len(config.Restores))
		m.updateTask(mainTask)

		// 创建子任务
		subTask := &types.Task{
			ID:          mainTask.SubTaskIDs[i],
			Type:        types.RestoreTask,
			Source:      restoreConfig.SourceType,
			Status:      types.TaskStatusRunning,
			StartTime:   time.Now(),
			Description: restoreConfig.Description,
			Metadata:    map[string]interface{}{"parent_task_id": mainTask.ID, "restore_config": restoreConfig},
		}
		m.updateTask(subTask)

		// 执行恢复
		err := m.executeSubRestore(ctx, subTask, restoreConfig)

		// 处理结果
		if err != nil {
			m.failTask(subTask, berrors.NewTaskManagerError("SUB_TASK_FAILED", "executeSubRestore", err, false))
			finalError = berrors.NewTaskManagerError("ATOMIC_RESTORE_FAILED", "executeSerialRestoreAll",
				fmt.Errorf("在恢复 '%s' 时失败，原子操作中止: %w", restoreConfig.SourceName, err), false)
			break // 任何失败都会立即中断整个过程
		} else {
			subTask.Status = types.TaskStatusCompleted
			subTask.Progress = 100
			m.updateTask(subTask)
			// 转换为RestoreRequest格式
			source := types.RestoreRequest{
				SourceType:  restoreConfig.SourceType,
				SourceName:  restoreConfig.SourceName,
				BackupID:    restoreConfig.BackupID,
				Description: restoreConfig.Description,
			}
			completedSources = append(completedSources, source)
		}
	}

	// 最终状态判断
	if finalError != nil {
		mainTask.Status = types.TaskStatusFailed
		mainTask.Error = finalError.Message
		mainTask.ErrorDetail = finalError
		// 如果需要，执行回滚（检查是否有任何配置启用了回滚）
		shouldRollback := false
		for _, restoreConfig := range config.Restores {
			if restoreConfig.RollbackOnFailure {
				shouldRollback = true
				break
			}
		}
		if shouldRollback && len(completedSources) > 0 {
			mainTask.Metadata["phase"] = "执行回滚"
			m.updateTask(mainTask)
			m.logger.Warn("原子恢复失败，开始回滚已完成的部分",
				zap.String("task_id", mainTask.ID),
				zap.Int("rollback_count", len(completedSources)))
			// 同步执行回滚
			m.rollbackCompletedRestores(mainTask.ID, completedSources, snapshots)
			// 回滚后，也清理快照
			if len(snapshots) > 0 {
				m.logger.Info("回滚操作后，开始清理预恢复快照", zap.Int("count", len(snapshots)))
				go m.cleanupPreRestoreSnapshots(context.Background(), mainTask.ID, snapshots)
			}
		} else {
			// 失败但不需要回滚，或者被取消，直接清理快照
			if len(snapshots) > 0 {
				m.logger.Info("串行恢复失败/取消，清理预恢复快照", zap.Int("count", len(snapshots)))
				go m.cleanupPreRestoreSnapshots(context.Background(), mainTask.ID, snapshots)
			}
		}
	} else {
		mainTask.Status = types.TaskStatusCompleted
		m.logger.Info("原子恢复成功完成", zap.String("task_id", mainTask.ID))
		// 执行分组恢复成功回调
		m.executeRestoreCallback(ctx, config.OnSuccess, mainTask)
		// 如果成功，清理掉预恢复快照
		if len(snapshots) > 0 {
			m.logger.Info("原子恢复成功，将清理预恢复快照", zap.Int("count", len(snapshots)))
			// 异步清理，因为它不是关键路径
			go m.cleanupPreRestoreSnapshots(context.Background(), mainTask.ID, snapshots)
		}
	}

	mainTask.Progress = 100
	m.updateTask(mainTask)
}

// rollbackCompletedRestores 是原子恢复失败后的核心补偿措施。
// 它使用之前创建的预恢复快照来回滚那些已经成功恢复的数据源。
func (m *Manager) rollbackCompletedRestores(mainTaskID string, completedSources []types.RestoreRequest, snapshots map[string]string) {
	m.logger.Warn("开始回滚已完成的恢复操作",
		zap.String("main_task_id", mainTaskID),
		zap.Int("count", len(completedSources)))

	for _, source := range completedSources {
		snapshotID, ok := snapshots[source.SourceName]
		if !ok {
			m.logger.Error("严重错误：无法找到用于回滚的快照", zap.String("source", source.SourceName))

			// 更新主任务状态
			if mainTask, loadErr := m.GetTask(mainTaskID); loadErr == nil {
				mainTask.Error = fmt.Sprintf("无法回滚 %s: 快照丢失，数据可能不一致，需要人工检查", source.SourceName)
				m.updateTask(mainTask)
			}
			continue
		}

		m.logger.Info("正在回滚数据源",
			zap.String("source", source.SourceName),
			zap.String("using_snapshot", snapshotID))

		// 使用一个新的后台上下文来执行回滚，因为它不应被原始的、可能已取消的上下文影响。
		rollbackCtx, cancel := context.WithTimeout(context.Background(), m.cfg.RestoreTimeout.ToDuration())
		defer cancel()

		// 直接调用核心的 Restore 方法来执行回滚。
		rollbackConfig := types.NewRestoreConfig(source.SourceType, source.SourceName, snapshotID)
		rollbackConfig.Description = "自动回滚快照"
		if err := m.Restore(rollbackCtx, rollbackConfig); err != nil {
			m.logger.Error("回滚失败！数据源可能处于不一致状态，需要人工干预！",
				zap.String("source", source.SourceName),
				zap.String("snapshot_id", snapshotID),
				zap.Error(err))

			// 更新主任务状态以反映回滚失败
			if mainTask, loadErr := m.GetTask(mainTaskID); loadErr == nil {
				mainTask.Error = fmt.Sprintf("回滚失败: %s. 数据可能不一致，需要人工检查. %s", source.SourceName, err.Message)
				m.updateTask(mainTask)
			}
		} else {
			m.logger.Info("成功回滚数据源", zap.String("source", source.SourceName))
		}
	}

	m.logger.Warn("所有已完成的恢复操作的回滚尝试已结束", zap.String("main_task_id", mainTaskID))
}

// cleanupPreRestoreSnapshots 在分组恢复任务完成后（无论成功或失败），清理掉为原子性而创建的临时快照。
func (m *Manager) cleanupPreRestoreSnapshots(ctx context.Context, mainTaskID string, snapshots map[string]string) {
	m.logger.Info("开始清理预恢复快照",
		zap.String("main_task_id", mainTaskID),
		zap.Int("snapshot_count", len(snapshots)))
	cleanedCount := 0
	for sourceName, snapshotID := range snapshots {
		// 使用新的跨类型查找方法来获取备份记录，从而得到sourceType
		record, err := m.storageMgr.FindBackupRecordAcrossTypes(snapshotID)
		if err != nil {
			m.logger.Error("清理快照时无法找到记录，可能已被删除或路径错误",
				zap.String("snapshot_id", snapshotID),
				zap.Error(err))
			continue
		}

		m.logger.Info("正在清理快照",
			zap.String("snapshot_id", snapshotID),
			zap.String("source_name", sourceName))
		if err := m.DeleteBackup(ctx, record.Source, snapshotID); err != nil {
			m.logger.Error("清理预恢复快照失败",
				zap.String("snapshot_id", snapshotID),
				zap.Error(err))
		} else {
			cleanedCount++
			m.logger.Info("成功清理预恢复快照", zap.String("snapshot_id", snapshotID))
		}
	}
	m.logger.Info("预恢复快照清理完成",
		zap.String("main_task_id", mainTaskID),
		zap.Int("cleaned_count", cleanedCount))
}

// periodicCleanup 定期运行，清理旧的、已完成的任务，以防止任务列表无限增长。
func (m *Manager) periodicCleanup() {
	for {
		select {
		case <-m.cleanupTicker.C:
			m.logger.Info("开始定期清理旧任务")
			m.cleanupOldTasks()
			m.cleanupZombieTasks() // 新增：清理僵尸任务
		case <-m.shutdownChan:
			m.logger.Info("关闭信号已收到，定期清理任务退出")
			return
		}
	}
}

// cleanupOldTasks 从内存和文件中移除超过 `maxTaskHistory` 限制的、最旧的已完成任务。
func (m *Manager) cleanupOldTasks() {
	var allTasks []*types.Task
	m.tasks.Range(func(key, value interface{}) bool {
		allTasks = append(allTasks, value.(*types.Task))
		return true
	})

	if len(allTasks) == 0 {
		return // 没有任务可清理
	}

	// 1. 筛选出所有已进入终态的任务
	var terminalTasks []*types.Task
	for _, task := range allTasks {
		if task.Status.IsTerminal() {
			terminalTasks = append(terminalTasks, task)
		}
	}

	if len(terminalTasks) == 0 {
		return // 没有已完成的任务可清理
	}

	// 2. 按结束时间排序，最早结束的在前面
	sort.Slice(terminalTasks, func(i, j int) bool {
		// 确保 EndTime 不为零值，否则排序可能不准确
		if terminalTasks[i].EndTime.IsZero() {
			return false
		}
		if terminalTasks[j].EndTime.IsZero() {
			return true
		}
		return terminalTasks[i].EndTime.Before(terminalTasks[j].EndTime)
	})

	tasksToDelete := make(map[string]bool) // 用于标记要删除的任务ID

	// 3. 基于时间保留策略进行标记
	if m.taskRetentionDays > 0 {
		retentionCutoff := time.Now().AddDate(0, 0, -m.taskRetentionDays)
		for _, task := range terminalTasks {
			if task.EndTime.Before(retentionCutoff) {
				tasksToDelete[task.ID] = true
			}
		}
	}

	// 4. 基于数量保留策略进行标记 (如果任务总数超过限制)
	// 优先保留最新的任务，所以从排序后的列表末尾开始计算要保留的数量
	if m.maxTaskHistory > 0 && len(terminalTasks) > m.maxTaskHistory {
		// 计算需要删除的数量
		numToDeleteByCount := len(terminalTasks) - m.maxTaskHistory
		for i := 0; i < numToDeleteByCount; i++ {
			// 标记最旧的那些任务进行删除
			tasksToDelete[terminalTasks[i].ID] = true
		}
	}

	deletedCount := 0
	cleanedBackupCount := 0
	if len(tasksToDelete) > 0 {
		// 遍历所有任务，删除被标记的
		m.tasks.Range(func(key, value interface{}) bool {
			taskID := key.(string)
			if tasksToDelete[taskID] {
				task := value.(*types.Task)

				// 清理备份文件的逻辑
				if task.Type == types.BackupTask && task.Metadata["backup_record_id"] != nil {
					backupID := task.Metadata["backup_record_id"].(string)

					// 根据配置决定清理策略
					shouldCleanupBackup := false
					if m.cleanupBackupData && task.Status == types.TaskStatusCompleted {
						// 如果启用了备份数据清理，清理成功的备份
						shouldCleanupBackup = true
						m.logger.Info("清理成功备份的数据",
							zap.String("backup_id", backupID),
							zap.String("task_id", taskID))
					} else if task.Status == types.TaskStatusFailed {
						// 始终清理失败的备份（保持原有行为）
						shouldCleanupBackup = true
						m.logger.Info("清理失败备份的数据",
							zap.String("backup_id", backupID),
							zap.String("task_id", taskID))
					}

					if shouldCleanupBackup {
						if err := m.DeleteBackup(context.Background(), task.Source, backupID); err != nil {
							m.logger.Warn("清理备份文件时出错",
								zap.String("backup_id", backupID),
								zap.String("task_id", taskID),
								zap.String("status", string(task.Status)),
								zap.Error(err))
						} else {
							cleanedBackupCount++
							m.logger.Info("成功清理备份文件",
								zap.String("backup_id", backupID),
								zap.String("task_id", taskID),
								zap.String("status", string(task.Status)))
						}
					}
				}

				m.tasks.Delete(taskID)
				deletedCount++
			}
			return true
		})
	}

	if deletedCount > 0 {
		m.logger.Info("成功清理了旧任务",
			zap.Int("task_count", deletedCount),
			zap.Int("failed_backup_count", cleanedBackupCount))
		if err := m.writeTasksToFile(); err != nil {
			m.logger.Error("清理旧任务后持久化失败", zap.Error(err))
		}
	} else {
		m.logger.Info("没有旧任务需要清理")
	}
}

// DeleteTask 删除指定的任务记录。
// 这个方法用于支持基于taskID的备份删除功能。
func (m *Manager) DeleteTask(taskID string) error {
	// 从内存中删除任务
	_, exists := m.tasks.LoadAndDelete(taskID)
	if !exists {
		return fmt.Errorf("任务 %s 不存在", taskID)
	}

	// 保存更新后的任务列表到文件
	if err := m.writeTasksToFile(); err != nil {
		// 如果保存失败，需要将任务重新加载到内存中
		// 这里简化处理，记录错误日志
		m.logger.Error("删除任务后保存文件失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return fmt.Errorf("删除任务记录失败: %w", err)
	}

	m.logger.Info("成功删除任务记录", zap.String("task_id", taskID))
	return nil
}

// getRunningTasksForSource 获取指定数据源正在运行的任务
func (m *Manager) getRunningTasksForSource(sourceKey string) []*types.Task {
	var runningTasks []*types.Task

	m.tasks.Range(func(key, value interface{}) bool {
		task := value.(*types.Task)
		if task.Status == types.TaskStatusRunning || task.Status == types.TaskStatusPending {
			// 检查是否是同一数据源
			if taskSourceKeys := m.getTaskSourceKeys(task); contains(taskSourceKeys, sourceKey) {
				runningTasks = append(runningTasks, task)
			}
		}
		return true
	})

	return runningTasks
}

// getTaskSourceKeys 获取任务涉及的所有数据源键
func (m *Manager) getTaskSourceKeys(task *types.Task) []string {
	var sourceKeys []string

	switch task.Type {
	case types.BackupTask, types.RestoreTask:
		// 单个任务
		if sourceName, ok := task.Metadata["source_name"].(string); ok {
			sourceKeys = append(sourceKeys, fmt.Sprintf("%s-%s", task.Source, sourceName))
		}

	case types.BackupAllTask:
		// 分组备份任务 - 正确处理 []types.BackupRequest 类型
		if sources, ok := task.Metadata["sources"].([]types.BackupRequest); ok {
			for _, source := range sources {
				sourceKeys = append(sourceKeys, fmt.Sprintf("%s-%s", source.SourceType, source.SourceName))
			}
		}

	case types.RestoreAllTask:
		// 分组恢复任务 - 正确处理 []types.RestoreConfig 类型
		if restores, ok := task.Metadata["restores"].([]types.RestoreConfig); ok {
			for _, restore := range restores {
				sourceKeys = append(sourceKeys, fmt.Sprintf("%s-%s", restore.SourceType, restore.SourceName))
			}
		}
	}

	return sourceKeys
}

// checkConcurrencyConflict 检查并发冲突
func (m *Manager) checkConcurrencyConflict(taskType types.TaskType, sourceKeys []string, newBackupType types.BackupType) error {
	for _, sourceKey := range sourceKeys {
		runningTasks := m.getRunningTasksForSource(sourceKey)

		for _, runningTask := range runningTasks {
			// 规则1: 恢复任务独占，不能与任何其他任务并发
			if taskType.IsRestoreTask() || runningTask.Type.IsRestoreTask() {
				return fmt.Errorf("恢复任务不能与其他任务并发执行，数据源 %s 有任务 %s 正在运行",
					sourceKey, runningTask.ID)
			}

			// 规则2: 备份任务的类型冲突检查
			if taskType.IsBackupTask() && runningTask.Type.IsBackupTask() {
				runningBackupType := m.getTaskBackupType(runningTask)

				// 全量备份同源不能并发
				if isFullBackup(newBackupType) && isFullBackup(runningBackupType) {
					return fmt.Errorf("全量备份不能并发执行，数据源 %s 有任务 %s 正在运行",
						sourceKey, runningTask.ID)
				}

				// 增量备份同源不能并发
				if isIncrementalBackup(newBackupType) && isIncrementalBackup(runningBackupType) {
					return fmt.Errorf("增量备份不能并发执行，数据源 %s 有任务 %s 正在运行",
						sourceKey, runningTask.ID)
				}

				// 全量与增量可以并发 - 不做限制
			}
		}
	}

	return nil
}

// isFullBackup 判断是否为全量备份
func isFullBackup(backupType types.BackupType) bool {
	return backupType == types.BackupTypeArchival || backupType == types.BackupTypeChainInitial
}

// isIncrementalBackup 判断是否为增量备份
func isIncrementalBackup(backupType types.BackupType) bool {
	return backupType == types.BackupTypeChainIncremental
}

// getTaskBackupType 获取任务的备份类型
func (m *Manager) getTaskBackupType(task *types.Task) types.BackupType {
	if backupType, ok := task.Metadata["backup_type"].(string); ok {
		return types.BackupType(backupType)
	}
	// 默认认为是全量备份
	return types.BackupTypeArchival
}

// forceInterruptBackupTasks 强制中断指定数据源的备份任务
func (m *Manager) forceInterruptBackupTasks(sourceKeys []string) error {
	var interruptedTasks []string

	for _, sourceKey := range sourceKeys {
		runningTasks := m.getRunningTasksForSource(sourceKey)
		for _, task := range runningTasks {
			if task.Type.IsBackupTask() {
				m.logger.Info("强制恢复：中断备份任务",
					zap.String("backup_task", task.ID),
					zap.String("source", sourceKey))
				if err := m.CancelTask(task.ID); err != nil {
					m.logger.Error("中断备份任务失败",
						zap.String("task_id", task.ID),
						zap.Error(err))
					continue
				}
				interruptedTasks = append(interruptedTasks, task.ID)
			}
		}
	}

	// 等待备份任务被中断（最多等待10秒）
	if len(interruptedTasks) > 0 {
		m.logger.Info("等待备份任务中断完成", zap.Strings("interrupted_tasks", interruptedTasks))
		for i := 0; i < 10; i++ {
			allStopped := true
			for _, sourceKey := range sourceKeys {
				if len(m.getRunningTasksForSource(sourceKey)) > 0 {
					allStopped = false
					break
				}
			}
			if allStopped {
				break
			}
			time.Sleep(1 * time.Second)
		}
	}

	return nil
}

// createSinglePreRestoreSnapshot 为单个恢复任务创建预恢复快照。
// 这是 createPreRestoreSnapshots 的简化版本，专门用于单个恢复任务。
func (m *Manager) createSinglePreRestoreSnapshot(ctx context.Context, taskID string, sourceType types.SourceType, sourceName string) (string, error) {
	m.logger.Info("开始创建单个预恢复快照",
		zap.String("task_id", taskID),
		zap.String("source_name", sourceName))

	// 快照描述应包含任务ID，便于追踪
	desc := fmt.Sprintf("预恢复快照，用于恢复任务 %s，针对数据源 %s", taskID, sourceName)

	// 创建一个一次性的归档备份作为快照
	record, err := m.storageMgr.CreateBackupRecord(sourceType, sourceName, types.BackupTypeArchival, "", desc)
	if err != nil {
		return "", fmt.Errorf("为预恢复快照创建记录失败: %w", err)
	}

	provider := m.providers[sourceType]
	if err := provider.Backup(ctx, record, nil); err != nil {
		// 清理已创建的记录
		if delErr := m.storageMgr.DeleteBackupRecord(record); delErr != nil {
			m.logger.Error("清理失败快照记录时出错",
				zap.String("source", sourceName),
				zap.String("record_id", record.ID),
				zap.Error(delErr))
		}
		return "", fmt.Errorf("创建预恢复快照失败: %w", err)
	}

	// 保存成功的快照记录
	record.Status = types.BackupStatusCompleted
	if err := m.storageMgr.SaveBackupRecord(record); err != nil {
		return "", fmt.Errorf("保存预恢复快照记录失败: %w", err)
	}

	m.logger.Info("成功创建单个预恢复快照",
		zap.String("source", sourceName),
		zap.String("snapshot_id", record.ID))

	return record.ID, nil
}

// rollbackSingleRestore 为单个恢复任务执行回滚操作。
// 这是 rollbackCompletedRestores 的简化版本，专门用于单个恢复任务。
func (m *Manager) rollbackSingleRestore(ctx context.Context, taskID string, sourceType types.SourceType, sourceName, snapshotID string) error {
	m.logger.Info("开始回滚单个恢复操作",
		zap.String("task_id", taskID),
		zap.String("source", sourceName),
		zap.String("using_snapshot", snapshotID))

	// 使用一个新的后台上下文来执行回滚，因为它不应被原始的、可能已取消的上下文影响。
	rollbackCtx, cancel := context.WithTimeout(context.Background(), m.cfg.RestoreTimeout.ToDuration())
	defer cancel()

	// 直接调用核心的 Restore 方法来执行回滚。
	rollbackConfig := types.NewRestoreConfig(sourceType, sourceName, snapshotID)
	rollbackConfig.Description = "自动回滚快照"
	// 重要：回滚时不再创建快照，避免无限递归
	rollbackConfig.CreateSafetyBackup = false
	rollbackConfig.RollbackOnFailure = false

	if err := m.Restore(rollbackCtx, rollbackConfig); err != nil {
		return fmt.Errorf("回滚失败: %w", err)
	}

	m.logger.Info("成功回滚单个恢复操作",
		zap.String("task_id", taskID),
		zap.String("source", sourceName))
	return nil
}

// cleanupSinglePreRestoreSnapshot 清理单个预恢复快照。
// 这是 cleanupPreRestoreSnapshots 的简化版本，专门用于单个恢复任务。
func (m *Manager) cleanupSinglePreRestoreSnapshot(ctx context.Context, taskID, snapshotID string) {
	m.logger.Info("开始清理单个预恢复快照",
		zap.String("task_id", taskID),
		zap.String("snapshot_id", snapshotID))

	// 使用新的跨类型查找方法来获取备份记录，从而得到sourceType
	record, err := m.storageMgr.FindBackupRecordAcrossTypes(snapshotID)
	if err != nil {
		// 改进错误处理：如果快照记录不存在，可能是已经被清理了，这是正常情况
		// 只记录警告而不是错误，避免在测试中产生误导性的错误信息
		m.logger.Warn("清理快照时无法找到记录，可能已被删除或清理",
			zap.String("task_id", taskID),
			zap.String("snapshot_id", snapshotID),
			zap.String("reason", "快照可能已被其他流程清理，这是正常情况"),
			zap.Error(err))
		return
	}

	if err := m.DeleteBackup(ctx, record.Source, snapshotID); err != nil {
		m.logger.Error("清理单个预恢复快照失败",
			zap.String("task_id", taskID),
			zap.String("snapshot_id", snapshotID),
			zap.Error(err))
	} else {
		m.logger.Info("成功清理单个预恢复快照",
			zap.String("task_id", taskID),
			zap.String("snapshot_id", snapshotID))
	}
}

// cleanupZombieTasks 清理僵尸任务（长时间运行但可能已经异常退出的任务）
func (m *Manager) cleanupZombieTasks() {
	// 使用配置的超时时间作为僵尸任务检测阈值
	// 取备份和恢复超时时间的最大值，确保不会误杀正常的长时间任务
	backupTimeout := m.cfg.BackupTimeout.ToDuration()
	restoreTimeout := m.cfg.RestoreTimeout.ToDuration()

	zombieThreshold := backupTimeout
	if restoreTimeout > backupTimeout {
		zombieThreshold = restoreTimeout
	}

	// 如果配置的超时时间为0或过小，使用默认值
	if zombieThreshold <= 0 {
		zombieThreshold = 24 * time.Hour // 默认24小时
	} else if zombieThreshold < 30*time.Minute {
		zombieThreshold = 30 * time.Minute // 最小30分钟，避免误杀
	}

	now := time.Now()

	m.tasks.Range(func(key, value interface{}) bool {
		task := value.(*types.Task)
		taskID := task.ID

		// 只检查运行中或待处理的任务
		if task.Status == types.TaskStatusRunning || task.Status == types.TaskStatusPending {
			runningDuration := now.Sub(task.StartTime)

			// 检查是否超过僵尸任务阈值
			if runningDuration > zombieThreshold {
				// 检查是否还有对应的执行上下文
				if _, hasContext := m.taskContexts.Load(taskID); !hasContext {
					// 没有执行上下文，直接标记为失败
					m.logger.Warn("检测到僵尸任务（无执行上下文），正在清理",
						zap.String("task_id", taskID),
						zap.String("type", string(task.Type)),
						zap.Duration("running_duration", runningDuration))

					task.Status = types.TaskStatusFailed
					task.Error = fmt.Sprintf("任务运行超时（%v），执行上下文已丢失，可能因系统挂起或网络中断导致", runningDuration)
					task.EndTime = now
					m.updateTask(task)
				} else {
					// 有执行上下文但运行时间过长，记录警告但不强制清理
					// 让超时机制自然处理
					m.logger.Warn("检测到长时间运行的任务",
						zap.String("task_id", taskID),
						zap.String("type", string(task.Type)),
						zap.Duration("running_duration", runningDuration),
						zap.String("action", "等待超时机制处理"))
				}
			}
		}
		return true
	})
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
