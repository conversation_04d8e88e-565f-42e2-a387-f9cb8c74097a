package testutil

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"regexp"
	"sync"

	"github.com/elastic/go-elasticsearch/v8"
)

// MockESServer 模拟Elasticsearch服务器
type MockESServer struct {
	mu        sync.RWMutex
	handlers  map[string]http.HandlerFunc
	server    *httptest.Server
	snapshots map[string]map[string]*MockSnapshot // repo -> snapshot_id -> snapshot
}

// MockSnapshot 模拟快照数据
type MockSnapshot struct {
	Snapshot string                 `json:"snapshot"`
	State    string                 `json:"state"`
	Metadata map[string]interface{} `json:"metadata"`
}

// MockSnapshotResponse 模拟快照响应
type MockSnapshotResponse struct {
	Snapshots []*MockSnapshot `json:"snapshots"`
}

// NewMockESServer 创建新的mock ES服务器
func NewMockESServer() *MockESServer {
	ms := &MockESServer{
		handlers:  make(map[string]http.HandlerFunc),
		snapshots: make(map[string]map[string]*MockSnapshot),
	}

	ms.server = httptest.NewServer(http.HandlerFunc(ms.ServeHTTP))
	ms.setupDefaultHandlers()
	return ms
}

// setupDefaultHandlers 设置默认的处理器
func (ms *MockESServer) setupDefaultHandlers() {
	// ES根路径处理器 - 返回集群信息，模拟真实ES的根路径响应
	// 这个响应对于ES客户端验证服务器身份很重要
	ms.RegisterHandler("^/$", func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"name":         "mock-es-node",      // 模拟节点名称
			"cluster_name": "mock-cluster",      // 模拟集群名称
			"cluster_uuid": "mock-cluster-uuid", // 模拟集群UUID
			"version": map[string]interface{}{
				"number":                              "8.0.0",                          // ES版本号
				"build_flavor":                        "default",                        // 构建类型
				"build_type":                          "docker",                         // 构建方式
				"build_hash":                          "mock-build-hash",                // 构建哈希
				"build_date":                          "2023-01-01T00:00:00.000000000Z", // 构建日期
				"build_snapshot":                      false,                            // 是否为快照版本
				"lucene_version":                      "9.0.0",                          // Lucene版本
				"minimum_wire_compatibility_version":  "7.17.0",                         // 最小线路兼容版本
				"minimum_index_compatibility_version": "7.0.0",                          // 最小索引兼容版本
			},
			"tagline": "You Know, for Search", // ES的标语
		}
		ms.writeJSONResponse(w, response)
	})

	// 快照恢复 - POST /_snapshot/{repo}/{snapshot}/_restore
	ms.RegisterHandler("^/_snapshot/([^/]+)/([^/]+)/_restore$", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		matches := regexp.MustCompile("^/_snapshot/([^/]+)/([^/]+)/_restore$").FindStringSubmatch(r.URL.Path)
		if len(matches) != 3 {
			http.Error(w, "Invalid path", http.StatusBadRequest)
			return
		}

		repo := matches[1]
		snapshotID := matches[2]

		// 检查快照是否存在
		ms.mu.RLock()
		repoSnapshots, repoExists := ms.snapshots[repo]
		var snapshotExists bool
		if repoExists {
			_, snapshotExists = repoSnapshots[snapshotID]
		}
		ms.mu.RUnlock()

		if !snapshotExists {
			http.Error(w, fmt.Sprintf("Snapshot [%s:%s] not found", repo, snapshotID), http.StatusNotFound)
			return
		}

		response := map[string]interface{}{
			"snapshot": map[string]interface{}{
				"snapshot": snapshotID,
				"indices":  []string{"restored-index"},
			},
		}
		ms.writeJSONResponse(w, response)
	})

	// 快照列表 - GET /_snapshot/{repo}/_all
	ms.RegisterHandler("^/_snapshot/([^/]+)/_all$", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		matches := regexp.MustCompile("^/_snapshot/([^/]+)/_all$").FindStringSubmatch(r.URL.Path)
		if len(matches) != 2 {
			http.Error(w, "Invalid path", http.StatusBadRequest)
			return
		}

		repo := matches[1]

		ms.mu.RLock()
		repoSnapshots, exists := ms.snapshots[repo]
		ms.mu.RUnlock()

		if !exists {
			// 仓库不存在，返回404
			http.Error(w, fmt.Sprintf("Repository [%s] not found", repo), http.StatusNotFound)
			return
		}

		snapshots := make([]*MockSnapshot, 0, len(repoSnapshots))
		for _, snapshot := range repoSnapshots {
			snapshots = append(snapshots, snapshot)
		}

		response := MockSnapshotResponse{
			Snapshots: snapshots,
		}
		ms.writeJSONResponse(w, response)
	})

	// 快照恢复处理器 - POST /_snapshot/{repo}/{snapshot}/_restore
	// 处理快照恢复请求，模拟ES的快照恢复API
	ms.RegisterHandler("^/_snapshot/([^/]+)/([^/]+)/_restore$", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 解析URL路径，提取仓库名和快照ID
		matches := regexp.MustCompile("^/_snapshot/([^/]+)/([^/]+)/_restore$").FindStringSubmatch(r.URL.Path)
		if len(matches) != 3 {
			http.Error(w, "Invalid path", http.StatusBadRequest)
			return
		}

		repo := matches[1]       // 仓库名称
		snapshotID := matches[2] // 快照ID

		// 检查快照是否存在
		ms.mu.RLock()
		if repoSnapshots, exists := ms.snapshots[repo]; exists {
			if _, snapshotExists := repoSnapshots[snapshotID]; snapshotExists {
				ms.mu.RUnlock()

				// 模拟恢复成功
				response := map[string]interface{}{
					"snapshot": map[string]interface{}{
						"snapshot": snapshotID,
						"indices":  []string{"restored-index"},
						"shards": map[string]interface{}{
							"total":      1,
							"failed":     0,
							"successful": 1,
						},
					},
				}
				ms.writeJSONResponse(w, response)
				return
			}
		}
		ms.mu.RUnlock()

		// 快照不存在
		http.Error(w, fmt.Sprintf("Snapshot [%s:%s] not found", repo, snapshotID), http.StatusNotFound)
	})

	// 模拟 _cat/indices API，返回空的索引列表
	ms.RegisterHandler("^/_cat/indices$", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}
		// 返回一个空的JSON数组，模拟没有索引的情况
		ms.writeJSONResponse(w, []interface{}{})
	})

	// 模拟 GET /_all 或 GET /_all/_settings，返回空的索引列表
	ms.RegisterHandler("^/_all(?:/_settings)?$", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}
		// 返回一个空的JSON对象，模拟没有索引的情况
		ms.writeJSONResponse(w, map[string]interface{}{})
	})

	// 模拟 GET /*，返回空的索引列表
	ms.RegisterHandler("^/\\*$", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}
		// 返回一个空的JSON对象，模拟没有索引的情况
		ms.writeJSONResponse(w, map[string]interface{}{})
	})

	// 快照创建、删除和获取的统一处理器 - PUT/POST/DELETE/GET /_snapshot/{repo}/{snapshot}
	// 这个处理器处理单个快照的所有操作
	ms.RegisterHandler("^/_snapshot/([^/]+)/([^/]+)$", func(w http.ResponseWriter, r *http.Request) {
		// 解析URL路径，提取仓库名和快照ID
		matches := regexp.MustCompile("^/_snapshot/([^/]+)/([^/]+)$").FindStringSubmatch(r.URL.Path)
		if len(matches) != 3 {
			http.Error(w, "Invalid path", http.StatusBadRequest)
			return
		}

		repo := matches[1]       // 仓库名称
		snapshotID := matches[2] // 快照ID

		if r.Method == "PUT" || r.Method == "POST" {
			// 处理快照创建请求（PUT和POST都支持）

			// 读取请求体获取元数据
			body, err := io.ReadAll(r.Body)
			if err != nil {
				http.Error(w, "Failed to read body", http.StatusBadRequest)
				return
			}

			var reqBody map[string]interface{}
			if len(body) > 0 {
				json.Unmarshal(body, &reqBody)
			}

			// 创建快照
			ms.mu.Lock()
			if ms.snapshots[repo] == nil {
				ms.snapshots[repo] = make(map[string]*MockSnapshot)
			}

			snapshot := &MockSnapshot{
				Snapshot: snapshotID,
				State:    "SUCCESS",
				Metadata: make(map[string]interface{}),
			}

			if reqBody != nil && reqBody["metadata"] != nil {
				// 处理metadata可能是json.RawMessage或者已经是map的情况
				switch meta := reqBody["metadata"].(type) {
				case map[string]interface{}:
					snapshot.Metadata = meta
				case string:
					// 如果是字符串，尝试解析为JSON
					var metaMap map[string]interface{}
					if err := json.Unmarshal([]byte(meta), &metaMap); err == nil {
						snapshot.Metadata = metaMap
					}
				default:
					// 尝试通过JSON序列化/反序列化来处理
					if metaBytes, err := json.Marshal(meta); err == nil {
						var metaMap map[string]interface{}
						if err := json.Unmarshal(metaBytes, &metaMap); err == nil {
							snapshot.Metadata = metaMap
						}
					}
				}
			}

			ms.snapshots[repo][snapshotID] = snapshot
			ms.mu.Unlock()

			response := map[string]interface{}{
				"snapshot": map[string]interface{}{
					"snapshot": snapshotID,
					"state":    "SUCCESS",
				},
			}
			ms.writeJSONResponse(w, response)

		} else if r.Method == "DELETE" {
			// 处理删除请求

			ms.mu.Lock()
			if repoSnapshots, exists := ms.snapshots[repo]; exists {
				if _, snapshotExists := repoSnapshots[snapshotID]; snapshotExists {
					delete(repoSnapshots, snapshotID)
					ms.mu.Unlock()

					response := map[string]interface{}{
						"acknowledged": true,
					}
					ms.writeJSONResponse(w, response)
					return
				}
			}
			ms.mu.Unlock()

			// 快照不存在，返回404（这是可接受的）
			http.Error(w, fmt.Sprintf("Snapshot [%s:%s] not found", repo, snapshotID), http.StatusNotFound)

		} else if r.Method == "GET" {
			// 处理获取单个快照请求

			ms.mu.RLock()
			if repoSnapshots, exists := ms.snapshots[repo]; exists {
				if snapshot, snapshotExists := repoSnapshots[snapshotID]; snapshotExists {
					ms.mu.RUnlock()

					response := map[string]interface{}{
						"snapshots": []interface{}{
							map[string]interface{}{
								"snapshot": snapshot.Snapshot,
								"state":    snapshot.State,
								"metadata": snapshot.Metadata,
							},
						},
					}
					ms.writeJSONResponse(w, response)
					return
				}
			}
			ms.mu.RUnlock()

			// 快照不存在
			http.Error(w, fmt.Sprintf("Snapshot [%s:%s] not found", repo, snapshotID), http.StatusNotFound)

		} else {
			// 不支持的方法
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})
}

// RegisterHandler 注册自定义处理器
func (ms *MockESServer) RegisterHandler(pattern string, handler http.HandlerFunc) {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.handlers[pattern] = handler
}

// ServeHTTP 实现http.Handler接口
// 处理所有HTTP请求，根据URL模式路由到相应的处理器
func (ms *MockESServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 复制handlers映射以避免在处理器执行时持有读锁
	// 这样可以防止死锁，因为处理器内部可能需要获取写锁
	ms.mu.RLock()
	handlersCopy := make(map[string]http.HandlerFunc)
	for pattern, handler := range ms.handlers {
		handlersCopy[pattern] = handler
	}
	ms.mu.RUnlock()

	// 按照特定顺序检查模式，确保更具体的模式先被匹配
	// 这个顺序很重要，因为某些模式可能会重叠
	patterns := []string{
		"^/_cat/indices$",                       // 新增的索引列表API
		"^/_all(?:/_settings)?$",                // 新增的 _all 索引列表API
		"^/\\*$",                                // 新增的 /* 索引列表API
		"^/$",                                   // ES根路径
		"^/_snapshot/([^/]+)/_all$",             // 列出仓库中所有快照
		"^/_snapshot/([^/]+)/([^/]+)/_restore$", // 快照恢复
		"^/_snapshot/([^/]+)/([^/]+)$",          // 单个快照操作
	}

	// 遍历模式，找到第一个匹配的处理器
	for _, pattern := range patterns {
		if handler, exists := handlersCopy[pattern]; exists {
			if matched, _ := regexp.MatchString(pattern, r.URL.Path); matched {
				handler(w, r)
				return
			}
		}
	}

	// 如果没有找到匹配的处理器，记录请求路径并返回404
	fmt.Printf("MockESServer: No handler found for path: %s\n", r.URL.Path)
	http.NotFound(w, r)
}

// writeJSONResponse 写入JSON响应
// 设置正确的响应头，包括ES客户端验证需要的X-Elastic-Product头
func (ms *MockESServer) writeJSONResponse(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("X-Elastic-Product", "Elasticsearch") // ES客户端验证必需的响应头
	json.NewEncoder(w).Encode(data)
}

// URL 返回服务器URL
func (ms *MockESServer) URL() string {
	return ms.server.URL
}

// Close 关闭服务器
func (ms *MockESServer) Close() {
	ms.server.Close()
}

// CreateESClient 创建连接到mock服务器的ES客户端
func (ms *MockESServer) CreateESClient() (*elasticsearch.Client, error) {
	cfg := elasticsearch.Config{
		Addresses: []string{ms.URL()},
	}
	return elasticsearch.NewClient(cfg)
}

// AddSnapshot 手动添加快照（用于测试）
func (ms *MockESServer) AddSnapshot(repo, snapshotID string, metadata map[string]interface{}) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if ms.snapshots[repo] == nil {
		ms.snapshots[repo] = make(map[string]*MockSnapshot)
	}

	ms.snapshots[repo][snapshotID] = &MockSnapshot{
		Snapshot: snapshotID,
		State:    "SUCCESS",
		Metadata: metadata,
	}
}

// GetSnapshot 获取快照（用于测试验证）
func (ms *MockESServer) GetSnapshot(repo, snapshotID string) *MockSnapshot {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	if repoSnapshots, exists := ms.snapshots[repo]; exists {
		return repoSnapshots[snapshotID]
	}
	return nil
}

// ClearSnapshots 清空所有快照（用于测试清理）
func (ms *MockESServer) ClearSnapshots() {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.snapshots = make(map[string]map[string]*MockSnapshot)
}
