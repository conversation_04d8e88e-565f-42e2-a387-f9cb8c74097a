package testutil

import (
	"encoding/json"
	"fmt"
	"testing"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// BenchmarkStorageManager_CreateBackupRecord 测试创建备份记录的性能
func BenchmarkStorageManager_CreateBackupRecord(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_storage")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}

	// 阶段二改造：使用Backend接口创建StorageManager
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			_, err := manager.CreateBackupRecord(
				types.MySQL,
				fmt.Sprintf("test_db_%d", i),
				types.BackupTypeArchival,
				"",
				fmt.Sprintf("benchmark backup %d", i),
			)
			if err != nil {
				b.Fatalf("创建备份记录失败: %v", err)
			}
			i++
		}
	})
}

// BenchmarkStorageManager_SaveBackupRecord 测试保存备份记录的性能
func BenchmarkStorageManager_SaveBackupRecord(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_storage")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}

	// 阶段二改造：使用Backend接口创建StorageManager
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	// 预创建备份记录
	records := make([]*types.BackupRecord, b.N)
	for i := 0; i < b.N; i++ {
		record, err := manager.CreateBackupRecord(
			types.MySQL,
			fmt.Sprintf("test_db_%d", i),
			types.BackupTypeArchival,
			"",
			fmt.Sprintf("benchmark backup %d", i),
		)
		if err != nil {
			b.Fatalf("创建备份记录失败: %v", err)
		}
		record.Status = types.BackupStatusCompleted
		records[i] = record
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := manager.SaveBackupRecord(records[i])
		if err != nil {
			b.Fatalf("保存备份记录失败: %v", err)
		}
	}
}

// BenchmarkStorageManager_ListArchivalBackups 测试列出归档备份的性能
func BenchmarkStorageManager_ListArchivalBackups(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_storage")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	// 预创建一些备份记录
	numBackups := 100
	for i := 0; i < numBackups; i++ {
		record, err := manager.CreateBackupRecord(
			types.MySQL,
			"testdb",
			types.BackupTypeArchival,
			"",
			fmt.Sprintf("benchmark backup %d", i),
		)
		if err != nil {
			b.Fatalf("创建备份记录失败: %v", err)
		}
		record.Status = types.BackupStatusCompleted
		err = manager.SaveBackupRecord(record)
		if err != nil {
			b.Fatalf("保存备份记录失败: %v", err)
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.ListArchivalBackups(types.MySQL)
		if err != nil {
			b.Fatalf("列出归档备份失败: %v", err)
		}
	}
}

// BenchmarkStorageManager_FindBackupRecord 测试查找备份记录的性能
func BenchmarkStorageManager_FindBackupRecord(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_storage")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	// 预创建一些备份记录
	numBackups := 100
	backupIDs := make([]string, numBackups)
	for i := 0; i < numBackups; i++ {
		record, err := manager.CreateBackupRecord(
			types.MySQL,
			fmt.Sprintf("test_db_%d", i),
			types.BackupTypeArchival,
			"",
			fmt.Sprintf("benchmark backup %d", i),
		)
		if err != nil {
			b.Fatalf("创建备份记录失败: %v", err)
		}
		record.Status = types.BackupStatusCompleted
		err = manager.SaveBackupRecord(record)
		if err != nil {
			b.Fatalf("保存备份记录失败: %v", err)
		}
		backupIDs[i] = record.ID
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		backupID := backupIDs[i%numBackups]
		_, err := manager.FindBackupRecord(types.MySQL, backupID)
		if err != nil {
			b.Fatalf("查找备份记录失败: %v", err)
		}
	}
}

// BenchmarkTypes_BackupRecord_Marshal 测试备份记录序列化性能
func BenchmarkTypes_BackupRecord_Marshal(b *testing.B) {
	record := CreateTestBackupRecord(types.MySQL, types.BackupTypeArchival, "benchmark-record")
	record.Extra = `{"large_metadata": "` + string(make([]byte, 1024)) + `"}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(record)
		if err != nil {
			b.Fatalf("序列化备份记录失败: %v", err)
		}
	}
}

// BenchmarkTypes_Task_StatusCheck 测试任务状态检查性能
func BenchmarkTypes_Task_StatusCheck(b *testing.B) {
	task := CreateTestTask(types.BackupTask, types.MySQL)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = task.Status.IsTerminal()
	}
}

// BenchmarkConcurrentBackupCreation 测试并发创建备份的性能
func BenchmarkConcurrentBackupCreation(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_concurrent")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			record, err := manager.CreateBackupRecord(
				types.MySQL,
				fmt.Sprintf("test_db_%d", i),
				types.BackupTypeArchival,
				"",
				fmt.Sprintf("concurrent backup %d", i),
			)
			if err != nil {
				b.Fatalf("创建备份记录失败: %v", err)
			}
			record.Status = types.BackupStatusCompleted
			err = manager.SaveBackupRecord(record)
			if err != nil {
				b.Fatalf("保存备份记录失败: %v", err)
			}
			i++
		}
	})
}

// BenchmarkMemoryUsage 测试内存使用情况
func BenchmarkMemoryUsage(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_memory")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	// 创建大量备份记录来测试内存使用
	numRecords := 1000
	records := make([]*types.BackupRecord, numRecords)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 创建记录
		for j := 0; j < numRecords; j++ {
			record, err := manager.CreateBackupRecord(
				types.MySQL,
				"testdb",
				types.BackupTypeArchival,
				"",
				fmt.Sprintf("memory test backup %d-%d", i, j),
			)
			if err != nil {
				b.Fatalf("创建备份记录失败: %v", err)
			}
			records[j] = record
		}

		// 保存记录
		for j := 0; j < numRecords; j++ {
			records[j].Status = types.BackupStatusCompleted
			err := manager.SaveBackupRecord(records[j])
			if err != nil {
				b.Fatalf("保存备份记录失败: %v", err)
			}
		}

		// 列出记录
		_, err := manager.ListArchivalBackups(types.MySQL)
		if err != nil {
			b.Fatalf("列出备份失败: %v", err)
		}
	}
}

// BenchmarkErrorHandling 测试错误处理性能
func BenchmarkErrorHandling(b *testing.B) {
	tempDir, cleanup := CreateTempDir(&testing.T{}, "benchmark_error")
	defer cleanup()

	logger := zap.NewNop()
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
	}
	backend, err := local.NewLocalBackend(tempDir)
	if err != nil {
		b.Fatalf("Failed to create LocalBackend: %v", err)
	}
	mockTaskManager := &MockTaskManager{}
	manager := storage.NewManager(backend, cfg, logger, mockTaskManager)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 尝试查找不存在的备份记录
		_, err := manager.FindBackupRecord(types.MySQL, "non-existent-backup")
		if err == nil {
			b.Fatal("期望返回错误，但没有返回")
		}
	}
}

// 运行基准测试的示例：
// go test -bench=. -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof ./internal/testutil/
//
// 分析性能：
// go tool pprof cpu.prof
// go tool pprof mem.prof
