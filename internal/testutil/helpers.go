// Package testutil 提供了用于测试的通用工具函数和辅助方法。
// 这个包包含了创建测试配置、临时目录、测试数据等功能，
// 旨在简化测试代码的编写并提高测试的可维护性。
package testutil

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// TestConfig 创建用于测试的标准配置。
// 该函数返回一个预配置的Config实例，包含测试所需的所有基本设置。
// 参数:
//   - t: 测试实例，用于错误报告
//   - backupRoot: 备份根目录路径
//
// 返回: 配置好的Config实例
func TestConfig(t *testing.T, backupRoot string) *types.Config {
	logger := zap.NewNop()

	return &types.Config{
		BackupRoot:         backupRoot,
		Logger:             logger,
		MaxConcurrentTasks: 2, // 测试时使用较小的并发数
		TaskRetentionDays:  1,
		MaxTaskHistory:     10,
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "test_user",
			Password: "test_password",
			DBName:   "test_db",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "mysqldump",
				Mysql:       "mysql",
				Mysqlbinlog: "mysqlbinlog",
				Mysqladmin:  "mysqladmin",
			},
		},
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			User:             "elastic",
			Password:         "changeme",
			ArchivalRepoName: "test_archival",
			ManagedRepoName:  "test_managed",
		},
	}
}

// CreateTempDir 创建临时测试目录。
// 该函数会在系统临时目录中创建一个唯一的测试目录，
// 并返回清理函数用于测试结束后的资源清理。
// 参数:
//   - t: 测试实例
//   - prefix: 目录名前缀
//
// 返回: 临时目录路径和清理函数
func CreateTempDir(t *testing.T, prefix string) (string, func()) {
	tempDir, err := os.MkdirTemp("", prefix+"_*")
	require.NoError(t, err, "创建临时目录失败")

	cleanup := func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Logf("警告：清理临时目录失败 %s: %v", tempDir, err)
		}
	}

	return tempDir, cleanup
}

// CreateTestBackupRecord 创建用于测试的备份记录。
// 该函数生成一个包含基本测试数据的BackupRecord实例。
// 参数:
//   - sourceType: 数据源类型（如MySQL、Elasticsearch）
//   - backupType: 备份类型（如归档备份、增量备份等）
//
// 返回: 配置好的备份记录实例
func CreateTestBackupRecord(sourceType types.SourceType, backupType types.BackupType, id string) *types.BackupRecord {
	return &types.BackupRecord{
		ID:            id,
		Timestamp:     time.Now(),
		Source:        sourceType,
		Type:          backupType,
		Path:          filepath.Join("/test/path", id),
		Description:   fmt.Sprintf("测试备份记录 %s", id),
		Status:        types.BackupStatusCompleted,
		Size:          1024,
		ExecutionTime: types.Duration(5 * time.Second),
	}
}

// CreateTestChainRecords 创建用于测试的增量链记录
func CreateTestChainRecords(chainID string, count int) []*types.BackupRecord {
	records := make([]*types.BackupRecord, count)

	for i := 0; i < count; i++ {
		backupType := types.BackupTypeChainInitial
		if i > 0 {
			backupType = types.BackupTypeChainIncremental
		}

		record := &types.BackupRecord{
			ID:            fmt.Sprintf("%s-backup-%d", chainID, i),
			Timestamp:     time.Now().Add(time.Duration(i) * time.Hour),
			Source:        types.MySQL,
			Type:          backupType,
			Path:          filepath.Join("/test/path", chainID, fmt.Sprintf("backup-%d", i)),
			Description:   fmt.Sprintf("增量链 %s 备份 %d", chainID, i),
			ChainID:       chainID,
			Status:        types.BackupStatusCompleted,
			Size:          1024 * int64(i+1),
			ExecutionTime: types.Duration(time.Duration(i+1) * time.Second),
		}

		if i > 0 {
			record.ParentID = records[i-1].ID
		}

		records[i] = record
	}

	return records
}

// CreateTestTask 创建用于测试的任务
func CreateTestTask(taskType types.TaskType, sourceType types.SourceType) *types.Task {
	// 为了避免并发测试中的竞态条件，每个任务使用独立的Metadata map
	metadata := make(map[string]interface{})
	metadata["test"] = true

	return &types.Task{
		ID:          fmt.Sprintf("test-task-%d", time.Now().UnixNano()),
		Type:        taskType,
		Source:      sourceType,
		Status:      types.TaskStatusPending,
		Progress:    0,
		StartTime:   time.Now(),
		Description: "测试任务",
		Metadata:    metadata,
	}
}

// WaitForCondition 等待条件满足或超时
func WaitForCondition(condition func() bool, timeout time.Duration, interval time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		if condition() {
			return nil
		}
		time.Sleep(interval)
	}

	return fmt.Errorf("等待条件超时")
}

// AssertTaskCompleted 断言任务已完成
func AssertTaskCompleted(t *testing.T, task *types.Task) {
	require.NotNil(t, task, "任务不能为nil")
	require.True(t, task.Status.IsTerminal(), "任务应该处于终结状态")
	require.Equal(t, types.TaskStatusCompleted, task.Status, "任务应该已完成")
}

// AssertTaskFailed 断言任务已失败
func AssertTaskFailed(t *testing.T, task *types.Task) {
	require.NotNil(t, task, "任务不能为nil")
	require.True(t, task.Status.IsTerminal(), "任务应该处于终结状态")
	require.Equal(t, types.TaskStatusFailed, task.Status, "任务应该已失败")
	require.NotEmpty(t, task.Error, "失败的任务应该有错误信息")
}

// CreateTestContext 创建带超时的测试上下文
func CreateTestContext(timeout time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), timeout)
}

// SkipIfNoMySQL 如果没有 MySQL 环境则跳过测试
func SkipIfNoMySQL(t *testing.T) {
	// 这里可以添加检查 MySQL 是否可用的逻辑
	// 例如尝试连接 MySQL 服务器
	// 如果连接失败，则调用 t.Skip()

	// 简单的环境变量检查
	if os.Getenv("SKIP_MYSQL_TESTS") == "true" {
		t.Skip("跳过 MySQL 测试（设置了 SKIP_MYSQL_TESTS=true）")
	}
}

// SkipIfNoElasticsearch 如果没有 Elasticsearch 环境则跳过测试
func SkipIfNoElasticsearch(t *testing.T) {
	// 这里可以添加检查 Elasticsearch 是否可用的逻辑
	// 例如尝试连接 Elasticsearch 集群
	// 如果连接失败，则调用 t.Skip()

	// 简单的环境变量检查
	if os.Getenv("SKIP_ES_TESTS") == "true" {
		t.Skip("跳过 Elasticsearch 测试（设置了 SKIP_ES_TESTS=true）")
	}
}

// MockError 创建用于测试的错误
func MockError(code, message string) *types.BackupError {
	return &types.BackupError{
		Code:      code,
		Message:   message,
		Component: "TestMock",
		Operation: "MockOperation",
		Timestamp: time.Now(),
		Retryable: false,
	}
}

// AssertBackupError 断言备份错误的属性
func AssertBackupError(t *testing.T, err *types.BackupError, expectedCode string) {
	require.NotNil(t, err, "错误不能为nil")
	require.Equal(t, expectedCode, err.Code, "错误代码不匹配")
	require.NotEmpty(t, err.Message, "错误消息不能为空")
	require.NotEmpty(t, err.Component, "错误组件不能为空")
	require.NotEmpty(t, err.Operation, "错误操作不能为空")
}

// CreateTestFile 创建测试文件
func CreateTestFile(t *testing.T, dir, filename, content string) string {
	filePath := filepath.Join(dir, filename)
	err := os.WriteFile(filePath, []byte(content), 0644)
	require.NoError(t, err, "创建测试文件失败")
	return filePath
}

// FileExists 检查文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// DirExists 检查目录是否存在
func DirExists(path string) bool {
	info, err := os.Stat(path)
	return err == nil && info.IsDir()
}

// GetFileSize 获取文件大小
func GetFileSize(path string) (int64, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// CompareFiles 比较两个文件是否相同
func CompareFiles(t *testing.T, file1, file2 string) {
	content1, err := os.ReadFile(file1)
	require.NoError(t, err, "读取文件1失败")

	content2, err := os.ReadFile(file2)
	require.NoError(t, err, "读取文件2失败")

	require.Equal(t, content1, content2, "文件内容不匹配")
}

// MockTaskManager 实现 TaskManager 接口用于测试
type MockTaskManager struct {
	mock.Mock
}

func (m *MockTaskManager) GetTask(taskID string) (*types.Task, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.Task), args.Error(1)
}

func (m *MockTaskManager) ListTasks() ([]*types.Task, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*types.Task), args.Error(1)
}

func (m *MockTaskManager) DeleteTask(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}
