package testutil

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/elastic/go-elasticsearch/v8"
	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

// IntegrationTestConfig 集成测试配置
type IntegrationTestConfig struct {
	MySQL struct {
		Address      string `yaml:"address"`
		Port         int    `yaml:"port"`
		Username     string `yaml:"username"`
		Password     string `yaml:"password"`
		Database     string `yaml:"database"`
		TestDatabase string `yaml:"test_database"`
		ToolsPath    struct {
			Mysqldump   string `yaml:"mysqldump"`
			MySQL       string `yaml:"mysql"`
			Mysqlbinlog string `yaml:"mysqlbinlog"`
			Mysqladmin  string `yaml:"mysqladmin"`
		} `yaml:"tools_path"`
	} `yaml:"mysql"`

	Elasticsearch struct {
		Address          string `yaml:"address"`
		Port             int    `yaml:"port"`
		Username         string `yaml:"username"`
		Password         string `yaml:"password"`
		TestIndexPrefix  string `yaml:"test_index_prefix"`
		ArchivalRepoName string `yaml:"archival_repo_name"`
		ManagedRepoName  string `yaml:"managed_repo_name"`
	} `yaml:"elasticsearch"`

	Storage struct {
		BasePath string `yaml:"base_path"`
		TempPath string `yaml:"temp_path"`
	} `yaml:"storage"`

	Test struct {
		EnableIntegration bool   `yaml:"enable_integration"`
		Timeout           int    `yaml:"timeout"`
		CleanupAfterTest  bool   `yaml:"cleanup_after_test"`
		MaxTestDataSize   string `yaml:"max_test_data_size"`
	} `yaml:"test"`
}

// LoadIntegrationConfig 加载集成测试配置
func LoadIntegrationConfig() (*IntegrationTestConfig, error) {
	// 尝试多个可能的配置文件路径
	configPaths := []string{
		"test_integration_config.yaml",          // 当前目录
		"../../test_integration_config.yaml",    // 从子目录向上两级（如果在internal/provider/mysql中运行）
		"../../../test_integration_config.yaml", // 从更深的子目录向上三级
	}

	var configPath string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configPath = path
			break
		}
	}

	if configPath == "" {
		return nil, fmt.Errorf("integration config file not found in any of the expected locations")
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config IntegrationTestConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return &config, nil
}

// SkipIfNoIntegration 如果没有启用集成测试则跳过
func SkipIfNoIntegration(t *testing.T) *IntegrationTestConfig {
	config, err := LoadIntegrationConfig()
	if err != nil {
		t.Skipf("Skipping integration test: %v", err)
	}

	if !config.Test.EnableIntegration {
		t.Skip("Integration tests disabled in config")
	}

	return config
}

// MySQLTestHelper MySQL测试辅助工具
type MySQLTestHelper struct {
	t      *testing.T
	config *IntegrationTestConfig
	db     *sql.DB
}

// NewMySQLTestHelper 创建MySQL测试辅助工具
func NewMySQLTestHelper(t *testing.T, config *IntegrationTestConfig) *MySQLTestHelper {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.MySQL.Username,
		config.MySQL.Password,
		config.MySQL.Address,
		config.MySQL.Port,
		config.MySQL.Database,
	)

	db, err := sql.Open("mysql", dsn)
	require.NoError(t, err, "Failed to connect to MySQL")

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = db.PingContext(ctx)
	if err != nil {
		t.Skipf("MySQL not available: %v", err)
	}

	return &MySQLTestHelper{
		t:      t,
		config: config,
		db:     db,
	}
}

// CreateTestDatabase 创建测试数据库
func (h *MySQLTestHelper) CreateTestDatabase() {
	_, err := h.db.Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s", h.config.MySQL.TestDatabase))
	require.NoError(h.t, err, "Failed to create test database")
}

// DropTestDatabase 删除测试数据库
func (h *MySQLTestHelper) DropTestDatabase() {
	_, err := h.db.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", h.config.MySQL.TestDatabase))
	require.NoError(h.t, err, "Failed to drop test database")
}

// CreateTestTable 创建测试表
func (h *MySQLTestHelper) CreateTestTable() {
	useDB := fmt.Sprintf("USE %s", h.config.MySQL.TestDatabase)
	_, err := h.db.Exec(useDB)
	require.NoError(h.t, err, "Failed to use test database")

	createTable := `
		CREATE TABLE IF NOT EXISTS test_table (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(100) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`
	_, err = h.db.Exec(createTable)
	require.NoError(h.t, err, "Failed to create test table")
}

// InsertTestData 插入测试数据
func (h *MySQLTestHelper) InsertTestData(count int) {
	for i := 0; i < count; i++ {
		_, err := h.db.Exec("INSERT INTO test_table (name) VALUES (?)", fmt.Sprintf("test_record_%d", i))
		require.NoError(h.t, err, "Failed to insert test data")
	}
}

// GetMySQLConfig 获取MySQL配置
func (h *MySQLTestHelper) GetMySQLConfig() *types.MySQLConfig {
	return &types.MySQLConfig{
		Host:     "127.0.0.1", // 使用IP地址强制使用TCP连接而不是socket
		Port:     h.config.MySQL.Port,
		User:     h.config.MySQL.Username,
		Password: h.config.MySQL.Password,
		DBName:   h.config.MySQL.TestDatabase,
		ToolsPath: types.MySQLToolsPath{
			Mysqldump:   h.config.MySQL.ToolsPath.Mysqldump,
			Mysql:       h.config.MySQL.ToolsPath.MySQL,
			Mysqlbinlog: h.config.MySQL.ToolsPath.Mysqlbinlog,
			Mysqladmin:  h.config.MySQL.ToolsPath.Mysqladmin,
		},
	}
}

// Close 关闭数据库连接
func (h *MySQLTestHelper) Close() {
	if h.db != nil {
		h.db.Close()
	}
}

// DropTestTable 删除测试表
func (h *MySQLTestHelper) DropTestTable() {
	useDB := fmt.Sprintf("USE %s", h.config.MySQL.TestDatabase)
	_, err := h.db.Exec(useDB)
	require.NoError(h.t, err, "Failed to use test database")

	dropTable := "DROP TABLE IF EXISTS test_table"
	_, err = h.db.Exec(dropTable)
	require.NoError(h.t, err, "Failed to drop test table")
}

// ElasticsearchTestHelper Elasticsearch测试辅助工具
type ElasticsearchTestHelper struct {
	t      *testing.T
	config *IntegrationTestConfig
	client *elasticsearch.Client
}

// NewElasticsearchTestHelper 创建Elasticsearch测试辅助工具
func NewElasticsearchTestHelper(t *testing.T, config *IntegrationTestConfig) *ElasticsearchTestHelper {
	esConfig := elasticsearch.Config{
		Addresses: []string{fmt.Sprintf("http://%s:%d", config.Elasticsearch.Address, config.Elasticsearch.Port)},
	}

	if config.Elasticsearch.Username != "" {
		esConfig.Username = config.Elasticsearch.Username
		esConfig.Password = config.Elasticsearch.Password
	}

	client, err := elasticsearch.NewClient(esConfig)
	require.NoError(t, err, "Failed to create Elasticsearch client")

	// 测试连接
	res, err := client.Info()
	if err != nil {
		t.Skipf("Elasticsearch not available: %v", err)
	}
	defer res.Body.Close()

	return &ElasticsearchTestHelper{
		t:      t,
		config: config,
		client: client,
	}
}

// GetESConfig 获取ES配置
func (h *ElasticsearchTestHelper) GetESConfig() *types.ESConfig {
	return &types.ESConfig{
		Addresses:        []string{fmt.Sprintf("http://%s:%d", h.config.Elasticsearch.Address, h.config.Elasticsearch.Port)},
		User:             h.config.Elasticsearch.Username,
		Password:         h.config.Elasticsearch.Password,
		ArchivalRepoName: h.config.Elasticsearch.ArchivalRepoName,
		ManagedRepoName:  h.config.Elasticsearch.ManagedRepoName,
	}
}

// CreateTestIndex 创建测试索引
func (h *ElasticsearchTestHelper) CreateTestIndex(indexName string) {
	mapping := `{
		"mappings": {
			"properties": {
				"title": {"type": "text"},
				"content": {"type": "text"},
				"timestamp": {"type": "date"}
			}
		}
	}`

	res, err := h.client.Indices.Create(indexName, h.client.Indices.Create.WithBody(strings.NewReader(mapping)))
	require.NoError(h.t, err, "Failed to create test index")
	defer res.Body.Close()
}

// DeleteTestIndex 删除测试索引
func (h *ElasticsearchTestHelper) DeleteTestIndex(indexName string) {
	res, err := h.client.Indices.Delete([]string{indexName})
	if err == nil {
		defer res.Body.Close()
	}
}

// SetupTestDirectories 设置测试目录
func SetupTestDirectories(t *testing.T, config *IntegrationTestConfig) {
	err := os.MkdirAll(config.Storage.BasePath, 0755)
	require.NoError(t, err, "Failed to create test base path")

	err = os.MkdirAll(config.Storage.TempPath, 0755)
	require.NoError(t, err, "Failed to create test temp path")
}

// CleanupTestDirectories 清理测试目录
func CleanupTestDirectories(config *IntegrationTestConfig) {
	os.RemoveAll(config.Storage.BasePath)
	os.RemoveAll(config.Storage.TempPath)
}

// GetTestLogger 获取测试用的logger
func GetTestLogger() *zap.Logger {
	logger, _ := zap.NewDevelopment()
	return logger
}
