package api

import (
	"context"
	"fmt"
	"io"
	"sort"
	"strings"
	"time"

	configpkg "git.gobies.org/fobrain/unibackup/internal/config"
	berrors "git.gobies.org/fobrain/unibackup/internal/errors"
	"git.gobies.org/fobrain/unibackup/internal/lock"
	"git.gobies.org/fobrain/unibackup/internal/provider"
	"git.gobies.org/fobrain/unibackup/internal/provider/elasticsearch"
	"git.gobies.org/fobrain/unibackup/internal/provider/mysql"
	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/internal/task"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"
)

// Package api 实现了 unibackup SDK 的核心业务逻辑和公开接口。
//
// 这个包中的 `manager` 结构体是 unibackup.BackupManager 接口的实现，它扮演着总调度器的角色。
// 它本身不执行具体的备份、恢复或文件系统操作，而是通过协调以下几个底层组件来完成复杂的任务编排：
//
// 1. **lock.Locker**: 在执行任何操作前，获取基于资源名称的互斥锁，确保对同一数据源的操作是串行的，防止竞态条件。
// 2. **storage.StorageManager**: 负责所有与元数据和备份文件相关的磁盘操作，如创建/保存备份记录、管理增量链元数据等。
// 3. **task.TaskManager**: 负责执行实际的、可能长时间运行的备份/恢复任务。
//
// BackupManager 的主要职责是将用户的简单调用（如 "备份这个数据库"）转化为一个包含多个步骤的、
// 具有事务性保证的、健壮的工作流。

// manager 是 unibackup.BackupManager 接口的具体实现。
// 它聚合了所有必要的底层管理器，作为SDK的中心协调器。
type manager struct {
	storageManager storage.StorageManager
	taskManager    task.TaskManager
	cfg            *types.Config
	locker         *lock.Locker
	providers      map[types.SourceType]provider.BackupProvider
}

// NewManager 创建一个新的内部 manager 实例。
// 这是 BackupManager 接口的内部构造函数。
// 公开的 NewManager 位于 pkg/unibackup 包中，它会先初始化所有依赖，然后调用此函数。
func NewManager(cfg *types.Config, sm storage.StorageManager, tm task.TaskManager, locker *lock.Locker, providers map[types.SourceType]provider.BackupProvider) (*manager, error) {
	return &manager{
		storageManager: sm,
		taskManager:    tm,
		cfg:            cfg,
		locker:         locker,
		providers:      providers,
	}, nil
}

// === 异步操作实现 ===
// 所有异步操作都直接委托给 TaskManager，因为 TaskManager 专门负责管理异步任务的整个生命周期，
// 包括状态持久化、并发控制和故障恢复。BackupManager 在这里仅作为API的入口。

// BackupAsync 异步地启动一个备份任务。
func (m *manager) BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error) {
	// 数据源健康检查
	if err := m.storageManager.CheckDataSourceHealth(sourceType, m.providers); err != nil {
		return "", fmt.Errorf("数据源 %s 健康检查失败: %w", sourceType, err)
	}

	// 获取数据源锁确保同一数据源的备份/恢复操作是串行的
	sourceKey := fmt.Sprintf("%s-%s", sourceType, sourceName)
	if err := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); err != nil {
		return "", fmt.Errorf("获取锁失败: %w", err)
	}
	defer m.locker.Unlock(sourceKey)

	return m.taskManager.BackupAsync(ctx, sourceType, sourceName, backupType, description)
}

// RestoreAsync 异步地启动一个恢复任务（使用完全重建策略）。
func (m *manager) RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error) {
	// 数据源健康检查
	if err := m.storageManager.CheckDataSourceHealth(config.SourceType, m.providers); err != nil {
		return "", fmt.Errorf("数据源 %s 健康检查失败: %w", config.SourceType, err)
	}

	// 获取数据源锁确保同一数据源的操作是串行的
	sourceKey := fmt.Sprintf("%s-%s", config.SourceType, config.SourceName)
	if err := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); err != nil {
		return "", fmt.Errorf("获取锁失败: %w", err)
	}
	defer m.locker.Unlock(sourceKey)

	return m.taskManager.RestoreAsync(ctx, config)
}

// BackupAllAsync 异步地启动一个分组备份任务。
func (m *manager) BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error) {
	// 在分派任务前，先对所有涉及的数据源进行一次健康检查，实现快速失败。
	// 使用 sourceType-sourceName 组合作为键，确保每个具体的数据源实例都被检查
	checked := make(map[string]bool)
	for _, src := range req.Sources {
		sourceKey := fmt.Sprintf("%s-%s", src.SourceType, src.SourceName)
		if checked[sourceKey] {
			continue
		}
		// 检查数据源类型级别的健康状态
		if err := m.storageManager.CheckDataSourceHealth(src.SourceType, m.providers); err != nil {
			return "", fmt.Errorf("数据源 %s 健康检查失败: %w", src.SourceType, err)
		}
		checked[sourceKey] = true
	}
	return m.taskManager.BackupAllAsync(ctx, req)
}

// RestoreAllAsync 异步地启动一个分组恢复任务（使用完全重建策略）。
func (m *manager) RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error) {
	// 在分派任务前进行健康检查
	checked := make(map[string]bool)
	for _, restoreConfig := range config.Restores {
		sourceKey := fmt.Sprintf("%s-%s", restoreConfig.SourceType, restoreConfig.SourceName)
		if checked[sourceKey] {
			continue
		}
		// 检查数据源类型级别的健康状态
		if err := m.storageManager.CheckDataSourceHealth(restoreConfig.SourceType, m.providers); err != nil {
			return "", fmt.Errorf("数据源 %s 健康检查失败: %w", restoreConfig.SourceType, err)
		}
		checked[sourceKey] = true
	}
	return m.taskManager.RestoreAllAsync(ctx, config)
}

// === 任务管理实现 ===
// 任务管理相关的接口也直接委托给 TaskManager。

// GetTask 获取任务详情。
func (m *manager) GetTask(taskID string) (*types.Task, error) {
	return m.taskManager.GetTask(taskID)
}

// ListTasks 列出最近的任务。
func (m *manager) ListTasks() ([]*types.Task, error) {
	return m.taskManager.ListTasks()
}

// CancelTask 尝试取消一个任务。
func (m *manager) CancelTask(taskID string) error {
	return m.taskManager.CancelTask(taskID)
}

// ClearOldTasks 手动清理旧的任务记录。
func (m *manager) ClearOldTasks() error {
	return m.taskManager.ClearOldTasks()
}

// Shutdown 优雅地关闭任务管理器。
func (m *manager) Shutdown() error {
	return m.taskManager.Shutdown()
}

// === 同步操作实现 ===

// Backup 编排一个完整的同步备份操作。这是SDK中最核心的业务逻辑之一。
func (m *manager) Backup(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (*types.BackupRecord, *types.BackupError) {
	// 步骤 1: 数据源健康检查
	if err := m.storageManager.CheckDataSourceHealth(sourceType, m.providers); err != nil {
		return nil, berrors.NewAPIManagerError("HEALTH_CHECK_FAILED", "Backup", fmt.Errorf("数据源健康检查失败: %w", err), false)
	}

	// 步骤 2: 获取数据源锁
	sourceKey := fmt.Sprintf("%s-%s", sourceType, sourceName)
	if err := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); err != nil {
		return nil, berrors.NewAPIManagerError("LOCK_TIMEOUT", "LockWithTimeout", err, true)
	}
	defer m.locker.Unlock(sourceKey)

	m.cfg.Logger.Info("启动同步备份流程",
		zap.String("source", sourceName),
		zap.String("type", string(backupType)))

	// 步骤 3: 创建初始备份记录
	// 对于增量备份，需要先找到父链ID
	var parentChainID string
	if backupType == types.BackupTypeChainIncremental {
		chains, err := m.storageManager.ListIncrementalChains(sourceType, sourceName)
		if err != nil {
			return nil, err
		}
		if len(chains) == 0 {
			return nil, berrors.NewAPIManagerError("INVALID_BACKUP_TYPE", "Backup", fmt.Errorf("无法为 %s 执行增量备份：未找到现有备份链", sourceName), false)
		}
		parentChainID = chains[len(chains)-1].ChainID
	}

	record, err := m.storageManager.CreateBackupRecord(sourceType, sourceName, backupType, parentChainID, description)
	if err != nil {
		return nil, berrors.NewAPIManagerError("STORAGE_ERROR", "CreateBackupRecord", err, false)
	}

	// 步骤 4: 执行核心备份逻辑
	startTime := time.Now()
	if execErr := m.taskManager.RunBackup(ctx, record); execErr != nil {
		record.Status = types.BackupStatusFailed
		record.Error = execErr.Message
		record.ErrorDetail = execErr
	} else {
		record.Status = types.BackupStatusCompleted
	}
	record.ExecutionTime = types.Duration(time.Since(startTime))

	// 步骤 5: 保存最终元数据
	if saveErr := m.storageManager.SaveBackupRecordAtomic(record); saveErr != nil {
		return record, berrors.NewAPIManagerError("STORAGE_ERROR", "SaveBackupRecordAtomic", fmt.Errorf("保存最终元数据失败: %w", saveErr), false)
	}

	m.cfg.Logger.Info("同步备份流程完成",
		zap.String("id", record.ID),
		zap.String("status", string(record.Status)))

	if record.Status == types.BackupStatusCompleted {
		return record, nil
	}
	return record, record.ErrorDetail
}

// Restore 编排一个完整的同步恢复操作（使用完全重建策略）。
func (m *manager) Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError {
	// 获取数据源锁，防止与其它操作冲突。
	sourceKey := fmt.Sprintf("%s-%s", config.SourceType, config.SourceName)
	if err := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); err != nil {
		return berrors.NewAPIManagerError("LOCK_TIMEOUT", "LockWithTimeout", err, true)
	}
	defer m.locker.Unlock(sourceKey)

	m.cfg.Logger.Info("启动恢复流程",
		zap.String("source", config.SourceName),
		zap.String("backupID", config.BackupID))

	// 步骤 1: 在开始重量级的恢复操作之前，先快速验证目标备份记录是否存在。
	// 这是一个"快速失败"检查。真正的恢复链查找和校验逻辑由下层的 task.Manager 和 storage.Manager 处理。
	record, err := m.storageManager.FindBackupRecord(config.SourceType, config.BackupID)
	if err != nil {
		return berrors.NewAPIManagerError("STORAGE_ERROR", "FindBackupRecord", fmt.Errorf("查找用于发起恢复的备份记录 %s 失败: %w", config.BackupID, err), false)
	}
	if record == nil {
		return berrors.NewAPIManagerError("BACKUP_NOT_FOUND", "Restore", fmt.Errorf("未找到ID为 %s 的备份记录", config.BackupID), false)
	}
	// TODO: 可以在这里添加额外的校验，例如检查恢复的目标环境是否与备份时的环境兼容。

	// 步骤 2: 调用 TaskManager 执行实际的恢复任务。
	if err = m.taskManager.Restore(ctx, config); err != nil {
		m.cfg.Logger.Error("恢复流程失败",
			zap.String("backupID", config.BackupID),
			zap.Error(err))
		return berrors.NewAPIManagerError("TASK_EXECUTION_FAILED", "Restore", err, false)
	}

	m.cfg.Logger.Info("恢复流程成功完成", zap.String("backupID", config.BackupID))
	return nil
}

// === 备份管理实现 ===

// ListArchivalBackups 列出所有独立的归档备份。
func (m *manager) ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError) {
	return m.storageManager.ListArchivalBackups(sourceType)
}

// ListIncrementalChains 列出所有增量备份链。
// 注意：此方法已废弃，因为它无法区分不同的数据源实例。
// 建议使用 ListAllBackups 方法进行更精确的查询。
// 为了向后兼容性，此方法会尝试从所有已知的数据源实例中收集增量链。
func (m *manager) ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError) {
	// 使用统一查询接口获取指定数据源类型的所有任务，以发现数据源实例
	filter := types.BackupFilter{
		SourceTypes: []types.SourceType{sourceType},
	}

	result, err := m.storageManager.ListAllBackups(filter)
	if err != nil {
		return nil, berrors.NewAPIManagerError("LIST_BACKUPS_ERROR", "ListIncrementalChains", fmt.Errorf("%w", err), false)
	}

	// 按数据源实例分组，只考虑增量链相关的任务
	sourceNames := make(map[string]bool)
	for _, task := range result.Tasks {
		// 从任务的 Metadata 中获取 backup_type，只处理增量链相关的备份
		if backupTypeStr, ok := task.Metadata["backup_type"].(string); ok {
			backupType := types.BackupType(backupTypeStr)
			if backupType == types.BackupTypeChainInitial || backupType == types.BackupTypeChainIncremental {
				// 从任务的 Metadata 中获取 source_name
				if sourceName, ok := task.Metadata["source_name"].(string); ok && sourceName != "" {
					sourceNames[sourceName] = true
				}
			}
		}
	}

	allChains := make([]types.IncrementalChain, 0)
	var errors []string

	for sourceName := range sourceNames {
		chains, err := m.storageManager.ListIncrementalChains(sourceType, sourceName)
		if err != nil {
			// 收集错误信息，但继续处理其他数据源实例
			errorMsg := fmt.Sprintf("数据源实例 %s: %s", sourceName, err.Message)
			errors = append(errors, errorMsg)
			zap.L().Warn("获取数据源实例的增量链失败",
				zap.String("source_type", string(sourceType)),
				zap.String("source_name", sourceName),
				zap.String("error", err.Message))
			continue
		}
		allChains = append(allChains, chains...)
	}

	// 如果所有数据源实例都失败了，返回错误
	if len(errors) > 0 && len(allChains) == 0 {
		return nil, berrors.NewAPIManagerError("ALL_SOURCES_FAILED", "ListIncrementalChains",
			fmt.Errorf("所有数据源实例都失败: %s", strings.Join(errors, "; ")), false)
	}

	return allChains, nil
}

// DeleteBackup 编排一个完整的删除备份操作。
//
// 此方法作为删除操作的协调器，负责：
// 1. 获取适当的资源锁以防止并发冲突
// 2. 委托给TaskManager执行具体的删除逻辑
//
// TaskManager的删除流程包括：
// - 查找备份记录确定类型
// - 调用Provider清理外部资源（如ES快照）
// - 根据备份类型删除本地文件（归档备份或整条增量链）
//
// 锁定策略：使用与备份操作相同的锁粒度（按数据源实例），确保操作的一致性。
func (m *manager) DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) *types.BackupError {
	// 先查找备份记录获取源信息，以便使用一致的锁粒度
	record, err := m.storageManager.FindBackupRecord(sourceType, backupID)
	if err != nil {
		return berrors.NewAPIManagerError("STORAGE_ERROR", "FindBackupRecord", fmt.Errorf("查找备份记录失败: %w", err), false)
	}

	// 使用与备份操作相同的锁粒度（按数据源实例锁定，使用超时防止死锁）
	sourceKey := fmt.Sprintf("%s-%s", sourceType, record.SourceName)
	if lockErr := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); lockErr != nil {
		return berrors.NewAPIManagerError("LOCK_TIMEOUT", "LockWithTimeout", fmt.Errorf("获取删除锁超时: %w", lockErr), true)
	}
	defer m.locker.Unlock(sourceKey)

	m.cfg.Logger.Info("启动删除备份流程",
		zap.String("source", string(sourceType)),
		zap.String("backupID", backupID))

	// 委托给TaskManager执行具体的删除逻辑
	return m.taskManager.DeleteBackup(ctx, sourceType, backupID)
}

// RestoreByTaskID 基于taskID恢复备份到原始位置（向后兼容版本）。
func (m *manager) RestoreByTaskID(ctx context.Context, taskID string, force bool) (string, error) {
	return m.RestoreByTaskIDWithCallback(ctx, taskID, force, nil)
}

// RestoreByTaskIDWithCallback 基于taskID恢复备份到原始位置（带回调版本）。
func (m *manager) RestoreByTaskIDWithCallback(ctx context.Context, taskID string, force bool, callback types.RestoreSuccessCallback) (string, error) {
	// 步骤1: 获取任务信息
	task, err := m.taskManager.GetTask(taskID)
	if err != nil {
		return "", fmt.Errorf("获取任务信息失败: %w", err)
	}

	// 步骤2: 验证是否为备份任务
	if task.Type != types.BackupTask && task.Type != types.BackupAllTask {
		return "", fmt.Errorf("任务ID %s 不是备份任务，类型为: %s", taskID, task.Type)
	}

	// 步骤3: 验证任务状态
	if task.Status != types.TaskStatusCompleted {
		return "", fmt.Errorf("只能恢复已完成的备份任务，当前状态: %s", task.Status)
	}

	// 步骤4: 处理分组备份的情况
	if task.Type == types.BackupAllTask {
		return m.handleGroupBackupRestore(ctx, task, force, callback)
	}

	// 步骤5: 获取备份记录ID
	backupRecordID, ok := task.Metadata["backup_record_id"].(string)
	if !ok || backupRecordID == "" {
		return "", fmt.Errorf("任务记录中缺少备份记录ID")
	}

	// 步骤6: 获取原始数据源名称
	sourceName, ok := task.Metadata["source_name"].(string)
	if !ok || sourceName == "" {
		return "", fmt.Errorf("任务记录中缺少原始数据源名称")
	}

	// 步骤7: 构造恢复配置
	restoreConfig := types.NewRestoreConfig(task.Source, sourceName, backupRecordID)
	restoreConfig.Description = fmt.Sprintf("基于任务ID %s 的自动恢复", taskID)
	restoreConfig.Force = force        // 添加强制恢复支持
	restoreConfig.OnSuccess = callback // 设置单个恢复回调

	// 步骤8: 执行异步恢复
	m.cfg.Logger.Info("启动基于任务ID的恢复流程",
		zap.String("task_id", taskID),
		zap.String("source_type", string(task.Source)),
		zap.String("source_name", sourceName),
		zap.String("backup_record_id", backupRecordID))

	return m.RestoreAsync(ctx, restoreConfig)
}

// handleGroupBackupRestore 处理分组备份任务的恢复
func (m *manager) handleGroupBackupRestore(ctx context.Context, task *types.Task, force bool, callback types.RestoreSuccessCallback) (string, error) {
	// 步骤1: 获取分组备份的子任务ID列表
	if len(task.SubTaskIDs) == 0 {
		return "", fmt.Errorf("分组备份任务缺少子任务信息")
	}

	// 步骤2: 构建分组恢复配置
	var restoreConfigs []types.RestoreConfig
	var taskIDStrings []string

	for _, subTaskID := range task.SubTaskIDs {
		taskIDStrings = append(taskIDStrings, subTaskID)

		// 获取子任务信息
		subTask, err := m.taskManager.GetTask(subTaskID)
		if err != nil {
			m.cfg.Logger.Warn("获取子任务信息失败，跳过",
				zap.String("sub_task_id", subTaskID),
				zap.Error(err))
			continue
		}

		// 验证子任务状态
		if subTask.Status != types.TaskStatusCompleted {
			m.cfg.Logger.Warn("子任务未完成，跳过",
				zap.String("sub_task_id", subTaskID),
				zap.String("status", string(subTask.Status)))
			continue
		}

		// 获取备份记录ID和数据源名称
		backupRecordID, ok := subTask.Metadata["backup_record_id"].(string)
		if !ok || backupRecordID == "" {
			m.cfg.Logger.Warn("子任务缺少备份记录ID，跳过", zap.String("sub_task_id", subTaskID))
			continue
		}

		sourceName, ok := subTask.Metadata["source_name"].(string)
		if !ok || sourceName == "" {
			m.cfg.Logger.Warn("子任务缺少数据源名称，跳过", zap.String("sub_task_id", subTaskID))
			continue
		}

		// 创建恢复配置
		restoreConfig := types.NewRestoreConfig(subTask.Source, sourceName, backupRecordID)
		restoreConfig.Description = fmt.Sprintf("基于分组任务ID %s 的子任务 %s 自动恢复", task.ID, subTaskID)
		restoreConfig.Force = force
		restoreConfigs = append(restoreConfigs, restoreConfig)
	}

	// 步骤3: 验证是否有有效的恢复配置
	if len(restoreConfigs) == 0 {
		return "", fmt.Errorf("分组备份任务中没有可恢复的子任务")
	}

	// 步骤4: 创建分组恢复配置
	batchConfig := types.BatchRestoreConfig{
		Restores:    restoreConfigs,
		Description: fmt.Sprintf("基于分组备份任务ID %s 的自动分组恢复", task.ID),
		Atomic:      true,  // 默认使用原子性操作
		Parallel:    false, // 默认串行执行
		Force:       force,
		OnSuccess:   callback, // 设置分组恢复回调
	}

	// 步骤5: 执行分组恢复
	m.cfg.Logger.Info("启动基于分组任务ID的恢复流程",
		zap.String("group_task_id", task.ID),
		zap.Int("restore_count", len(restoreConfigs)),
		zap.Bool("force", force))

	return m.RestoreAllAsync(ctx, batchConfig)
}

// === 新的统一查询接口实现 ===

// ListAllBackups 基于tasks.json的统一备份查询接口。
// 提供高性能的内存查询，支持过滤、搜索和分页功能。
func (m *manager) ListAllBackups(ctx context.Context, filter types.BackupFilter) (*types.BackupListResult, error) {
	// 直接委托给storageManager的新方法
	return m.storageManager.ListAllBackups(filter)
}

// GetBackupDetails 获取备份任务的详细信息。
// 支持单个备份和分组备份的详情获取，分组备份会递归显示所有子任务。
func (m *manager) GetBackupDetails(ctx context.Context, taskID string) (*types.Task, error) {
	// 直接委托给storageManager的新方法
	return m.storageManager.GetBackupDetails(taskID)
}

// DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型。
// 重要说明：
// - 归档备份：直接删除单个备份
// - 增量备份：删除整条增量链（保持数据完整性）
// - 分组备份：删除所有子任务的备份数据
func (m *manager) DeleteBackupByTaskID(ctx context.Context, taskID string) error {
	// 获取任务信息以确定锁定策略
	task, err := m.taskManager.GetTask(taskID)
	if err != nil {
		return fmt.Errorf("获取任务信息失败: %w", err)
	}

	// 根据任务类型确定锁定策略
	if task.Type == types.BackupAllTask {
		// 分组备份：需要锁定所有相关的数据源
		// 首先获取所有子任务信息，确定需要锁定的数据源
		var sourceKeys []string

		// 从任务的子任务ID中获取数据源信息
		for _, subTaskID := range task.SubTaskIDs {
			subTask, err := m.taskManager.GetTask(subTaskID)
			if err != nil {
				m.cfg.Logger.Warn("获取子任务信息失败", zap.String("sub_task_id", subTaskID), zap.Error(err))
				continue
			}
			if sourceName, ok := subTask.Metadata["source_name"].(string); ok {
				sourceKey := fmt.Sprintf("%s-%s", subTask.Source, sourceName)
				sourceKeys = append(sourceKeys, sourceKey)
			}
		}

		// 如果无法获取子任务信息，使用全局锁作为降级方案
		if len(sourceKeys) == 0 {
			m.cfg.Logger.Warn("无法获取分组任务的子任务信息，使用全局锁", zap.String("task_id", taskID))
			globalKey := fmt.Sprintf("group-backup-%s", taskID)
			if lockErr := m.locker.LockWithTimeout(globalKey, configpkg.DefaultLockTimeout); lockErr != nil {
				return fmt.Errorf("获取分组删除锁超时: %w", lockErr)
			}
			defer m.locker.Unlock(globalKey)
		} else {
			// 对数据源键进行排序以避免死锁
			sort.Strings(sourceKeys)

			// 依次获取所有数据源的锁
			var lockedKeys []string
			for _, sourceKey := range sourceKeys {
				if lockErr := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); lockErr != nil {
					// 如果获取锁失败，回滚已获取的锁
					for _, lockedKey := range lockedKeys {
						m.locker.Unlock(lockedKey)
					}
					return fmt.Errorf("获取数据源锁失败 %s: %w", sourceKey, lockErr)
				}
				lockedKeys = append(lockedKeys, sourceKey)
			}

			// 确保在函数退出时释放所有锁
			defer func() {
				for _, lockedKey := range lockedKeys {
					m.locker.Unlock(lockedKey)
				}
			}()
		}
	} else {
		// 单个备份：使用数据源级别的锁
		sourceName, ok := task.Metadata["source_name"].(string)
		if !ok {
			return fmt.Errorf("无法获取任务的数据源名称")
		}
		sourceKey := fmt.Sprintf("%s-%s", task.Source, sourceName)
		if lockErr := m.locker.LockWithTimeout(sourceKey, configpkg.DefaultLockTimeout); lockErr != nil {
			return fmt.Errorf("获取删除锁超时: %w", lockErr)
		}
		defer m.locker.Unlock(sourceKey)
	}

	m.cfg.Logger.Info("启动基于任务ID的删除流程", zap.String("task_id", taskID), zap.String("task_type", string(task.Type)))

	// 委托给storageManager执行具体的删除逻辑
	return m.storageManager.DeleteBackupByTaskID(taskID)
}

// GetBackupDeletionInfo 获取备份删除的影响信息。
// 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等。
func (m *manager) GetBackupDeletionInfo(ctx context.Context, taskID string) (*types.BackupDeletionInfo, error) {
	// 直接委托给storageManager的新方法
	return m.storageManager.GetBackupDeletionInfo(taskID)
}

// UpdateConfig 动态更新配置，支持运行时配置变更
func (m *manager) UpdateConfig(cfg *types.Config) error {
	// 验证新配置
	if err := configpkg.Validate(cfg); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 1. 更新StorageManager配置
	if err := m.storageManager.UpdateConfig(cfg); err != nil {
		return fmt.Errorf("更新StorageManager配置失败: %w", err)
	}

	// 2. 重新创建Providers（使用新配置）
	newProviders := make(map[types.SourceType]provider.BackupProvider)
	if cfg.MySQL != nil {
		storageManagerImpl, ok := m.storageManager.(*storage.Manager)
		if !ok {
			return fmt.Errorf("StorageManager类型断言失败")
		}
		mysqlProvider, err := mysql.NewProvider(cfg.MySQL, storageManagerImpl, cfg.Logger)
		if err != nil {
			return fmt.Errorf("创建MySQL Provider失败: %w", err)
		}
		newProviders[types.MySQL] = mysqlProvider
	}
	if cfg.ES != nil {
		storageManagerImpl, ok := m.storageManager.(*storage.Manager)
		if !ok {
			return fmt.Errorf("StorageManager类型断言失败")
		}
		esProvider, providerErr := elasticsearch.NewProvider(cfg.ES, cfg, storageManagerImpl, cfg.Logger)
		if providerErr != nil {
			return fmt.Errorf("创建ES Provider失败: %w", providerErr)
		}
		newProviders[types.Elasticsearch] = esProvider
	}

	// 3. 更新TaskManager的配置和Providers
	if err := m.taskManager.UpdateConfig(cfg, newProviders); err != nil {
		return fmt.Errorf("更新TaskManager失败: %w", err)
	}

	// 4. 确保TaskManager有正确的StorageManager引用（解决nil指针问题）
	if err := m.taskManager.SetStorageManager(m.storageManager); err != nil {
		return fmt.Errorf("设置TaskManager的StorageManager失败: %w", err)
	}

	// 4. 更新API层状态
	m.providers = newProviders
	m.cfg = cfg

	cfg.Logger.Info("配置更新完成",
		zap.String("backup_root", cfg.BackupRoot),
		zap.Bool("mysql_enabled", cfg.MySQL != nil),
		zap.Bool("es_enabled", cfg.ES != nil))

	return nil
}

// TestCloudStorageConnectivity 测试云存储连通性
func (m *manager) TestCloudStorageConnectivity(ctx context.Context, cloudConfig *types.CloudStorageConfig) (*types.ConnectivityTestResult, error) {
	result := &types.ConnectivityTestResult{
		Steps: make([]types.ConnectivityStep, 0),
	}

	// 安全地设置存储类型
	if cloudConfig != nil {
		result.StorageType = cloudConfig.Type
	}

	startTime := time.Now()

	// 步骤1: 配置验证
	step1 := m.testConfigValidation(cloudConfig)
	result.Steps = append(result.Steps, step1)
	if !step1.Success {
		result.Success = false
		result.Duration = time.Since(startTime)
		result.Error = step1.Error
		return result, nil
	}

	// 步骤2: Backend创建测试
	step2 := m.testBackendCreation(ctx, cloudConfig)
	result.Steps = append(result.Steps, step2)
	if !step2.Success {
		result.Success = false
		result.Duration = time.Since(startTime)
		result.Error = step2.Error
		return result, nil
	}

	// 步骤3: 健康检查
	step3 := m.testHealthCheck(ctx, cloudConfig)
	result.Steps = append(result.Steps, step3)
	if !step3.Success {
		result.Success = false
		result.Duration = time.Since(startTime)
		result.Error = step3.Error
		return result, nil
	}

	// 步骤4: 读写操作测试
	step4 := m.testReadWriteOperations(ctx, cloudConfig)
	result.Steps = append(result.Steps, step4)
	if !step4.Success {
		result.Success = false
		result.Duration = time.Since(startTime)
		result.Error = step4.Error
		return result, nil
	}

	result.Success = true
	result.Duration = time.Since(startTime)
	return result, nil
}

// testConfigValidation 测试配置验证
func (m *manager) testConfigValidation(cloudConfig *types.CloudStorageConfig) types.ConnectivityStep {
	step := types.ConnectivityStep{
		Name:        "配置验证",
		Description: "验证云存储配置的完整性和有效性",
	}

	startTime := time.Now()

	if cloudConfig == nil {
		step.Success = false
		step.Error = "云存储配置为空"
		step.Duration = time.Since(startTime)
		return step
	}

	// 复用现有的配置验证逻辑
	if err := configpkg.ValidateCloudStorageConfig(cloudConfig); err != nil {
		step.Success = false
		step.Error = err.Message
		step.Duration = time.Since(startTime)
		return step
	}

	step.Success = true
	step.Duration = time.Since(startTime)
	return step
}

// testBackendCreation 测试Backend创建
func (m *manager) testBackendCreation(ctx context.Context, cloudConfig *types.CloudStorageConfig) types.ConnectivityStep {
	step := types.ConnectivityStep{
		Name:        "Backend创建",
		Description: "测试是否能够成功创建云存储Backend实例",
	}

	startTime := time.Now()

	// 创建临时配置
	tempConfig := &types.Config{
		BackupRoot:   "/tmp/unibackup-test",
		CloudStorage: cloudConfig,
		Logger:       m.cfg.Logger,
	}

	// 尝试创建Backend
	_, err := storage.NewBackend(tempConfig, m.cfg.Logger)
	if err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("创建Backend失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}

	step.Success = true
	step.Duration = time.Since(startTime)
	return step
}

// testHealthCheck 测试健康检查
func (m *manager) testHealthCheck(ctx context.Context, cloudConfig *types.CloudStorageConfig) types.ConnectivityStep {
	step := types.ConnectivityStep{
		Name:        "健康检查",
		Description: "测试云存储的基本连通性和认证状态",
	}

	startTime := time.Now()

	// 创建Backend
	tempConfig := &types.Config{
		BackupRoot:   "/tmp/unibackup-test",
		CloudStorage: cloudConfig,
		Logger:       m.cfg.Logger,
	}

	backend, err := storage.NewBackend(tempConfig, m.cfg.Logger)
	if err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("创建Backend失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}

	// 执行健康检查
	if err := backend.HealthCheck(ctx); err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("健康检查失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}

	step.Success = true
	step.Duration = time.Since(startTime)
	return step
}

// testReadWriteOperations 测试读写操作
func (m *manager) testReadWriteOperations(ctx context.Context, cloudConfig *types.CloudStorageConfig) types.ConnectivityStep {
	step := types.ConnectivityStep{
		Name:        "读写操作测试",
		Description: "测试云存储的基本读写功能",
	}

	startTime := time.Now()

	// 创建Backend
	tempConfig := &types.Config{
		BackupRoot:   "/tmp/unibackup-test",
		CloudStorage: cloudConfig,
		Logger:       m.cfg.Logger,
	}

	backend, err := storage.NewBackend(tempConfig, m.cfg.Logger)
	if err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("创建Backend失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}

	// 测试写入
	testKey := fmt.Sprintf("connectivity-test/%d.txt", time.Now().Unix())
	testData := "UniBackup云存储连通性测试数据"

	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("写入测试失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}

	// 测试读取
	reader, err := backend.Get(ctx, testKey)
	if err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("读取测试失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}
	defer reader.Close()

	readData, err := io.ReadAll(reader)
	if err != nil {
		step.Success = false
		step.Error = fmt.Sprintf("读取数据失败: %v", err)
		step.Duration = time.Since(startTime)
		return step
	}

	if string(readData) != testData {
		step.Success = false
		step.Error = "读写数据不匹配"
		step.Duration = time.Since(startTime)
		return step
	}

	// 清理测试数据
	_ = backend.Delete(ctx, testKey)

	step.Success = true
	step.Duration = time.Since(startTime)
	return step
}
