package api

import (
	"context"
	"testing"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/lock"
	"git.gobies.org/fobrain/unibackup/internal/provider"
	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// --- <PERSON><PERSON> ---

type MockStorageManager struct {
	mock.Mock
}

func (m *MockStorageManager) CreateBackupRecord(sourceType types.SourceType, dsName string, backupType types.BackupType, chainID, description string) (*types.BackupRecord, *types.BackupError) {
	args := m.Called(sourceType, dsName, backupType, chainID, description)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.BackupRecord), err
}

func (m *MockStorageManager) SaveBackupRecord(record *types.BackupRecord) *types.BackupError {
	args := m.Called(record)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockStorageManager) SaveBackupRecordAtomic(record *types.BackupRecord) *types.BackupError {
	args := m.Called(record)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockStorageManager) GetBackupRecord(sourceType types.SourceType, id string) (*types.BackupRecord, *types.BackupError) {
	args := m.Called(sourceType, id)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.BackupRecord), err
}

func (m *MockStorageManager) GetChainBackupRecord(sourceType types.SourceType, chainID, backupID string) (*types.BackupRecord, *types.BackupError) {
	args := m.Called(sourceType, chainID, backupID)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.BackupRecord), err
}

func (m *MockStorageManager) DeleteBackupRecord(record *types.BackupRecord) *types.BackupError {
	args := m.Called(record)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockStorageManager) ListIncrementalChains(sourceType types.SourceType, sourceName string) ([]types.IncrementalChain, *types.BackupError) {
	args := m.Called(sourceType, sourceName)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).([]types.IncrementalChain), err
}

func (m *MockStorageManager) GetRestorationChain(sourceType types.SourceType, id string) ([]*types.BackupRecord, *types.BackupError) {
	args := m.Called(sourceType, id)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).([]*types.BackupRecord), err
}

func (m *MockStorageManager) ListArchivalBackups(sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError) {
	args := m.Called(sourceType)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).([]types.ArchivalBackup), err
}

func (m *MockStorageManager) FindBackupRecord(sourceType types.SourceType, backupID string) (*types.BackupRecord, *types.BackupError) {
	args := m.Called(sourceType, backupID)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.BackupRecord), err
}

func (m *MockStorageManager) FindBackupRecordAcrossTypes(backupID string) (*types.BackupRecord, *types.BackupError) {
	args := m.Called(backupID)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.BackupRecord), err
}

func (m *MockStorageManager) ReadChainMeta(sourceType types.SourceType, chainID string) (*types.ChainMeta, *types.BackupError) {
	args := m.Called(sourceType, chainID)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.ChainMeta), err
}

func (m *MockStorageManager) WriteChainMeta(sourceType types.SourceType, chainID string, meta *types.ChainMeta) *types.BackupError {
	args := m.Called(sourceType, chainID, meta)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockStorageManager) DeleteIncrementalChain(sourceType types.SourceType, chainID string) *types.BackupError {
	args := m.Called(sourceType, chainID)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockStorageManager) CheckDataSourceHealth(source types.SourceType, providers map[types.SourceType]provider.BackupProvider) *types.BackupError {
	args := m.Called(source, providers)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockStorageManager) GetLatestChainRecord(sourceType types.SourceType, chainID string) (*types.BackupRecord, *types.BackupError) {
	args := m.Called(sourceType, chainID)
	var err *types.BackupError
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	if args.Get(0) == nil {
		return nil, err
	}
	return args.Get(0).(*types.BackupRecord), err
}

// 系统文件操作方法
func (m *MockStorageManager) GetBackupRoot() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockStorageManager) WriteSystemFile(filePath string, data []byte) error {
	args := m.Called(filePath, data)
	return args.Error(0)
}

func (m *MockStorageManager) ReadSystemFile(filePath string) ([]byte, error) {
	args := m.Called(filePath)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockStorageManager) EnsureSystemDir(dirPath string) error {
	args := m.Called(dirPath)
	return args.Error(0)
}

func (m *MockStorageManager) RenameSystemFile(oldPath, newPath string) error {
	args := m.Called(oldPath, newPath)
	return args.Error(0)
}

// --- 阶段三新增：统一查询接口 ---

func (m *MockStorageManager) ListAllBackups(filter types.BackupFilter) (*types.BackupListResult, error) {
	args := m.Called(filter)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.BackupListResult), args.Error(1)
}

func (m *MockStorageManager) GetBackupDetails(taskID string) (*types.Task, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.Task), args.Error(1)
}

func (m *MockStorageManager) DeleteBackupByTaskID(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}

func (m *MockStorageManager) GetBackupDeletionInfo(taskID string) (*types.BackupDeletionInfo, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.BackupDeletionInfo), args.Error(1)
}

func (m *MockStorageManager) UpdateConfig(cfg *types.Config) error {
	args := m.Called(cfg)
	return args.Error(0)
}

func (m *MockStorageManager) HasAnyBackups(sourceType types.SourceType, sourceName string) (bool, error) {
	args := m.Called(sourceType, sourceName)
	return args.Bool(0), args.Error(1)
}

type MockTaskManager struct {
	mock.Mock
}

func (m *MockTaskManager) RunBackup(ctx context.Context, record *types.BackupRecord) *types.BackupError {
	args := m.Called(ctx, record)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

func (m *MockTaskManager) Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError {
	args := m.Called(ctx, config)
	if args.Get(0) == nil {
		return nil
	}
	var err *types.BackupError
	if args.Get(0) != nil {
		err = args.Get(0).(*types.BackupError)
	}
	return err
}

// 新增的异步方法
func (m *MockTaskManager) BackupAsync(ctx context.Context, sourceType types.SourceType, dsName string, backupType types.BackupType, description string) (string, error) {
	args := m.Called(ctx, sourceType, dsName, backupType, description)
	return args.String(0), args.Error(1)
}

func (m *MockTaskManager) RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error) {
	args := m.Called(ctx, config)
	return args.String(0), args.Error(1)
}

func (m *MockTaskManager) BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error) {
	args := m.Called(ctx, req)
	return args.String(0), args.Error(1)
}

func (m *MockTaskManager) RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error) {
	args := m.Called(ctx, config)
	return args.String(0), args.Error(1)
}

func (m *MockTaskManager) GetTask(taskID string) (*types.Task, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.Task), args.Error(1)
}

func (m *MockTaskManager) ListTasks() ([]*types.Task, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*types.Task), args.Error(1)
}

func (m *MockTaskManager) CancelTask(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}

func (m *MockTaskManager) ClearOldTasks() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockTaskManager) Shutdown() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockTaskManager) DeleteBackup(ctx context.Context, source types.SourceType, backupID string) *types.BackupError {
	args := m.Called(ctx, source, backupID)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*types.BackupError)
}

func (m *MockTaskManager) DeleteTask(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}

func (m *MockTaskManager) UpdateConfig(cfg *types.Config, providers map[types.SourceType]provider.BackupProvider) error {
	args := m.Called(cfg, providers)
	return args.Error(0)
}

func (m *MockTaskManager) SetStorageManager(storageMgr storage.StorageManager) error {
	args := m.Called(storageMgr)
	return args.Error(0)
}

// --- Test Helpers ---

// createTestManager 创建一个用于测试的manager实例
func createTestManager(mockStorage *MockStorageManager, mockTask *MockTaskManager) (*manager, error) {
	logger := zap.NewNop()
	cfg := &types.Config{Logger: logger}
	locker := lock.NewLocker()

	// 创建空的providers map用于测试
	providers := make(map[types.SourceType]provider.BackupProvider)

	mgr, err := NewManager(cfg, mockStorage, mockTask, locker, providers)
	if err != nil {
		return nil, err
	}
	return mgr, nil
}

// --- Tests ---

func TestBackupManager_Backup_ChainInitial_Success(t *testing.T) {
	// Setup
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)

	manager, err := createTestManager(mockStorage, mockTask)
	assert.NoError(t, err)

	sourceType := types.MySQL
	backupType := types.BackupTypeChainInitial
	desc := "test initial backup"
	dsName := "test-mysql"

	record := &types.BackupRecord{
		ID:        "ulid1",
		Timestamp: time.Now(),
		Status:    types.BackupStatusInProgress,
		Type:      backupType,
		Source:    sourceType,
	}

	// Mock expectations
	mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
	mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, "", desc).Return(record, nil).Once()
	mockTask.On("RunBackup", mock.Anything, record).Return(nil).Once()
	mockStorage.On("SaveBackupRecordAtomic", mock.AnythingOfType("*types.BackupRecord")).Run(func(args mock.Arguments) {
		rec := args.Get(0).(*types.BackupRecord)
		assert.Equal(t, types.BackupStatusCompleted, rec.Status)
	}).Return(nil).Once()

	// Execute
	result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

	// Assert
	assert.Nil(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, types.BackupStatusCompleted, result.Status)
	mockStorage.AssertExpectations(t)
	mockTask.AssertExpectations(t)
}

func TestBackupManager_Backup_ChainIncremental_Success(t *testing.T) {
	// Setup
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)
	manager, _ := createTestManager(mockStorage, mockTask)

	sourceType := types.MySQL
	backupType := types.BackupTypeChainIncremental
	desc := "test incremental backup"
	dsName := "test-mysql"
	chainID := "chain1"

	// Mock existing chain
	existingChain := []types.IncrementalChain{{ChainID: chainID}}
	mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
	mockStorage.On("ListIncrementalChains", sourceType, dsName).Return(existingChain, nil).Once()

	// Mock record creation
	record := &types.BackupRecord{
		ID:      "ulid2",
		ChainID: chainID,
		Status:  types.BackupStatusInProgress,
		Type:    backupType,
		Source:  sourceType,
	}
	mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, chainID, desc).Return(record, nil).Once()

	// Mock task execution
	mockTask.On("RunBackup", mock.Anything, record).Return(nil).Once()

	// Mock final save
	mockStorage.On("SaveBackupRecordAtomic", mock.AnythingOfType("*types.BackupRecord")).Return(nil).Once()

	// Execute
	result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

	// Assert
	assert.Nil(t, err)
	assert.NotNil(t, result)
	mockStorage.AssertExpectations(t)
	mockTask.AssertExpectations(t)
}

// TestBackupManager_AsyncOperations 测试异步操作
func TestBackupManager_AsyncOperations(t *testing.T) {
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)
	locker := lock.NewLocker()
	logger := zap.NewNop()

	cfg := &types.Config{
		Logger: logger,
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "root",
			Password: "password",
			DBName:   "test",
		},
		ES: &types.ESConfig{
			Addresses: []string{"http://localhost:9200"},
			User:      "elastic",
			Password:  "password",
		},
		BackupRoot: "/tmp/unibackup",
	}

	// 创建空的providers map用于测试
	providers := make(map[types.SourceType]provider.BackupProvider)
	manager, err := NewManager(cfg, mockStorage, mockTask, locker, providers)
	assert.Nil(t, err)

	t.Run("BackupAsync should delegate to task manager", func(t *testing.T) {
		mockStorage.On("CheckDataSourceHealth", types.MySQL, mock.Anything).Return((*types.BackupError)(nil)).Once()
		mockTask.On("BackupAsync", mock.Anything, types.MySQL, "test-source", types.BackupTypeArchival, "async test").Return("task-id-123", nil)

		taskID, err := manager.BackupAsync(context.Background(), types.MySQL, "test-source", types.BackupTypeArchival, "async test")
		assert.Nil(t, err)
		assert.Equal(t, "task-id-123", taskID)
	})

	t.Run("RestoreAsync should delegate to task manager", func(t *testing.T) {
		restoreConfig := types.NewRestoreConfig(types.MySQL, "test-source", "backup-id")
		mockStorage.On("CheckDataSourceHealth", types.MySQL, mock.Anything).Return((*types.BackupError)(nil))
		mockTask.On("RestoreAsync", mock.Anything, restoreConfig).Return("restore-task-id", nil)

		taskID, err := manager.RestoreAsync(context.Background(), restoreConfig)
		assert.Nil(t, err)
		assert.Equal(t, "restore-task-id", taskID)
	})

	t.Run("BackupAllAsync should delegate to task manager", func(t *testing.T) {
		req := types.BackupAllRequest{
			Sources: []types.BackupRequest{
				{SourceType: types.MySQL, SourceName: "mysql-1", BackupType: types.BackupTypeArchival, Description: "MySQL backup"},
			},
			Description: "Group backup",
		}

		// 添加健康检查的Mock
		mockStorage.On("CheckDataSourceHealth", types.MySQL, mock.Anything).Return((*types.BackupError)(nil))
		mockTask.On("BackupAllAsync", mock.Anything, req).Return("group-backup-task", nil)

		taskID, err := manager.BackupAllAsync(context.Background(), req)
		assert.Nil(t, err)
		assert.Equal(t, "group-backup-task", taskID)
	})

	t.Run("RestoreAllAsync should delegate to task manager", func(t *testing.T) {
		restoreConfigs := []types.RestoreConfig{
			types.NewRestoreConfig(types.MySQL, "mysql-1", "backup-id"),
		}
		config := types.NewBatchRestoreConfig(restoreConfigs, false)
		config.Description = "Group restore"

		mockTask.On("RestoreAllAsync", mock.Anything, config).Return("group-restore-task", nil)

		taskID, err := manager.RestoreAllAsync(context.Background(), config)
		assert.Nil(t, err)
		assert.Equal(t, "group-restore-task", taskID)
	})

	mockTask.AssertExpectations(t)
}

// TestBackupManager_TaskManagement 测试任务管理功能
func TestBackupManager_TaskManagement(t *testing.T) {
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)
	locker := lock.NewLocker()
	logger := zap.NewNop()

	cfg := &types.Config{Logger: logger}
	// 创建空的providers map用于测试
	providers := make(map[types.SourceType]provider.BackupProvider)
	manager, err := NewManager(cfg, mockStorage, mockTask, locker, providers)
	assert.Nil(t, err)

	t.Run("GetTask should delegate to task manager", func(t *testing.T) {
		expectedTask := &types.Task{
			ID:     "task-123",
			Type:   types.BackupAllTask,
			Status: types.TaskStatusCompleted,
		}

		mockTask.On("GetTask", "task-123").Return(expectedTask, nil)

		task, err := manager.GetTask("task-123")
		assert.Nil(t, err)
		assert.Equal(t, expectedTask, task)
	})

	t.Run("ListTasks should delegate to task manager", func(t *testing.T) {
		expectedTasks := []*types.Task{
			{ID: "task-1", Type: types.BackupAllTask, Status: types.TaskStatusCompleted},
			{ID: "task-2", Type: types.RestoreAllTask, Status: types.TaskStatusRunning},
		}

		mockTask.On("ListTasks").Return(expectedTasks, nil)

		tasks, err := manager.ListTasks()
		assert.Nil(t, err)
		assert.Equal(t, expectedTasks, tasks)
	})

	t.Run("CancelTask should delegate to task manager", func(t *testing.T) {
		mockTask.On("CancelTask", "task-123").Return(nil)

		err := manager.CancelTask("task-123")
		assert.Nil(t, err)
	})

	t.Run("ClearOldTasks should delegate to task manager", func(t *testing.T) {
		mockTask.On("ClearOldTasks").Return(nil)

		err := manager.ClearOldTasks()
		assert.Nil(t, err)
	})

	t.Run("Shutdown should delegate to task manager", func(t *testing.T) {
		mockTask.On("Shutdown").Return(nil)

		err := manager.Shutdown()
		assert.Nil(t, err)
	})

	mockTask.AssertExpectations(t)
}

// TestBackupManager_Restore 测试恢复功能
func TestBackupManager_Restore(t *testing.T) {
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)
	locker := lock.NewLocker()
	logger := zap.NewNop()

	cfg := &types.Config{Logger: logger}
	// 创建空的providers map用于测试
	providers := make(map[types.SourceType]provider.BackupProvider)
	manager, err := NewManager(cfg, mockStorage, mockTask, locker, providers)
	assert.Nil(t, err)

	t.Run("Restore should execute restore operation", func(t *testing.T) {
		sourceType := types.MySQL
		dsName := "test-mysql"
		backupID := "backup-123"

		// 添加FindBackupRecord的Mock
		mockStorage.On("FindBackupRecord", sourceType, backupID).Return(&types.BackupRecord{
			ID:     backupID,
			Type:   types.BackupTypeArchival,
			Source: sourceType,
		}, (*types.BackupError)(nil))

		restoreConfig := types.NewRestoreConfig(sourceType, dsName, backupID)
		mockTask.On("Restore", mock.Anything, restoreConfig).Return(nil)

		err := manager.Restore(context.Background(), restoreConfig)
		assert.Nil(t, err)
	})

	t.Run("Restore with task manager error should return error", func(t *testing.T) {
		// 跳过这个测试，因为Mock设置冲突
		t.Skip("Skipping restore error test due to mock conflicts")
	})

	mockTask.AssertExpectations(t)
}

// TestBackupManager_BackupManagement 测试备份管理功能
func TestBackupManager_BackupManagement(t *testing.T) {
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)
	locker := lock.NewLocker()
	logger := zap.NewNop()

	cfg := &types.Config{Logger: logger}
	// 创建空的providers map用于测试
	providers := make(map[types.SourceType]provider.BackupProvider)
	manager, err := NewManager(cfg, mockStorage, mockTask, locker, providers)
	assert.Nil(t, err)

	t.Run("ListArchivalBackups should delegate to storage manager", func(t *testing.T) {
		expectedBackups := []types.ArchivalBackup{
			{Record: &types.BackupRecord{ID: "backup-1", Description: "First backup"}},
			{Record: &types.BackupRecord{ID: "backup-2", Description: "Second backup"}},
		}

		mockStorage.On("ListArchivalBackups", types.MySQL).Return(expectedBackups, (*types.BackupError)(nil))

		backups, err := manager.ListArchivalBackups(context.Background(), types.MySQL)
		assert.Nil(t, err)
		assert.Equal(t, expectedBackups, backups)
	})

	t.Run("ListIncrementalChains should return empty list (deprecated)", func(t *testing.T) {
		// Mock ListAllBackups to return empty result（使用新的多值字段）
		emptyResult := &types.BackupListResult{
			Tasks:   []*types.Task{},
			Total:   0,
			HasMore: false,
		}
		mockStorage.On("ListAllBackups", mock.MatchedBy(func(filter types.BackupFilter) bool {
			return len(filter.SourceTypes) > 0 && filter.SourceTypes[0] == types.MySQL
		})).Return(emptyResult, nil).Once()

		chains, err := manager.ListIncrementalChains(context.Background(), types.MySQL)
		assert.Nil(t, err)
		assert.Equal(t, []types.IncrementalChain{}, chains)
	})

	t.Run("DeleteBackup should delegate to task manager", func(t *testing.T) {
		// Mock expectations - 需要先查找备份记录
		record := &types.BackupRecord{ID: "backup-123", Source: "test-source"}
		mockStorage.On("FindBackupRecord", types.MySQL, "backup-123").Return(record, (*types.BackupError)(nil)).Once()
		mockTask.On("DeleteBackup", mock.Anything, types.MySQL, "backup-123").Return(nil)

		err := manager.DeleteBackup(context.Background(), types.MySQL, "backup-123")
		assert.Nil(t, err)
	})

	mockStorage.AssertExpectations(t)
	mockTask.AssertExpectations(t)
}

func TestBackupManager_Backup_ProviderFails(t *testing.T) {
	// Setup
	mockStorage := new(MockStorageManager)
	mockTask := new(MockTaskManager)
	manager, _ := createTestManager(mockStorage, mockTask)

	sourceType := types.MySQL
	backupType := types.BackupTypeArchival
	desc := "test archival backup"
	dsName := "test-mysql"
	providerError := &types.BackupError{Message: "provider failed", Component: "MockProvider"}

	record := &types.BackupRecord{ID: "ulid3", Status: types.BackupStatusInProgress}

	// Mock expectations
	mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
	mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, "", desc).Return(record, nil).Once()
	mockTask.On("RunBackup", mock.Anything, record).Return(providerError).Once()
	mockStorage.On("SaveBackupRecordAtomic", mock.AnythingOfType("*types.BackupRecord")).Run(func(args mock.Arguments) {
		rec := args.Get(0).(*types.BackupRecord)
		assert.Equal(t, types.BackupStatusFailed, rec.Status)
		assert.Equal(t, providerError.Error(), rec.Error)
	}).Return(nil).Once()

	// Execute
	result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

	// Assert
	assert.ErrorIs(t, err, providerError)
	assert.NotNil(t, result)
	assert.Equal(t, types.BackupStatusFailed, result.Status)
	mockStorage.AssertExpectations(t)
	mockTask.AssertExpectations(t)
}

// TestBackupManager_RestoreOperations 测试恢复操作功能
func TestBackupManager_RestoreOperations(t *testing.T) {
	t.Run("Restore should work successfully", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		dsName := "test-mysql"
		backupID := "backup-123"

		// Mock expectations
		record := &types.BackupRecord{
			ID:     backupID,
			Source: sourceType,
			Status: types.BackupStatusCompleted,
		}
		mockStorage.On("FindBackupRecord", sourceType, backupID).Return(record, (*types.BackupError)(nil)).Once()
		restoreConfig := types.NewRestoreConfig(sourceType, dsName, backupID)
		mockTask.On("Restore", mock.Anything, restoreConfig).Return((*types.BackupError)(nil)).Once()

		// Execute
		err := manager.Restore(context.Background(), restoreConfig)

		// Assert
		assert.Nil(t, err)
		mockStorage.AssertExpectations(t)
		mockTask.AssertExpectations(t)
	})

	t.Run("Restore should handle task manager error", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		dsName := "test-mysql"
		backupID := "backup-123"
		restoreError := &types.BackupError{
			Code:    "RESTORE_FAILED",
			Message: "恢复失败",
		}

		// Mock expectations
		record := &types.BackupRecord{
			ID:     backupID,
			Source: sourceType,
			Status: types.BackupStatusCompleted,
		}
		mockStorage.On("FindBackupRecord", sourceType, backupID).Return(record, (*types.BackupError)(nil)).Once()
		restoreConfig := types.NewRestoreConfig(sourceType, dsName, backupID)
		mockTask.On("Restore", mock.Anything, restoreConfig).Return(restoreError).Once()

		// Execute
		err := manager.Restore(context.Background(), restoreConfig)

		// Assert
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "恢复失败")
		assert.Equal(t, "TASK_EXECUTION_FAILED", err.Code)
		mockStorage.AssertExpectations(t)
		mockTask.AssertExpectations(t)
	})
}

// TestBackupManager_ErrorHandling 测试错误处理
func TestBackupManager_ErrorHandling(t *testing.T) {
	t.Run("Backup should handle storage creation error", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		backupType := types.BackupTypeArchival
		desc := "test backup"
		dsName := "test-mysql"
		storageError := &types.BackupError{
			Code:    "STORAGE_ERROR",
			Message: "存储创建失败",
		}

		// Mock expectations
		mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, "", desc).Return((*types.BackupRecord)(nil), storageError).Once()

		// Execute
		result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

		// Assert
		assert.NotNil(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "存储创建失败")
		assert.Equal(t, "STORAGE_ERROR", err.Code)
		mockStorage.AssertExpectations(t)
	})

	t.Run("Backup should handle save metadata error", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		backupType := types.BackupTypeArchival
		desc := "test backup"
		dsName := "test-mysql"
		record := &types.BackupRecord{ID: "test-id", Status: types.BackupStatusInProgress}
		saveError := &types.BackupError{
			Code:    "SAVE_ERROR",
			Message: "保存元数据失败",
		}

		// Mock expectations
		mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, "", desc).Return(record, (*types.BackupError)(nil)).Once()
		mockTask.On("RunBackup", mock.Anything, record).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("SaveBackupRecordAtomic", mock.AnythingOfType("*types.BackupRecord")).Return(saveError).Once()

		// Execute
		result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

		// Assert
		assert.NotNil(t, err)
		assert.NotNil(t, result)
		assert.Contains(t, err.Error(), "保存最终元数据失败")
		mockStorage.AssertExpectations(t)
		mockTask.AssertExpectations(t)
	})
}

// TestBackupManager_Configuration 测试配置相关功能
func TestBackupManager_Configuration(t *testing.T) {
	t.Run("NewManager should accept valid configuration", func(t *testing.T) {
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		locker := lock.NewLocker()
		logger := zap.NewNop()
		cfg := &types.Config{Logger: logger}

		// 内部 NewManager 不验证配置，因为配置验证在外部进行
		// 创建空的providers map用于测试
		providers := make(map[types.SourceType]provider.BackupProvider)
		manager, err := NewManager(cfg, mockStorage, mockTask, locker, providers)
		assert.NoError(t, err)
		assert.NotNil(t, manager)
	})

	t.Run("Manager should handle shutdown gracefully", func(t *testing.T) {
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// Mock expectations
		mockTask.On("Shutdown").Return(nil).Once()

		// Execute
		err := manager.Shutdown()

		// Assert
		assert.Nil(t, err)
		mockTask.AssertExpectations(t)
	})

	t.Run("Manager should handle shutdown error", func(t *testing.T) {
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		shutdownError := &types.BackupError{
			Code:    "SHUTDOWN_ERROR",
			Message: "关闭失败",
		}

		// Mock expectations
		mockTask.On("Shutdown").Return(shutdownError).Once()

		// Execute
		err := manager.Shutdown()

		// Assert
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "关闭失败")
		mockTask.AssertExpectations(t)
	})
}

// TestBackupManager_ConcurrencyAndLocking 测试并发和锁定
func TestBackupManager_ConcurrencyAndLocking(t *testing.T) {
	t.Run("Backup should use locking correctly", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		backupType := types.BackupTypeArchival
		desc := "test backup"
		dsName := "test-mysql"
		record := &types.BackupRecord{ID: "test-id", Status: types.BackupStatusInProgress}

		// Mock expectations
		mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, "", desc).Return(record, (*types.BackupError)(nil)).Once()
		mockTask.On("RunBackup", mock.Anything, record).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("SaveBackupRecordAtomic", mock.AnythingOfType("*types.BackupRecord")).Return((*types.BackupError)(nil)).Once()

		// Execute - 这应该获取和释放锁
		result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

		// Assert
		assert.Nil(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, types.BackupStatusCompleted, result.Status)
		mockStorage.AssertExpectations(t)
		mockTask.AssertExpectations(t)
	})
}

// TestBackupManager_EdgeCases 测试边界条件
func TestBackupManager_EdgeCases(t *testing.T) {
	t.Run("Backup with empty description should work", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		backupType := types.BackupTypeArchival
		desc := "" // 空描述
		dsName := "test-mysql"
		record := &types.BackupRecord{ID: "test-id", Status: types.BackupStatusInProgress}

		// Mock expectations
		mockStorage.On("CheckDataSourceHealth", sourceType, mock.Anything).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("CreateBackupRecord", sourceType, dsName, backupType, "", desc).Return(record, (*types.BackupError)(nil)).Once()
		mockTask.On("RunBackup", mock.Anything, record).Return((*types.BackupError)(nil)).Once()
		mockStorage.On("SaveBackupRecordAtomic", mock.AnythingOfType("*types.BackupRecord")).Return((*types.BackupError)(nil)).Once()

		// Execute
		result, err := manager.Backup(context.Background(), sourceType, dsName, backupType, desc)

		// Assert
		assert.Nil(t, err)
		assert.NotNil(t, result)
		mockStorage.AssertExpectations(t)
		mockTask.AssertExpectations(t)
	})

	t.Run("DeleteBackup with empty ID should handle gracefully", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		sourceType := types.MySQL
		backupID := "" // 空ID

		// Mock expectations - 先查找备份记录（空ID会失败）
		mockStorage.On("FindBackupRecord", sourceType, backupID).Return((*types.BackupRecord)(nil), &types.BackupError{
			Code:    "INVALID_BACKUP_ID",
			Message: "备份ID不能为空",
		}).Once()

		// Execute
		err := manager.DeleteBackup(context.Background(), sourceType, backupID)

		// Assert
		assert.NotNil(t, err)
		mockStorage.AssertExpectations(t)
	})
}

// TestBackupManager_GroupOperations 测试分组操作功能
func TestBackupManager_GroupOperations(t *testing.T) {
	t.Run("BackupAllAsync should work successfully", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// 创建分组备份请求
		req := types.BackupAllRequest{
			Sources: []types.BackupRequest{
				{
					SourceType:  types.MySQL,
					SourceName:  "db1",
					BackupType:  types.BackupTypeArchival,
					Description: "数据库1备份",
				},
				{
					SourceType:  types.MySQL,
					SourceName:  "db2",
					BackupType:  types.BackupTypeArchival,
					Description: "数据库2备份",
				},
			},
			Description:      "分组备份测试",
			CleanupOnFailure: true,
		}

		// Mock expectations
		mockStorage.On("CheckDataSourceHealth", types.MySQL, mock.Anything).Return((*types.BackupError)(nil)).Twice()
		mockTask.On("BackupAllAsync", mock.Anything, req).Return("group-task-id", nil).Once()

		// Execute
		taskID, err := manager.BackupAllAsync(context.Background(), req)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "group-task-id", taskID)
		mockTask.AssertExpectations(t)
	})

	t.Run("RestoreAllAsync should work successfully", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// 创建分组恢复请求
		restoreConfigs := []types.RestoreConfig{
			types.NewRestoreConfig(types.MySQL, "db1", "backup-id-1"),
			types.NewRestoreConfig(types.MySQL, "db2", "backup-id-2"),
		}
		config := types.NewBatchRestoreConfig(restoreConfigs, true) // atomic = true
		config.Description = "分组恢复测试"

		// 设置额外属性
		for i := range config.Restores {
			config.Restores[i].CreateSafetyBackup = true
			config.Restores[i].RollbackOnFailure = true
		}

		// Mock expectations
		mockStorage.On("CheckDataSourceHealth", types.MySQL, mock.Anything).Return((*types.BackupError)(nil)).Twice()
		mockTask.On("RestoreAllAsync", mock.Anything, config).Return("restore-group-task-id", nil).Once()

		// Execute
		taskID, err := manager.RestoreAllAsync(context.Background(), config)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "restore-group-task-id", taskID)
		mockTask.AssertExpectations(t)
	})

	t.Run("BackupAllAsync should handle empty sources", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// 创建空的分组备份请求
		req := types.BackupAllRequest{
			Sources:     []types.BackupRequest{},
			Description: "空分组备份测试",
		}

		// Mock expectations - 任务管理器应该返回错误
		mockTask.On("BackupAllAsync", mock.Anything, req).Return("", assert.AnError).Once()

		// Execute
		taskID, err := manager.BackupAllAsync(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Empty(t, taskID)
		mockTask.AssertExpectations(t)
	})

	t.Run("RestoreAllAsync should handle empty sources", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// 创建空的分组恢复请求
		config := types.NewBatchRestoreConfig([]types.RestoreConfig{}, false)
		config.Description = "空分组恢复测试"

		// Mock expectations - 任务管理器应该返回错误
		mockTask.On("RestoreAllAsync", mock.Anything, config).Return("", assert.AnError).Once()

		// Execute
		taskID, err := manager.RestoreAllAsync(context.Background(), config)

		// Assert
		assert.Error(t, err)
		assert.Empty(t, taskID)
		mockTask.AssertExpectations(t)
	})
}

// TestBackupManager_SystemManagement 测试系统管理功能
func TestBackupManager_SystemManagement(t *testing.T) {
	t.Run("Shutdown should work successfully", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// Mock expectations
		mockTask.On("Shutdown").Return(nil).Once()

		// Execute
		err := manager.Shutdown()

		// Assert
		assert.NoError(t, err)
		mockTask.AssertExpectations(t)
	})

	t.Run("Shutdown should handle task manager error", func(t *testing.T) {
		// Setup
		mockStorage := new(MockStorageManager)
		mockTask := new(MockTaskManager)
		manager, _ := createTestManager(mockStorage, mockTask)

		// Mock expectations - 任务管理器关闭失败
		mockTask.On("Shutdown").Return(assert.AnError).Once()

		// Execute
		err := manager.Shutdown()

		// Assert
		assert.Error(t, err)
		mockTask.AssertExpectations(t)
	})
}
