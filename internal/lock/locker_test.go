package lock

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

func TestLocker_BasicLockUnlock(t *testing.T) {
	locker := NewLocker()
	resource := "test_resource"

	// 测试基本的锁定和解锁
	locker.Lock(resource)

	// 验证资源已被锁定
	if !locker.IsLocked(resource) {
		t.<PERSON>rror("Resource should be locked")
	}

	locker.Unlock(resource)

	// 验证资源已被解锁
	if locker.IsLocked(resource) {
		t.Error("Resource should be unlocked")
	}
}

func TestLocker_ConcurrentAccess(t *testing.T) {
	locker := NewLocker()
	resource := "concurrent_resource"

	var wg sync.WaitGroup
	var counter int
	var mu sync.Mutex

	// 启动多个goroutine尝试同时访问同一资源
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			locker.Lock(resource)
			defer locker.Unlock(resource)

			// 模拟一些工作
			time.Sleep(10 * time.Millisecond)

			mu.Lock()
			counter++
			mu.Unlock()
		}()
	}

	wg.Wait()

	if counter != 10 {
		t.<PERSON><PERSON><PERSON>("Expected counter to be 10, got %d", counter)
	}

	// 验证最终资源是解锁的
	if locker.IsLocked(resource) {
		t.Error("Resource should be unlocked after all goroutines finish")
	}
}

func TestLocker_MultipleResources(t *testing.T) {
	locker := NewLocker()
	resource1 := "resource_1"
	resource2 := "resource_2"

	// 锁定第一个资源
	locker.Lock(resource1)
	if !locker.IsLocked(resource1) {
		t.Error("Resource 1 should be locked")
	}

	// 锁定第二个资源
	locker.Lock(resource2)
	if !locker.IsLocked(resource2) {
		t.Error("Resource 2 should be locked")
	}

	// 两个资源都应该被锁定
	if !locker.IsLocked(resource1) || !locker.IsLocked(resource2) {
		t.Error("Both resources should be locked")
	}

	// 解锁第一个资源
	locker.Unlock(resource1)
	if locker.IsLocked(resource1) {
		t.Error("Resource 1 should be unlocked")
	}
	if !locker.IsLocked(resource2) {
		t.Error("Resource 2 should still be locked")
	}

	// 解锁第二个资源
	locker.Unlock(resource2)
	if locker.IsLocked(resource2) {
		t.Error("Resource 2 should be unlocked")
	}
}

func TestLocker_UnlockNonExistentResource(t *testing.T) {
	locker := NewLocker()
	resource := "non_existent"

	// 解锁不存在的资源应该panic（这是设计行为）
	defer func() {
		if r := recover(); r == nil {
			t.Error("Unlocking non-existent resource should panic")
		}
	}()

	locker.Unlock(resource)
}

func TestLocker_IsLockedNonExistentResource(t *testing.T) {
	locker := NewLocker()
	resource := "non_existent"

	// 检查不存在的资源应该返回false
	if locker.IsLocked(resource) {
		t.Error("Non-existent resource should not be locked")
	}
}

func TestLocker_StressTest(t *testing.T) {
	locker := NewLocker()
	numGoroutines := 100
	numOperations := 100

	var wg sync.WaitGroup

	// 启动大量goroutine进行压力测试
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			resource := fmt.Sprintf("resource_%d", id%10) // 10个不同的资源

			for j := 0; j < numOperations; j++ {
				locker.Lock(resource)

				// 模拟一些工作
				time.Sleep(time.Microsecond)

				// 验证资源确实被锁定
				if !locker.IsLocked(resource) {
					t.Errorf("Resource %s should be locked", resource)
				}

				locker.Unlock(resource)
			}
		}(i)
	}

	wg.Wait()
}

func TestLocker_DeadlockPrevention(t *testing.T) {
	locker := NewLocker()
	resource1 := "resource_1"
	resource2 := "resource_2"

	var wg sync.WaitGroup
	completed := make(chan bool, 2)

	// Goroutine 1: 锁定resource1
	wg.Add(1)
	go func() {
		defer wg.Done()

		locker.Lock(resource1)
		time.Sleep(100 * time.Millisecond) // 持有锁一段时间
		locker.Unlock(resource1)

		completed <- true
	}()

	// Goroutine 2: 锁定resource2
	wg.Add(1)
	go func() {
		defer wg.Done()

		locker.Lock(resource2)
		time.Sleep(100 * time.Millisecond) // 持有锁一段时间
		locker.Unlock(resource2)

		completed <- true
	}()

	// 等待完成，设置超时
	timeout := time.After(2 * time.Second)
	completedCount := 0

	for completedCount < 2 {
		select {
		case <-completed:
			completedCount++
		case <-timeout:
			t.Fatal("Test timed out")
		}
	}

	wg.Wait()

	// 验证两个资源都已解锁
	if locker.IsLocked(resource1) {
		t.Error("Resource 1 should be unlocked")
	}
	if locker.IsLocked(resource2) {
		t.Error("Resource 2 should be unlocked")
	}
}

func BenchmarkLocker_LockUnlock(b *testing.B) {
	locker := NewLocker()
	resource := "benchmark_resource"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		locker.Lock(resource)
		locker.Unlock(resource)
	}
}

func BenchmarkLocker_ConcurrentLockUnlock(b *testing.B) {
	locker := NewLocker()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			resource := fmt.Sprintf("resource_%d", i%10)
			locker.Lock(resource)
			locker.Unlock(resource)
			i++
		}
	})
}
