package lock

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// Locker 提供了一个基于名称的互斥锁（named mutex）机制。
// 它的核心价值在于：允许对特定资源（由一个字符串名称标识，例如数据源名称）的操作进行序列化，
// 同时允许对不同资源的操作并发执行。
// 例如，可以同时对 'mysql-db1' 和 'es-cluster2' 进行备份，但不能同时对 'mysql-db1' 进行两次操作。
type Locker struct {
	mu    sync.Mutex             // 这是一个元锁（meta-mutex），用于保护下面的 locks map 本身，确保map的并发访问安全。
	locks map[string]*sync.Mutex // 存储每个名称到其对应锁的映射。
}

// NewLocker 创建一个新的 Locker 实例。
func NewLocker() *Locker {
	return &Locker{
		locks: make(map[string]*sync.Mutex),
	}
}

// Lock 为一个特定的名称获取锁。
// 如果该名称的锁已经被其他goroutine持有，则此调用会阻塞，直到锁被释放。
func (l *Locker) Lock(name string) {
	// 1. 锁住元锁，以安全地访问和修改map。
	l.mu.Lock()
	m, ok := l.locks[name]
	if !ok {
		// 如果是第一次遇到这个名称，就为它创建一个新的锁，并存入map中。
		m = &sync.Mutex{}
		l.locks[name] = m
	}
	// 2. **关键步骤**: 立即释放元锁。
	//    一旦我们获取了特定名称的锁（m）的引用，就不再需要保持对整个map的锁定。
	//    这使得其他尝试锁定 *不同名称* 的goroutine可以继续进行，从而实现了高并发性。
	l.mu.Unlock()

	// 3. 锁定特定名称的锁。如果其他goroutine正在持有这个锁，这里会发生阻塞。
	m.Lock()
}

// Unlock 释放一个特定名称的锁。
// 重要：如果尝试解锁一个未被锁定的锁，此方法会引发panic，这与标准库 `sync.Mutex` 的行为保持一致。
func (l *Locker) Unlock(name string) {
	// 同样，先锁住元锁以安全地读取map。
	l.mu.Lock()
	m, ok := l.locks[name]
	// 立即释放元锁。
	l.mu.Unlock()

	if !ok {
		// 这种情况理论上不应该发生，除非代码中出现了不匹配的 Lock/Unlock 调用。
		// 在这里引发panic是合理的，因为它暴露了程序中的一个严重逻辑错误。
		panic("unlock of unlocked mutex")
	}

	// 解锁特定名称的锁。
	m.Unlock()
}

// IsLocked 检查指定名称的锁是否被持有
// 注意：这个方法主要用于测试，在生产代码中应谨慎使用
func (l *Locker) IsLocked(name string) bool {
	l.mu.Lock()
	defer l.mu.Unlock()

	m, ok := l.locks[name]
	if !ok {
		return false
	}

	// 尝试非阻塞地获取锁来检查是否被持有
	// 如果能获取到，说明没有被锁定，立即释放并返回false
	// 如果不能获取到，说明被锁定，返回true
	if m.TryLock() {
		m.Unlock()
		return false
	}
	return true
}

// LockWithTimeout 带超时的锁获取方法，防止死锁
func (l *Locker) LockWithTimeout(name string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	done := make(chan struct{})
	var lockErr error

	go func() {
		defer func() {
			if r := recover(); r != nil {
				lockErr = fmt.Errorf("获取锁时发生panic: %v", r)
			}
			close(done)
		}()
		l.Lock(name)
	}()

	select {
	case <-done:
		if lockErr != nil {
			return lockErr
		}
		// 锁已成功获取，直接返回成功
		return nil
	case <-ctx.Done():
		// 超时了，但是goroutine可能还在等待锁
		// 启动一个清理goroutine来处理可能的锁泄漏
		go func() {
			<-done // 等待Lock调用完成
			if lockErr == nil && ctx.Err() != nil {
				// 如果没有其他错误且确实超时了，释放锁防止泄漏
				l.Unlock(name)
			}
		}()
		return fmt.Errorf("获取锁 '%s' 超时", name)
	}
}
