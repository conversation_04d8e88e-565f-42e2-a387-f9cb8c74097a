// Package gocloud 提供基于 gocloud.dev/blob 的云存储后端适配器。
//
// GocloudBackend 将 gocloud.dev/blob.Bucket 适配为 unibackup 的 Backend 接口，
// 实现了统一的云存储抽象，支持 AWS S3、Google Cloud Storage、Azure Blob 等多种云存储。
//
// 设计原则：
//  1. **零破坏性适配**: 完全兼容现有 Backend 接口
//  2. **错误标准化**: 将 gocloud 错误映射为标准错误类型
//  3. **性能优化**: 利用 gocloud 的流式操作和并发优化
//  4. **监控友好**: 提供详细的日志记录和错误上下文
package gocloud

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"gocloud.dev/blob"
	"gocloud.dev/gcerrors"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"
)

// GocloudBackend 将 gocloud.dev/blob.Bucket 适配为 Backend 接口。
// 它封装了 gocloud 的 Bucket，提供与本地存储一致的接口。
type GocloudBackend struct {
	bucket *blob.Bucket
	logger *zap.Logger
}

// NewGocloudBackend 创建一个新的 gocloud 存储后端适配器。
//
// 参数：
//   bucket: gocloud.dev/blob.Bucket 实例
//   logger: 日志记录器，用于记录操作日志
//
// 返回：
//   *GocloudBackend: 适配器实例
//
// 注意：调用者负责 bucket 的生命周期管理，适配器不会关闭 bucket。
func NewGocloudBackend(bucket *blob.Bucket, logger *zap.Logger) *GocloudBackend {
	if logger == nil {
		logger = zap.NewNop()
	}

	return &GocloudBackend{
		bucket: bucket,
		logger: logger,
	}
}

// Put 将数据流写入到指定的 key。
// 实现 Backend.Put 接口，使用 gocloud 的 Upload 方法。
func (b *GocloudBackend) Put(ctx context.Context, key string, data io.Reader) (int64, error) {
	b.logger.Debug("Put方法开始", zap.String("key", key))

	if err := b.validateKey(key); err != nil {
		b.logger.Error("Put方法validateKey失败",
			zap.String("key", key),
			zap.Error(err))
		return 0, err
	}

	b.logger.Debug("开始上传对象", zap.String("key", key))
	start := time.Now()

	// 使用 TeeReader 计算写入字节数
	counter := &byteCounter{}
	teeReader := io.TeeReader(data, counter)

	// 使用 gocloud Upload 方法，设置ContentType
	opts := &blob.WriterOptions{
		ContentType: "application/octet-stream", // 默认二进制类型
	}
	err := b.bucket.Upload(ctx, key, teeReader, opts)
	if err != nil {
		b.logger.Error("上传对象失败",
			zap.String("key", key),
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return counter.count, b.mapError(err)
	}

	b.logger.Debug("上传对象成功",
		zap.String("key", key),
		zap.Int64("bytes", counter.count),
		zap.Duration("duration", time.Since(start)))
	return counter.count, nil
}

// Get 从指定的 key 获取数据流。
// 实现 Backend.Get 接口，使用 gocloud 的 NewReader 方法。
func (b *GocloudBackend) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	if err := b.validateKey(key); err != nil {
		return nil, err
	}

	b.logger.Debug("开始下载对象", zap.String("key", key))
	start := time.Now()

	reader, err := b.bucket.NewReader(ctx, key, nil)
	if err != nil {
		// 对于文件不存在的情况，使用DEBUG级别而不是ERROR
		if gcerrors.Code(err) == gcerrors.NotFound {
			b.logger.Debug("对象不存在（这通常是正常的）",
				zap.String("key", key),
				zap.Duration("duration", time.Since(start)))
		} else {
			b.logger.Error("下载对象失败",
				zap.String("key", key),
				zap.Error(err),
				zap.Duration("duration", time.Since(start)))
		}
		return nil, b.mapError(err)
	}

	b.logger.Debug("下载对象成功",
		zap.String("key", key),
		zap.Int64("size", reader.Size()),
		zap.Duration("duration", time.Since(start)))
	return reader, nil
}

// Delete 删除指定的 key。
// 实现 Backend.Delete 接口，支持删除单个对象或目录下的所有对象。
// 为了与本地存储的行为保持一致，如果 key 对应的是一个"目录"（即存在以该 key 为前缀的对象），
// 则删除该前缀下的所有对象。
func (b *GocloudBackend) Delete(ctx context.Context, key string) error {
	if err := b.validateKey(key); err != nil {
		return err
	}

	b.logger.Debug("开始删除对象", zap.String("key", key))
	start := time.Now()

	// 首先尝试直接删除单个对象
	err := b.bucket.Delete(ctx, key)
	if err == nil {
		b.logger.Debug("删除单个对象成功",
			zap.String("key", key),
			zap.Duration("duration", time.Since(start)))
		return nil
	}

	// 如果直接删除失败且是 NotFound 错误，尝试作为目录删除
	if gcerrors.Code(err) == gcerrors.NotFound {
		b.logger.Debug("单个对象不存在，尝试作为目录删除", zap.String("key", key))
		return b.deleteDirectory(ctx, key, start)
	}

	// 其他错误直接返回
	b.logger.Error("删除对象失败",
		zap.String("key", key),
		zap.Error(err),
		zap.Duration("duration", time.Since(start)))
	return b.mapError(err)
}

// deleteDirectory 删除指定前缀下的所有对象，模拟目录删除行为
func (b *GocloudBackend) deleteDirectory(ctx context.Context, prefix string, start time.Time) error {
	// 确保前缀以 "/" 结尾，以便正确匹配目录下的对象
	if !strings.HasSuffix(prefix, "/") {
		prefix = prefix + "/"
	}

	b.logger.Debug("开始删除目录", zap.String("prefix", prefix))

	// 列出所有匹配前缀的对象
	iter := b.bucket.List(&blob.ListOptions{Prefix: prefix})

	var deletedCount int
	var errors []error

	for {
		obj, err := iter.Next(ctx)
		if err == io.EOF {
			break
		}
		if err != nil {
			b.logger.Error("列出目录对象失败",
				zap.String("prefix", prefix),
				zap.Error(err))
			errors = append(errors, err)
			continue
		}

		// 跳过目录对象本身
		if obj.IsDir {
			continue
		}

		// 删除单个对象
		if err := b.bucket.Delete(ctx, obj.Key); err != nil {
			b.logger.Warn("删除目录中的对象失败",
				zap.String("key", obj.Key),
				zap.Error(err))
			errors = append(errors, err)
		} else {
			deletedCount++
			b.logger.Debug("删除目录中的对象成功", zap.String("key", obj.Key))
		}
	}

	duration := time.Since(start)

	if len(errors) > 0 {
		b.logger.Error("删除目录部分失败",
			zap.String("prefix", prefix),
			zap.Int("deleted_count", deletedCount),
			zap.Int("error_count", len(errors)),
			zap.Duration("duration", duration))
		// 返回第一个错误
		return b.mapError(errors[0])
	}

	if deletedCount == 0 {
		// 没有找到任何对象，这与本地存储的行为一致（目录不存在时不报错）
		b.logger.Debug("目录不存在或为空",
			zap.String("prefix", prefix),
			zap.Duration("duration", duration))
		return nil
	}

	b.logger.Debug("删除目录成功",
		zap.String("prefix", prefix),
		zap.Int("deleted_count", deletedCount),
		zap.Duration("duration", duration))
	return nil
}

// NewWriter 创建一个流式写入器。
// 实现 Backend.NewWriter 接口，使用 gocloud 的 NewWriter 方法。
func (b *GocloudBackend) NewWriter(ctx context.Context, key string) (io.WriteCloser, error) {
	if err := b.validateKey(key); err != nil {
		return nil, err
	}

	b.logger.Debug("创建写入器", zap.String("key", key))

	opts := &blob.WriterOptions{
		ContentType: "application/octet-stream", // 默认二进制类型
	}
	writer, err := b.bucket.NewWriter(ctx, key, opts)
	if err != nil {
		b.logger.Error("创建写入器失败",
			zap.String("key", key),
			zap.Error(err))
		return nil, b.mapError(err)
	}

	// 包装 writer 以添加日志记录
	return &writerWrapper{
		Writer: writer,
		key:    key,
		logger: b.logger,
		start:  time.Now(),
	}, nil
}

// List 列出指定前缀下的所有对象。
// 实现 Backend.List 接口，使用 gocloud 的 List 方法。
func (b *GocloudBackend) List(ctx context.Context, prefix string) ([]types.StorageObject, error) {
	b.logger.Debug("开始列出对象", zap.String("prefix", prefix))
	start := time.Now()

	// 使用 gocloud ListIterator
	iter := b.bucket.List(&blob.ListOptions{Prefix: prefix})

	var objects []types.StorageObject
	for {
		obj, err := iter.Next(ctx)
		if err == io.EOF {
			break
		}
		if err != nil {
			b.logger.Error("列出对象失败",
				zap.String("prefix", prefix),
				zap.Error(err),
				zap.Duration("duration", time.Since(start)))
			return nil, b.mapError(err)
		}

		// 跳过目录对象
		if obj.IsDir {
			continue
		}

		// 转换为 StorageObject
		storageObj := types.StorageObject{
			Key:          obj.Key,
			Size:         obj.Size,
			LastModified: obj.ModTime,
		}

		// 设置 ETag（如果可用）
		if len(obj.MD5) > 0 {
			storageObj.ETag = fmt.Sprintf("%x", obj.MD5)
		}

		objects = append(objects, storageObj)
	}

	b.logger.Debug("列出对象成功",
		zap.String("prefix", prefix),
		zap.Int("count", len(objects)),
		zap.Duration("duration", time.Since(start)))
	return objects, nil
}

// Exists 检查指定的 key 是否存在。
// 实现 Backend.Exists 接口，使用 gocloud 的 Exists 方法。
func (b *GocloudBackend) Exists(ctx context.Context, key string) (bool, error) {
	if err := b.validateKey(key); err != nil {
		return false, err
	}

	b.logger.Debug("检查对象存在性", zap.String("key", key))
	start := time.Now()

	exists, err := b.bucket.Exists(ctx, key)
	if err != nil {
		b.logger.Error("检查对象存在性失败",
			zap.String("key", key),
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return false, b.mapError(err)
	}

	b.logger.Debug("检查对象存在性完成",
		zap.String("key", key),
		zap.Bool("exists", exists),
		zap.Duration("duration", time.Since(start)))
	return exists, nil
}

// HealthCheck 检查存储后端的健康状态。
// 实现 Backend.HealthCheck 接口，使用 gocloud 的 IsAccessible 方法。
func (b *GocloudBackend) HealthCheck(ctx context.Context) error {
	b.logger.Debug("开始健康检查")
	start := time.Now()

	accessible, err := b.bucket.IsAccessible(ctx)
	if err != nil {
		b.logger.Error("健康检查失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return b.mapError(err)
	}

	if !accessible {
		err := fmt.Errorf("存储后端不可访问")
		b.logger.Error("健康检查失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return interfaces.ErrStorageUnavailable
	}

	b.logger.Debug("健康检查成功", zap.Duration("duration", time.Since(start)))
	return nil
}

// validateKey 验证存储键的有效性
func (b *GocloudBackend) validateKey(key string) error {
	b.logger.Debug("存储键验证开始",
		zap.String("key", key),
		zap.Int("length", len(key)))

	if key == "" {
		b.logger.Error("存储键验证失败：空键", zap.String("key", key))
		return interfaces.ErrInvalidKey
	}

	// 检查是否包含绝对路径
	if strings.HasPrefix(key, "/") {
		b.logger.Error("存储键验证失败：不能是绝对路径", zap.String("key", key))
		return interfaces.ErrInvalidKey
	}

	b.logger.Debug("存储键验证通过", zap.String("key", key))
	return nil
}

// mapError 将 gocloud 错误映射为标准错误类型
func (b *GocloudBackend) mapError(err error) error {
	if err == nil {
		return nil
	}

	// 使用 gocloud 的错误码进行映射
	code := gcerrors.Code(err)
	switch code {
	case gcerrors.NotFound:
		return interfaces.ErrObjectNotFound
	case gcerrors.AlreadyExists:
		return interfaces.ErrObjectAlreadyExists
	case gcerrors.PermissionDenied:
		return interfaces.ErrPermissionDenied
	case gcerrors.InvalidArgument:
		return interfaces.ErrInvalidKey
	case gcerrors.DeadlineExceeded:
		return interfaces.ErrStorageUnavailable
	case gcerrors.ResourceExhausted:
		return interfaces.ErrInsufficientSpace
	default:
		// 对于未知错误，包装原始错误
		return fmt.Errorf("云存储操作失败: %w", err)
	}
}

// byteCounter 用于计算写入的字节数
type byteCounter struct {
	count int64
}

func (c *byteCounter) Write(p []byte) (n int, err error) {
	n = len(p)
	c.count += int64(n)
	return n, nil
}

// writerWrapper 包装 gocloud Writer 以添加日志记录
type writerWrapper struct {
	*blob.Writer
	key    string
	logger *zap.Logger
	start  time.Time
}

func (w *writerWrapper) Close() error {
	err := w.Writer.Close()
	duration := time.Since(w.start)

	if err != nil {
		w.logger.Error("写入器关闭失败",
			zap.String("key", w.key),
			zap.Error(err),
			zap.Duration("duration", duration))
	} else {
		w.logger.Debug("写入器关闭成功",
			zap.String("key", w.key),
			zap.Duration("duration", duration))
	}

	return err
}
