// Package interfaces 定义了存储抽象层的核心接口。
//
// 这个包包含了所有存储后端必须实现的接口定义，为不同类型的存储后端
// （本地文件系统、云存储等）提供统一的抽象。
//
// 设计原则：
//  1. 接口定义与具体实现分离
//  2. 支持流式操作，适合大文件处理
//  3. 提供完整的错误处理契约
//  4. 支持上下文取消和超时控制
package interfaces

import (
	"context"
	"io"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// Backend 定义了统一的存储后端接口。
// 所有存储后端（本地、云存储）都必须实现这个接口。
//
// 设计目标：
//   - 为 Provider 提供统一的存储抽象
//   - 支持流式读写，适合大文件操作
//   - 提供完整的 CRUD 操作
//   - 支持对象列表和存在性检查
//   - 包含健康检查机制
//
// 错误处理契约：
//   - 使用本包定义的标准错误类型
//   - 支持上下文取消和超时
//   - 提供详细的错误信息
type Backend interface {
	// Put 将数据流写入到指定的 key。
	// 这是一个流式操作，支持大文件写入而不会占用过多内存。
	// 参数：
	//   ctx: 上下文，用于取消操作和超时控制
	//   key: 存储键，通常是相对路径（如 "mysql/backup-123/data.sql.gz"）
	//   data: 要写入的数据流
	// 返回：
	//   int64: 实际写入的字节数
	//   error: 操作错误，如磁盘空间不足、权限错误等
	Put(ctx context.Context, key string, data io.Reader) (int64, error)

	// Get 从指定的 key 获取数据流。
	// 返回的 ReadCloser 必须由调用者负责关闭。
	// 参数：
	//   ctx: 上下文，用于取消操作和超时控制
	//   key: 存储键
	// 返回：
	//   io.ReadCloser: 数据读取器，调用者负责关闭
	//   error: 操作错误，如对象不存在则返回 ErrObjectNotFound
	Get(ctx context.Context, key string) (io.ReadCloser, error)

	// Delete 删除指定的 key。
	// 参数：
	//   ctx: 上下文，用于取消操作和超时控制
	//   key: 要删除的存储键
	// 返回：
	//   error: 操作错误，如果对象不存在则返回 ErrObjectNotFound
	Delete(ctx context.Context, key string) error

	// NewWriter 创建一个流式写入器，用于直接写入数据到指定的 key。
	// 这个方法专门为 Provider 提供，支持大文件的流式写入，避免内存占用过高。
	// 典型使用场景：
	//   - MySQL Provider 的 mysqldump 输出直接写入压缩文件
	//   - Elasticsearch Provider 的快照元数据写入
	//   - 任何需要流式写入的大文件场景
	// 参数：
	//   ctx: 上下文，用于取消操作和超时控制
	//   key: 存储键，通常是相对路径（如 "mysql/backup-123/data.sql.gz"）
	// 返回：
	//   io.WriteCloser: 写入器，调用者负责关闭以确保数据完整性
	//   error: 操作错误，如权限错误、磁盘空间不足等
	// 注意：
	//   - 调用者必须调用 Close() 方法以确保数据完整写入
	//   - 如果写入过程中发生错误，应该删除部分写入的文件
	//   - 支持上下文取消，但已写入的数据可能需要手动清理
	NewWriter(ctx context.Context, key string) (io.WriteCloser, error)

	// List 列出指定前缀下的所有对象。
	// 参数：
	//   ctx: 上下文，用于取消操作和超时控制
	//   prefix: 对象前缀，用于过滤（如 "mysql/" 列出所有 MySQL 相关对象）
	// 返回：
	//   []types.StorageObject: 对象列表，包含键名、大小、修改时间等信息
	//   error: 操作错误
	List(ctx context.Context, prefix string) ([]types.StorageObject, error)

	// Exists 检查指定的 key 是否存在。
	// 参数：
	//   ctx: 上下文，用于取消操作和超时控制
	//   key: 要检查的存储键
	// 返回：
	//   bool: 对象是否存在
	//   error: 操作错误（注意：对象不存在不是错误）
	Exists(ctx context.Context, key string) (bool, error)

	// HealthCheck 检查存储后端的健康状态。
	// 对于本地存储：检查目录访问权限和磁盘空间
	// 对于云存储：检查网络连接和认证状态
	// 参数：
	//   ctx: 上下文，用于超时控制
	// 返回：
	//   error: 健康检查失败时的错误信息
	HealthCheck(ctx context.Context) error
}

// StorageManager 定义了存储管理器接口（预留）
// 这个接口将在阶段二中实现，用于管理备份记录、任务状态等高级功能
type StorageManager interface {
	// GetBackend 返回底层的存储后端
	GetBackend() Backend

	// SaveBackupRecord 保存备份记录
	SaveBackupRecord(ctx context.Context, record *types.BackupRecord) error

	// GetBackupRecord 获取备份记录
	GetBackupRecord(ctx context.Context, id string) (*types.BackupRecord, error)

	// DeleteBackupRecord 删除备份记录
	DeleteBackupRecord(ctx context.Context, record *types.BackupRecord) error

	// ListArchivalBackups 列出归档备份
	ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]*types.BackupRecord, error)

	// GetBackupWriter 为 Provider 提供写入器
	GetBackupWriter(ctx context.Context, record *types.BackupRecord, filename string) (io.WriteCloser, error)
}
