package interfaces

import "errors"

// 存储层标准错误定义
// 这些错误提供了统一的错误处理契约，所有 Backend 实现都应该使用这些错误类型

// ErrObjectNotFound 表示请求的存储对象不存在。
// 这是一个预期的错误，通常不需要记录为系统错误。
// 使用场景：Get、Delete 操作中对象不存在时返回此错误。
var ErrObjectNotFound = errors.New("storage object not found")

// ErrObjectAlreadyExists 表示要创建的存储对象已经存在。
// 这通常发生在需要确保对象唯一性的操作中。
var ErrObjectAlreadyExists = errors.New("storage object already exists")

// ErrInvalidKey 表示提供的存储键无效。
// 无效的键包括：空字符串、包含非法字符、路径遍历攻击等。
var ErrInvalidKey = errors.New("invalid storage key")

// ErrStorageUnavailable 表示存储后端当前不可用。
// 这可能是临时性问题，如网络中断、服务维护等。
// 这类错误通常是可重试的。
var ErrStorageUnavailable = errors.New("storage backend unavailable")

// ErrInsufficientSpace 表示存储空间不足。
// 这通常发生在写入操作中，磁盘空间或配额不足时返回此错误。
var ErrInsufficientSpace = errors.New("insufficient storage space")

// ErrPermissionDenied 表示权限不足。
// 这通常是一个配置错误，需要修正权限设置。
var ErrPermissionDenied = errors.New("permission denied")

// 为了与设计文档中的错误契约保持严格一致，提供以下别名：
// 这些别名确保所有 Backend 实现都能使用统一的错误类型，
// 无论是使用详细的错误名称还是设计文档中的简化名称。

// ErrPermission 是 ErrPermissionDenied 的别名，与设计文档契约保持一致。
var ErrPermission = ErrPermissionDenied

// ErrAlreadyExists 是 ErrObjectAlreadyExists 的别名，与设计文档契约保持一致。
var ErrAlreadyExists = ErrObjectAlreadyExists

// 错误码常量，用于创建结构化的 BackupError
const (
	// 存储层错误码
	ErrCodeObjectNotFound      = "STORAGE_OBJECT_NOT_FOUND"
	ErrCodeObjectAlreadyExists = "STORAGE_OBJECT_ALREADY_EXISTS"
	ErrCodeInvalidKey          = "STORAGE_INVALID_KEY"
	ErrCodeStorageUnavailable  = "STORAGE_UNAVAILABLE"
	ErrCodeInsufficientSpace   = "STORAGE_INSUFFICIENT_SPACE"
	ErrCodePermissionDenied    = "STORAGE_PERMISSION_DENIED"
)

// IsNotFound 检查错误是否为对象不存在错误
func IsNotFound(err error) bool {
	return errors.Is(err, ErrObjectNotFound)
}

// IsAlreadyExists 检查错误是否为对象已存在错误
func IsAlreadyExists(err error) bool {
	return errors.Is(err, ErrObjectAlreadyExists)
}

// IsInvalidKey 检查错误是否为无效键错误
func IsInvalidKey(err error) bool {
	return errors.Is(err, ErrInvalidKey)
}

// IsPermissionDenied 检查错误是否为权限错误
func IsPermissionDenied(err error) bool {
	return errors.Is(err, ErrPermissionDenied)
}

// IsStorageUnavailable 检查错误是否为存储不可用错误
func IsStorageUnavailable(err error) bool {
	return errors.Is(err, ErrStorageUnavailable)
}

// IsInsufficientSpace 检查错误是否为空间不足错误
func IsInsufficientSpace(err error) bool {
	return errors.Is(err, ErrInsufficientSpace)
}
