package storage

import (
	"context"
	"os"
	"strings"
	"testing"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// TestNewBackend 测试工厂函数的基本功能
func TestNewBackend(t *testing.T) {
	logger := zap.NewNop()

	tests := []struct {
		name        string
		config      *types.Config
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
			errorMsg:    "配置不能为空",
		},
		{
			name: "empty backup root",
			config: &types.Config{
				BackupRoot: "",
			},
			expectError: true,
			errorMsg:    "备份根目录不能为空",
		},
		{
			name: "non-existent backup root",
			config: &types.Config{
				BackupRoot: "/non/existent/path",
			},
			expectError: true,
			errorMsg:    "存储目录不存在",
		},
		{
			name: "relative path",
			config: &types.Config{
				BackupRoot: "relative/path",
			},
			expectError: true,
			errorMsg:    "存储路径必须是绝对路径",
		},
		{
			name: "valid config",
			config: &types.Config{
				BackupRoot: t.TempDir(),
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			backend, err := NewBackend(tt.config, logger)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("错误消息不匹配，期望包含 %q，实际 %q", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			if backend == nil {
				t.Errorf("backend 不应该为 nil")
				return
			}

			// 验证返回的是 LocalBackend
			// 通过调用 HealthCheck 来验证 backend 是否正常工作
			ctx := context.Background()
			if err := backend.HealthCheck(ctx); err != nil {
				t.Errorf("backend HealthCheck 失败: %v", err)
			}
		})
	}
}

// TestValidateBackendConfig 测试配置验证功能
func TestValidateBackendConfig(t *testing.T) {
	tempDir := t.TempDir()

	tests := []struct {
		name        string
		config      *types.Config
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
			errorMsg:    "配置不能为空",
		},
		{
			name: "empty backup root",
			config: &types.Config{
				BackupRoot: "",
			},
			expectError: true,
			errorMsg:    "备份根目录不能为空",
		},
		{
			name: "valid config",
			config: &types.Config{
				BackupRoot: tempDir,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateBackendConfig(tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("错误消息不匹配，期望包含 %q，实际 %q", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
			}
		})
	}
}

// TestLocalBackendConfig 测试本地存储配置
func TestLocalBackendConfig(t *testing.T) {
	tempDir := t.TempDir()

	tests := []struct {
		name        string
		config      *LocalBackendConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "empty path",
			config: &LocalBackendConfig{
				BasePath: "",
			},
			expectError: true,
		},
		{
			name: "relative path",
			config: &LocalBackendConfig{
				BasePath: "relative/path",
			},
			expectError: true,
			errorMsg:    "存储路径必须是绝对路径",
		},
		{
			name: "non-existent path",
			config: &LocalBackendConfig{
				BasePath: "/non/existent/path",
			},
			expectError: true,
			errorMsg:    "存储目录不存在",
		},
		{
			name: "file instead of directory",
			config: &LocalBackendConfig{
				BasePath: createTempFile(t, tempDir),
			},
			expectError: true,
			errorMsg:    "存储路径不是目录",
		},
		{
			name: "valid directory",
			config: &LocalBackendConfig{
				BasePath: tempDir,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试 GetType
			if tt.config.GetType() != BackendTypeLocal {
				t.Errorf("GetType() = %v, 期望 %v", tt.config.GetType(), BackendTypeLocal)
			}

			// 测试 Validate
			err := tt.config.Validate()

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("错误消息不匹配，期望包含 %q，实际 %q", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
			}
		})
	}
}

// TestGetSupportedBackendTypes 测试支持的后端类型列表
func TestGetSupportedBackendTypes(t *testing.T) {
	types := GetSupportedBackendTypes()

	if len(types) == 0 {
		t.Errorf("支持的后端类型列表不应该为空")
	}

	// 验证包含本地存储类型
	found := false
	for _, backendType := range types {
		if backendType == BackendTypeLocal {
			found = true
			break
		}
	}

	if !found {
		t.Errorf("支持的后端类型列表应该包含 %v", BackendTypeLocal)
	}
}

// TestIsBackendTypeSupported 测试后端类型支持检查
func TestIsBackendTypeSupported(t *testing.T) {
	tests := []struct {
		backendType BackendType
		expected    bool
	}{
		{BackendTypeLocal, true},
		{BackendTypeS3, false},
		{BackendTypeGCS, false},
		{BackendTypeAzure, false},
		{"unknown", false},
	}

	for _, tt := range tests {
		t.Run(string(tt.backendType), func(t *testing.T) {
			result := IsBackendTypeSupported(tt.backendType)
			if result != tt.expected {
				t.Errorf("IsBackendTypeSupported(%v) = %v, 期望 %v", tt.backendType, result, tt.expected)
			}
		})
	}
}

// createTempFile 创建临时文件并返回路径
func createTempFile(t *testing.T, dir string) string {
	file, err := os.CreateTemp(dir, "test")
	if err != nil {
		t.Fatalf("创建临时文件失败: %v", err)
	}
	defer file.Close()
	return file.Name()
}
