package storage

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	berrors "git.gobies.org/fobrain/unibackup/internal/errors"
	"git.gobies.org/fobrain/unibackup/internal/provider"
	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/oklog/ulid/v2"
	"go.uber.org/zap"
)

// TaskManager 定义了storage包需要的任务管理接口。
// 这是一个简化的接口，只包含统一查询和删除功能需要的方法。
type TaskManager interface {
	// GetTask 通过任务ID获取任务详情
	GetTask(taskID string) (*types.Task, error)

	// ListTasks 列出所有任务
	ListTasks() ([]*types.Task, error)

	// DeleteTask 删除任务记录
	DeleteTask(taskID string) error
}

// Package storage 实现了备份存储和元数据的核心管理逻辑。
//
// StorageManager 是整个 UniBackup 系统的基石，它扮演着以下几个关键角色：
//  1. **文件系统抽象层**: 它封装了所有对备份根目录的读写操作，如创建目录、写入元数据文件等。
//     这使得上层逻辑可以完全不关心具体的目录结构和文件命名规范。
//  2. **元数据权威**: 它是备份记录（`BackupRecord`）和增量链元数据（`ChainMeta`）的唯一管理者。
//     所有对元数据的创建、更新、读取和删除都必须通过 StorageManager 进行。
//  3. **增量链完整性保护者**: 它包含了最核心的安全逻辑，即严格禁止单独删除增量链的任何部分，
//     只允许通过 `DeleteIncrementalChain` 原子地删除整条链，从而保证了增量链的逻辑完整性。
//  4. **恢复逻辑构建器**: `GetRestorationChain` 方法负责根据用户指定的任意一个备份点，构建出
//     一个从头到尾、顺序正确的备份记录序列，供上层的 `task.Manager` 执行恢复。
//
// 这个模块的设计目标是健壮、安全和可扩展。通过将文件系统操作集中化，它为未来支持
// 其他存储后端（如 S3 对象存储）打下了坚实的基础。
type StorageManager interface {
	// CreateBackupRecord 是启动新备份的核心入口。
	// 它负责生成唯一的备份ID，创建目录结构，并返回一个已初始化的备份记录供Provider填充。
	CreateBackupRecord(sourceType types.SourceType, sourceName string, backupType types.BackupType, chainID string, description string) (*types.BackupRecord, *types.BackupError)
	// SaveBackupRecord 将一个完成（或失败）的备份记录的元数据保存到其对应的 `metadata.json` 文件中。
	// 这个方法也负责在备份成功时，将记录追加到增量链的聚合元数据（`chain_meta.json`）中。
	// 注意：此方法直接写入最终文件，适用于初始状态保存（InProgress）。
	SaveBackupRecord(record *types.BackupRecord) *types.BackupError
	// SaveBackupRecordAtomic 原子性地保存备份记录，确保元数据一致性。
	// 使用临时文件+原子重命名模式，防止写入过程中的文件损坏。
	// 推荐用于最终状态保存（Completed/Failed），提供更强的数据完整性保证。
	SaveBackupRecordAtomic(record *types.BackupRecord) *types.BackupError
	// GetBackupRecord 通过备份ID从文件系统中获取单个备份记录的元数据。
	GetBackupRecord(sourceType types.SourceType, id string) (*types.BackupRecord, *types.BackupError)
	// GetChainBackupRecord 通过提供chainID和backupID，精确查找增量链中的单个备份记录。
	GetChainBackupRecord(sourceType types.SourceType, chainID, backupID string) (*types.BackupRecord, *types.BackupError)
	// DeleteBackupRecord 删除一个独立的归档备份。
	// 包含关键的安全检查，禁止删除属于增量链的任何备份点。
	DeleteBackupRecord(record *types.BackupRecord) *types.BackupError
	// ListIncrementalChains 列出指定数据源实例的所有增量备份链。
	ListIncrementalChains(sourceType types.SourceType, sourceName string) ([]types.IncrementalChain, *types.BackupError)
	// GetLatestChainRecord 获取增量链中最新的一个备份记录。
	GetLatestChainRecord(sourceType types.SourceType, chainID string) (*types.BackupRecord, *types.BackupError)
	// GetRestorationChain 获取用于恢复的备份记录链。
	// 如果指定的 backupID 属于增量链，它将返回从链的起点到该点的所有备份记录，并按正确的顺序排列。
	GetRestorationChain(sourceType types.SourceType, id string) ([]*types.BackupRecord, *types.BackupError)
	// ListArchivalBackups 列出指定数据源的所有独立归档备份。
	ListArchivalBackups(sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError)
	// FindBackupRecord 在所有备份中（包括归档和增量链）查找指定的backupID。
	FindBackupRecord(sourceType types.SourceType, backupID string) (*types.BackupRecord, *types.BackupError)
	// FindBackupRecordAcrossTypes 跨所有已知的数据源类型查找并返回一个备份记录。
	// 这比 FindBackupRecord 慢，因为它可能需要扫描多个目录，但对于清理等场景非常有用。
	FindBackupRecordAcrossTypes(backupID string) (*types.BackupRecord, *types.BackupError)
	// ReadChainMeta 读取并解析指定增量链的聚合元数据文件 (`chain_meta.json`)。
	ReadChainMeta(sourceType types.SourceType, chainID string) (*types.ChainMeta, *types.BackupError)
	// WriteChainMeta 将增量链的聚合元数据写入到 `chain_meta.json` 文件中。
	WriteChainMeta(sourceType types.SourceType, chainID string, meta *types.ChainMeta) *types.BackupError
	// DeleteIncrementalChain 原子地删除一整条增量链，包括其所有的备份点和元数据。
	DeleteIncrementalChain(sourceType types.SourceType, chainID string) *types.BackupError
	// CheckDataSourceHealth 检查数据源的健康状态。
	CheckDataSourceHealth(source types.SourceType, providers map[types.SourceType]provider.BackupProvider) *types.BackupError

	// --- 阶段二新增：系统文件操作接口 ---
	// 这些方法专门用于TaskManager等系统组件的文件操作需求

	// GetBackupRoot 获取备份根目录路径。
	GetBackupRoot() string

	// WriteSystemFile 写入系统文件（如tasks.json）。
	WriteSystemFile(filePath string, data []byte) error

	// ReadSystemFile 读取系统文件（如tasks.json）。
	ReadSystemFile(filePath string) ([]byte, error)

	// EnsureSystemDir 确保系统目录存在。
	EnsureSystemDir(dirPath string) error

	// RenameSystemFile 重命名系统文件。
	RenameSystemFile(oldPath, newPath string) error

	// --- 阶段三新增：统一查询接口 ---
	// 这些方法基于tasks.json提供高性能的备份查询功能

	// ListAllBackups 基于tasks.json的统一备份查询接口。
	// 提供高性能的内存查询，支持过滤、搜索和分页功能。
	ListAllBackups(filter types.BackupFilter) (*types.BackupListResult, error)

	// GetBackupDetails 获取备份任务的详细信息。
	// 支持单个备份和分组备份的详情获取，分组备份会递归显示所有子任务。
	GetBackupDetails(taskID string) (*types.Task, error)

	// DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型。
	// 重要说明：增量备份删除时会删除整条增量链，保持数据完整性。
	DeleteBackupByTaskID(taskID string) error

	// GetBackupDeletionInfo 获取备份删除的影响信息。
	// 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等。
	GetBackupDeletionInfo(taskID string) (*types.BackupDeletionInfo, error)

	// UpdateConfig 动态更新配置，支持运行时配置变更
	UpdateConfig(cfg *types.Config) error

	// HasAnyBackups 检查指定数据源是否存在任何备份记录
	// 用于增量备份的智能处理：如果没有任何备份，则需要先执行初始备份
	HasAnyBackups(sourceType types.SourceType, sourceName string) (bool, error)
}

// 定义了备份产物目录中使用的标准文件名和目录名。
const (
	// MetadataFileName 是存储在每个备份目录中的元数据文件的标准名称。
	MetadataFileName = "metadata.json"
	// ArchivalDir 是用于存放独立的、一次性的归档备份的子目录名称。
	ArchivalDir = "archival"
	// ChainsDir 是用于存放受保护的增量备份链的子目录名称。
	ChainsDir = "chains"
	// ChainMetadataFileName 是增量链的聚合元数据文件的名称。
	ChainMetadataFileName = "chain_meta.json"
	// StatusFileName 是用于存储数据源锁定状态的文件名，用于防止在手动干预期间进行自动操作。
	StatusFileName = "status.json"
)

// Manager [[memory:1940293]] 负责所有对备份根目录的存储操作。
// 它严格遵循设计的目录结构和元数据规范，并包含了关键的安全检查以保护备份数据的完整性。
//
// 阶段二改造：Manager 现在通过 Backend 接口进行所有存储操作，而不是直接操作文件系统。
// 这种设计使得 StorageManager 可以支持多种存储后端（本地文件系统、云存储等）。
type Manager struct {
	backend     interfaces.Backend // 存储后端接口，负责实际的存储操作
	backupRoot  string             // 备份文件的根目录（用于路径生成）
	logger      *zap.Logger        // 用于记录内部操作日志的结构化日志记录器
	cfg         *types.Config      // 全局配置，用于获取所有数据源类型
	taskManager TaskManager        // 任务管理器接口，用于访问tasks.json数据
}

// 编译时检查，确保 *Manager 实现了 StorageManager 接口。
var _ StorageManager = (*Manager)(nil)

// NewManager 创建一个新的存储管理器实例。
//
// 阶段二改造：现在接受 Backend 接口作为依赖注入，实现存储抽象。
//
// 参数：
//
//	backend: 存储后端接口，负责实际的存储操作
//	cfg: 全局配置，用于获取备份根目录和数据源配置
//	logger: 用于记录内部操作日志
//
// 返回：
//
//	*Manager: 配置好的存储管理器实例
func NewManager(backend interfaces.Backend, cfg *types.Config, logger *zap.Logger, taskManager TaskManager) *Manager {
	return &Manager{
		backend:     backend,
		backupRoot:  cfg.BackupRoot,
		logger:      logger.With(zap.String("component", "storage_manager")),
		cfg:         cfg,
		taskManager: taskManager,
	}
}

// --- 路径管理 (Path Management) ---

// GetBackupRoot 获取备份根目录路径。
// 这个方法为需要计算相对路径的场景提供支持。
func (m *Manager) GetBackupRoot() string {
	return m.backupRoot
}

// UpdateConfig 动态更新配置，支持运行时配置变更
func (m *Manager) UpdateConfig(cfg *types.Config) error {
	// 更新配置引用
	m.cfg = cfg

	// 更新备份根目录
	if m.backupRoot != cfg.BackupRoot {
		m.logger.Info("BackupRoot已更新",
			zap.String("old", m.backupRoot),
			zap.String("new", cfg.BackupRoot))
		m.backupRoot = cfg.BackupRoot
	}

	// 重新创建存储后端以应用新的存储配置
	newBackend, err := NewBackend(cfg, m.logger)
	if err != nil {
		return fmt.Errorf("重新创建存储后端失败: %w", err)
	}

	// 更新存储后端
	m.backend = newBackend

	m.logger.Info("StorageManager配置更新完成", zap.String("backup_root", cfg.BackupRoot))
	return nil
}

// GetDataSourcePath 获取特定数据源的根路径。
// 路径结构: {BackupRoot}/{source_type}
func (m *Manager) GetDataSourcePath(source types.SourceType) string {
	return filepath.Join(m.backupRoot, string(source))
}

// GetArchivalBackupPath 获取特定归档备份的完整路径。
// 路径结构: {BackupRoot}/{source_type}/archival/{backup_id}
func (m *Manager) GetArchivalBackupPath(source types.SourceType, backupID string) string {
	return filepath.Join(m.GetDataSourcePath(source), ArchivalDir, backupID)
}

// GetChainPath 获取特定增量链中某个备份点的完整路径。
// 路径结构: {BackupRoot}/{source_type}/chains/{chain_id}/{backup_id}
func (m *Manager) GetChainPath(source types.SourceType, chainID, backupID string) string {
	return filepath.Join(m.GetDataSourcePath(source), ChainsDir, chainID, backupID)
}

// GetMetadataPath  获取特定备份记录的元数据文件路径。
func (m *Manager) GetMetadataPath(recordPath string) string {
	return filepath.Join(recordPath, MetadataFileName)
}

// GetChainMetadataPath  获取特定增量链的聚合元数据文件路径。
func (m *Manager) GetChainMetadataPath(source types.SourceType, chainID string) string {
	// 注意：chain_meta.json直接存在于chainID目录下
	return filepath.Join(m.GetChainPath(source, chainID, ""), ChainMetadataFileName)
}

// GetDataSourceStatusPath 获取数据源状态文件的路径。
func (m *Manager) GetDataSourceStatusPath(source types.SourceType) string {
	return filepath.Join(m.GetDataSourcePath(source), StatusFileName)
}

// --- 元数据I/O (Metadata I/O) ---

// ReadMetadata  读取并解析备份记录的元数据文件。
// 阶段二改造：使用 Backend 接口替代直接文件操作
func (m *Manager) ReadMetadata(path string) (*types.BackupRecord, *types.BackupError) {
	// 阶段二改造：将文件路径转换为存储键
	key := m.getStorageKey(path)

	ctx := context.Background()
	reader, err := m.backend.Get(ctx, key)
	if err != nil {
		// 将 Backend 错误映射到 StorageManager 错误
		if interfaces.IsNotFound(err) {
			return nil, berrors.NewStorageError("FILE_READ_ERROR", "ReadMetadata", fmt.Errorf("元数据文件 %s 不存在", path), false)
		}
		return nil, berrors.NewStorageError("FILE_READ_ERROR", "ReadMetadata", fmt.Errorf("读取元数据文件 %s 失败: %w", path, err), false)
	}
	defer reader.Close()

	// 读取所有数据
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, berrors.NewStorageError("FILE_READ_ERROR", "ReadMetadata", fmt.Errorf("读取元数据文件内容 %s 失败: %w", path, err), false)
	}

	var record types.BackupRecord
	if err := json.Unmarshal(data, &record); err != nil {
		return nil, berrors.NewStorageError("METADATA_PARSE_ERROR", "ReadMetadata", fmt.Errorf("从 %s 解析元数据失败: %w", path, err), false)
	}
	return &record, nil
}

// WriteChainMeta  将增量链的聚合元数据写入文件。
// 阶段二改造：使用 Backend 接口替代直接文件操作
func (m *Manager) WriteChainMeta(source types.SourceType, chainID string, meta *types.ChainMeta) *types.BackupError {
	path := m.GetChainMetadataPath(source, chainID)
	data, err := json.MarshalIndent(meta, "", "  ")
	if err != nil {
		return berrors.NewStorageError("METADATA_SERIALIZE_ERROR", "WriteChainMeta", fmt.Errorf("为 %s 序列化链元数据失败: %w", chainID, err), false)
	}

	// 阶段二改造：使用 Backend 接口替代直接文件操作
	key := m.getStorageKey(path)
	ctx := context.Background()
	_, err = m.backend.Put(ctx, key, strings.NewReader(string(data)))
	if err != nil {
		return berrors.NewStorageError("FILE_WRITE_ERROR", "WriteChainMeta", fmt.Errorf("写入链元数据文件 %s 失败: %w", path, err), false)
	}
	return nil
}

// ReadChainMeta 读取并解析增量链的聚合元数据文件。
// 如果文件不存在，它将返回一个nil的meta和nil的error，表示该链可能尚未创建或为空。
// 阶段二改造：使用 Backend 接口替代直接文件操作
func (m *Manager) ReadChainMeta(source types.SourceType, chainID string) (*types.ChainMeta, *types.BackupError) {
	path := m.GetChainMetadataPath(source, chainID)
	key := m.getStorageKey(path)

	ctx := context.Background()
	reader, err := m.backend.Get(ctx, key)
	if err != nil {
		if interfaces.IsNotFound(err) {
			return nil, nil // 文件不存在是正常情况，意味着还没有链元数据。
		}
		return nil, berrors.NewStorageError("FILE_READ_ERROR", "ReadChainMeta", fmt.Errorf("读取链元数据文件 %s 失败: %w", path, err), false)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, berrors.NewStorageError("FILE_READ_ERROR", "ReadChainMeta", fmt.Errorf("读取链元数据文件内容 %s 失败: %w", path, err), false)
	}

	var meta types.ChainMeta
	if err := json.Unmarshal(data, &meta); err != nil {
		return nil, berrors.NewStorageError("METADATA_PARSE_ERROR", "ReadChainMeta", fmt.Errorf("从 %s 解析链元数据失败: %w", path, err), false)
	}
	return &meta, nil
}

// --- Provider 支持方法 (Provider Support Methods) ---

// GetBackupWriter 为 Provider 提供写入器，支持流式写入到指定的备份文件。
// 这是阶段二新增的方法，专门为 Provider 的流式写入场景设计。
//
// 参数：
//
//	record: 备份记录，用于确定文件路径
//	filename: 要写入的文件名（如 "data.sql.gz", "binlog.sql.gz"）
//
// 返回：
//
//	io.WriteCloser: 写入器，调用者负责关闭
//	*types.BackupError: 操作错误
//
// 使用示例：
//
//	writer, err := manager.GetBackupWriter(record, "data.sql.gz")
//	if err != nil { return err }
//	defer writer.Close()
//	// 直接写入数据...
func (m *Manager) GetBackupWriter(record *types.BackupRecord, filename string) (io.WriteCloser, *types.BackupError) {
	// 参数验证
	if record == nil {
		return nil, berrors.NewStorageError("INVALID_ARGUMENT", "GetBackupWriter", fmt.Errorf("备份记录不能为空"), false)
	}
	if record.Path == "" {
		return nil, berrors.NewStorageError("INVALID_ARGUMENT", "GetBackupWriter", fmt.Errorf("备份记录路径不能为空"), false)
	}
	if filename == "" {
		return nil, berrors.NewStorageError("INVALID_ARGUMENT", "GetBackupWriter", fmt.Errorf("文件名不能为空"), false)
	}

	filePath := filepath.Join(record.Path, filename)
	key := m.getStorageKey(filePath)

	ctx := context.Background()
	writer, err := m.backend.NewWriter(ctx, key)
	if err != nil {
		return nil, berrors.NewStorageError("FILE_WRITE_ERROR", "GetBackupWriter", fmt.Errorf("创建写入器失败 %s: %w", filePath, err), false)
	}

	return writer, nil
}

// GetBackupReader 为 Provider 提供读取器，支持从指定的备份文件读取数据。
// 这个方法支持本地存储和云存储的统一抽象，Provider 可以透明地读取备份文件。
//
// 使用示例：
//
//	reader, err := manager.GetBackupReader(record, "data.sql.gz")
//	if err != nil { return err }
//	defer reader.Close()
//	// 直接读取数据...
func (m *Manager) GetBackupReader(record *types.BackupRecord, filename string) (io.ReadCloser, *types.BackupError) {
	// 参数验证
	if record == nil {
		return nil, berrors.NewStorageError("INVALID_ARGUMENT", "GetBackupReader", fmt.Errorf("备份记录不能为空"), false)
	}
	if record.Path == "" {
		return nil, berrors.NewStorageError("INVALID_ARGUMENT", "GetBackupReader", fmt.Errorf("备份记录路径不能为空"), false)
	}
	if filename == "" {
		return nil, berrors.NewStorageError("INVALID_ARGUMENT", "GetBackupReader", fmt.Errorf("文件名不能为空"), false)
	}

	filePath := filepath.Join(record.Path, filename)
	key := m.getStorageKey(filePath)

	ctx := context.Background()
	reader, err := m.backend.Get(ctx, key)
	if err != nil {
		if interfaces.IsNotFound(err) {
			return nil, berrors.NewStorageError("FILE_NOT_FOUND", "GetBackupReader", fmt.Errorf("备份文件不存在 %s", filePath), false)
		}
		return nil, berrors.NewStorageError("FILE_READ_ERROR", "GetBackupReader", fmt.Errorf("创建读取器失败 %s: %w", filePath, err), false)
	}

	return reader, nil
}

// GetBackend 返回底层的存储后端接口。
// 这个方法为需要直接访问 Backend 的场景提供支持。
func (m *Manager) GetBackend() interfaces.Backend {
	return m.backend
}

// --- 系统文件操作 (System File Operations) ---
// 阶段二新增：为TaskManager等系统组件提供文件操作支持

// WriteSystemFile 写入系统文件（如tasks.json）。
// 这个方法专门用于系统内部文件的写入，与备份数据分离。
//
// 参数：
//
//	filePath: 相对于备份根目录的文件路径
//	data: 要写入的数据
//
// 返回：
//
//	error: 写入失败时的错误
func (m *Manager) WriteSystemFile(filePath string, data []byte) error {
	key := m.getStorageKey(filePath)

	// 添加调试信息
	m.logger.Debug("WriteSystemFile调试",
		zap.String("filePath", filePath),
		zap.String("backupRoot", m.backupRoot),
		zap.String("key", key))

	ctx := context.Background()
	_, err := m.backend.Put(ctx, key, strings.NewReader(string(data)))
	if err != nil {
		return fmt.Errorf("写入系统文件 %s 失败: %w", filePath, err)
	}

	m.logger.Debug("系统文件写入成功",
		zap.String("path", filePath),
		zap.Int("size", len(data)))
	return nil
}

// ReadSystemFile 读取系统文件（如tasks.json）。
// 这个方法专门用于系统内部文件的读取。
//
// 参数：
//
//	filePath: 相对于备份根目录的文件路径
//
// 返回：
//
//	[]byte: 文件内容
//	error: 读取失败时的错误
func (m *Manager) ReadSystemFile(filePath string) ([]byte, error) {
	key := m.getStorageKey(filePath)

	ctx := context.Background()
	reader, err := m.backend.Get(ctx, key)
	if err != nil {
		if interfaces.IsNotFound(err) {
			return nil, os.ErrNotExist
		}
		return nil, fmt.Errorf("读取系统文件 %s 失败: %w", filePath, err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("读取系统文件内容失败 %s: %w", filePath, err)
	}

	m.logger.Debug("系统文件读取成功",
		zap.String("path", filePath),
		zap.Int("size", len(data)))
	return data, nil
}

// EnsureSystemDir 确保系统目录存在。
// 对于Backend接口，这通常是一个空操作，因为大多数存储后端会自动创建目录。
//
// 参数：
//
//	dirPath: 相对于备份根目录的目录路径
//
// 返回：
//
//	error: 创建失败时的错误
func (m *Manager) EnsureSystemDir(dirPath string) error {
	// 对于大多数Backend实现，目录会在写入文件时自动创建
	// 这里主要是为了保持接口的一致性
	m.logger.Debug("确保系统目录存在", zap.String("path", dirPath))
	return nil
}

// RenameSystemFile 重命名系统文件。
// 这个方法用于备份损坏的文件等场景。
//
// 参数：
//
//	oldPath: 原文件路径
//	newPath: 新文件路径
//
// 返回：
//
//	error: 重命名失败时的错误
func (m *Manager) RenameSystemFile(oldPath, newPath string) error {
	// 先读取原文件
	data, err := m.ReadSystemFile(oldPath)
	if err != nil {
		return fmt.Errorf("读取原文件失败: %w", err)
	}

	// 写入新文件
	if err := m.WriteSystemFile(newPath, data); err != nil {
		return fmt.Errorf("写入新文件失败: %w", err)
	}

	// 删除原文件
	oldKey := m.getStorageKey(oldPath)
	ctx := context.Background()
	if err := m.backend.Delete(ctx, oldKey); err != nil {
		// 删除失败不是致命错误，记录警告即可
		m.logger.Warn("删除原文件失败",
			zap.String("path", oldPath),
			zap.Error(err))
	}

	m.logger.Debug("系统文件重命名成功",
		zap.String("old_path", oldPath),
		zap.String("new_path", newPath))
	return nil
}

// --- 状态管理 (Status Management) ---

// SetDataSourceStatus 持久化存储数据源的状态。
// 例如，在恢复失败且无法自动回滚时，可将状态设置为 `LockedForManualIntervention`，以阻止后续操作。
// 阶段二改造：使用 Backend 接口替代直接文件操作
func (m *Manager) SetDataSourceStatus(source types.SourceType, status string) *types.BackupError {
	path := m.GetDataSourceStatusPath(source)
	key := m.getStorageKey(path)
	m.logger.Warn("设置数据源状态",
		zap.String("source", string(source)),
		zap.String("status", status),
		zap.String("path", path))

	ctx := context.Background()
	_, err := m.backend.Put(ctx, key, strings.NewReader(status))
	if err != nil {
		return berrors.NewStorageError("FILE_WRITE_ERROR", "SetDataSourceStatus", fmt.Errorf("写入状态文件 %s 失败: %w", path, err), false)
	}
	return nil
}

// GetDataSourceStatus [[memory:1940293]] 读取数据源的持久化状态。
// 如果状态文件不存在，默认为 "OK"。
// 阶段二改造：使用 Backend 接口替代直接文件操作
func (m *Manager) GetDataSourceStatus(source types.SourceType) (string, *types.BackupError) {
	path := m.GetDataSourceStatusPath(source)
	key := m.getStorageKey(path)

	ctx := context.Background()
	reader, err := m.backend.Get(ctx, key)
	if err != nil {
		if interfaces.IsNotFound(err) {
			return "OK", nil // 默认状态
		}
		return "", berrors.NewStorageError("FILE_READ_ERROR", "GetDataSourceStatus", fmt.Errorf("读取状态文件 %s 失败: %w", path, err), false)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return "", berrors.NewStorageError("FILE_READ_ERROR", "GetDataSourceStatus", fmt.Errorf("读取状态文件内容 %s 失败: %w", path, err), false)
	}
	return string(data), nil
}

// --- 核心逻辑 (Core Logic) ---

// CreateBackupRecord [[memory:1940293]] 是启动新备份的核心入口。
// 它负责生成ID，创建目录结构，并返回一个已初始化的备份记录供Provider填充和处理。
func (m *Manager) CreateBackupRecord(source types.SourceType, sourceName string, backupType types.BackupType, chainID, description string) (*types.BackupRecord, *types.BackupError) {
	// 1. 初始化备份记录的基本信息
	record := &types.BackupRecord{
		ID:          ulid.Make().String(),
		Timestamp:   time.Now(),
		Type:        backupType,
		Source:      source,
		SourceName:  sourceName,
		Status:      types.BackupStatusInProgress,
		Description: description,
	}

	// 2. 根据备份类型，确定其路径和链信息
	switch backupType {
	case types.BackupTypeArchival:
		record.Path = m.GetArchivalBackupPath(source, record.ID)
	case types.BackupTypeChainInitial:
		// 对于增量链的初始备份，其自身的ID就是链ID
		record.ChainID = record.ID
		record.Path = m.GetChainPath(source, record.ChainID, record.ID)
	case types.BackupTypeChainIncremental:
		if chainID == "" {
			return nil, berrors.NewStorageError("INVALID_ARGUMENT", "CreateBackupRecord", errors.New("增量备份需要提供chainID"), false)
		}
		// 找到链上的最后一个备份作为父备份
		parentRecord, err := m.GetLatestChainRecord(source, chainID)
		if err != nil {
			return nil, berrors.NewStorageError("CHAIN_METADATA_ERROR", "GetLatestChainRecord", fmt.Errorf("获取链 %s 的最新记录失败: %w", chainID, err), false)
		}
		record.ChainID = chainID
		record.ParentID = parentRecord.ID
		record.Path = m.GetChainPath(source, record.ChainID, record.ID)
	default:
		return nil, berrors.NewStorageError("UNKNOWN_BACKUP_TYPE", "CreateBackupRecord", fmt.Errorf("未知的备份类型: %s", backupType), false)
	}

	// 3. 为备份产物创建物理目录（阶段二改造：通过 Backend 接口）
	// 注意：Backend 接口会在需要时自动创建目录结构，这里不需要显式创建
	// 但我们仍需要确保目录路径的有效性
	if record.Path == "" {
		return nil, berrors.NewStorageError("INVALID_PATH", "CreateBackupRecord", fmt.Errorf("备份路径不能为空"), false)
	}

	// 4. 立即保存初始的元数据文件 (状态为 InProgress)
	if err := m.SaveBackupRecordInitial(record); err != nil {
		// 如果元数据保存失败，通过 Backend 清理（阶段二改造）
		key := m.getStorageKey(record.Path)
		ctx := context.Background()
		m.backend.Delete(ctx, key) // 尽力清理，忽略错误
		return nil, berrors.NewStorageError("METADATA_SAVE_ERROR", "SaveBackupRecordInitial", fmt.Errorf("保存初始备份记录失败: %w", err), false)
	}

	m.logger.Info("成功创建备份记录",
		zap.String("id", record.ID),
		zap.String("path", record.Path))
	return record, nil
}

// SaveBackupRecordAtomic 原子性地保存备份记录，确保元数据一致性
// 阶段二改造：使用已改造的方法实现原子性保存，简化复杂的临时文件逻辑
func (m *Manager) SaveBackupRecordAtomic(record *types.BackupRecord) *types.BackupError {
	// 0. 防御性检查 - 确保记录不为nil且包含必需字段
	if record == nil {
		return berrors.NewStorageError("INVALID_ARGUMENT", "SaveBackupRecordAtomic", errors.New("backup record cannot be nil"), false)
	}
	if record.Path == "" || record.Source == "" || record.ID == "" {
		return berrors.NewStorageError("INVALID_RECORD", "SaveBackupRecordAtomic",
			fmt.Errorf("backup record has empty required fields: Path=%s, Source=%s, ID=%s", record.Path, record.Source, record.ID), false)
	}

	// 1. 如果是链备份，先保存链元数据的备份用于回滚
	var originalChainMeta *types.ChainMeta
	if record.IsPartOfChain() {
		if record.ChainID == "" {
			return berrors.NewStorageError("INVALID_CHAIN_ID", "SaveBackupRecordAtomic", errors.New("chain backup record has empty ChainID"), false)
		}

		// 读取现有的链元数据作为备份
		chainMeta, err := m.ReadChainMeta(record.Source, record.ChainID)
		if err != nil {
			return berrors.NewStorageError("CHAIN_META_READ_ERROR", "SaveBackupRecordAtomic", fmt.Errorf("读取链元数据失败: %w", err), false)
		}

		// 保存原始链元数据用于回滚
		if chainMeta != nil {
			originalChainMeta = &types.ChainMeta{
				ChainID:   chainMeta.ChainID,
				BackupIDs: make([]string, len(chainMeta.BackupIDs)),
			}
			copy(originalChainMeta.BackupIDs, chainMeta.BackupIDs)
		}
	}

	// 2. 先保存备份记录（最终状态，不允许InProgress）
	if err := m.SaveBackupRecord(record); err != nil {
		return berrors.NewStorageError("BACKUP_RECORD_SAVE_ERROR", "SaveBackupRecordAtomic", fmt.Errorf("保存备份记录失败: %w", err), false)
	}

	// 3. 如果是链备份，更新链元数据（这会通过 AppendToChainMeta 自动处理）
	// SaveBackupRecord 已经会调用 AppendToChainMeta，所以这里不需要额外操作

	// 注意：由于 Backend 接口不直接支持事务，我们依赖于 Backend 实现的原子性
	// 对于本地文件系统，单个文件的写入是原子的
	// 对于云存储，通常也提供原子性保证

	return nil
}

// SaveBackupRecord [[memory:1940293]] 将一个备份记录的元数据保存到其 `metadata.json` 文件中。
// 这是备份流程中的一个关键步骤，通常在Provider完成其工作后调用。
func (m *Manager) SaveBackupRecord(record *types.BackupRecord) *types.BackupError {
	return m.saveBackupRecordInternal(record, false)
}

// SaveBackupRecordInitial 保存初始状态的备份记录（允许InProgress状态）
func (m *Manager) SaveBackupRecordInitial(record *types.BackupRecord) *types.BackupError {
	return m.saveBackupRecordInternal(record, true)
}

// saveBackupRecordInternal 内部实现，支持控制是否允许InProgress状态
func (m *Manager) saveBackupRecordInternal(record *types.BackupRecord, allowInProgress bool) *types.BackupError {
	// 阶段二改造：将文件路径转换为存储键（相对于备份根目录）
	key := m.getStorageKey(m.GetMetadataPath(record.Path))

	// 在Provider完成工作之前，备份记录的状态是 InProgress。
	// Provider完成工作后，上层调用者（如BackupManager）应将状态更新为 Completed 或 Failed，然后再调用此函数。
	// 这里加一个警告是为了防止意外地将一个"正在进行中"的状态持久化为最终状态。
	if record.Status == types.BackupStatusInProgress && !allowInProgress {
		m.logger.Warn("SaveBackupRecord被以'InProgress'状态调用。这通常应避免。调用者应设置一个最终状态。",
			zap.String("id", record.ID))
	}

	data, err := json.MarshalIndent(record, "", "  ")
	if err != nil {
		return berrors.NewStorageError("METADATA_SERIALIZE_ERROR", "SaveBackupRecord", fmt.Errorf("为 %s 序列化元数据失败: %w", record.ID, err), false)
	}

	// 阶段二改造：使用 Backend 接口替代直接文件操作
	ctx := context.Background()
	_, err = m.backend.Put(ctx, key, strings.NewReader(string(data)))
	if err != nil {
		// 将 Backend 错误映射到 StorageManager 错误
		return berrors.NewStorageError("FILE_WRITE_ERROR", "SaveBackupRecord", fmt.Errorf("写入元数据文件 %s 失败: %w", key, err), false)
	}

	// 在成功保存记录后，如果它是一个已完成的、属于增量链的备份，则更新链的聚合元数据。
	if record.IsPartOfChain() && record.Status == types.BackupStatusCompleted {
		return m.AppendToChainMeta(record)
	}

	return nil
}

// getStorageKey 将绝对文件路径转换为相对于备份根目录的存储键
// 这是阶段二改造的关键辅助方法，确保路径正确映射到 Backend 接口
func (m *Manager) getStorageKey(absolutePath string) string {
	// 移除备份根目录前缀，得到相对路径作为存储键
	if strings.HasPrefix(absolutePath, m.backupRoot) {
		// 移除根目录前缀和可能的路径分隔符
		key := strings.TrimPrefix(absolutePath, m.backupRoot)
		key = strings.TrimPrefix(key, string(filepath.Separator))
		return key
	}

	// 如果路径不在备份根目录下，尝试获取相对路径
	if relPath, err := filepath.Rel(m.backupRoot, absolutePath); err == nil && !strings.HasPrefix(relPath, "..") {
		return relPath
	}

	// 最后的回退：如果是绝对路径，只返回文件名
	if filepath.IsAbs(absolutePath) {
		return filepath.Base(absolutePath)
	}

	// 如果已经是相对路径，直接返回
	return absolutePath
}

// AppendToChainMeta [[memory:1940293]] 将一个新的备份记录ID追加到其对应增量链的元数据文件 (`chain_meta.json`) 中。
// 这个函数是事务性的：它读取、修改、然后写回整个文件，以确保一致性。
func (m *Manager) AppendToChainMeta(record *types.BackupRecord) *types.BackupError {
	if !record.IsPartOfChain() {
		m.logger.Warn("AppendToChainMeta被一个不属于任何链的记录调用，已跳过",
			zap.String("recordID", record.ID))
		return nil
	}

	m.logger.Info("正在向增量链追加新记录",
		zap.String("chainID", record.ChainID),
		zap.String("recordID", record.ID))

	// 读取现有的链元数据，如果不存在则创建一个新的。
	meta, err := m.ReadChainMeta(record.Source, record.ChainID)
	if err != nil {
		return berrors.NewStorageError("CHAIN_METADATA_ERROR", "ReadChainMeta", fmt.Errorf("为链 %s 读取元数据失败: %w", record.ChainID, err), false)
	}
	if meta == nil {
		// 这是链中的第一个记录
		if record.Type != types.BackupTypeChainInitial {
			return berrors.NewStorageError("INVALID_BACKUP_TYPE", "AppendToChainMeta", fmt.Errorf("不能为一个非初始备份记录 %s 创建新的链元数据", record.ID), false)
		}
		meta = &types.ChainMeta{
			ChainID:   record.ChainID,
			BackupIDs: []string{record.ID},
		}
	} else {
		// 追加到现有链
		// 检查重复，防止同一备份被添加多次。
		for _, id := range meta.BackupIDs {
			if id == record.ID {
				m.logger.Warn("记录已存在于链元数据中，无需重复添加",
					zap.String("recordID", record.ID),
					zap.String("chainID", record.ChainID))
				return nil
			}
		}
		// 追加新的备份ID。
		meta.BackupIDs = append(meta.BackupIDs, record.ID)
	}

	return m.WriteChainMeta(record.Source, record.ChainID, meta)
}

// GetLatestChainRecord 获取增量链中最新的一个备份记录。
// 这对于创建新的增量备份至关重要，因为它需要知道父备份的信息。
// 它现在使用 `chain_meta.json` 文件作为唯一可信的数据源，以确保准确性。
func (m *Manager) GetLatestChainRecord(source types.SourceType, chainID string) (*types.BackupRecord, *types.BackupError) {
	meta, err := m.ReadChainMeta(source, chainID)
	if err != nil {
		return nil, berrors.NewStorageError("CHAIN_METADATA_ERROR", "ReadChainMeta", fmt.Errorf("读取链元数据以查找链 %s 的最新记录失败: %w", chainID, err), false)
	}
	if meta == nil || len(meta.BackupIDs) == 0 {
		return nil, berrors.NewStorageError("CHAIN_NOT_FOUND", "GetLatestChainRecord", fmt.Errorf("在元数据中未找到链 %s 的任何记录", chainID), false)
	}

	latestID := meta.BackupIDs[len(meta.BackupIDs)-1]
	// 优化: 直接构建路径，避免遍历
	recordPath := m.GetChainPath(source, chainID, latestID)
	metadataPath := m.GetMetadataPath(recordPath)
	return m.ReadMetadata(metadataPath)
}

// DeleteBackupRecord 删除一个独立的归档备份记录及其物理文件。
// !! 这是一个包含关键安全检查的核心方法。
// 它只允许删除独立的"归档"类型备份。
// 删除增量备份必须通过 `DeleteIncrementalChain` 方法来删除整条链，以防破坏增量链的完整性。
func (m *Manager) DeleteBackupRecord(record *types.BackupRecord) *types.BackupError {
	// 关键安全检查：严格禁止删除任何属于增量链的单个备份点。
	// 这是为了保护增量链的完整性，防止因意外删除中间环节而导致整条链失效。
	if record.IsPartOfChain() {
		return berrors.NewStorageError("SECURITY_VIOLATION", "DeleteBackupRecord", fmt.Errorf("安全检查失败：不能删除属于受保护增量链 (chainID: %s) 的单个记录 (%s)。请使用 DeleteIncrementalChain 方法删除整条链", record.ChainID, record.ID), false)
	}

	// 双重检查：确保只有归档类型的备份可以被单独删除。
	if record.Type != types.BackupTypeArchival {
		return berrors.NewStorageError("INVALID_BACKUP_TYPE", "DeleteBackupRecord", fmt.Errorf("一致性错误：尝试单独删除一个非归档类型的记录: type %s, id %s", record.Type, record.ID), false)
	}

	m.logger.Warn("正在删除归档备份记录",
		zap.String("id", record.ID),
		zap.String("path", record.Path))

	// 阶段二改造：使用 Backend 接口替代直接文件操作
	key := m.getStorageKey(record.Path)
	ctx := context.Background()
	if err := m.backend.Delete(ctx, key); err != nil {
		// 将 Backend 错误映射到 StorageManager 错误
		return berrors.NewStorageError("FILE_DELETE_ERROR", "DeleteBackupRecord", fmt.Errorf("删除备份目录 %s 失败: %w", record.Path, err), false)
	}
	return nil
}

// DeleteIncrementalChain 原子地删除一整条增量链，包括其所有的备份点和元数据。
// 这是删除增量备份的唯一正确方式。
func (m *Manager) DeleteIncrementalChain(sourceType types.SourceType, chainID string) *types.BackupError {
	chainPath := filepath.Join(m.GetDataSourcePath(sourceType), ChainsDir, chainID)
	m.logger.Warn("正在删除整条增量链",
		zap.String("chainID", chainID),
		zap.String("path", chainPath))

	// 阶段二改造：Backend 接口会处理路径验证和存在性检查
	// 移除直接的文件系统检查，依赖 Backend 接口的错误处理
	// 确保要删除的目录名与chainID完全匹配。
	if filepath.Base(chainPath) != chainID {
		return berrors.NewStorageError("SECURITY_VIOLATION", "DeleteIncrementalChain", fmt.Errorf("安全检查失败：要删除的路径 (%s) 与 chainID (%s) 不匹配", chainPath, chainID), false)
	}

	// 阶段二改造：使用 Backend 接口替代直接文件操作
	key := m.getStorageKey(chainPath)
	ctx := context.Background()
	if err := m.backend.Delete(ctx, key); err != nil {
		return berrors.NewStorageError("DIRECTORY_DELETE_ERROR", "DeleteIncrementalChain", fmt.Errorf("删除链目录 %s 失败: %w", chainPath, err), false)
	}
	return nil
}

// ListArchivalBackups [[memory:1940293]] 列出指定数据源所有可用的、独立的归档备份。
// 阶段二改造：使用 Backend 接口替代直接文件系统操作
func (m *Manager) ListArchivalBackups(source types.SourceType) ([]types.ArchivalBackup, *types.BackupError) {
	archivalRoot := filepath.Join(m.GetDataSourcePath(source), ArchivalDir)
	// 阶段二改造：将目录路径转换为存储键前缀
	prefix := m.getStorageKey(archivalRoot)
	if prefix != "" {
		prefix = prefix + "/"
	}

	ctx := context.Background()
	objects, err := m.backend.List(ctx, prefix)
	if err != nil {
		// 如果归档目录不存在，这是正常情况，说明还没有归档备份
		if interfaces.IsNotFound(err) {
			return []types.ArchivalBackup{}, nil
		}
		return nil, berrors.NewStorageError("DIRECTORY_READ_ERROR", "ListArchivalBackups", fmt.Errorf("列出归档目录 %s 失败: %w", archivalRoot, err), false)
	}

	// 从对象列表中提取目录名（备份ID）
	backupDirs := make(map[string]bool)
	for _, obj := range objects {
		// 对象键格式类似：mysql/archival/backup-123/metadata.json
		// 我们需要提取备份目录名
		relativePath := strings.TrimPrefix(obj.Key, prefix)
		parts := strings.Split(relativePath, "/")
		if len(parts) >= 2 && parts[1] == MetadataFileName {
			backupDirs[parts[0]] = true
		}
	}

	archival := make([]types.ArchivalBackup, 0, len(backupDirs))
	for backupDir := range backupDirs {
		metadataPath := filepath.Join(archivalRoot, backupDir, MetadataFileName)
		record, readErr := m.ReadMetadata(metadataPath)
		if readErr != nil {
			// 如果某个备份的元数据损坏或无法读取，记录错误并跳过它，而不是让整个列表操作失败。
			m.logger.Error("读取或解析元数据失败，已跳过",
				zap.String("path", metadataPath),
				zap.String("error", readErr.Message))
			continue
		}
		archival = append(archival, types.ArchivalBackup{Record: record})
	}

	return archival, nil
}

// ListIncrementalChains 列出指定数据源实例所有可用的增量备份链。
// 阶段二改造：使用 Backend 接口替代直接文件系统操作
func (m *Manager) ListIncrementalChains(source types.SourceType, sourceName string) ([]types.IncrementalChain, *types.BackupError) {
	chainsPath := filepath.Join(m.GetDataSourcePath(source), ChainsDir)
	prefix := m.getStorageKey(chainsPath)
	if prefix != "" {
		prefix = prefix + "/"
	}

	ctx := context.Background()
	objects, err := m.backend.List(ctx, prefix)
	if err != nil {
		if interfaces.IsNotFound(err) {
			return []types.IncrementalChain{}, nil // 链目录不存在，说明没有增量链。
		}
		return nil, berrors.NewStorageError("DIRECTORY_READ_ERROR", "ListIncrementalChains", fmt.Errorf("无法读取链目录 %s: %w", chainsPath, err), false)
	}

	// 从对象列表中提取链目录名
	chainDirs := make(map[string]bool)
	for _, obj := range objects {
		relativePath := strings.TrimPrefix(obj.Key, prefix)
		parts := strings.Split(relativePath, "/")
		if len(parts) >= 1 && parts[0] != "" && !strings.HasPrefix(parts[0], ".") {
			chainDirs[parts[0]] = true
		}
	}

	var chains []types.IncrementalChain
	for chainDirName := range chainDirs {
		chainID := chainDirName
		meta, err := m.ReadChainMeta(source, chainID)
		if err != nil {
			m.logger.Error("读取链元数据失败，已跳过该链",
				zap.String("chainID", chainID),
				zap.String("error", err.Message))
			continue
		}
		if meta == nil {
			m.logger.Warn("链目录存在但 chain_meta.json 丢失或为空，已跳过",
				zap.String("chainID", chainID))
			continue
		}

		// 检查链中的第一个备份记录，确认它属于指定的sourceName
		if len(meta.BackupIDs) > 0 {
			firstBackupID := meta.BackupIDs[0]
			recordPath := m.GetChainPath(source, chainID, firstBackupID)
			metadataPath := m.GetMetadataPath(recordPath)
			firstRecord, err := m.ReadMetadata(metadataPath)
			if err != nil {
				m.logger.Error("读取链首个备份记录失败，已跳过该链",
					zap.String("chainID", chainID),
					zap.String("backupID", firstBackupID),
					zap.String("error", err.Message))
				continue
			}

			// 如果链的首个备份不属于指定的sourceName，跳过该链
			if firstRecord.SourceName != sourceName {
				continue
			}
		}

		chain := types.IncrementalChain{
			ChainID: meta.ChainID,
			Backups: make([]*types.BackupRecord, 0, len(meta.BackupIDs)),
		}

		chainValid := true
		for _, backupID := range meta.BackupIDs {
			// 优化: 直接构建路径，避免遍历
			recordPath := m.GetChainPath(source, chainID, backupID)
			metadataPath := m.GetMetadataPath(recordPath)
			record, err := m.ReadMetadata(metadataPath)
			if err != nil {
				m.logger.Error("从链中获取备份记录失败，链数据可能不一致",
					zap.String("backupID", backupID),
					zap.String("chainID", chainID),
					zap.String("error", err.Message))
				// 为安全起见，如果链中的某个记录丢失，则跳过整条链。
				chainValid = false
				break
			}
			chain.Backups = append(chain.Backups, record)
		}

		// 只有当链完全有效时才添加到结果中
		if chainValid && len(chain.Backups) > 0 {
			chains = append(chains, chain)
		}
	}

	return chains, nil
}

// GetIncrementalChainRecords 检索指定增量链的所有备份记录，并按时间顺序排列。
// 它现在使用 `chain_meta.json` 作为唯一可信信息源。
func (m *Manager) GetIncrementalChainRecords(source types.SourceType, chainID string) ([]*types.BackupRecord, *types.BackupError) {
	meta, err := m.ReadChainMeta(source, chainID)
	if err != nil {
		return nil, berrors.NewStorageError("CHAIN_METADATA_ERROR", "ReadChainMeta", fmt.Errorf("读取链 %s 的元数据以获取记录失败: %w", chainID, err), false)
	}
	if meta == nil {
		// 链元数据不存在，返回空记录
		return []*types.BackupRecord{}, nil
	}

	var records []*types.BackupRecord
	for _, backupID := range meta.BackupIDs {
		// 优化: 直接构建路径，避免遍历
		recordPath := m.GetChainPath(source, chainID, backupID)
		metadataPath := m.GetMetadataPath(recordPath)
		record, err := m.ReadMetadata(metadataPath)
		if err != nil {
			return nil, berrors.NewStorageError("RECORD_NOT_FOUND", "GetBackupRecord", fmt.Errorf("无法从链 %s 中检索记录 %s: %w", backupID, chainID, err), false)
		}
		records = append(records, record)
	}

	return records, nil
}

// GetRestorationChain 获取用于恢复的备份记录链。
// 它根据指定的backupID找到对应的备份记录。如果记录是增量链的一部分，
// 它将返回从链的起点到该记录的所有备份点，并按正确的恢复顺序排列。
// 注意：此方法会自动过滤掉失败的备份，只返回成功完成的备份记录。
func (m *Manager) GetRestorationChain(source types.SourceType, backupID string) ([]*types.BackupRecord, *types.BackupError) {
	// 首先查找目标备份记录以获取sourceName
	targetRecord, err := m.FindBackupRecord(source, backupID)
	if err != nil {
		return nil, err
	}

	// 如果是归档备份，直接返回
	if targetRecord.Type == types.BackupTypeArchival {
		return []*types.BackupRecord{targetRecord}, nil
	}

	// 如果是增量链备份，获取该sourceName的所有增量链
	chains, err := m.ListIncrementalChains(source, targetRecord.SourceName)
	if err != nil {
		return nil, err
	}

	for _, chain := range chains {
		var validBackups []*types.BackupRecord
		targetFound := false

		for _, record := range chain.Backups {
			// 检查备份是否成功完成
			if m.isBackupSuccessful(record) {
				validBackups = append(validBackups, record)
			} else {
				m.logger.Warn("恢复时跳过失败的备份记录",
					zap.String("backup_id", record.ID),
					zap.String("chain_id", record.ChainID),
					zap.String("source", string(source)))
			}

			// 检查是否找到目标备份
			if record.ID == backupID {
				targetFound = true
				break
			}
		}

		if targetFound {
			if len(validBackups) == 0 {
				return nil, berrors.NewStorageError("NO_VALID_BACKUPS", "GetRestorationChain",
					fmt.Errorf("目标备份 %s 之前没有有效的备份可用于恢复", backupID), false)
			}
			return validBackups, nil
		}
	}

	// 如果在增量链中没找到，则在归档备份中查找
	archivals, err := m.ListArchivalBackups(source)
	if err != nil {
		return nil, err
	}
	for _, archival := range archivals {
		if archival.Record.ID == backupID {
			return []*types.BackupRecord{archival.Record}, nil
		}
	}

	return nil, berrors.NewStorageError("BACKUP_NOT_FOUND", "GetRestorationChain", fmt.Errorf("未找到ID为 %s 的备份（数据源: %s）", backupID, source), false)
}

// isBackupSuccessful 检查备份是否成功完成
// 通过检查备份记录的状态和相关文件的存在性来判断
func (m *Manager) isBackupSuccessful(record *types.BackupRecord) bool {
	// 方法1: 检查备份记录的状态字段
	if record.Status == types.BackupStatusCompleted {
		return true
	}

	// 方法2: 对于没有明确状态的记录，通过其他方式判断
	// 检查元数据文件是否完整存在
	if record.Path == "" {
		m.logger.Debug("备份记录缺少路径信息，认为失败",
			zap.String("backup_id", record.ID))
		return false
	}

	// 检查元数据文件是否存在
	metadataPath := m.GetMetadataPath(record.Path)
	key := m.getStorageKey(metadataPath)
	ctx := context.Background()

	_, err := m.backend.Get(ctx, key)
	if err != nil {
		if interfaces.IsNotFound(err) {
			m.logger.Debug("备份元数据文件不存在，认为失败",
				zap.String("backup_id", record.ID),
				zap.String("metadata_path", metadataPath))
			return false
		}
		// 其他错误也认为备份失败
		m.logger.Debug("检查备份元数据时出错，认为失败",
			zap.String("backup_id", record.ID),
			zap.Error(err))
		return false
	}

	// 如果元数据文件存在，认为备份成功
	return true
}

// FindBackupRecord 在所有备份中（包括归档和所有增量链）查找指定的backupID。
// 这是一个辅助函数，用于在不清楚备份类型的情况下定位一个备份。
func (m *Manager) FindBackupRecord(source types.SourceType, backupID string) (*types.BackupRecord, *types.BackupError) {
	// 目前，此逻辑与GetBackupRecord相同。
	// 我们保留此方法是为了将来可能添加更复杂的查找逻辑（例如按标签、按日期查找）。
	return m.GetBackupRecord(source, backupID)
}

// FindBackupRecordAcrossTypes 跨所有已知的数据源类型查找并返回一个备份记录。
func (m *Manager) FindBackupRecordAcrossTypes(backupID string) (*types.BackupRecord, *types.BackupError) {
	// 遍历所有在配置中定义的源类型
	for _, sourceType := range m.cfg.GetAllSourceTypes() {
		if record, err := m.FindBackupRecord(sourceType, backupID); err == nil {
			// 找到即返回
			return record, nil
		}
	}
	return nil, berrors.NewStorageError("BACKUP_NOT_FOUND", "FindBackupRecordAcrossTypes", fmt.Errorf("在所有数据源中都未找到备份ID '%s'", backupID), false)
}

// GetBackupRecord [[memory:1940293]] 通过ID从存储中获取单个备份记录的元数据。
// 阶段二改造：使用 Backend 接口替代文件系统遍历
func (m *Manager) GetBackupRecord(sourceType types.SourceType, id string) (*types.BackupRecord, *types.BackupError) {
	// 我们需要搜索记录，因为仅凭ID我们无法获知完整路径。
	sourcePath := m.GetDataSourcePath(sourceType)
	prefix := m.getStorageKey(sourcePath)
	if prefix != "" {
		prefix = prefix + "/"
	}

	ctx := context.Background()
	objects, err := m.backend.List(ctx, prefix)
	if err != nil {
		if interfaces.IsNotFound(err) {
			return nil, berrors.NewStorageError("BACKUP_NOT_FOUND", "GetBackupRecord", fmt.Errorf("未找到ID为 %s 的备份记录（数据源: %s）", id, sourceType), false)
		}
		return nil, berrors.NewStorageError("FILE_SYSTEM_ERROR", "GetBackupRecord", fmt.Errorf("搜索备份记录 %s 时出错: %w", id, err), false)
	}

	// 在对象列表中查找包含指定ID的metadata.json文件
	var foundRecord *types.BackupRecord
	for _, obj := range objects {
		// 检查对象键是否是metadata.json文件
		if strings.HasSuffix(obj.Key, "/"+MetadataFileName) {
			// 直接尝试读取这个metadata文件并检查ID
			reader, readErr := m.backend.Get(ctx, obj.Key)
			if readErr != nil {
				continue // 跳过无法读取的文件
			}

			data, readErr := io.ReadAll(reader)
			reader.Close()
			if readErr != nil {
				continue // 跳过无法读取的文件
			}

			var record types.BackupRecord
			if json.Unmarshal(data, &record) == nil && record.ID == id {
				foundRecord = &record
				break // 找到匹配的记录
			}
		}
	}

	if foundRecord == nil {
		return nil, berrors.NewStorageError("BACKUP_NOT_FOUND", "GetBackupRecord", fmt.Errorf("未找到ID为 %s 的备份记录（数据源: %s）", id, sourceType), false)
	}

	return foundRecord, nil
}

// GetChainBackupRecord 通过提供chainID和backupID，精确查找增量链中的单个备份记录。
func (m *Manager) GetChainBackupRecord(sourceType types.SourceType, chainID, backupID string) (*types.BackupRecord, *types.BackupError) {
	// 直接构建路径，避免遍历
	recordPath := m.GetChainPath(sourceType, chainID, backupID)
	metadataPath := m.GetMetadataPath(recordPath)
	return m.ReadMetadata(metadataPath)
}

// CheckDataSourceHealth 检查数据源的健康状态。
// 此方法现在只负责本地状态检查，并将远程服务连接检查委托给Provider。
func (m *Manager) CheckDataSourceHealth(source types.SourceType, providers map[types.SourceType]provider.BackupProvider) *types.BackupError {
	// 1. 本地锁定状态检查
	status, err := m.GetDataSourceStatus(source)
	if err != nil {
		return berrors.NewStorageError("STATUS_READ_ERROR", "GetDataSourceStatus", fmt.Errorf("读取本地状态失败: %w", err), false)
	}
	if status != "" && status != "OK" {
		return berrors.NewStorageError("DATA_SOURCE_LOCKED", "CheckDataSourceHealth", fmt.Errorf("数据源处于锁定或异常状态（%s），操作被拒绝", status), false)
	}

	// 2. 远程服务健康检查（委托给Provider）
	p, ok := providers[source]
	if !ok {
		return berrors.NewStorageError("PROVIDER_NOT_FOUND", "CheckDataSourceHealth", fmt.Errorf("未找到数据源类型 %s 的提供者", source), false)
	}
	// 使用 context.Background() 因为这里是同步检查，不应受外部任务上下文取消的影响
	if healthErr := p.CheckHealth(context.Background()); healthErr != nil {
		return healthErr // 直接返回Provider的错误
	}

	return nil
}

// === 统一查询接口实现 ===

// ListAllBackups 基于tasks.json实现统一的任务查询接口。
// 默认查询所有任务类型，按开始时间倒序排列（最新的在前）。
func (m *Manager) ListAllBackups(filter types.BackupFilter) (*types.BackupListResult, error) {
	// 应用默认值
	filter = m.applyFilterDefaults(filter)

	// 1. 从taskManager获取所有任务
	allTasks, err := m.taskManager.ListTasks()
	if err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	// 2. 应用过滤条件
	var filteredTasks []*types.Task
	for _, task := range allTasks {
		if m.matchesNewFilter(task, filter) {
			filteredTasks = append(filteredTasks, task)
		}
	}

	// 3. 搜索功能（对任务描述进行模糊搜索）
	if filter.SearchText != "" {
		var searchedTasks []*types.Task
		for _, task := range filteredTasks {
			if strings.Contains(strings.ToLower(task.Description), strings.ToLower(filter.SearchText)) {
				searchedTasks = append(searchedTasks, task)
			}
		}
		filteredTasks = searchedTasks
	}

	// 4. 按开始时间排序（最新的在前）
	sort.Slice(filteredTasks, func(i, j int) bool {
		return filteredTasks[i].StartTime.UnixNano() > filteredTasks[j].StartTime.UnixNano()
	})

	// 5. 分页处理
	total := len(filteredTasks)
	start := filter.Offset
	if start > total {
		start = total
	}

	end := start + filter.Limit
	if end > total {
		end = total
	}

	pagedTasks := filteredTasks[start:end]
	hasMore := end < total

	return &types.BackupListResult{
		Tasks:   pagedTasks,
		Total:   total,
		HasMore: hasMore,
		Filter:  filter, // 回显实际使用的过滤条件
	}, nil
}

// applyFilterDefaults 应用过滤器的默认值
func (m *Manager) applyFilterDefaults(filter types.BackupFilter) types.BackupFilter {
	// 默认分页大小
	if filter.Limit <= 0 {
		filter.Limit = 50 // 默认50条
	}
	if filter.Limit > 1000 {
		filter.Limit = 1000 // 最大1000条
	}

	// 默认时间范围：如果没有指定时间范围，查询最近30天
	if filter.StartTime == nil && filter.EndTime == nil {
		defaultStart := time.Now().AddDate(0, 0, -30)
		filter.StartTime = &defaultStart
	}

	return filter
}

// matchesNewFilter 检查任务是否匹配新的过滤条件（AND逻辑）
func (m *Manager) matchesNewFilter(task *types.Task, filter types.BackupFilter) bool {
	// 1. 任务类型过滤
	if len(filter.TaskTypes) > 0 {
		found := false
		for _, taskType := range filter.TaskTypes {
			if task.Type == taskType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	// 如果没有指定TaskTypes，默认查询所有任务类型

	// 2. 数据源类型过滤
	if len(filter.SourceTypes) > 0 {
		found := false
		for _, sourceType := range filter.SourceTypes {
			if task.Source == sourceType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 3. 备份类型过滤
	if len(filter.BackupTypes) > 0 {
		if backupType, ok := task.Metadata["backup_type"].(string); ok {
			found := false
			for _, bt := range filter.BackupTypes {
				if types.BackupType(backupType) == bt {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		} else {
			return false // 如果没有backup_type信息，不匹配
		}
	}

	// 4. 任务状态过滤
	if len(filter.Statuses) > 0 {
		found := false
		for _, status := range filter.Statuses {
			if task.Status == status {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 5. 时间范围过滤
	if filter.StartTime != nil && task.StartTime.Before(*filter.StartTime) {
		return false
	}
	if filter.EndTime != nil && task.StartTime.After(*filter.EndTime) {
		return false
	}

	return true
}

// GetBackupDetails 获取备份任务的详细信息，支持分组备份的递归显示。
// 按需读取metadata.json，验证备份文件的存在性。
func (m *Manager) GetBackupDetails(taskID string) (*types.Task, error) {
	// 1. 从taskManager获取任务基本信息
	task, err := m.taskManager.GetTask(taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务失败: %w", err)
	}

	// 2. 验证是否为备份任务
	if task.Type != types.BackupTask && task.Type != types.BackupAllTask {
		return nil, fmt.Errorf("任务ID %s 不是备份任务，类型为: %s", taskID, task.Type)
	}

	// 3. 验证任务状态
	if task.Status != types.TaskStatusCompleted {
		// 对于未完成的任务，直接返回任务信息，不需要验证备份文件
		return task, nil
	}

	// 4. 处理分组备份的递归显示
	if task.Type == types.BackupAllTask {
		return m.getGroupBackupDetails(task)
	}

	// 5. 处理单个备份任务
	return m.getSingleBackupDetails(task)
}

// getSingleBackupDetails 处理单个备份任务的详情获取
func (m *Manager) getSingleBackupDetails(task *types.Task) (*types.Task, error) {
	// 验证backup_record_id是否存在
	backupRecordID, ok := task.Metadata["backup_record_id"].(string)
	if !ok || backupRecordID == "" {
		return task, nil // 如果没有backup_record_id，直接返回任务信息
	}

	// 验证对应的metadata.json是否存在
	_, err := m.GetBackupRecord(task.Source, backupRecordID)
	if err != nil {
		// 如果metadata.json不存在，说明备份文件可能已损坏，删除任务记录
		m.logger.Warn("备份记录不存在，可能需要清理任务记录",
			zap.String("task_id", task.ID),
			zap.String("backup_record_id", backupRecordID),
			zap.String("error", err.Error()))

		// 注意：这里不直接删除任务记录，而是在任务信息中标记问题
		// 实际的清理操作应该由专门的清理任务来处理
		if task.Metadata == nil {
			task.Metadata = make(map[string]interface{})
		}
		task.Metadata["backup_file_missing"] = true
		task.Metadata["backup_file_error"] = err.Error()
	}

	return task, nil
}

// getGroupBackupDetails 处理分组备份任务的详情获取，递归显示所有子任务
func (m *Manager) getGroupBackupDetails(mainTask *types.Task) (*types.Task, error) {
	// 创建主任务的副本，避免修改原始任务
	result := *mainTask
	result.Metadata = make(map[string]interface{})

	// 复制原有的Metadata
	for k, v := range mainTask.Metadata {
		result.Metadata[k] = v
	}

	// 获取所有子任务的详细信息
	var subTaskDetails []*types.Task
	var validSubTasks int
	var totalSize int64

	for _, subTaskID := range mainTask.SubTaskIDs {
		subTask, err := m.taskManager.GetTask(subTaskID)
		if err != nil {
			m.logger.Warn("获取子任务失败",
				zap.String("sub_task_id", subTaskID),
				zap.Error(err))
			continue
		}

		// 获取子任务的详细信息
		subTaskDetail, err := m.getSingleBackupDetails(subTask)
		if err != nil {
			m.logger.Warn("获取子任务详情失败",
				zap.String("sub_task_id", subTaskID),
				zap.Error(err))
			subTaskDetails = append(subTaskDetails, subTask) // 添加原始任务信息
		} else {
			subTaskDetails = append(subTaskDetails, subTaskDetail)
		}

		// 统计有效的子任务和总大小
		if subTask.Status == types.TaskStatusCompleted {
			validSubTasks++

			// 尝试从备份记录中获取实际大小
			if backupRecordID, ok := subTask.Metadata["backup_record_id"].(string); ok {
				if record, err := m.GetBackupRecord(subTask.Source, backupRecordID); err == nil {
					totalSize += record.Size
				}
			}
		}
	}

	// 更新汇总信息
	result.Metadata["sub_tasks"] = subTaskDetails
	result.Metadata["valid_sub_tasks"] = validSubTasks
	result.Metadata["total_size"] = types.FormatBackupSize(totalSize)

	return &result, nil
}

// DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型。
// 这是一个适配器方法，将基于taskID的删除请求转换为现有删除方法的调用。
//
// 重要说明：
// - 归档备份：直接删除单个备份
// - 增量备份：删除整条增量链（保持数据完整性）
// - 分组备份：删除所有子任务的备份数据
//
// 注意：对于增量备份，此方法会删除整条增量链，而不是单个增量备份点。
// 这是为了保持增量链的完整性，避免破坏链的连续性。
// SDK调用方应该在UI层面向用户明确说明这一行为，并提供相应的确认机制。
func (m *Manager) DeleteBackupByTaskID(taskID string) error {
	// 1. 获取任务信息，判断类型
	task, err := m.taskManager.GetTask(taskID)
	if err != nil {
		return fmt.Errorf("获取任务失败: %w", err)
	}

	// 2. 验证是否为备份任务
	if task.Type != types.BackupTask && task.Type != types.BackupAllTask {
		return fmt.Errorf("任务ID %s 不是备份任务，类型为: %s", taskID, task.Type)
	}

	// 3. 验证任务状态
	if task.Status != types.TaskStatusCompleted {
		return fmt.Errorf("只能删除已完成的备份任务，当前状态: %s", task.Status)
	}

	// 4. 根据任务类型执行不同的删除逻辑
	if task.Type == types.BackupAllTask {
		return m.deleteGroupBackupByTask(task)
	}

	return m.deleteSingleBackupByTask(task)
}

// deleteSingleBackupByTask 删除单个备份任务，复用现有的删除逻辑
func (m *Manager) deleteSingleBackupByTask(task *types.Task) error {
	// 1. 获取backup_record_id
	backupRecordID, ok := task.Metadata["backup_record_id"].(string)
	if !ok || backupRecordID == "" {
		// 如果没有backup_record_id，说明备份可能没有完成，直接删除任务记录
		return m.deleteTaskRecord(task.ID)
	}

	// 2. 复用现有的删除逻辑（DeleteBackupRecord方法）
	// 先获取备份记录
	record, err := m.GetBackupRecord(task.Source, backupRecordID)
	if err != nil {
		// 如果备份记录不存在，可能已经被删除，直接删除任务记录
		m.logger.Warn("备份记录不存在，直接删除任务记录",
			zap.String("task_id", task.ID),
			zap.String("backup_record_id", backupRecordID),
			zap.String("error", err.Error()))
		return m.deleteTaskRecord(task.ID)
	}

	// 3. 使用现有的删除方法
	if record.IsPartOfChain() {
		// 增量链备份：删除整条链
		if berr := m.DeleteIncrementalChain(task.Source, record.ChainID); berr != nil {
			return fmt.Errorf("删除增量链失败: %s", berr.Message)
		}
	} else {
		// 归档备份：直接删除
		if berr := m.DeleteBackupRecord(record); berr != nil {
			return fmt.Errorf("删除备份记录失败: %s", berr.Message)
		}
	}

	// 4. 删除任务记录
	return m.deleteTaskRecord(task.ID)
}

// deleteGroupBackupByTask 删除分组备份任务，复用现有的删除逻辑
func (m *Manager) deleteGroupBackupByTask(mainTask *types.Task) error {
	// 1. 收集所有子任务的备份ID
	var failedDeletions []string

	for _, subTaskID := range mainTask.SubTaskIDs {
		subTask, err := m.taskManager.GetTask(subTaskID)
		if err != nil {
			m.logger.Warn("获取子任务失败，跳过",
				zap.String("sub_task_id", subTaskID),
				zap.Error(err))
			continue
		}

		// 获取子任务的backup_record_id
		if backupRecordID, ok := subTask.Metadata["backup_record_id"].(string); ok && backupRecordID != "" {
			// 复用现有的删除逻辑
			err := m.deleteSingleBackupByTask(subTask)
			if err != nil {
				m.logger.Error("删除子任务备份失败",
					zap.String("sub_task_id", subTaskID),
					zap.String("backup_id", backupRecordID),
					zap.Error(err))
				failedDeletions = append(failedDeletions, fmt.Sprintf("子任务%s: %v", subTaskID, err))
			} else {
				m.logger.Info("成功删除子任务备份",
					zap.String("sub_task_id", subTaskID),
					zap.String("backup_id", backupRecordID))
			}
		} else {
			// 没有backup_record_id，直接删除任务记录
			if err := m.deleteTaskRecord(subTaskID); err != nil {
				m.logger.Error("删除子任务记录失败",
					zap.String("sub_task_id", subTaskID),
					zap.Error(err))
				failedDeletions = append(failedDeletions, fmt.Sprintf("子任务记录%s: %v", subTaskID, err))
			}
		}
	}

	// 2. 删除主任务记录
	if err := m.deleteTaskRecord(mainTask.ID); err != nil {
		failedDeletions = append(failedDeletions, fmt.Sprintf("主任务记录: %v", err))
	}

	// 3. 如果有删除失败的情况，返回汇总错误
	if len(failedDeletions) > 0 {
		return fmt.Errorf("分组备份删除部分失败: %s", strings.Join(failedDeletions, "; "))
	}

	m.logger.Info("成功删除分组备份",
		zap.String("main_task_id", mainTask.ID),
		zap.Int("sub_task_count", len(mainTask.SubTaskIDs)))
	return nil
}

// deleteTaskRecord 删除任务记录
func (m *Manager) deleteTaskRecord(taskID string) error {
	err := m.taskManager.DeleteTask(taskID)
	if err != nil {
		return fmt.Errorf("删除任务记录失败: %w", err)
	}
	m.logger.Info("成功删除任务记录", zap.String("task_id", taskID))
	return nil
}

// GetBackupDeletionInfo 获取备份删除的影响信息，帮助SDK调用方向用户展示删除影响。
// 返回删除操作的详细信息，包括是否为增量链、影响范围等。
func (m *Manager) GetBackupDeletionInfo(taskID string) (*types.BackupDeletionInfo, error) {
	// 1. 获取任务信息
	task, err := m.taskManager.GetTask(taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务失败: %w", err)
	}

	// 2. 验证是否为备份任务
	if task.Type != types.BackupTask && task.Type != types.BackupAllTask {
		return nil, fmt.Errorf("任务ID %s 不是备份任务，类型为: %s", taskID, task.Type)
	}

	info := &types.BackupDeletionInfo{
		TaskID:     taskID,
		TaskType:   task.Type,
		SourceType: task.Source,
		Status:     task.Status,
	}

	// 3. 处理分组备份
	if task.Type == types.BackupAllTask {
		info.IsGroupBackup = true
		info.SubTaskCount = len(task.SubTaskIDs)

		// 分析子任务
		for _, subTaskID := range task.SubTaskIDs {
			subTask, err := m.taskManager.GetTask(subTaskID)
			if err != nil {
				continue
			}

			if backupRecordID, ok := subTask.Metadata["backup_record_id"].(string); ok && backupRecordID != "" {
				subInfo, err := m.getBackupRecordDeletionInfo(subTask.Source, backupRecordID)
				if err == nil {
					info.SubBackups = append(info.SubBackups, *subInfo)
				}
			}
		}
		return info, nil
	}

	// 4. 处理单个备份
	backupRecordID, ok := task.Metadata["backup_record_id"].(string)
	if !ok || backupRecordID == "" {
		info.HasBackupData = false
		return info, nil
	}

	backupInfo, err := m.getBackupRecordDeletionInfo(task.Source, backupRecordID)
	if err != nil {
		info.HasBackupData = false
		info.Error = err.Error()
		return info, nil
	}

	info.BackupRecordDeletionInfo = *backupInfo
	return info, nil
}

// getBackupRecordDeletionInfo 获取单个备份记录的删除信息
func (m *Manager) getBackupRecordDeletionInfo(sourceType types.SourceType, backupRecordID string) (*types.BackupRecordDeletionInfo, error) {
	record, err := m.GetBackupRecord(sourceType, backupRecordID)
	if err != nil {
		return nil, err
	}

	info := &types.BackupRecordDeletionInfo{
		BackupID:      record.ID,
		BackupType:    record.Type,
		HasBackupData: true,
		Timestamp:     record.Timestamp,
	}

	// 检查是否为增量链
	if record.IsPartOfChain() {
		info.IsIncrementalChain = true
		info.ChainID = record.ChainID

		// 获取链中的所有备份
		chainRecords, err := m.GetIncrementalChainRecords(sourceType, record.ChainID)
		if err == nil {
			info.ChainBackupCount = len(chainRecords)
			if len(chainRecords) > 0 {
				info.ChainStartTime = chainRecords[0].Timestamp
				info.ChainEndTime = chainRecords[len(chainRecords)-1].Timestamp
			}
		}
	}

	return info, nil
}

// HasAnyBackups 检查指定数据源是否存在任何备份记录
// 用于增量备份的智能处理：如果没有任何备份，则需要先执行初始备份
func (m *Manager) HasAnyBackups(sourceType types.SourceType, sourceName string) (bool, error) {
	sourceKey := fmt.Sprintf("%s-%s", sourceType, sourceName)

	// 构建数据源目录路径前缀 - 使用实际的存储路径结构
	sourcePath := m.GetDataSourcePath(sourceType)
	prefix := m.getStorageKey(sourcePath)
	if prefix != "" {
		prefix = prefix + "/"
	}

	// 列出数据源目录下的内容
	objects, err := m.backend.List(context.Background(), prefix)
	if err != nil {
		// 如果目录不存在，说明没有备份历史
		if interfaces.IsNotFound(err) {
			m.logger.Debug("数据源目录不存在，无备份历史",
				zap.String("source", sourceKey),
				zap.String("prefix", prefix))
			return false, nil
		}
		return false, fmt.Errorf("列出数据源目录内容失败: %w", err)
	}

	// 检查是否存在属于指定sourceName的备份记录
	// 由于备份按sourceType存储，需要检查metadata.json中的SourceName字段
	for _, obj := range objects {
		// 检查是否是metadata.json文件
		if strings.HasSuffix(obj.Key, "/metadata.json") {
			// 读取metadata.json并检查SourceName
			ctx := context.Background()
			reader, err := m.backend.Get(ctx, obj.Key)
			if err != nil {
				continue // 跳过无法读取的文件
			}

			data, err := io.ReadAll(reader)
			reader.Close()
			if err != nil {
				continue // 跳过无法读取的文件
			}

			var record types.BackupRecord
			if err := json.Unmarshal(data, &record); err != nil {
				continue // 跳过无法解析的文件
			}

			// 检查是否匹配指定的sourceName
			if record.SourceName == sourceName {
				m.logger.Debug("发现匹配的备份记录，存在备份历史",
					zap.String("source", sourceKey),
					zap.String("backup_id", record.ID))
				return true, nil
			}
		}
	}

	m.logger.Debug("未找到匹配的备份记录，无备份历史",
		zap.String("source", sourceKey))
	return false, nil
}
