// Package local 实现了基于本地文件系统的存储后端。
//
// LocalBackend 将现有的本地文件操作封装为统一的 Backend 接口实现，
// 为云存储集成方案提供本地存储支持。它严格遵循以下设计原则：
//
//  1. **保持兼容性**: 与现有 StorageManager 的文件操作完全兼容
//  2. **职责清晰**: 专注于文件系统 I/O 操作，不处理业务逻辑
//  3. **简洁实现**: 避免过度设计，使用标准库的直接实现
//  4. **错误统一**: 使用项目统一的错误处理体系
//
// 支持的存储场景：
//   - MySQL 备份文件 (data.sql.gz, binlog.sql.gz)
//   - 元数据文件 (metadata.json, chain_meta.json)
//   - 任务记录文件 (tasks.json)
//   - 锁文件和状态文件 (status.json)
//   - 目录遍历和列表操作
package local

import (
	"context"
	"fmt"
	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"io"
	"os"
	"path/filepath"
	"strings"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// LocalBackend 实现了基于本地文件系统的存储后端。
// 它封装了所有本地文件操作，提供与云存储一致的接口。
type LocalBackend struct {
	// basePath 是所有存储操作的根路径
	// 通常对应于现有的 BackupRoot 配置
	basePath string
}

// 编译时接口兼容性检查
// 确保 LocalBackend 完整实现了 Backend 接口
var _ interfaces.Backend = (*LocalBackend)(nil)

// NewLocalBackend 创建一个新的本地存储后端实例。
//
// 参数：
//
//	basePath: 存储根路径，必须是绝对路径且具有读写权限
//
// 返回：
//
//	*LocalBackend: 本地存储后端实例
//	error: 创建失败时的错误信息
//
// 该函数会验证路径的有效性并确保目录存在。
func NewLocalBackend(basePath string) (*LocalBackend, error) {
	if basePath == "" {
		return nil, fmt.Errorf("basePath 不能为空")
	}

	// 将路径转换为绝对路径，便于后续操作
	absPath, err := filepath.Abs(basePath)
	if err != nil {
		return nil, fmt.Errorf("无法解析绝对路径 %s: %w", basePath, err)
	}

	// 确保基础路径存在，使用与现有 StorageManager 相同的权限设置
	if err := os.MkdirAll(absPath, 0755); err != nil {
		return nil, fmt.Errorf("创建基础路径失败 %s: %w", absPath, err)
	}

	// 验证路径的读写权限
	if err := validatePathPermissions(absPath); err != nil {
		return nil, fmt.Errorf("路径权限验证失败 %s: %w", absPath, err)
	}

	return &LocalBackend{
		basePath: absPath,
	}, nil
}

// Put 将数据流存储到指定的 key。
// 实现与现有 StorageManager 的文件写入操作兼容。
func (b *LocalBackend) Put(ctx context.Context, key string, data io.Reader) (int64, error) {
	if err := b.validateKey(key); err != nil {
		return 0, err
	}

	// 构建完整的文件路径
	fullPath := filepath.Join(b.basePath, key)

	// 确保父目录存在，使用与现有代码相同的权限
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		if os.IsPermission(err) {
			return 0, interfaces.ErrPermissionDenied
		}
		return 0, fmt.Errorf("创建目录失败 %s: %w", dir, err)
	}

	// 创建时直接设置ES权限：如果是Elasticsearch相关目录，直接设置正确权限
	if b.isElasticsearchPath(dir) {
		b.setElasticsearchPermissions(dir) // 静默执行，不处理错误
	}

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return 0, ctx.Err()
	default:
	}

	// 创建文件，使用与现有代码相同的权限设置
	file, err := os.Create(fullPath)
	if err != nil {
		if os.IsPermission(err) {
			return 0, interfaces.ErrPermissionDenied
		}
		return 0, fmt.Errorf("创建文件失败 %s: %w", fullPath, err)
	}
	defer file.Close()

	// 复制数据，支持上下文取消
	written, err := b.copyWithContext(ctx, file, data)
	if err != nil {
		// 如果写入失败，清理部分写入的文件
		os.Remove(fullPath)
		return 0, fmt.Errorf("写入数据失败 %s: %w", fullPath, err)
	}

	return written, nil
}

// NewWriter 创建一个流式写入器，用于直接写入数据到指定的 key。
// 这个方法为 Provider 提供流式写入支持，避免大文件的内存占用。
func (b *LocalBackend) NewWriter(ctx context.Context, key string) (io.WriteCloser, error) {
	if err := b.validateKey(key); err != nil {
		return nil, err
	}

	// 构建完整的文件路径
	fullPath := filepath.Join(b.basePath, key)

	// 确保父目录存在，使用与现有代码相同的权限
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		if os.IsPermission(err) {
			return nil, interfaces.ErrPermissionDenied
		}
		return nil, fmt.Errorf("创建目录失败 %s: %w", dir, err)
	}

	// 创建时直接设置ES权限：如果是Elasticsearch相关目录，直接设置正确权限
	if b.isElasticsearchPath(dir) {
		b.setElasticsearchPermissions(dir) // 静默执行，不处理错误
	}

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 创建文件写入器，使用与现有代码相同的权限设置
	file, err := os.Create(fullPath)
	if err != nil {
		if os.IsPermission(err) {
			return nil, interfaces.ErrPermissionDenied
		}
		return nil, fmt.Errorf("创建文件失败 %s: %w", fullPath, err)
	}

	// 返回文件句柄，调用者负责关闭
	return file, nil
}

// Get 从指定的 key 获取数据流。
// 如果文件不存在，返回 interfaces.ErrObjectNotFound 哨兵错误。
func (b *LocalBackend) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	if err := b.validateKey(key); err != nil {
		return nil, err
	}

	fullPath := filepath.Join(b.basePath, key)

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	file, err := os.Open(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, interfaces.ErrObjectNotFound
		}
		if os.IsPermission(err) {
			return nil, interfaces.ErrPermissionDenied
		}
		return nil, fmt.Errorf("打开文件失败 %s: %w", fullPath, err)
	}

	// 返回文件句柄，调用者负责关闭
	return file, nil
}

// Delete 删除指定的 key。
// 支持删除文件和目录，与现有 StorageManager 的 DeleteBackupRecord 操作兼容。
// 使用 os.RemoveAll 以支持递归删除目录结构。
func (b *LocalBackend) Delete(ctx context.Context, key string) error {
	if err := b.validateKey(key); err != nil {
		return err
	}

	fullPath := filepath.Join(b.basePath, key)

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 使用 RemoveAll 支持删除目录和文件，与现有 StorageManager 行为一致
	// os.RemoveAll 在目标不存在时不会返回错误，这与设计文档的要求一致
	err := os.RemoveAll(fullPath)
	if err != nil {
		// 检查是否是权限错误，映射到标准错误
		if os.IsPermission(err) {
			return interfaces.ErrPermissionDenied
		}
		return fmt.Errorf("删除失败 %s: %w", fullPath, err)
	}

	return nil
}

// List 列出指定前缀下的所有对象。
// 支持 StorageManager 的目录遍历功能，如 ListArchivalBackups。
func (b *LocalBackend) List(ctx context.Context, prefix string) ([]types.StorageObject, error) {
	// 允许空前缀，表示列出根目录下的所有对象
	prefixPath := filepath.Join(b.basePath, prefix)

	var objects []types.StorageObject

	// 使用 filepath.WalkDir 遍历目录，与现有代码保持一致
	err := filepath.WalkDir(prefixPath, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			// 如果前缀路径不存在，返回空列表而不是错误
			if os.IsNotExist(err) && path == prefixPath {
				return filepath.SkipAll
			}
			return err
		}

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 跳过目录，只返回文件
		if d.IsDir() {
			return nil
		}

		// 计算相对于 basePath 的键值
		relPath, err := filepath.Rel(b.basePath, path)
		if err != nil {
			return err
		}

		// 获取文件信息
		info, err := d.Info()
		if err != nil {
			return err
		}

		objects = append(objects, types.StorageObject{
			Key:          filepath.ToSlash(relPath), // 统一使用正斜杠
			Size:         info.Size(),
			LastModified: info.ModTime(),
			ETag:         "", // 本地文件系统不支持 ETag
		})

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("遍历目录失败 %s: %w", prefixPath, err)
	}

	return objects, nil
}

// Exists 检查指定的 key 是否存在。
// 相比 Get 方法更高效，只检查文件状态而不打开文件。
func (b *LocalBackend) Exists(ctx context.Context, key string) (bool, error) {
	if err := b.validateKey(key); err != nil {
		return false, err
	}

	fullPath := filepath.Join(b.basePath, key)

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return false, ctx.Err()
	default:
	}

	_, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		if os.IsPermission(err) {
			return false, interfaces.ErrPermissionDenied
		}
		return false, fmt.Errorf("检查文件状态失败 %s: %w", fullPath, err)
	}

	return true, nil
}

// HealthCheck 检查本地存储后端的健康状态。
// 验证基础路径的可访问性和读写权限。
func (b *LocalBackend) HealthCheck(ctx context.Context) error {
	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 检查基础路径是否存在且可访问
	info, err := os.Stat(b.basePath)
	if err != nil {
		return fmt.Errorf("基础路径不可访问 %s: %w", b.basePath, err)
	}

	if !info.IsDir() {
		return fmt.Errorf("基础路径不是目录: %s", b.basePath)
	}

	// 测试写权限：创建临时文件
	tempFile := filepath.Join(b.basePath, ".health_check_temp")
	file, err := os.Create(tempFile)
	if err != nil {
		return fmt.Errorf("无写权限 %s: %w", b.basePath, err)
	}
	file.Close()

	// 测试删除权限
	if err := os.Remove(tempFile); err != nil {
		return fmt.Errorf("无删除权限 %s: %w", b.basePath, err)
	}

	return nil
}

// validateKey 验证存储键的有效性
func (b *LocalBackend) validateKey(key string) error {
	if key == "" {
		return interfaces.ErrInvalidKey
	}

	// 防止路径遍历攻击
	if strings.Contains(key, "..") {
		return interfaces.ErrInvalidKey
	}

	// 防止绝对路径
	if filepath.IsAbs(key) {
		return interfaces.ErrInvalidKey
	}

	return nil
}

// validatePathPermissions 验证路径的读写权限
func validatePathPermissions(path string) error {
	// 测试读权限
	_, err := os.ReadDir(path)
	if err != nil {
		return fmt.Errorf("无读权限: %w", err)
	}

	// 测试写权限：尝试创建临时文件
	tempFile := filepath.Join(path, ".permission_test_temp")
	file, err := os.Create(tempFile)
	if err != nil {
		return fmt.Errorf("无写权限: %w", err)
	}
	file.Close()
	os.Remove(tempFile) // 清理临时文件

	return nil
}

// isElasticsearchPath 检查路径是否为Elasticsearch相关目录
func (b *LocalBackend) isElasticsearchPath(path string) bool {
	// 检查路径中是否包含elasticsearch相关关键词
	lowerPath := strings.ToLower(path)
	return strings.Contains(lowerPath, "elasticsearch") ||
		strings.Contains(lowerPath, "snapshots_archival") ||
		strings.Contains(lowerPath, "snapshots_chains")
}

// setElasticsearchPermissions 为Elasticsearch目录递归设置正确的权限
func (b *LocalBackend) setElasticsearchPermissions(path string) {
	// 使用常见的elasticsearch用户ID (Docker镜像默认)
	uid, gid := 1000, 1000

	// 递归设置目录及其所有子目录和文件的权限
	filepath.Walk(path, func(walkPath string, info os.FileInfo, err error) error {
		if err != nil {
			// 忽略权限错误，继续处理其他文件
			return nil
		}

		// 静默设置权限，不处理错误
		os.Chown(walkPath, uid, gid)

		if info.IsDir() {
			// 目录权限：755 (rwxr-xr-x)
			os.Chmod(walkPath, 0755)
		} else {
			// 文件权限：644 (rw-r--r--)
			os.Chmod(walkPath, 0644)
		}

		return nil
	})
}

// copyWithContext 支持上下文取消的数据复制
func (b *LocalBackend) copyWithContext(ctx context.Context, dst io.Writer, src io.Reader) (int64, error) {
	// 使用缓冲区进行分块复制，支持上下文取消
	buf := make([]byte, 32*1024) // 32KB 缓冲区
	var written int64

	for {
		select {
		case <-ctx.Done():
			return written, ctx.Err()
		default:
		}

		nr, er := src.Read(buf)
		if nr > 0 {
			nw, ew := dst.Write(buf[0:nr])
			if nw < 0 || nr < nw {
				nw = 0
				if ew == nil {
					ew = fmt.Errorf("invalid write result")
				}
			}
			written += int64(nw)
			if ew != nil {
				return written, ew
			}
			if nr != nw {
				return written, io.ErrShortWrite
			}
		}
		if er != nil {
			if er != io.EOF {
				return written, er
			}
			break
		}
	}
	return written, nil
}
