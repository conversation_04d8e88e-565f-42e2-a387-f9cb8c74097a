package local

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
)

// TestNewLocalBackend 测试 LocalBackend 的创建
func TestNewLocalBackend(t *testing.T) {
	tests := []struct {
		name        string
		basePath    string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid path",
			basePath:    t.TempDir(),
			expectError: false,
		},
		{
			name:        "empty path",
			basePath:    "",
			expectError: true,
			errorMsg:    "basePath 不能为空",
		},
		{
			name:        "relative path",
			basePath:    "./test-backend",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			backend, err := NewLocalBackend(tt.basePath)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到错误")
				} else if !strings.Contains(err.<PERSON>rror(), tt.errorMsg) {
					t.Errorf("错误信息不匹配，期望包含 %q，实际 %q", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			if backend == nil {
				t.Errorf("backend 不应该为 nil")
				return
			}

			// 验证基础路径是绝对路径
			if !filepath.IsAbs(backend.basePath) {
				t.Errorf("basePath 应该是绝对路径，实际: %s", backend.basePath)
			}

			// 验证目录存在
			if _, err := os.Stat(backend.basePath); os.IsNotExist(err) {
				t.Errorf("基础路径应该存在: %s", backend.basePath)
			}
		})
	}
}

// TestLocalBackend_Put 测试文件写入功能
func TestLocalBackend_Put(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	tests := []struct {
		name        string
		key         string
		data        string
		expectError bool
		errorType   error
	}{
		{
			name:        "valid file",
			key:         "test/data.txt",
			data:        "hello world",
			expectError: false,
		},
		{
			name:        "nested path",
			key:         "mysql/backup-123/data.sql.gz",
			data:        "compressed data",
			expectError: false,
		},
		{
			name:        "empty key",
			key:         "",
			data:        "data",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
		{
			name:        "path traversal attack",
			key:         "../../../etc/passwd",
			data:        "malicious",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
		{
			name:        "absolute path",
			key:         "/tmp/test.txt",
			data:        "data",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reader := strings.NewReader(tt.data)
			written, err := backend.Put(ctx, tt.key, reader)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					return
				}
				if tt.errorType != nil && !errors.Is(err, tt.errorType) {
					t.Errorf("错误类型不匹配，期望 %v，实际 %v", tt.errorType, err)
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			expectedWritten := int64(len(tt.data))
			if written != expectedWritten {
				t.Errorf("写入字节数不匹配，期望 %d，实际 %d", expectedWritten, written)
			}

			// 验证文件确实被创建
			fullPath := filepath.Join(backend.basePath, tt.key)
			if _, err := os.Stat(fullPath); os.IsNotExist(err) {
				t.Errorf("文件应该存在: %s", fullPath)
			}

			// 验证文件内容
			content, err := os.ReadFile(fullPath)
			if err != nil {
				t.Errorf("读取文件失败: %v", err)
			} else if string(content) != tt.data {
				t.Errorf("文件内容不匹配，期望 %q，实际 %q", tt.data, string(content))
			}
		})
	}
}

// TestLocalBackend_Get 测试文件读取功能
func TestLocalBackend_Get(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 先创建一个测试文件
	testKey := "test/data.txt"
	testData := "hello world"
	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		t.Fatalf("创建测试文件失败: %v", err)
	}

	tests := []struct {
		name         string
		key          string
		expectError  bool
		errorType    error
		expectedData string
	}{
		{
			name:         "existing file",
			key:          testKey,
			expectError:  false,
			expectedData: testData,
		},
		{
			name:        "non-existent file",
			key:         "non-existent.txt",
			expectError: true,
			errorType:   interfaces.ErrObjectNotFound,
		},
		{
			name:        "invalid key",
			key:         "",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reader, err := backend.Get(ctx, tt.key)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					if reader != nil {
						reader.Close()
					}
					return
				}
				if tt.errorType != nil && !errors.Is(err, tt.errorType) {
					t.Errorf("错误类型不匹配，期望 %v，实际 %v", tt.errorType, err)
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			if reader == nil {
				t.Errorf("reader 不应该为 nil")
				return
			}
			defer reader.Close()

			// 读取内容并验证
			content, err := io.ReadAll(reader)
			if err != nil {
				t.Errorf("读取内容失败: %v", err)
				return
			}

			if string(content) != tt.expectedData {
				t.Errorf("内容不匹配，期望 %q，实际 %q", tt.expectedData, string(content))
			}
		})
	}
}

// TestLocalBackend_Delete 测试文件删除功能
func TestLocalBackend_Delete(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 先创建一个测试文件
	testKey := "test/data.txt"
	testData := "hello world"
	_, err = backend.Put(ctx, testKey, strings.NewReader(testData))
	if err != nil {
		t.Fatalf("创建测试文件失败: %v", err)
	}

	tests := []struct {
		name        string
		key         string
		expectError bool
		errorType   error
	}{
		{
			name:        "existing file",
			key:         testKey,
			expectError: false,
		},
		{
			name:        "non-existent file - should not error with RemoveAll",
			key:         "non-existent.txt",
			expectError: false, // os.RemoveAll 不会在目标不存在时返回错误
		},
		{
			name:        "invalid key",
			key:         "",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := backend.Delete(ctx, tt.key)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					return
				}
				if tt.errorType != nil && !errors.Is(err, tt.errorType) {
					t.Errorf("错误类型不匹配，期望 %v，实际 %v", tt.errorType, err)
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			// 验证文件确实被删除（如果存在的话）
			if tt.key != "non-existent.txt" {
				fullPath := filepath.Join(backend.basePath, tt.key)
				if _, err := os.Stat(fullPath); !os.IsNotExist(err) {
					t.Errorf("文件应该被删除: %s", fullPath)
				}
			}
		})
	}
}

// TestLocalBackend_Delete_Directory 测试目录删除功能（验证 os.RemoveAll 行为）
func TestLocalBackend_Delete_Directory(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 创建一个包含多个文件的目录结构，模拟备份目录
	backupDir := "mysql/backup-123"
	files := map[string]string{
		backupDir + "/metadata.json": `{"id":"backup-123","type":"archival"}`,
		backupDir + "/data.sql.gz":   "compressed mysql data",
		backupDir + "/binlog.sql.gz": "compressed binlog data",
	}

	for key, data := range files {
		_, err := backend.Put(ctx, key, strings.NewReader(data))
		if err != nil {
			t.Fatalf("创建测试文件失败 %s: %v", key, err)
		}
	}

	// 验证目录和文件都存在
	for key := range files {
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			t.Fatalf("检查文件存在性失败 %s: %v", key, err)
		}
		if !exists {
			t.Fatalf("文件应该存在: %s", key)
		}
	}

	// 删除整个备份目录（模拟 StorageManager.DeleteBackupRecord 的行为）
	err = backend.Delete(ctx, backupDir)
	if err != nil {
		t.Fatalf("删除备份目录失败: %v", err)
	}

	// 验证整个目录及其所有内容都被删除
	for key := range files {
		exists, err := backend.Exists(ctx, key)
		if err != nil {
			t.Errorf("检查文件存在性失败 %s: %v", key, err)
		}
		if exists {
			t.Errorf("文件应该被删除: %s", key)
		}
	}

	// 验证目录本身也被删除
	fullDirPath := filepath.Join(backend.basePath, backupDir)
	if _, err := os.Stat(fullDirPath); !os.IsNotExist(err) {
		t.Errorf("目录应该被删除: %s", fullDirPath)
	}
}

// TestLocalBackend_NewWriter 测试流式写入器功能
func TestLocalBackend_NewWriter(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	tests := []struct {
		name        string
		key         string
		data        string
		expectError bool
		errorType   error
	}{
		{
			name:        "valid file",
			key:         "test/data.txt",
			data:        "hello world",
			expectError: false,
		},
		{
			name:        "nested path for mysql backup",
			key:         "mysql/backup-123/data.sql.gz",
			data:        "compressed mysql data",
			expectError: false,
		},
		{
			name:        "empty key",
			key:         "",
			data:        "data",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
		{
			name:        "path traversal attack",
			key:         "../../../etc/passwd",
			data:        "malicious",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
		{
			name:        "absolute path",
			key:         "/tmp/test.txt",
			data:        "data",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			writer, err := backend.NewWriter(ctx, tt.key)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					if writer != nil {
						writer.Close()
					}
					return
				}
				if tt.errorType != nil && !errors.Is(err, tt.errorType) {
					t.Errorf("错误类型不匹配，期望 %v，实际 %v", tt.errorType, err)
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			if writer == nil {
				t.Errorf("writer 不应该为 nil")
				return
			}

			// 写入数据
			written, err := writer.Write([]byte(tt.data))
			if err != nil {
				writer.Close()
				t.Errorf("写入数据失败: %v", err)
				return
			}

			if written != len(tt.data) {
				writer.Close()
				t.Errorf("写入字节数不匹配，期望 %d，实际 %d", len(tt.data), written)
				return
			}

			// 关闭写入器
			err = writer.Close()
			if err != nil {
				t.Errorf("关闭写入器失败: %v", err)
				return
			}

			// 验证文件确实被创建
			fullPath := filepath.Join(backend.basePath, tt.key)
			if _, err := os.Stat(fullPath); os.IsNotExist(err) {
				t.Errorf("文件应该存在: %s", fullPath)
				return
			}

			// 验证文件内容
			content, err := os.ReadFile(fullPath)
			if err != nil {
				t.Errorf("读取文件失败: %v", err)
			} else if string(content) != tt.data {
				t.Errorf("文件内容不匹配，期望 %q，实际 %q", tt.data, string(content))
			}
		})
	}
}

// TestLocalBackend_NewWriter_StreamingScenario 测试流式写入场景（模拟 Provider 使用）
func TestLocalBackend_NewWriter_StreamingScenario(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 模拟 MySQL Provider 的流式写入场景
	t.Run("mysql provider streaming", func(t *testing.T) {
		key := "mysql/backup-456/data.sql.gz"
		writer, err := backend.NewWriter(ctx, key)
		if err != nil {
			t.Fatalf("创建写入器失败: %v", err)
		}
		defer writer.Close()

		// 模拟分块写入大量数据（如 mysqldump 输出）
		chunks := []string{
			"-- MySQL dump 10.13\n",
			"CREATE TABLE users (\n",
			"  id INT PRIMARY KEY,\n",
			"  name VARCHAR(255)\n",
			");\n",
			"INSERT INTO users VALUES (1, 'Alice');\n",
			"INSERT INTO users VALUES (2, 'Bob');\n",
		}

		var totalWritten int
		for i, chunk := range chunks {
			written, err := writer.Write([]byte(chunk))
			if err != nil {
				t.Fatalf("写入块 %d 失败: %v", i, err)
			}
			totalWritten += written
		}

		// 关闭写入器
		err = writer.Close()
		if err != nil {
			t.Fatalf("关闭写入器失败: %v", err)
		}

		// 验证文件内容
		expectedContent := strings.Join(chunks, "")
		content, err := os.ReadFile(filepath.Join(backend.basePath, key))
		if err != nil {
			t.Fatalf("读取文件失败: %v", err)
		}

		if string(content) != expectedContent {
			t.Errorf("文件内容不匹配")
		}

		if len(content) != totalWritten {
			t.Errorf("文件大小不匹配，期望 %d，实际 %d", totalWritten, len(content))
		}
	})

	// 测试写入失败后的清理（模拟磁盘空间不足等情况）
	t.Run("write failure cleanup", func(t *testing.T) {
		key := "test/cleanup.txt"
		writer, err := backend.NewWriter(ctx, key)
		if err != nil {
			t.Fatalf("创建写入器失败: %v", err)
		}

		// 写入一些数据
		_, err = writer.Write([]byte("partial data"))
		if err != nil {
			t.Fatalf("写入数据失败: %v", err)
		}

		// 不调用 Close()，直接检查文件是否存在
		fullPath := filepath.Join(backend.basePath, key)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("部分写入的文件应该存在: %s", fullPath)
		}

		// 手动关闭以清理资源
		writer.Close()
	})
}

// TestLocalBackend_List 测试目录列表功能
func TestLocalBackend_List(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 创建测试文件结构
	testFiles := map[string]string{
		"mysql/backup-1/data.sql.gz":      "mysql data 1",
		"mysql/backup-2/data.sql.gz":      "mysql data 2",
		"metadata/backup-1/metadata.json": "metadata 1",
		"metadata/backup-2/metadata.json": "metadata 2",
		"es/snapshot-1/data.json":         "es data 1",
	}

	for key, data := range testFiles {
		_, err := backend.Put(ctx, key, strings.NewReader(data))
		if err != nil {
			t.Fatalf("创建测试文件失败 %s: %v", key, err)
		}
	}

	tests := []struct {
		name          string
		prefix        string
		expectedCount int
		expectedKeys  []string
	}{
		{
			name:          "list all",
			prefix:        "",
			expectedCount: 5,
		},
		{
			name:          "list mysql files",
			prefix:        "mysql",
			expectedCount: 2,
			expectedKeys:  []string{"mysql/backup-1/data.sql.gz", "mysql/backup-2/data.sql.gz"},
		},
		{
			name:          "list metadata files",
			prefix:        "metadata",
			expectedCount: 2,
			expectedKeys:  []string{"metadata/backup-1/metadata.json", "metadata/backup-2/metadata.json"},
		},
		{
			name:          "non-existent prefix",
			prefix:        "non-existent",
			expectedCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			objects, err := backend.List(ctx, tt.prefix)
			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			if len(objects) != tt.expectedCount {
				t.Errorf("对象数量不匹配，期望 %d，实际 %d", tt.expectedCount, len(objects))
			}

			// 验证特定的键
			if tt.expectedKeys != nil {
				foundKeys := make(map[string]bool)
				for _, obj := range objects {
					foundKeys[obj.Key] = true
				}

				for _, expectedKey := range tt.expectedKeys {
					if !foundKeys[expectedKey] {
						t.Errorf("期望找到键 %s，但没有找到", expectedKey)
					}
				}
			}

			// 验证对象属性
			for _, obj := range objects {
				if obj.Key == "" {
					t.Errorf("对象键不应该为空")
				}
				if obj.Size <= 0 {
					t.Errorf("对象大小应该大于0，实际: %d", obj.Size)
				}
				if obj.LastModified.IsZero() {
					t.Errorf("对象修改时间不应该为零值")
				}
			}
		})
	}
}

// TestLocalBackend_Exists 测试文件存在性检查
func TestLocalBackend_Exists(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 创建一个测试文件
	testKey := "test/data.txt"
	_, err = backend.Put(ctx, testKey, strings.NewReader("test data"))
	if err != nil {
		t.Fatalf("创建测试文件失败: %v", err)
	}

	tests := []struct {
		name         string
		key          string
		expectError  bool
		errorType    error
		expectExists bool
	}{
		{
			name:         "existing file",
			key:          testKey,
			expectError:  false,
			expectExists: true,
		},
		{
			name:         "non-existent file",
			key:          "non-existent.txt",
			expectError:  false,
			expectExists: false,
		},
		{
			name:        "invalid key",
			key:         "",
			expectError: true,
			errorType:   interfaces.ErrInvalidKey,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			exists, err := backend.Exists(ctx, tt.key)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到")
					return
				}
				if tt.errorType != nil && !errors.Is(err, tt.errorType) {
					t.Errorf("错误类型不匹配，期望 %v，实际 %v", tt.errorType, err)
				}
				return
			}

			if err != nil {
				t.Errorf("不期望错误但收到: %v", err)
				return
			}

			if exists != tt.expectExists {
				t.Errorf("存在性检查结果不匹配，期望 %v，实际 %v", tt.expectExists, exists)
			}
		})
	}
}

// TestLocalBackend_HealthCheck 测试健康检查功能
func TestLocalBackend_HealthCheck(t *testing.T) {
	// 测试正常情况
	t.Run("healthy backend", func(t *testing.T) {
		backend, err := NewLocalBackend(t.TempDir())
		if err != nil {
			t.Fatalf("创建 backend 失败: %v", err)
		}

		ctx := context.Background()
		err = backend.HealthCheck(ctx)
		if err != nil {
			t.Errorf("健康检查应该通过，但收到错误: %v", err)
		}
	})

	// 测试不存在的路径
	t.Run("non-existent path", func(t *testing.T) {
		tempDir := t.TempDir()
		nonExistentPath := filepath.Join(tempDir, "non-existent")

		backend := &LocalBackend{basePath: nonExistentPath}
		ctx := context.Background()
		err := backend.HealthCheck(ctx)
		if err == nil {
			t.Errorf("健康检查应该失败，但没有收到错误")
		}
	})
}

// TestLocalBackend_ContextCancellation 测试上下文取消
func TestLocalBackend_ContextCancellation(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	// 创建一个已取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	// 测试各个方法是否正确处理上下文取消
	t.Run("Put with cancelled context", func(t *testing.T) {
		_, err := backend.Put(ctx, "test.txt", strings.NewReader("data"))
		if err != context.Canceled {
			t.Errorf("期望 context.Canceled 错误，实际: %v", err)
		}
	})

	t.Run("Get with cancelled context", func(t *testing.T) {
		_, err := backend.Get(ctx, "test.txt")
		if err != context.Canceled {
			t.Errorf("期望 context.Canceled 错误，实际: %v", err)
		}
	})

	t.Run("Delete with cancelled context", func(t *testing.T) {
		err := backend.Delete(ctx, "test.txt")
		if err != context.Canceled {
			t.Errorf("期望 context.Canceled 错误，实际: %v", err)
		}
	})

	t.Run("List with cancelled context", func(t *testing.T) {
		_, err := backend.List(ctx, "")
		if err == nil {
			t.Errorf("期望错误但没有收到")
		} else if !errors.Is(err, context.Canceled) {
			t.Errorf("期望 context.Canceled 错误，实际: %v", err)
		}
	})

	t.Run("Exists with cancelled context", func(t *testing.T) {
		_, err := backend.Exists(ctx, "test.txt")
		if err != context.Canceled {
			t.Errorf("期望 context.Canceled 错误，实际: %v", err)
		}
	})

	t.Run("HealthCheck with cancelled context", func(t *testing.T) {
		err := backend.HealthCheck(ctx)
		if err != context.Canceled {
			t.Errorf("期望 context.Canceled 错误，实际: %v", err)
		}
	})
}

// TestLocalBackend_EdgeCases 测试边界情况和错误处理
func TestLocalBackend_EdgeCases(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 测试大文件写入
	t.Run("large file", func(t *testing.T) {
		largeData := strings.Repeat("a", 1024*1024) // 1MB
		written, err := backend.Put(ctx, "large.txt", strings.NewReader(largeData))
		if err != nil {
			t.Errorf("大文件写入失败: %v", err)
		}
		if written != int64(len(largeData)) {
			t.Errorf("写入字节数不匹配，期望 %d，实际 %d", len(largeData), written)
		}
	})

	// 测试空文件
	t.Run("empty file", func(t *testing.T) {
		written, err := backend.Put(ctx, "empty.txt", strings.NewReader(""))
		if err != nil {
			t.Errorf("空文件写入失败: %v", err)
		}
		if written != 0 {
			t.Errorf("空文件写入字节数应该为0，实际 %d", written)
		}

		// 验证可以读取空文件
		reader, err := backend.Get(ctx, "empty.txt")
		if err != nil {
			t.Errorf("读取空文件失败: %v", err)
		} else {
			defer reader.Close()
			content, err := io.ReadAll(reader)
			if err != nil {
				t.Errorf("读取空文件内容失败: %v", err)
			} else if len(content) != 0 {
				t.Errorf("空文件内容应该为空，实际长度 %d", len(content))
			}
		}
	})

	// 测试深层嵌套路径
	t.Run("deep nested path", func(t *testing.T) {
		deepPath := "a/b/c/d/e/f/g/h/i/j/deep.txt"
		data := "deep nested data"
		_, err := backend.Put(ctx, deepPath, strings.NewReader(data))
		if err != nil {
			t.Errorf("深层嵌套路径写入失败: %v", err)
		}

		// 验证可以读取
		reader, err := backend.Get(ctx, deepPath)
		if err != nil {
			t.Errorf("读取深层嵌套文件失败: %v", err)
		} else {
			defer reader.Close()
			content, err := io.ReadAll(reader)
			if err != nil {
				t.Errorf("读取深层嵌套文件内容失败: %v", err)
			} else if string(content) != data {
				t.Errorf("内容不匹配，期望 %q，实际 %q", data, string(content))
			}
		}
	})

	// 测试特殊字符路径
	t.Run("special characters in path", func(t *testing.T) {
		specialPath := "测试/文件-名称_123.txt"
		data := "special characters data"
		_, err := backend.Put(ctx, specialPath, strings.NewReader(data))
		if err != nil {
			t.Errorf("特殊字符路径写入失败: %v", err)
		}

		exists, err := backend.Exists(ctx, specialPath)
		if err != nil {
			t.Errorf("检查特殊字符路径存在性失败: %v", err)
		} else if !exists {
			t.Errorf("特殊字符路径文件应该存在")
		}
	})
}

// TestLocalBackend_ConcurrentAccess 测试并发访问
func TestLocalBackend_ConcurrentAccess(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()
	const numGoroutines = 10

	// 并发写入不同文件
	t.Run("concurrent writes", func(t *testing.T) {
		done := make(chan error, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				key := fmt.Sprintf("concurrent/file-%d.txt", id)
				data := fmt.Sprintf("data from goroutine %d", id)
				_, err := backend.Put(ctx, key, strings.NewReader(data))
				done <- err
			}(i)
		}

		// 等待所有 goroutine 完成
		for i := 0; i < numGoroutines; i++ {
			if err := <-done; err != nil {
				t.Errorf("并发写入失败: %v", err)
			}
		}

		// 验证所有文件都被创建
		objects, err := backend.List(ctx, "concurrent")
		if err != nil {
			t.Errorf("列出并发文件失败: %v", err)
		} else if len(objects) != numGoroutines {
			t.Errorf("并发文件数量不匹配，期望 %d，实际 %d", numGoroutines, len(objects))
		}
	})
}

// TestLocalBackend_PathValidation 测试路径验证的边界情况
func TestLocalBackend_PathValidation(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	invalidKeys := []string{
		"../parent.txt",
		"dir/../parent.txt",
		"./current/../parent.txt",
		"/absolute/path.txt",
	}

	for _, key := range invalidKeys {
		t.Run("invalid key: "+key, func(t *testing.T) {
			_, err := backend.Put(ctx, key, strings.NewReader("data"))
			if !errors.Is(err, interfaces.ErrInvalidKey) {
				t.Errorf("期望 ErrInvalidKey，实际: %v", err)
			}

			_, err = backend.Get(ctx, key)
			if !errors.Is(err, interfaces.ErrInvalidKey) {
				t.Errorf("期望 ErrInvalidKey，实际: %v", err)
			}

			err = backend.Delete(ctx, key)
			if !errors.Is(err, interfaces.ErrInvalidKey) {
				t.Errorf("期望 ErrInvalidKey，实际: %v", err)
			}

			_, err = backend.Exists(ctx, key)
			if !errors.Is(err, interfaces.ErrInvalidKey) {
				t.Errorf("期望 ErrInvalidKey，实际: %v", err)
			}

			// 测试 NewWriter 方法
			_, err = backend.NewWriter(ctx, key)
			if !errors.Is(err, interfaces.ErrInvalidKey) {
				t.Errorf("期望 ErrInvalidKey，实际: %v", err)
			}
		})
	}
}

// TestLocalBackend_ErrorMapping 测试错误映射的正确性
func TestLocalBackend_ErrorMapping(t *testing.T) {
	backend, err := NewLocalBackend(t.TempDir())
	if err != nil {
		t.Fatalf("创建 backend 失败: %v", err)
	}

	ctx := context.Background()

	// 测试 ErrObjectNotFound 映射
	t.Run("ErrObjectNotFound mapping", func(t *testing.T) {
		// Get 方法
		_, err := backend.Get(ctx, "non-existent.txt")
		if !errors.Is(err, interfaces.ErrObjectNotFound) {
			t.Errorf("Get 应该返回 ErrObjectNotFound，实际: %v", err)
		}

		// Exists 方法不应该返回错误，即使文件不存在
		exists, err := backend.Exists(ctx, "non-existent.txt")
		if err != nil {
			t.Errorf("Exists 不应该返回错误，实际: %v", err)
		}
		if exists {
			t.Errorf("Exists 应该返回 false")
		}
	})

	// 测试错误别名的一致性
	t.Run("error alias consistency", func(t *testing.T) {
		// 验证错误别名指向相同的错误
		if interfaces.ErrPermission != interfaces.ErrPermissionDenied {
			t.Errorf("ErrPermission 应该等于 ErrPermissionDenied")
		}

		if interfaces.ErrAlreadyExists != interfaces.ErrObjectAlreadyExists {
			t.Errorf("ErrAlreadyExists 应该等于 ErrObjectAlreadyExists")
		}
	})
}
