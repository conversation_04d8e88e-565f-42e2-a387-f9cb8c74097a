package storage

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/internal/storage/local"
	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockTaskManager 实现 TaskManager 接口用于测试
type MockTaskManager struct {
	mock.Mock
}

func (m *MockTaskManager) GetTask(taskID string) (*types.Task, error) {
	args := m.Called(taskID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*types.Task), args.Error(1)
}

func (m *MockTaskManager) ListTasks() ([]*types.Task, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*types.Task), args.Error(1)
}

func (m *MockTaskManager) DeleteTask(taskID string) error {
	args := m.Called(taskID)
	return args.Error(0)
}

// setupTestManager is a helper function to create an isolated test environment for each test case.
// It creates a temporary directory as the backup root and initializes a Manager instance.
// It returns the Manager instance, the path to the temporary directory, and a cleanup function.
func setupTestManager(t *testing.T) (*Manager, string, func()) {
	// Create a unique temporary directory for the test
	tempDir, err := os.MkdirTemp("", "unibackup_storage_test_*")
	require.NoError(t, err, "Failed to create temp dir for test")

	logger := zap.NewNop()

	// Create a dummy config for the storage manager
	cfg := &types.Config{
		BackupRoot: tempDir,
		Logger:     logger,
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "test",
			DBName: "test",
		},
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "archival",
			ManagedRepoName:  "managed",
		},
	}

	// 阶段二改造：创建 LocalBackend 并注入到 Manager
	backend, err := local.NewLocalBackend(tempDir)
	require.NoError(t, err, "Failed to create LocalBackend for test")

	// 创建 MockTaskManager
	mockTaskManager := &MockTaskManager{}
	manager := NewManager(backend, cfg, logger, mockTaskManager)

	// Define a cleanup function to remove the temporary directory after the test
	cleanup := func() {
		err := os.RemoveAll(tempDir)
		if err != nil {
			t.Logf("Warning: failed to clean up temp dir %s: %v", tempDir, err)
		}
	}

	return manager, tempDir, cleanup
}

func TestCreateBackupRecord(t *testing.T) {
	t.Run("should create archival backup record correctly", func(t *testing.T) {
		manager, backupRoot, cleanup := setupTestManager(t)
		defer cleanup()

		record, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "test archival")
		require.Nil(t, err)

		// Verify the record's basic information
		assert.NotEmpty(t, record.ID)
		assert.Equal(t, types.MySQL, record.Source)
		assert.Equal(t, types.BackupTypeArchival, record.Type)
		assert.Equal(t, "test archival", record.Description)
		assert.Equal(t, types.BackupStatusInProgress, record.Status)

		// Verify the path and metadata file
		expectedPath := filepath.Join(backupRoot, string(types.MySQL), ArchivalDir, record.ID)
		assert.Equal(t, expectedPath, record.Path)
		metadataPath := filepath.Join(expectedPath, MetadataFileName)
		_, statErr := os.Stat(metadataPath)
		assert.NoError(t, statErr, "metadata.json should be created")
	})

	t.Run("should create incremental chain record correctly", func(t *testing.T) {
		manager, backupRoot, cleanup := setupTestManager(t)
		defer cleanup()

		record, err := manager.CreateBackupRecord(types.Elasticsearch, "test_cluster", types.BackupTypeChainInitial, "", "test chain")
		require.Nil(t, err)

		// Verify the record's basic information
		assert.NotEmpty(t, record.ChainID)
		assert.Equal(t, record.ID, record.ChainID)
		assert.Equal(t, types.BackupTypeChainInitial, record.Type)

		// Verify the path and metadata file
		expectedPath := filepath.Join(backupRoot, string(types.Elasticsearch), ChainsDir, record.ChainID, record.ID)
		assert.Equal(t, expectedPath, record.Path)
		_, statErr := os.Stat(expectedPath)
		assert.NoError(t, statErr, "chain backup directory should be created")
	})
}

// TestComplexScenarios 包含对更复杂的、涉及多个备份记录交互的场景的测试。
func TestComplexScenarios(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	// --- 1. 创建一个模拟的增量备份链 ---
	// 节点1: 初始全量备份
	rec1, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainInitial, "", "chain head")
	require.Nil(t, err)
	rec1.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec1))

	time.Sleep(10 * time.Millisecond) // 确保时间戳不同

	// 节点2: 第一个增量备份
	rec2, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, rec1.ChainID, "first inc")
	require.Nil(t, err)
	rec2.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec2))

	time.Sleep(10 * time.Millisecond)

	// 节点3: 第二个增量备份
	rec3, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, rec1.ChainID, "second inc")
	require.Nil(t, err)
	rec3.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec3))

	// 同时创建一个独立的归档备份，用于测试隔离性
	archivalRec, err := manager.CreateBackupRecord(types.Elasticsearch, "test_cluster", types.BackupTypeArchival, "", "isolated archival")
	require.Nil(t, err)
	archivalRec.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(archivalRec))

	// --- 2. 测试 GetRestorationChain ---
	t.Run("should get correct restoration chain", func(t *testing.T) {
		// 请求恢复到第二个节点
		chain, err := manager.GetRestorationChain(types.MySQL, rec2.ID)
		require.Nil(t, err)
		require.Len(t, chain, 2, "should return two records for restoring to the second node")
		assert.Equal(t, rec1.ID, chain[0].ID)
		assert.Equal(t, rec2.ID, chain[1].ID)

		// 请求恢复到最后一个节点
		fullChain, err := manager.GetRestorationChain(types.MySQL, rec3.ID)
		require.Nil(t, err)
		assert.Len(t, fullChain, 3, "should return all three records for restoring to the last node")
	})

	// --- 3. 测试 DeleteBackupRecord ---
	t.Run("should delete entire chain when deleting the head", func(t *testing.T) {
		// This test is now obsolete and incorrect. The new safety features prevent this.
		// 正确的行为已在 TestDeleteBackupRecordSafety 中测试。
		// We will remove this sub-test.
	})
}

func TestFinderMethods(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	// Create test data
	rec1, _ := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainInitial, "", "chain head")
	rec1.Status = types.BackupStatusCompleted
	manager.SaveBackupRecord(rec1)

	time.Sleep(10 * time.Millisecond)
	rec2, _ := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, rec1.ChainID, "second inc")
	rec2.Status = types.BackupStatusCompleted
	manager.SaveBackupRecord(rec2)

	archivalRec, _ := manager.CreateBackupRecord(types.Elasticsearch, "test_cluster", types.BackupTypeArchival, "", "isolated archival")
	archivalRec.Status = types.BackupStatusCompleted
	manager.SaveBackupRecord(archivalRec)

	t.Run("FindBackupRecord should find records correctly", func(t *testing.T) {
		foundChainRec, err := manager.FindBackupRecord(types.MySQL, rec1.ID)
		require.Nil(t, err)
		assert.Equal(t, rec1.ID, foundChainRec.ID)

		foundArchivalRec, err := manager.FindBackupRecord(types.Elasticsearch, archivalRec.ID)
		require.Nil(t, err)
		assert.Equal(t, archivalRec.ID, foundArchivalRec.ID)

		_, err = manager.FindBackupRecord(types.MySQL, "non-existent-id")
		assert.Error(t, err)
	})

	t.Run("GetLatestChainRecord should get the latest record", func(t *testing.T) {
		latest, err := manager.GetLatestChainRecord(types.MySQL, rec1.ChainID)
		require.Nil(t, err)
		assert.Equal(t, rec2.ID, latest.ID)
	})
}

func TestErrorScenarios(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("should fail creating incremental backup for non-existent chain", func(t *testing.T) {
		_, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, "non-existent-chain-id", "bad inc")
		assert.Error(t, err)
	})
}

func TestCoverageAndHelpers(t *testing.T) {
	manager, backupRoot, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("path helpers return correct paths", func(t *testing.T) {
		assert.Equal(t, filepath.Join(backupRoot, "mysql"), manager.GetDataSourcePath(types.MySQL))
		assert.Equal(t, filepath.Join(backupRoot, "mysql", ArchivalDir, "123"), manager.GetArchivalBackupPath(types.MySQL, "123"))
		assert.Equal(t, filepath.Join(backupRoot, "mysql", ChainsDir, "abc", "123"), manager.GetChainPath(types.MySQL, "abc", "123"))
		assert.Equal(t, "/tmp/foo/metadata.json", manager.GetMetadataPath("/tmp/foo"))
		assert.Equal(t, filepath.Join(backupRoot, "mysql", ChainsDir, "abc", ChainMetadataFileName), manager.GetChainMetadataPath(types.MySQL, "abc"))
		assert.Equal(t, filepath.Join(backupRoot, "mysql", StatusFileName), manager.GetDataSourceStatusPath(types.MySQL))
	})

	t.Run("set and get data source status", func(t *testing.T) {
		status, err := manager.GetDataSourceStatus(types.MySQL)
		require.Nil(t, err)
		assert.Equal(t, "OK", status)

		require.Nil(t, os.MkdirAll(filepath.Dir(manager.GetDataSourceStatusPath(types.MySQL)), 0755))
		require.Nil(t, manager.SetDataSourceStatus(types.MySQL, "LOCKED"))
		status, err = manager.GetDataSourceStatus(types.MySQL)
		require.Nil(t, err)
		assert.Equal(t, "LOCKED", status)
	})
}

func TestChainMetaLogic(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	// 1. Create a full chain
	rec1, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainInitial, "", "chain head")
	require.Nil(t, err)
	rec1.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec1))

	time.Sleep(2 * time.Millisecond)
	rec2, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, rec1.ChainID, "inc 1")
	require.Nil(t, err)
	rec2.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec2))

	// 2. Verify chain_meta.json content
	t.Run("should write correct chain_meta.json", func(t *testing.T) {
		meta, err := manager.ReadChainMeta(types.MySQL, rec1.ChainID)
		require.Nil(t, err)
		require.NotNil(t, meta)
		assert.Equal(t, rec1.ChainID, meta.ChainID)
		assert.Equal(t, []string{rec1.ID, rec2.ID}, meta.BackupIDs)
	})

	// 3. Verify GetLatestChainRecord uses the meta file
	t.Run("should get latest record from meta", func(t *testing.T) {
		latest, err := manager.GetLatestChainRecord(types.MySQL, rec1.ChainID)
		require.Nil(t, err)
		assert.Equal(t, rec2.ID, latest.ID)
	})

	// 4. Verify ListIncrementalChains uses the meta file
	t.Run("should list chains from meta", func(t *testing.T) {
		// Create a broken record directory without metadata to ensure it's ignored
		brokenRecordPath := manager.GetChainPath(types.MySQL, rec1.ChainID, "broken-record")
		os.MkdirAll(brokenRecordPath, 0755)

		chains, err := manager.ListIncrementalChains(types.MySQL, "test_db")
		require.Nil(t, err)
		require.Len(t, chains, 1)
		assert.Len(t, chains[0].Backups, 2, "ListIncrementalChains should only list records from meta file")
		assert.Equal(t, rec1.ID, chains[0].Backups[0].ID)
		assert.Equal(t, rec2.ID, chains[0].Backups[1].ID)
	})
}

func TestDeleteBackupRecordSafety(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	// 1. Create a full chain to test against
	rec1, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainInitial, "", "chain head")
	require.Nil(t, err)
	rec1.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec1))

	time.Sleep(2 * time.Millisecond)
	rec2, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, rec1.ChainID, "inc 1")
	require.Nil(t, err)
	rec2.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec2))

	// 2. Test cases
	t.Run("should forbid deleting chain head", func(t *testing.T) {
		err := manager.DeleteBackupRecord(rec1)
		assert.Error(t, err, "Expected an error when deleting chain head")
		if err != nil {
			assert.Contains(t, err.Error(), "安全检查失败", "Expected error message to contain '安全检查失败'")
		}
	})

	t.Run("should forbid deleting chain middle part", func(t *testing.T) {
		err := manager.DeleteBackupRecord(rec2)
		assert.Error(t, err, "Expected an error when deleting chain middle part")
		if err != nil {
			assert.Contains(t, err.Error(), "安全检查失败", "Expected error message to contain '安全检查失败'")
		}
	})

	t.Run("should allow deleting entire chain", func(t *testing.T) {
		err := manager.DeleteIncrementalChain(types.MySQL, rec1.ChainID)
		assert.Nil(t, err)
		// Verify that the chain directory is gone
		chainDir := filepath.Dir(rec1.Path)
		_, statErr := os.Stat(chainDir)
		assert.True(t, os.IsNotExist(statErr), "chain directory should be deleted")
	})
}

// TestStorageManager_ListArchivalBackups 测试归档备份列表功能
func TestStorageManager_ListArchivalBackups(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	// 创建几个归档备份
	rec1, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "first archival")
	require.Nil(t, err)
	rec1.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec1))

	rec2, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "second archival")
	require.Nil(t, err)
	rec2.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec2))

	// 测试列出归档备份
	backups, err := manager.ListArchivalBackups(types.MySQL)
	assert.Nil(t, err)
	assert.Len(t, backups, 2)
}

// TestStorageManager_GetIncrementalChainRecords 测试增量链记录获取
func TestStorageManager_GetIncrementalChainRecords(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	// 创建增量链
	rec1, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainInitial, "", "chain head")
	require.Nil(t, err)
	rec1.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec1))

	rec2, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, rec1.ChainID, "first inc")
	require.Nil(t, err)
	rec2.Status = types.BackupStatusCompleted
	require.Nil(t, manager.SaveBackupRecord(rec2))

	// 测试获取链记录
	records, err := manager.GetIncrementalChainRecords(types.MySQL, rec1.ChainID)
	assert.Nil(t, err)
	assert.Len(t, records, 2)
	assert.Equal(t, rec1.ID, records[0].ID)
	assert.Equal(t, rec2.ID, records[1].ID)
}

// TestStorageManager_CheckDataSourceHealth_Skip 跳过的数据源健康检查测试
func TestStorageManager_CheckDataSourceHealth_Skip(t *testing.T) {
	t.Run("should check data source health", func(t *testing.T) {
		// 跳过这个测试，因为CheckDataSourceHealth需要provider参数
		t.Skip("Skipping CheckDataSourceHealth test - requires provider parameter")
	})
}

// TestStorageManager_ErrorCases 测试错误情况
func TestStorageManager_ErrorCases(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("CreateBackupRecord with invalid chain ID should fail", func(t *testing.T) {
		_, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainIncremental, "non-existent-chain", "invalid inc")
		assert.Error(t, err)
	})

	t.Run("GetLatestChainRecord with non-existent chain should fail", func(t *testing.T) {
		_, err := manager.GetLatestChainRecord(types.MySQL, "non-existent-chain")
		assert.Error(t, err)
	})

	t.Run("GetRestorationChain with non-existent backup should fail", func(t *testing.T) {
		_, err := manager.GetRestorationChain(types.MySQL, "non-existent-backup")
		assert.Error(t, err)
	})

	t.Run("ReadMetadata with non-existent file should fail", func(t *testing.T) {
		_, err := manager.ReadMetadata("/non/existent/path")
		assert.Error(t, err)
	})

	t.Run("ReadChainMeta with non-existent chain should fail", func(t *testing.T) {
		_, err := manager.ReadChainMeta(types.MySQL, "non-existent-chain")
		assert.Error(t, err)
	})
}

// TestStorageManager_DataSourceManagement 测试数据源管理功能
func TestStorageManager_DataSourceManagement(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("GetDataSourcePath should return correct path", func(t *testing.T) {
		path := manager.GetDataSourcePath(types.MySQL)
		assert.Contains(t, path, "mysql")
		assert.Contains(t, path, manager.backupRoot)
	})

	t.Run("GetDataSourceStatusPath should return correct path", func(t *testing.T) {
		path := manager.GetDataSourceStatusPath(types.MySQL)
		assert.Contains(t, path, "mysql")
		assert.Contains(t, path, "status.json")
	})

	t.Run("SetDataSourceStatus should work", func(t *testing.T) {
		// 先创建数据源目录
		dataSourcePath := manager.GetDataSourcePath(types.MySQL)
		err := os.MkdirAll(dataSourcePath, 0755)
		assert.NoError(t, err)

		backupErr := manager.SetDataSourceStatus(types.MySQL, "healthy")
		assert.Nil(t, backupErr)

		// 验证状态文件是否创建
		statusPath := manager.GetDataSourceStatusPath(types.MySQL)
		fileContent, fileErr := os.ReadFile(statusPath)
		assert.NoError(t, fileErr)
		assert.Equal(t, "healthy", string(fileContent))
	})

	t.Run("GetDataSourceStatus should work", func(t *testing.T) {
		// 先创建数据源目录
		dataSourcePath := manager.GetDataSourcePath(types.MySQL)
		err := os.MkdirAll(dataSourcePath, 0755)
		assert.NoError(t, err)

		// 先设置状态
		backupErr := manager.SetDataSourceStatus(types.MySQL, "testing")
		assert.Nil(t, backupErr)

		// 读取状态
		status, backupErr := manager.GetDataSourceStatus(types.MySQL)
		assert.Nil(t, backupErr)
		assert.Equal(t, "testing", status)
	})

	t.Run("GetDataSourceStatus with non-existent status should return default", func(t *testing.T) {
		status, backupErr := manager.GetDataSourceStatus(types.Elasticsearch)
		assert.Nil(t, backupErr)
		assert.Equal(t, "OK", status) // 默认状态是 "OK"
	})
}

// TestStorageManager_PathGeneration 测试路径生成功能
func TestStorageManager_PathGeneration(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("GetArchivalBackupPath should generate correct path", func(t *testing.T) {
		path := manager.GetArchivalBackupPath(types.MySQL, "test-backup-id")
		assert.Contains(t, path, "mysql")
		assert.Contains(t, path, "archival")
		assert.Contains(t, path, "test-backup-id")
	})

	t.Run("GetChainPath should generate correct path", func(t *testing.T) {
		path := manager.GetChainPath(types.MySQL, "test-chain-id", "test-backup-id")
		assert.Contains(t, path, "mysql")
		assert.Contains(t, path, "chains")
		assert.Contains(t, path, "test-chain-id")
		assert.Contains(t, path, "test-backup-id")
	})

	t.Run("GetChainMetadataPath should generate correct path", func(t *testing.T) {
		path := manager.GetChainMetadataPath(types.MySQL, "test-chain-id")
		assert.Contains(t, path, "mysql")
		assert.Contains(t, path, "chains")
		assert.Contains(t, path, "test-chain-id")
		assert.Contains(t, path, "chain_meta.json")
	})
}

// TestStorageManager_ErrorHandling 测试错误处理
func TestStorageManager_ErrorHandling(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("SaveBackupRecord should handle valid record correctly", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test-id",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Path:   "test/backup/path",
		}

		backupErr := manager.SaveBackupRecord(record)
		assert.Nil(t, backupErr)
	})

	t.Run("WriteChainMeta with invalid chain ID should fail", func(t *testing.T) {
		meta := &types.ChainMeta{
			ChainID:   "test-chain",
			BackupIDs: []string{"backup1", "backup2"},
		}

		// 使用包含无效字符的路径
		err := manager.WriteChainMeta(types.MySQL, "invalid/chain/id", meta)
		assert.Error(t, err)
	})

	t.Run("FindBackupRecord with empty ID should fail", func(t *testing.T) {
		_, err := manager.FindBackupRecord(types.MySQL, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "未找到")
	})
}

// TestStorageManager_ConcurrentAccess 测试并发访问
func TestStorageManager_ConcurrentAccess(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("Concurrent SaveBackupRecord should work", func(t *testing.T) {
		// 创建多个备份记录
		records := make([]*types.BackupRecord, 5)
		for i := 0; i < 5; i++ {
			record, backupErr := manager.CreateBackupRecord(types.MySQL, fmt.Sprintf("test_db_%d", i), types.BackupTypeArchival, "", "并发测试")
			require.Nil(t, backupErr)
			records[i] = record
		}

		// 并发保存
		done := make(chan error, 5)
		for _, record := range records {
			go func(r *types.BackupRecord) {
				r.Status = types.BackupStatusCompleted
				done <- manager.SaveBackupRecord(r)
			}(record)
		}

		// 等待所有操作完成
		for i := 0; i < 5; i++ {
			backupErr := <-done
			assert.Nil(t, backupErr)
		}

		// 验证所有记录都已保存
		backups, backupErr := manager.ListArchivalBackups(types.MySQL)
		assert.Nil(t, backupErr)
		assert.Len(t, backups, 5)
	})
}

// 测试数据源健康检查功能
func TestStorageManager_CheckDataSourceHealth(t *testing.T) {
	t.Run("测试数据源健康检查", func(t *testing.T) {
		// 测试正常状态（跳过实际的数据库连接测试）
		// 由于测试环境没有真实的MySQL连接，我们跳过这个测试
		t.Skip("跳过数据源健康检查测试，需要真实的数据库连接")

	})
}

// 测试跨类型备份记录查找功能
func TestStorageManager_FindBackupRecordAcrossTypes(t *testing.T) {
	t.Run("测试跨类型备份记录查找", func(t *testing.T) {
		manager, _, cleanup := setupTestManager(t)
		defer cleanup()

		// 创建MySQL备份记录
		mysqlRecord, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "MySQL测试备份")
		require.Nil(t, err)
		mysqlRecord.Status = types.BackupStatusCompleted
		err = manager.SaveBackupRecord(mysqlRecord)
		require.Nil(t, err)

		// 创建ES备份记录
		esRecord, err := manager.CreateBackupRecord(types.Elasticsearch, "test_cluster", types.BackupTypeArchival, "", "ES测试备份")
		require.Nil(t, err)
		esRecord.Status = types.BackupStatusCompleted
		err = manager.SaveBackupRecord(esRecord)
		require.Nil(t, err)

		// 测试查找MySQL备份
		foundRecord, err := manager.FindBackupRecordAcrossTypes(mysqlRecord.ID)
		assert.Nil(t, err)
		assert.NotNil(t, foundRecord)
		assert.Equal(t, mysqlRecord.ID, foundRecord.ID)
		assert.Equal(t, types.MySQL, foundRecord.Source)

		// 测试查找ES备份
		foundRecord, err = manager.FindBackupRecordAcrossTypes(esRecord.ID)
		assert.Nil(t, err)
		assert.NotNil(t, foundRecord)
		assert.Equal(t, esRecord.ID, foundRecord.ID)
		assert.Equal(t, types.Elasticsearch, foundRecord.Source)

		// 测试查找不存在的备份
		foundRecord, err = manager.FindBackupRecordAcrossTypes("non-existent-backup")
		assert.Error(t, err)
		assert.Nil(t, foundRecord)
	})
}

// 测试增量链备份记录获取功能
func TestStorageManager_GetChainBackupRecord(t *testing.T) {
	t.Run("测试增量链备份记录获取", func(t *testing.T) {
		manager, _, cleanup := setupTestManager(t)
		defer cleanup()

		// 创建增量链记录
		initialRecord, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeChainInitial, "", "链初始备份")
		require.Nil(t, err)
		initialRecord.Status = types.BackupStatusCompleted
		err = manager.SaveBackupRecord(initialRecord)
		require.Nil(t, err)

		// 测试获取链中的备份记录
		foundRecord, err := manager.GetChainBackupRecord(types.MySQL, initialRecord.ChainID, initialRecord.ID)
		assert.Nil(t, err)
		assert.NotNil(t, foundRecord)
		assert.Equal(t, initialRecord.ID, foundRecord.ID)
		assert.Equal(t, initialRecord.ChainID, foundRecord.ChainID)

		// 测试获取不存在的备份记录
		foundRecord, err = manager.GetChainBackupRecord(types.MySQL, initialRecord.ChainID, "non-existent-backup")
		assert.Error(t, err)
		assert.Nil(t, foundRecord)

		// 测试获取不存在的链
		foundRecord, err = manager.GetChainBackupRecord(types.MySQL, "non-existent-chain", initialRecord.ID)
		assert.Error(t, err)
		assert.Nil(t, foundRecord)
	})
}

// TestStorageManager_GetBackupWriter tests the GetBackupWriter method for Provider support
func TestStorageManager_GetBackupWriter(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("should_create_writer_for_valid_backup_record", func(t *testing.T) {
		// Create a test backup record
		record, err := manager.CreateBackupRecord(types.MySQL, "test-db", types.BackupTypeArchival, "", "")
		if err != nil {
			t.Fatalf("CreateBackupRecord failed: %v", err)
		}
		require.NotNil(t, record, "Record should not be nil")
		require.NotEmpty(t, record.Path, "Record path should not be empty")

		// Test GetBackupWriter
		writer, backupErr := manager.GetBackupWriter(record, "data.sql.gz")
		if backupErr != nil {
			t.Fatalf("GetBackupWriter failed: %v", backupErr)
		}
		require.NotNil(t, writer, "Writer should not be nil")

		// Test writing and closing
		testData := "test data"
		_, writeErr := writer.Write([]byte(testData))
		require.NoError(t, writeErr)

		closeErr := writer.Close()
		require.NoError(t, closeErr)

		// Verify the file was created
		filePath := filepath.Join(record.Path, "data.sql.gz")
		require.FileExists(t, filePath, "Backup file should exist")
	})

	t.Run("should_fail_for_invalid_record", func(t *testing.T) {
		// Test with nil record
		writer, backupErr := manager.GetBackupWriter(nil, "data.sql.gz")
		require.Nil(t, writer, "Writer should be nil for invalid record")
		require.NotNil(t, backupErr, "Should return error for nil record")
	})
}

// TestStorageManager_GetBackend tests the GetBackend method
func TestStorageManager_GetBackend(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("should_return_backend_interface", func(t *testing.T) {
		backend := manager.GetBackend()
		require.NotNil(t, backend, "GetBackend should return non-nil backend")

		// Test that the returned backend is functional
		ctx := context.Background()

		// Test Put operation
		testKey := "test/key"
		testData := "test data"
		_, err := backend.Put(ctx, testKey, strings.NewReader(testData))
		require.NoError(t, err, "Backend Put should work")

		// Test Get operation
		reader, err := backend.Get(ctx, testKey)
		require.NoError(t, err, "Backend Get should work")
		defer reader.Close()

		data, err := io.ReadAll(reader)
		require.NoError(t, err, "Should be able to read from backend")
		require.Equal(t, testData, string(data), "Data should match")

		// Test Delete operation
		err = backend.Delete(ctx, testKey)
		require.NoError(t, err, "Backend Delete should work")

		// Verify deletion
		_, err = backend.Get(ctx, testKey)
		require.Error(t, err, "Get should fail after deletion")
	})
}

// TestStorageManager_BackendErrorMapping tests Backend interface error mapping
func TestStorageManager_BackendErrorMapping(t *testing.T) {
	manager, tempDir, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("should_map_not_found_errors_correctly", func(t *testing.T) {
		// Test ReadMetadata with non-existent file
		nonExistentPath := filepath.Join(tempDir, "non-existent", "metadata.json")
		record, err := manager.ReadMetadata(nonExistentPath)
		require.Nil(t, record, "Record should be nil for non-existent file")
		require.NotNil(t, err, "Should return error for non-existent file")
		require.Contains(t, err.Error(), "不存在", "Error should indicate file doesn't exist")

		// Test ReadChainMeta with non-existent chain
		chainMeta, err := manager.ReadChainMeta(types.MySQL, "non-existent-chain")
		require.Nil(t, chainMeta, "Chain meta should be nil for non-existent chain")
		require.Nil(t, err, "ReadChainMeta should return nil error for non-existent chain (expected behavior)")

		// Test GetDataSourceStatus with non-existent status
		status, err := manager.GetDataSourceStatus(types.MySQL)
		require.Equal(t, "OK", status, "Should return default status for non-existent status file")
		require.Nil(t, err, "Should not return error for non-existent status file")
	})
}

// TestStorageManager_NewMethodsDebug - 调试新增方法的测试
func TestStorageManager_NewMethodsDebug(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("debug_create_backup_record", func(t *testing.T) {
		// 使用与现有测试相同的参数
		record, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "test archival")
		t.Logf("CreateBackupRecord result: record=%+v, err=%v", record, err)
		if err != nil {
			t.Fatalf("CreateBackupRecord failed: %v", err)
		}
		require.NotNil(t, record, "Record should not be nil")

		// 测试 GetBackupWriter
		writer, backupErr := manager.GetBackupWriter(record, "data.sql.gz")
		t.Logf("GetBackupWriter result: writer=%v, err=%v", writer != nil, backupErr)
		if backupErr != nil {
			t.Fatalf("GetBackupWriter failed: %v", backupErr)
		}
		require.NotNil(t, writer, "Writer should not be nil")

		// 简单的写入测试
		_, writeErr := writer.Write([]byte("test"))
		if writeErr != nil {
			t.Fatalf("Write failed: %v", writeErr)
		}

		closeErr := writer.Close()
		if closeErr != nil {
			t.Fatalf("Close failed: %v", closeErr)
		}
	})

	t.Run("debug_get_backend", func(t *testing.T) {
		backend := manager.GetBackend()
		require.NotNil(t, backend, "GetBackend should return backend")

		// 简单的 backend 测试
		ctx := context.Background()
		_, err := backend.Put(ctx, "test-key", strings.NewReader("test-data"))
		require.NoError(t, err, "Backend Put should work")
	})
}

// TestStorageManager_NewMethods - 测试新增的方法
func TestStorageManager_NewMethods(t *testing.T) {
	manager, _, cleanup := setupTestManager(t)
	defer cleanup()

	t.Run("GetBackupWriter_should_work_correctly", func(t *testing.T) {
		// 创建测试备份记录
		record, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "test archival")
		require.Nil(t, err, "CreateBackupRecord should succeed")
		require.NotNil(t, record, "Record should not be nil")

		// 测试 GetBackupWriter
		writer, backupErr := manager.GetBackupWriter(record, "data.sql.gz")
		require.Nil(t, backupErr, "GetBackupWriter should succeed")
		require.NotNil(t, writer, "Writer should not be nil")

		// 测试写入数据
		testData := "-- MySQL dump test data\nCREATE TABLE test (id INT);\n"
		n, writeErr := writer.Write([]byte(testData))
		require.NoError(t, writeErr, "Write should succeed")
		require.Equal(t, len(testData), n, "Should write all data")

		// 关闭写入器
		closeErr := writer.Close()
		require.NoError(t, closeErr, "Close should succeed")

		// 验证文件是否创建
		filePath := filepath.Join(record.Path, "data.sql.gz")
		require.FileExists(t, filePath, "Backup file should exist")

		// 验证文件内容
		content, readErr := os.ReadFile(filePath)
		require.NoError(t, readErr, "Should be able to read the backup file")
		require.Equal(t, testData, string(content), "File content should match written data")
	})

	t.Run("GetBackupWriter_should_handle_multiple_files", func(t *testing.T) {
		// 创建测试备份记录
		record, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "test archival")
		require.Nil(t, err, "CreateBackupRecord should succeed")

		// 创建多个写入器
		dataWriter, backupErr := manager.GetBackupWriter(record, "data.sql.gz")
		require.Nil(t, backupErr, "GetBackupWriter for data should succeed")
		require.NotNil(t, dataWriter, "Data writer should not be nil")

		binlogWriter, backupErr := manager.GetBackupWriter(record, "binlog.sql.gz")
		require.Nil(t, backupErr, "GetBackupWriter for binlog should succeed")
		require.NotNil(t, binlogWriter, "Binlog writer should not be nil")

		// 写入不同的数据
		dataContent := "-- Data dump\nCREATE TABLE data (id INT);\n"
		binlogContent := "-- Binlog dump\nINSERT INTO data VALUES (1);\n"

		_, err1 := dataWriter.Write([]byte(dataContent))
		require.NoError(t, err1, "Data write should succeed")
		_, err2 := binlogWriter.Write([]byte(binlogContent))
		require.NoError(t, err2, "Binlog write should succeed")

		// 关闭写入器
		require.NoError(t, dataWriter.Close(), "Data writer close should succeed")
		require.NoError(t, binlogWriter.Close(), "Binlog writer close should succeed")

		// 验证两个文件都存在且内容正确
		dataPath := filepath.Join(record.Path, "data.sql.gz")
		binlogPath := filepath.Join(record.Path, "binlog.sql.gz")

		require.FileExists(t, dataPath, "Data file should exist")
		require.FileExists(t, binlogPath, "Binlog file should exist")

		dataRead, readErr := os.ReadFile(dataPath)
		require.NoError(t, readErr, "Should read data file")
		require.Equal(t, dataContent, string(dataRead), "Data content should match")

		binlogRead, readErr2 := os.ReadFile(binlogPath)
		require.NoError(t, readErr2, "Should read binlog file")
		require.Equal(t, binlogContent, string(binlogRead), "Binlog content should match")
	})

	t.Run("GetBackupWriter_should_handle_errors", func(t *testing.T) {
		// 测试 nil record
		writer, backupErr := manager.GetBackupWriter(nil, "data.sql.gz")
		require.Nil(t, writer, "Writer should be nil for nil record")
		require.NotNil(t, backupErr, "Should return error for nil record")
		require.Contains(t, backupErr.Error(), "备份记录不能为空", "Error should mention nil record")

		// 测试空路径的 record
		invalidRecord := &types.BackupRecord{
			ID:   "test-id",
			Path: "", // 空路径
		}
		writer, backupErr = manager.GetBackupWriter(invalidRecord, "data.sql.gz")
		require.Nil(t, writer, "Writer should be nil for record with empty path")
		require.NotNil(t, backupErr, "Should return error for record with empty path")
		require.Contains(t, backupErr.Error(), "路径不能为空", "Error should mention empty path")

		// 测试空文件名
		record, err := manager.CreateBackupRecord(types.MySQL, "test_db", types.BackupTypeArchival, "", "test")
		require.Nil(t, err, "CreateBackupRecord should succeed")
		writer, backupErr = manager.GetBackupWriter(record, "")
		require.Nil(t, writer, "Writer should be nil for empty filename")
		require.NotNil(t, backupErr, "Should return error for empty filename")
		require.Contains(t, backupErr.Error(), "文件名不能为空", "Error should mention empty filename")
	})

	t.Run("GetBackend_should_return_functional_backend", func(t *testing.T) {
		backend := manager.GetBackend()
		require.NotNil(t, backend, "GetBackend should return non-nil backend")

		// 测试 backend 的基本功能
		ctx := context.Background()

		// 测试 Put 操作
		testKey := "test/key"
		testData := "test data"
		_, err := backend.Put(ctx, testKey, strings.NewReader(testData))
		require.NoError(t, err, "Backend Put should work")

		// 测试 Get 操作
		reader, err := backend.Get(ctx, testKey)
		require.NoError(t, err, "Backend Get should work")
		defer reader.Close()

		data, err := io.ReadAll(reader)
		require.NoError(t, err, "Should be able to read from backend")
		require.Equal(t, testData, string(data), "Data should match")

		// 测试 Delete 操作
		err = backend.Delete(ctx, testKey)
		require.NoError(t, err, "Backend Delete should work")

		// 验证删除
		_, err = backend.Get(ctx, testKey)
		require.Error(t, err, "Get should fail after deletion")
	})
}
