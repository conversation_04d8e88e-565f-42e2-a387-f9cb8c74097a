package storage

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"gocloud.dev/blob"
	_ "gocloud.dev/blob/azureblob" // Azure Blob Storage
	_ "gocloud.dev/blob/gcsblob"   // Google Cloud Storage
	// 导入所有支持的云存储驱动
	_ "gocloud.dev/blob/s3blob" // AWS S3 和 S3 兼容存储（如 MinIO）

	"git.gobies.org/fobrain/unibackup/internal/storage/gocloud"
	"git.gobies.org/fobrain/unibackup/internal/storage/interfaces"
	"git.gobies.org/fobrain/unibackup/internal/storage/local"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"
)

// BackendType 定义支持的存储后端类型
type BackendType string

const (
	// BackendTypeLocal 本地文件系统存储
	BackendTypeLocal BackendType = "local"
	// BackendTypeS3 Amazon S3 存储（预留）
	BackendTypeS3 BackendType = "s3"
	// BackendTypeGCS Google Cloud Storage（预留）
	BackendTypeGCS BackendType = "gcs"
	// BackendTypeAzure Azure Blob Storage（预留）
	BackendTypeAzure BackendType = "azure"
)

// BackendConfig 存储后端配置接口
// 为不同类型的存储后端提供统一的配置抽象
type BackendConfig interface {
	// GetType 返回存储后端类型
	GetType() BackendType
	// Validate 验证配置的有效性
	Validate() error
}

// LocalBackendConfig 本地存储后端配置
type LocalBackendConfig struct {
	// BasePath 存储根目录路径
	BasePath string `json:"base_path" yaml:"base_path"`
}

// GetType 实现 BackendConfig 接口
func (c *LocalBackendConfig) GetType() BackendType {
	return BackendTypeLocal
}

// Validate 验证本地存储配置
func (c *LocalBackendConfig) Validate() error {
	if c.BasePath == "" {
		return interfaces.ErrInvalidKey // 复用现有错误类型
	}

	// 检查路径是否为绝对路径
	if !filepath.IsAbs(c.BasePath) {
		return fmt.Errorf("存储路径必须是绝对路径: %s", c.BasePath)
	}

	// 检查目录是否存在
	info, err := os.Stat(c.BasePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("存储目录不存在: %s", c.BasePath)
	}
	if err != nil {
		return fmt.Errorf("无法访问存储目录 %s: %w", c.BasePath, err)
	}
	if !info.IsDir() {
		return fmt.Errorf("存储路径不是目录: %s", c.BasePath)
	}

	return nil
}

// NewBackend 工厂函数，根据配置创建相应的存储后端
// 这是创建 Backend 实例的统一入口点，支持基于配置的类型选择
//
// 参数：
//
//	config: UniBackup 配置对象，包含存储相关配置
//	logger: 日志记录器，用于记录工厂操作日志
//
// 返回：
//
//	interfaces.Backend: 创建的存储后端实例
//	error: 创建过程中的错误
//
// 使用示例：
//
//	backend, err := storage.NewBackend(config, logger)
//	if err != nil {
//	    return fmt.Errorf("创建存储后端失败: %w", err)
//	}
func NewBackend(config *types.Config, logger *zap.Logger) (interfaces.Backend, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	// 验证基础配置
	if err := ValidateBackendConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 检查是否配置了云存储
	if hasCloudStorageConfig(config) {
		// 创建云存储后端
		backend, err := createCloudBackend(config.CloudStorage, logger)
		if err != nil {
			return nil, fmt.Errorf("创建云存储后端失败: %w", err)
		}

		logger.Info("存储后端创建成功",
			zap.String("type", "cloud"),
			zap.String("storage_type", config.CloudStorage.Type),
			zap.String("bucket", getBucketName(config.CloudStorage)))

		return backend, nil
	}

	// 默认创建本地存储后端
	backend, err := local.NewLocalBackend(config.BackupRoot)
	if err != nil {
		return nil, fmt.Errorf("创建本地存储后端失败: %w", err)
	}

	logger.Info("存储后端创建成功",
		zap.String("type", "local"),
		zap.String("path", config.BackupRoot))

	return backend, nil
}

// ValidateBackendConfig 验证存储后端配置的完整性
// 确保配置满足创建 Backend 的所有要求
//
// 参数：
//
//	config: UniBackup 配置对象
//
// 返回：
//
//	error: 验证失败时的错误信息
func ValidateBackendConfig(config *types.Config) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 检查是否启用了云存储
	if hasCloudStorageConfig(config) {
		// 云存储模式：只验证云存储配置，不验证本地目录存在性
		if config.BackupRoot == "" {
			// 云存储模式下，BackupRoot 用于路径生成，设置默认值
			config.BackupRoot = "/tmp/unibackup"
		}
		// 不验证本地目录的物理存在性，因为主要使用云存储
		return nil
	}

	// 本地存储模式：验证本地存储配置
	if config.BackupRoot == "" {
		return fmt.Errorf("备份根目录不能为空")
	}

	// 创建本地存储配置并验证（包括目录存在性检查）
	localConfig := &LocalBackendConfig{
		BasePath: config.BackupRoot,
	}

	if err := localConfig.Validate(); err != nil {
		return fmt.Errorf("本地存储配置无效: %w", err)
	}

	return nil
}

// hasCloudStorageConfig 检查配置中是否包含云存储配置
// 这是一个内部辅助函数，用于检测云存储配置的存在
func hasCloudStorageConfig(config *types.Config) bool {
	return config.CloudStorage != nil && config.CloudStorage.Enabled
}

// GetSupportedBackendTypes 返回当前支持的存储后端类型列表
// 用于配置验证和用户界面显示
func GetSupportedBackendTypes() []BackendType {
	return []BackendType{
		BackendTypeLocal,
		// 云存储类型将在后续版本中添加
		// BackendTypeS3,
		// BackendTypeGCS,
		// BackendTypeAzure,
	}
}

// IsBackendTypeSupported 检查指定的存储后端类型是否受支持
func IsBackendTypeSupported(backendType BackendType) bool {
	supportedTypes := GetSupportedBackendTypes()
	for _, supported := range supportedTypes {
		if supported == backendType {
			return true
		}
	}
	return false
}

// createCloudBackend 创建云存储后端
func createCloudBackend(config *types.CloudStorageConfig, logger *zap.Logger) (interfaces.Backend, error) {
	// 创建云存储 bucket
	bucket, err := createCloudBucket(context.Background(), config)
	if err != nil {
		return nil, fmt.Errorf("创建云存储bucket失败: %w", err)
	}

	// 使用新的GocloudBackend适配器
	backend := gocloud.NewGocloudBackend(bucket, logger)

	return backend, nil
}

// getBucketName 根据云存储类型获取存储桶名称
func getBucketName(config *types.CloudStorageConfig) string {
	switch config.Type {
	case "s3", "gcs":
		return config.Bucket
	case "azure":
		return config.Container
	default:
		return ""
	}
}

// buildGocloudURL 根据配置构建 gocloud URL
func buildGocloudURL(config *types.CloudStorageConfig) (string, error) {
	switch config.Type {
	case "s3":
		url := fmt.Sprintf("s3://%s?region=%s", config.Bucket, config.Region)
		if config.Endpoint != "" {
			url += "&endpoint=" + config.Endpoint
			// 当使用自定义端点时（如MinIO），强制使用路径风格URL
			url += "&s3ForcePathStyle=true"
		}
		return url, nil
	case "gcs":
		// GCS URL 格式：gs://bucket
		// ProjectID 通过环境变量 GOOGLE_CLOUD_PROJECT 或凭据文件设置
		return fmt.Sprintf("gs://%s", config.Bucket), nil
	case "azure":
		return fmt.Sprintf("azblob://%s", config.Container), nil
	default:
		return "", fmt.Errorf("不支持的云存储类型: %s", config.Type)
	}
}

// setupCloudAuth 设置云存储认证
//
// 注意：对于ES Provider，认证信息需要在ES进程层面配置：
// - 环境变量：在ES容器启动时设置 AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
// - keystore：在ES容器中执行 elasticsearch-keystore add s3.client.default.access_key
//
// 这里只处理云存储后端（gocloud.dev）的认证，不影响ES的S3仓库认证
func setupCloudAuth(ctx context.Context, config *types.CloudStorageConfig) (context.Context, error) {
	switch config.Type {
	case "s3":
		if config.AccessKey != "" && config.SecretKey != "" {
			// 设置 AWS 凭据环境变量（仅用于云存储后端，不影响ES）
			os.Setenv("AWS_ACCESS_KEY_ID", config.AccessKey)
			os.Setenv("AWS_SECRET_ACCESS_KEY", config.SecretKey)
		}
	case "gcs":
		if config.CredentialsFile != "" {
			// 设置 GCS 凭据文件路径
			os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", config.CredentialsFile)
		}
		if config.ProjectID != "" {
			// 设置 GCS 项目 ID
			os.Setenv("GOOGLE_CLOUD_PROJECT", config.ProjectID)
		}
	case "azure":
		if config.AccountName != "" && config.AccountKey != "" {
			// 设置 Azure 凭据环境变量
			os.Setenv("AZURE_STORAGE_ACCOUNT", config.AccountName)
			os.Setenv("AZURE_STORAGE_KEY", config.AccountKey)
		}
	}
	return ctx, nil
}

// createCloudBucket 创建云存储 bucket
func createCloudBucket(ctx context.Context, config *types.CloudStorageConfig) (*blob.Bucket, error) {
	// 设置认证
	authCtx, err := setupCloudAuth(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("设置认证失败: %w", err)
	}

	// 构建 URL
	bucketURL, err := buildGocloudURL(config)
	if err != nil {
		return nil, fmt.Errorf("构建URL失败: %w", err)
	}

	// 创建 bucket
	bucket, err := blob.OpenBucket(authCtx, bucketURL)
	if err != nil {
		return nil, fmt.Errorf("打开云存储失败: %w", err)
	}

	return bucket, nil
}
