package config

import (
	"os"
	"testing"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidate(t *testing.T) {
	// Helper to create a temporary directory for valid config tests
	tempDir, err := os.MkdirTemp("", "config_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Helper to create a temporary file for invalid config tests
	tempFile, err := os.CreateTemp("", "config_test_file_")
	require.NoError(t, err)
	defer os.Remove(tempFile.Name())

	validMySQL := &types.MySQLConfig{
		Host:     "localhost",
		Port:     3306,
		User:     "root",
		Password: "password",
		DBName:   "test",
		ToolsPath: types.MySQLToolsPath{
			Mysqldump:   "/usr/bin/mysqldump",
			Mysql:       "/usr/bin/mysql",
			Mysqlbinlog: "/usr/bin/mysqlbinlog",
			Mysqladmin:  "/usr/bin/mysqladmin",
		},
	}
	validES := &types.ESConfig{Addresses: []string{"http://localhost:9200"}, ArchivalRepoName: "a", ManagedRepoName: "b"}

	testCases := []struct {
		name        string
		config      *types.Config
		expectError bool
	}{
		{
			name:        "Valid Config",
			config:      &types.Config{BackupRoot: tempDir, ES: validES}, // 跳过MySQL工具验证
			expectError: false,
		},
		{
			name:        "Missing BackupRoot",
			config:      &types.Config{BackupRoot: "", MySQL: validMySQL},
			expectError: true,
		},
		{
			name:        "BackupRoot Does Not Exist",
			config:      &types.Config{BackupRoot: "/path/to/non/existent/dir", MySQL: validMySQL},
			expectError: true,
		},
		{
			name:        "BackupRoot Is a File",
			config:      &types.Config{BackupRoot: tempFile.Name(), MySQL: validMySQL},
			expectError: true,
		},
		{
			name:        "No Data Sources",
			config:      &types.Config{BackupRoot: tempDir},
			expectError: true,
		},
		{
			name:        "Incomplete MySQL Config",
			config:      &types.Config{BackupRoot: tempDir, MySQL: &types.MySQLConfig{User: "root"}},
			expectError: true,
		},
		{
			name:        "Incomplete ES Config - No Addresses",
			config:      &types.Config{BackupRoot: tempDir, ES: &types.ESConfig{ArchivalRepoName: "a", ManagedRepoName: "b"}},
			expectError: true,
		},
		{
			name:        "Incomplete ES Config - No Repo",
			config:      &types.Config{BackupRoot: tempDir, ES: &types.ESConfig{Addresses: []string{"http://localhost:9200"}}},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := Validate(tc.config)
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestSetDefaults(t *testing.T) {
	t.Run("Default Logger", func(t *testing.T) {
		cfg := &types.Config{}
		SetDefaults(cfg)
		assert.NotNil(t, cfg.Logger)
	})

	t.Run("Logger is Preserved if Provided", func(t *testing.T) {
		customLogger := zap.NewNop()
		cfg := &types.Config{
			Logger: customLogger,
		}
		SetDefaults(cfg)
		assert.Same(t, customLogger, cfg.Logger)
	})

	t.Run("Default values are set when not provided", func(t *testing.T) {
		cfg := &types.Config{}
		SetDefaults(cfg)
		// SetDefaults 设置默认值
		assert.Equal(t, 5, cfg.MaxConcurrentTasks)
		assert.Equal(t, 365, cfg.TaskRetentionDays)
		assert.Equal(t, 0, cfg.MaxTaskHistory)
		assert.False(t, cfg.CleanupBackupData) // 默认不清理备份数据
		assert.NotNil(t, cfg.Logger)           // Logger 应该被设置
	})

	t.Run("Preserve Existing Values", func(t *testing.T) {
		customLogger := zap.NewNop()
		cfg := &types.Config{
			MaxConcurrentTasks: 10,
			TaskRetentionDays:  14,
			MaxTaskHistory:     500,
			Logger:             customLogger,
		}
		SetDefaults(cfg)
		assert.Equal(t, 10, cfg.MaxConcurrentTasks)
		assert.Equal(t, 14, cfg.TaskRetentionDays)
		assert.Equal(t, 500, cfg.MaxTaskHistory)
		assert.Same(t, customLogger, cfg.Logger) // 自定义 Logger 应该被保留
	})
}

// TestValidate_MySQL 测试 MySQL 配置验证
func TestValidate_MySQL(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "config_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	testCases := []struct {
		name        string
		config      *types.Config
		expectError bool
	}{
		{
			name: "Valid MySQL Config",
			config: &types.Config{
				BackupRoot: tempDir,
				ES: &types.ESConfig{
					Addresses:        []string{"http://localhost:9200"},
					ArchivalRepoName: "test-archival",
					ManagedRepoName:  "test-managed",
				},
			},
			expectError: false,
		},
		{
			name: "Missing MySQL Host",
			config: &types.Config{
				BackupRoot: tempDir,
				MySQL: &types.MySQLConfig{
					Port:     3306,
					User:     "root",
					Password: "password",
					DBName:   "test",
				},
			},
			expectError: true,
		},
		{
			name: "Invalid MySQL Port",
			config: &types.Config{
				BackupRoot: tempDir,
				MySQL: &types.MySQLConfig{
					Host:     "localhost",
					Port:     0,
					User:     "root",
					Password: "password",
					DBName:   "test",
				},
			},
			expectError: true,
		},
		{
			name: "Missing MySQL User",
			config: &types.Config{
				BackupRoot: tempDir,
				MySQL: &types.MySQLConfig{
					Host:     "localhost",
					Port:     3306,
					Password: "password",
					DBName:   "test",
				},
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := Validate(tc.config)
			if tc.expectError {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestValidate_Elasticsearch 测试 Elasticsearch 配置验证
func TestValidate_Elasticsearch(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "config_test_*")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	testCases := []struct {
		name        string
		config      *types.Config
		expectError bool
	}{
		{
			name: "Valid ES Config",
			config: &types.Config{
				BackupRoot: tempDir,
				ES: &types.ESConfig{
					Addresses:        []string{"http://localhost:9200"},
					ArchivalRepoName: "archival",
					ManagedRepoName:  "managed",
				},
			},
			expectError: false,
		},
		{
			name: "Missing ES Addresses",
			config: &types.Config{
				BackupRoot: tempDir,
				ES: &types.ESConfig{
					ArchivalRepoName: "archival",
					ManagedRepoName:  "managed",
				},
			},
			expectError: true,
		},
		{
			name: "Empty ES Addresses",
			config: &types.Config{
				BackupRoot: tempDir,
				ES: &types.ESConfig{
					Addresses:        []string{},
					ArchivalRepoName: "archival",
					ManagedRepoName:  "managed",
				},
			},
			expectError: true,
		},
		{
			name: "Missing ArchivalRepoName",
			config: &types.Config{
				BackupRoot: tempDir,
				ES: &types.ESConfig{
					Addresses:       []string{"http://localhost:9200"},
					ManagedRepoName: "managed",
				},
			},
			expectError: true,
		},
		{
			name: "Missing ManagedRepoName",
			config: &types.Config{
				BackupRoot: tempDir,
				ES: &types.ESConfig{
					Addresses:        []string{"http://localhost:9200"},
					ArchivalRepoName: "archival",
				},
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := Validate(tc.config)
			if tc.expectError {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}
