package config

import (
	"fmt"
	"net"
	"os"
	"os/exec"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/errors"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"github.com/shirou/gopsutil/v3/disk" // 新增
	"go.uber.org/zap"
)

// 内部超常量，用于系统配置，不应对外暴露
const (
	// DefaultLockTimeout 默认的锁获取超时时间
	DefaultLockTimeout = 30 * time.Second

	// TestEnvironmentThreshold 用于检测测试环境的备份超时阈值
	// 当BackupTimeout小于等于此值时，认为是测试环境
	TestEnvironmentThreshold = 100 * time.Millisecond

	// TestEnvironmentWaitTime 测试环境下的等待时间
	TestEnvironmentWaitTime = 10 * time.Millisecond

	// DefaultShutdownWaitTime 生产环境下的默认关闭等待时间
	DefaultShutdownWaitTime = 2 * time.Second
)

// Validate 函数负责对SDK的配置进行全面的静态校验。
// 它是保证SDK能够正确、安全启动的第一道防线，旨在启动早期就捕获所有可预见的配置错误。
func Validate(cfg *types.Config) *types.BackupError {
	// 检查点 1: 检查云存储配置
	if cfg.CloudStorage != nil && cfg.CloudStorage.Enabled {
		// 云存储模式：验证云存储配置
		if err := validateCloudStorageConfig(cfg.CloudStorage); err != nil {
			return err
		}

		// 云存储模式下，BackupRoot 用于路径生成，可以设置默认值
		if cfg.BackupRoot == "" {
			cfg.BackupRoot = "/tmp/unibackup"
		}
		// 不验证目录的物理存在性和目录类型
	} else {
		// 本地存储模式：保持现有验证逻辑
		if cfg.BackupRoot == "" {
			return newConfigError("CONFIG_VALIDATION_FAILED", "配置项 'BackupRoot' 是必填项，不能为空")
		}

		// 验证 BackupRoot 目录的物理状态
		info, err := os.Stat(cfg.BackupRoot)
		if os.IsNotExist(err) {
			return newConfigError("CONFIG_VALIDATION_FAILED", fmt.Sprintf("备份根目录 '%s' 不存在", cfg.BackupRoot))
		}
		if err != nil {
			return newConfigError("CONFIG_VALIDATION_FAILED", fmt.Sprintf("无法获取备份根目录 '%s' 的状态: %v", cfg.BackupRoot, err))
		}
		if !info.IsDir() {
			return newConfigError("CONFIG_VALIDATION_FAILED", fmt.Sprintf("指定的备份根路径 '%s' 不是一个目录", cfg.BackupRoot))
		}

		// 检查备份根目录的磁盘空间
		if err := checkDiskSpace(cfg.BackupRoot); err != nil {
			return err
		}
	}

	// 检查点 3: 允许启动时不配置数据源，支持动态配置更新
	// 注释掉强制检查，支持运行时动态添加数据源
	// if cfg.MySQL == nil && cfg.ES == nil {
	//     return newConfigError("CONFIG_VALIDATION_FAILED", "必须至少配置一个数据源 (MySQL 或 ES)")
	// }

	// 检查点 4: 如果配置了MySQL，则深入校验其内部必填项。
	if cfg.MySQL != nil {
		if err := validateMySQLConfig(cfg.MySQL); err != nil {
			return err
		}
	}

	// 检查点 5: 如果配置了Elasticsearch，则校验其内部必填项。
	if cfg.ES != nil {
		if err := validateESConfig(cfg.ES); err != nil {
			return err
		}
	}

	return nil
}

// validateMySQLConfig 验证MySQL配置的详细信息
func validateMySQLConfig(cfg *types.MySQLConfig) *types.BackupError {
	// 检查必填字段
	if cfg.Host == "" {
		return newConfigError("MYSQL_CONFIG_INVALID", "MySQL配置错误: Host字段不能为空")
	}
	if cfg.User == "" {
		return newConfigError("MYSQL_CONFIG_INVALID", "MySQL配置错误: User字段不能为空")
	}
	if cfg.DBName == "" {
		return newConfigError("MYSQL_CONFIG_INVALID", "MySQL配置错误: DBName字段不能为空")
	}

	// 检查端口范围
	if cfg.Port <= 0 || cfg.Port > 65535 {
		return newConfigError("MYSQL_CONFIG_INVALID",
			fmt.Sprintf("MySQL配置错误: Port必须在1-65535范围内，当前值: %d", cfg.Port))
	}

	// 检查主机名格式
	if err := validateHostname(cfg.Host); err != nil {
		return newConfigError("MYSQL_CONFIG_INVALID", fmt.Sprintf("MySQL配置错误: Host字段格式无效: %v", err))
	}

	// 检查并自动发现MySQL工具路径
	if err := checkAndSetToolPath(&cfg.ToolsPath.Mysqldump, "mysqldump"); err != nil {
		return err
	}
	if err := checkAndSetToolPath(&cfg.ToolsPath.Mysql, "mysql"); err != nil {
		return err
	}
	if err := checkAndSetToolPath(&cfg.ToolsPath.Mysqlbinlog, "mysqlbinlog"); err != nil {
		return err
	}
	if err := checkAndSetToolPath(&cfg.ToolsPath.Mysqladmin, "mysqladmin"); err != nil {
		return err
	}

	return nil
}

// validateESConfig 验证Elasticsearch配置的详细信息
func validateESConfig(cfg *types.ESConfig) *types.BackupError {
	// 检查地址列表
	if len(cfg.Addresses) == 0 {
		return newConfigError("ES_CONFIG_INVALID", "Elasticsearch配置错误: Addresses列表不能为空")
	}

	// 验证每个地址格式
	for i, addr := range cfg.Addresses {
		if addr == "" {
			return newConfigError("ES_CONFIG_INVALID",
				fmt.Sprintf("Elasticsearch配置错误: Addresses[%d] 不能为空", i))
		}
		// 使用增强的地址验证
		if err := validateESAddress(addr); err != nil {
			return newConfigError("ES_CONFIG_INVALID",
				fmt.Sprintf("Elasticsearch配置错误: Addresses[%d] '%s' 格式无效: %v", i, addr, err))
		}
	}

	// 检查仓库名称
	if cfg.ArchivalRepoName == "" {
		return newConfigError("ES_CONFIG_INVALID",
			"Elasticsearch配置错误: ArchivalRepoName不能为空。建议使用如'unibackup-archival'的描述性名称")
	}
	if cfg.ManagedRepoName == "" {
		return newConfigError("ES_CONFIG_INVALID",
			"Elasticsearch配置错误: ManagedRepoName不能为空。建议使用如'unibackup-managed'的描述性名称")
	}

	// 检查仓库名称是否相同
	if cfg.ArchivalRepoName == cfg.ManagedRepoName {
		return newConfigError("ES_CONFIG_INVALID",
			"Elasticsearch配置错误: ArchivalRepoName和ManagedRepoName不能相同，它们用于不同的备份策略")
	}

	// 检查认证配置
	if cfg.APIKey != "" && (cfg.User != "" || cfg.Password != "") {
		return newConfigError("ES_CONFIG_INVALID",
			"Elasticsearch配置错误: 不能同时配置APIKey和User/Password认证，请选择其中一种")
	}

	return nil
}

// checkAndSetToolPath 检查工具路径，如果为空则自动查找，并更新路径变量
func checkAndSetToolPath(toolPath *string, toolName string) *types.BackupError {
	// 如果用户提供了路径，则验证该路径
	if *toolPath != "" {
		info, err := os.Stat(*toolPath)
		if os.IsNotExist(err) {
			return newConfigError("MYSQL_TOOLS_NOT_FOUND",
				fmt.Sprintf("MySQL工具未找到: '%s' 在指定路径 '%s' 不存在", toolName, *toolPath))
		}
		if err != nil {
			return newConfigError("MYSQL_TOOLS_ERROR",
				fmt.Sprintf("无法获取MySQL工具 '%s' 的状态: %v", toolName, err))
		}
		if info.IsDir() {
			return newConfigError("MYSQL_TOOLS_INVALID",
				fmt.Sprintf("MySQL工具路径 '%s' 是一个目录，不是一个文件", *toolPath))
		}
		// 检查可执行权限
		if info.Mode()&0111 == 0 {
			return newConfigError("MYSQL_TOOLS_NOT_EXECUTABLE",
				fmt.Sprintf("MySQL工具 '%s' 在路径 '%s' 不可执行", toolName, *toolPath))
		}
		return nil // 用户提供的路径有效
	}

	// 如果用户未提供路径，则在系统PATH中查找
	path, err := exec.LookPath(toolName)
	if err != nil {
		return newConfigError("MYSQL_TOOLS_NOT_FOUND",
			fmt.Sprintf("在系统PATH中找不到MySQL工具 '%s'。请在配置文件中指定其完整路径，或确保它在PATH环境变量中", toolName))
	}

	// 找到了，更新配置中的路径
	*toolPath = path
	return nil
}

// newConfigError 是一个内部辅助函数，用于创建内容统一、结构化的配置相关错误。
// 这确保了所有从配置验证中返回的错误都遵循相同的格式。
func newConfigError(code, message string) *types.BackupError {
	return &types.BackupError{
		Code:      code,
		Message:   message,
		Component: "Config",
		Operation: "Validate",
		Timestamp: time.Now(),
		Retryable: false, // 配置错误通常是不可重试的，需要人工干预修正。
	}
}

// SetDefaults 函数为配置结构体中的可选字段设置合理的、经过深思熟虑的默认值。
// 这一实践极大地简化了用户的配置过程，并确保了系统在缺少可选配置时，其行为仍然是一致和可预测的。
func SetDefaults(cfg *types.Config) {
	// 如果调用者没有提供自定义的日志记录器，则默认将所有SDK内部日志丢弃。
	// 这样做是为了避免在SDK内部因logger实例为nil而引发panic，保证了库的健壮性。
	// 如果调用者需要查看SDK的日志，必须显式地提供一个zap.Logger实例。
	if cfg.Logger == nil {
		cfg.Logger = zap.NewNop()
	}

	// 设置任务管理相关默认值
	if cfg.MaxConcurrentTasks <= 0 {
		cfg.MaxConcurrentTasks = 5
	}
	if cfg.TaskRetentionDays <= 0 {
		cfg.TaskRetentionDays = 365 // 默认保留一年
	}
	if cfg.MaxTaskHistory < 0 {
		cfg.MaxTaskHistory = 0 // 默认不限制数量，只按时间清理
	}
	// CleanupBackupData 默认为 false（Go的零值），不需要设置

	// 设置超时相关默认值
	if cfg.BackupTimeout <= 0 {
		cfg.BackupTimeout = types.Duration(24 * time.Hour)
	}
	if cfg.RestoreTimeout <= 0 {
		cfg.RestoreTimeout = types.Duration(24 * time.Hour)
	}

	// 设置ES配置默认值
	if cfg.ES != nil {
		setESDefaults(cfg.ES)
	}
}

func setESDefaults(cfg *types.ESConfig) {
	// 设置默认仓库名称
	if cfg.ArchivalRepoName == "" {
		cfg.ArchivalRepoName = "unibackup-archival"
	}
	if cfg.ManagedRepoName == "" {
		cfg.ManagedRepoName = "unibackup-managed"
	}

	// 设置AutoCreateRepos默认值为true
	// 由于Go中bool的零值是false，我们无法区分"用户未设置"和"用户显式设置为false"
	// 作为权衡，我们采用以下策略：
	// 1. 如果用户在代码中创建ESConfig时没有设置AutoCreateRepos，它将是false（零值）
	// 2. 在SetDefaults中，我们将其设置为true，这是对大多数用户友好的默认行为
	// 3. 如果用户真的想要禁用自动创建，他们需要在调用SetDefaults之后显式设置为false
	// 这不是完美的解决方案，但在不改变API的情况下是最实用的
	cfg.AutoCreateRepos = true
}

// checkDiskSpace 检查指定路径的磁盘空间，确保有足够的空间进行备份操作
func checkDiskSpace(path string) *types.BackupError {
	// 1. 获取磁盘使用情况
	usage, err := disk.Usage(path)
	if err != nil {
		return newConfigError("DISK_SPACE_CHECK_FAILED",
			fmt.Sprintf("无法获取备份根目录 '%s' 的磁盘使用情况: %v", path, err))
	}

	// 计算磁盘空间信息
	totalBytes := usage.Total
	freeBytes := usage.Free
	usedBytes := usage.Used

	// 转换为更易读的单位
	totalGB := float64(totalBytes) / (1024 * 1024 * 1024)
	freeGB := float64(freeBytes) / (1024 * 1024 * 1024)
	usedGB := float64(usedBytes) / (1024 * 1024 * 1024)
	usagePercent := usage.UsedPercent

	// 检查可用空间是否足够（最少需要1GB）
	const minRequiredGB = 1.0
	if freeGB < minRequiredGB {
		return newConfigError("INSUFFICIENT_DISK_SPACE",
			fmt.Sprintf("备份根目录 '%s' 磁盘空间不足。总空间: %.2fGB, 已用: %.2fGB (%.1f%%), 可用: %.2fGB, 最少需要: %.1fGB",
				path, totalGB, usedGB, usagePercent, freeGB, minRequiredGB))
	}

	// 检查使用率是否过高（警告阈值95%）
	const warningThreshold = 95.0
	if usagePercent >= warningThreshold {
		return newConfigError("DISK_SPACE_WARNING",
			fmt.Sprintf("备份根目录 '%s' 磁盘使用率过高。总空间: %.2fGB, 已用: %.2fGB (%.1f%%), 可用: %.2fGB, 建议保持使用率在95%%以下",
				path, totalGB, usedGB, usagePercent, freeGB))
	}

	// 2. 测试写入权限
	tmpFile, err := os.CreateTemp(path, "unibackup-diskcheck-*.tmp")
	if err != nil {
		return newConfigError("DISK_WRITE_PERMISSION_FAILED",
			fmt.Sprintf("无法在备份根目录 '%s' 中创建临时文件，请检查目录权限: %v", path, err))
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// 写入测试数据
	testData := []byte("unibackup disk space test")
	if _, err := tmpFile.Write(testData); err != nil {
		return newConfigError("DISK_WRITE_PERMISSION_FAILED",
			fmt.Sprintf("无法写入备份根目录 '%s'，请检查磁盘空间和权限: %v", path, err))
	}

	return nil
}

// validateHostname 验证主机名格式（简化版）
func validateHostname(hostname string) error {
	// 基本检查：不能为空
	if hostname == "" {
		return fmt.Errorf("主机名不能为空")
	}

	// 简单的长度检查
	if len(hostname) > 253 {
		return fmt.Errorf("主机名过长")
	}

	// 处理常见情况
	if hostname == "localhost" {
		return nil
	}

	// 检查是否是有效的IP地址
	if net.ParseIP(hostname) != nil {
		return nil
	}

	// 对于域名，只做基本的字符检查
	if strings.Contains(hostname, " ") {
		return fmt.Errorf("主机名不能包含空格")
	}

	return nil
}

// validateESAddress 验证Elasticsearch地址格式（简化版）
func validateESAddress(address string) error {
	// 检查是否包含协议
	if !strings.HasPrefix(address, "http://") && !strings.HasPrefix(address, "https://") {
		return fmt.Errorf("Elasticsearch地址必须包含协议(http://或https://)")
	}

	// 基本的格式检查
	if len(address) < 10 { // 至少要有 http://a:1
		return fmt.Errorf("地址格式无效")
	}

	return nil
}

// validateCloudStorageConfig 验证云存储配置的详细信息
func validateCloudStorageConfig(cfg *types.CloudStorageConfig) *types.BackupError {
	// 检查必填字段
	if cfg.Type == "" {
		return errors.NewConfigError("CLOUD_STORAGE_CONFIG_INVALID", "云存储配置错误: Type字段不能为空")
	}

	// 根据类型验证相应的必填字段
	switch cfg.Type {
	case "s3":
		if cfg.Bucket == "" {
			return errors.NewConfigError("S3_CONFIG_INVALID", "S3配置错误: Bucket字段不能为空")
		}
		if cfg.Region == "" {
			return errors.NewConfigError("S3_CONFIG_INVALID", "S3配置错误: Region字段不能为空")
		}
		// AccessKey 和 SecretKey 可以为空，此时使用默认凭据链
	case "gcs":
		if cfg.Bucket == "" {
			return errors.NewConfigError("GCS_CONFIG_INVALID", "GCS配置错误: Bucket字段不能为空")
		}
		// ProjectID 和 CredentialsFile 可以为空，此时使用默认凭据
	case "azure":
		if cfg.Container == "" {
			return errors.NewConfigError("AZURE_CONFIG_INVALID", "Azure配置错误: Container字段不能为空")
		}
		if cfg.AccountName == "" {
			return errors.NewConfigError("AZURE_CONFIG_INVALID", "Azure配置错误: AccountName字段不能为空")
		}
		// AccountKey 可以为空，此时使用默认凭据
	default:
		return errors.NewConfigError("CLOUD_STORAGE_CONFIG_INVALID",
			fmt.Sprintf("云存储配置错误: 不支持的存储类型 '%s'，支持的类型: s3, gcs, azure", cfg.Type))
	}

	return nil
}

// ValidateCloudStorageConfig 导出的云存储配置验证函数
func ValidateCloudStorageConfig(cfg *types.CloudStorageConfig) *types.BackupError {
	return validateCloudStorageConfig(cfg)
}
