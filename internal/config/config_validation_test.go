package config

import (
	"os"
	"path/filepath"
	"testing"
	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/assert"
)

// TestMySQLToolPathValidation tests the automatic discovery and validation of MySQL tool paths.
func TestMySQLToolPathValidation(t *testing.T) {
	// Create a temporary directory for mock tools
	tempDir := t.TempDir()
	mockTools := []string{"mysqldump", "mysql", "mysqlbinlog", "mysqladmin"}
	for _, tool := range mockTools {
		// Create an empty, executable file
		mockPath := filepath.Join(tempDir, tool)
		file, err := os.Create(mockPath)
		assert.NoError(t, err)
		file.Close()
		err = os.Chmod(mockPath, 0755)
		assert.NoError(t, err)
	}

	// --- Case 1: Tool paths are NOT provided, but tools exist in PATH ---
	t.Run("ToolsInPath", func(t *testing.T) {
		// Add the temp directory to the PATH
		originalPath := os.Getenv("PATH")
		os.Setenv("PATH", tempDir+string(os.PathListSeparator)+originalPath)
		defer os.Setenv("PATH", originalPath)

		cfg := &types.MySQLConfig{
			Host:   "localhost",
			User:   "test",
			DBName: "testdb",
			Port:   3306,
			// ToolsPath is empty
		}

		err := validateMySQLConfig(cfg)
		assert.Nil(t, err, "Expected validation to pass when tools are in PATH")

		// Check if paths were auto-filled
		assert.NotEmpty(t, cfg.ToolsPath.Mysqldump, "Expected mysqldump path to be auto-filled")
		assert.Contains(t, cfg.ToolsPath.Mysqldump, "mysqldump")
	})

	// --- Case 2: Tool paths are NOT provided and tools are NOT in PATH ---
	t.Run("ToolsNotInPath", func(t *testing.T) {
		// Use a clean PATH that doesn't have the tools
		originalPath := os.Getenv("PATH")
		os.Setenv("PATH", "/bin:/usr/bin") // A path unlikely to contain mysql tools
		defer os.Setenv("PATH", originalPath)

		cfg := &types.MySQLConfig{
			Host:   "localhost",
			User:   "test",
			DBName: "testdb",
			Port:   3306,
		}

		err := validateMySQLConfig(cfg)
		assert.NotNil(t, err, "Expected validation to fail when tools are not in PATH")
		assert.Equal(t, "MYSQL_TOOLS_NOT_FOUND", err.Code)
		assert.Contains(t, err.Message, "找不到MySQL工具 'mysqldump'")
	})

	// --- Case 3: Tool path IS provided and is valid ---
	t.Run("ValidProvidedPath", func(t *testing.T) {
		cfg := &types.MySQLConfig{
			Host:   "localhost",
			User:   "test",
			DBName: "testdb",
			Port:   3306,
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   filepath.Join(tempDir, "mysqldump"),
				Mysql:       filepath.Join(tempDir, "mysql"),
				Mysqlbinlog: filepath.Join(tempDir, "mysqlbinlog"),
				Mysqladmin:  filepath.Join(tempDir, "mysqladmin"),
			},
		}

		err := validateMySQLConfig(cfg)
		assert.Nil(t, err, "Expected validation to pass with valid provided paths")
	})

	// --- Case 4: Tool path IS provided but is INVALID ---
	t.Run("InvalidProvidedPath", func(t *testing.T) {
		cfg := &types.MySQLConfig{
			Host:   "localhost",
			User:   "test",
			DBName: "testdb",
			Port:   3306,
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/invalid/path/to/mysqldump",
				Mysql:       filepath.Join(tempDir, "mysql"),
				Mysqlbinlog: filepath.Join(tempDir, "mysqlbinlog"),
				Mysqladmin:  filepath.Join(tempDir, "mysqladmin"),
			},
		}

		err := validateMySQLConfig(cfg)
		assert.NotNil(t, err, "Expected validation to fail with an invalid provided path")
		assert.Equal(t, "MYSQL_TOOLS_NOT_FOUND", err.Code)
	})
}
