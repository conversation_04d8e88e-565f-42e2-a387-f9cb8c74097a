package provider

import (
	"context"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// Package provider 定义了所有数据源提供者必须遵守的接口和共享逻辑。
// 它是 UniBackup 扩展性的基石，通过定义一个统一的契约，使得添加新的数据源（如 PostgreSQL, MongoDB）成为可能，而无需修改核心的任务调度和存储管理逻辑。

// BackupProvider [[memory:1940293]] 是所有数据源提供者（Provider）都必须实现的接口。
// 它定义了备份、恢复、列出和删除备份的核心操作，将特定数据源的复杂实现细节与上层的任务编排逻辑（task.Manager）完全解耦。
//
// 职责划分原则：
// - Provider 专注于与特定数据源（如MySQL、ES）进行交互的"How"（如何执行）。
// - 上层管理器（如task.Manager, storage.Manager）专注于"What"和"When"（执行什么任务、何时执行），并处理通用的业务逻辑，如并发控制、状态管理、元数据读写等。
type BackupProvider interface {
	// Backup 执行一次备份操作。
	// Provider 的实现需要根据传入的 `backupRecord.Type` 来决定具体的备份行为（归档全量、链初始全量、或增量）。
	// 对于增量备份，`prevRecord` 参数会包含上一次的备份元数据，Provider 可以用它来确定增量备份的起点。
	// Provider 不关心数据最终存储在哪里，它只需从 `backupRecord.Path` 获取路径并写入备份产物即可。
	Backup(ctx context.Context, backupRecord *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError

	// Restore 执行恢复操作，使用完全重建策略。
	// 对于增量链的恢复，上层的 `task.Manager` 会按正确的顺序、多次调用此方法，每次传入一个备份点。
	// `prevRecord` 参数同样包含了恢复链中前一个备份的元数据，可用于增量恢复的上下文。
	//
	// 重构后的行为（完全重建策略）：
	// - MySQL: 删除并重建目标数据库，然后恢复数据
	// - Elasticsearch: 删除所有现有索引，然后恢复快照
	//
	// 这种方法确保恢复到确定的、干净的状态，避免数据冲突问题。
	Restore(ctx context.Context, backupRecord *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError

	// List 返回该提供者管理的所有备份记录的元数据列表。
	// 注意：此方法主要用于那些将元数据存储在外部系统（例如 Elasticsearch 的快照仓库）的 Provider。
	// 对于像 MySQL（文件备份模式）这样将元数据与文件一同存储在本地文件系统的 Provider，此方法可能返回空列表，
	// 因为其列表功能完全由 `storage.Manager` 通过扫描文件系统来实现。
	List(ctx context.Context) ([]*types.BackupRecord, *types.BackupError)

	// Delete 执行特定于 Provider 的删除操作。
	// 例如，在 Elasticsearch 中，它会调用 API 删除对应的快照。
	// 对于纯文件系统备份的 Provider，此方法可能是一个空操作（no-op），因为文件的物理删除由 `storage.Manager` 负责。
	//
	// !!! 重要 !!!
	// 此接口的实现不应包含任何业务级别的安全逻辑，比如"防止删除增量链的一部分"。
	// 这种关键的安全约束和生命周期管理逻辑由上层的 `storage.Manager` 统一处理，以确保策略的一致性。
	Delete(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError
	// CheckHealth 检查数据源的健康状态。
	// Provider 负责执行与特定数据源相关的健康检查（例如，连接数据库，检查集群状态）。
	CheckHealth(ctx context.Context) *types.BackupError
}
