package provider

import (
	"context"
	"testing"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockBackupProvider 模拟备份提供者，用于测试
type MockBackupProvider struct {
	mock.Mock
}

func (m *MockBackupProvider) Backup(ctx context.Context, backupRecord *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	args := m.Called(ctx, backupRecord, prevRecord)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*types.BackupError)
}

func (m *MockBackupProvider) Restore(ctx context.Context, backupRecord *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	args := m.Called(ctx, backupRecord, prevRecord)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*types.BackupError)
}

func (m *MockBackupProvider) List(ctx context.Context) ([]*types.BackupRecord, *types.BackupError) {
	args := m.Called(ctx)
	var records []*types.BackupRecord
	var err *types.BackupError

	if args.Get(0) != nil {
		records = args.Get(0).([]*types.BackupRecord)
	}
	if args.Get(1) != nil {
		err = args.Get(1).(*types.BackupError)
	}
	return records, err
}

func (m *MockBackupProvider) Delete(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError {
	args := m.Called(ctx, backupRecord)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*types.BackupError)
}

func (m *MockBackupProvider) CheckHealth(ctx context.Context) *types.BackupError {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*types.BackupError)
}

func TestBackupProvider_Interface(t *testing.T) {
	provider := &MockBackupProvider{}
	ctx := context.Background()

	t.Run("Backup should be callable", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusCompleted,
		}

		provider.On("Backup", ctx, record, (*types.BackupRecord)(nil)).Return((*types.BackupError)(nil))

		err := provider.Backup(ctx, record, nil)
		assert.Nil(t, err)
		provider.AssertExpectations(t)
	})

	t.Run("Restore should be callable", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusCompleted,
		}

		provider.On("Restore", ctx, record, (*types.BackupRecord)(nil)).Return((*types.BackupError)(nil))

		err := provider.Restore(ctx, record, nil)
		assert.Nil(t, err)
		provider.AssertExpectations(t)
	})

	t.Run("List should return backup records", func(t *testing.T) {
		expectedRecords := []*types.BackupRecord{
			{
				ID:     "backup_1",
				Source: types.MySQL,
				Type:   types.BackupTypeArchival,
				Status: types.BackupStatusCompleted,
			},
			{
				ID:     "backup_2",
				Source: types.MySQL,
				Type:   types.BackupTypeChainInitial,
				Status: types.BackupStatusCompleted,
			},
		}

		provider.On("List", ctx).Return(expectedRecords, (*types.BackupError)(nil))

		records, err := provider.List(ctx)
		assert.Nil(t, err)
		assert.Equal(t, expectedRecords, records)
		provider.AssertExpectations(t)
	})

	t.Run("Delete should be callable", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusCompleted,
		}

		provider.On("Delete", ctx, record).Return((*types.BackupError)(nil))

		err := provider.Delete(ctx, record)
		assert.Nil(t, err)
		provider.AssertExpectations(t)
	})

	t.Run("CheckHealth should be callable", func(t *testing.T) {
		provider.On("CheckHealth", ctx).Return((*types.BackupError)(nil))

		err := provider.CheckHealth(ctx)
		assert.Nil(t, err)
		provider.AssertExpectations(t)
	})
}

func TestBackupProvider_ErrorHandling(t *testing.T) {
	provider := &MockBackupProvider{}
	ctx := context.Background()

	t.Run("Backup should handle errors", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusInProgress,
		}

		expectedError := &types.BackupError{
			Code:    "TEST_ERROR",
			Message: "test error message",
		}
		provider.On("Backup", ctx, record, (*types.BackupRecord)(nil)).Return(expectedError)

		err := provider.Backup(ctx, record, nil)
		assert.NotNil(t, err)
		assert.Equal(t, expectedError, err)
		provider.AssertExpectations(t)
	})

	t.Run("Restore should handle errors", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusCompleted,
		}

		expectedError := &types.BackupError{
			Code:    "TEST_ERROR",
			Message: "test restore error",
		}
		provider.On("Restore", ctx, record, (*types.BackupRecord)(nil)).Return(expectedError)

		err := provider.Restore(ctx, record, nil)
		assert.NotNil(t, err)
		assert.Equal(t, expectedError, err)
		provider.AssertExpectations(t)
	})

	t.Run("List should handle errors", func(t *testing.T) {
		expectedError := &types.BackupError{
			Code:    "TEST_ERROR",
			Message: "test list error",
		}
		provider.On("List", ctx).Return(([]*types.BackupRecord)(nil), expectedError)

		records, err := provider.List(ctx)
		assert.NotNil(t, err)
		assert.Nil(t, records)
		assert.Equal(t, expectedError, err)
		provider.AssertExpectations(t)
	})

	t.Run("Delete should handle errors", func(t *testing.T) {
		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusCompleted,
		}

		expectedError := &types.BackupError{
			Code:    "TEST_ERROR",
			Message: "test delete error",
		}
		provider.On("Delete", ctx, record).Return(expectedError)

		err := provider.Delete(ctx, record)
		assert.NotNil(t, err)
		assert.Equal(t, expectedError, err)
		provider.AssertExpectations(t)
	})
}

func TestBackupProvider_ContextCancellation(t *testing.T) {
	provider := &MockBackupProvider{}

	t.Run("Backup should respect context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // 立即取消

		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusInProgress,
		}

		contextError := &types.BackupError{
			Code:    "CONTEXT_CANCELLED",
			Message: "context was cancelled",
		}
		provider.On("Backup", ctx, record, (*types.BackupRecord)(nil)).Return(contextError)

		err := provider.Backup(ctx, record, nil)
		assert.NotNil(t, err)
		assert.Equal(t, contextError, err)
		provider.AssertExpectations(t)
	})

	t.Run("Restore should respect context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		time.Sleep(2 * time.Millisecond) // 确保超时

		record := &types.BackupRecord{
			ID:     "test_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeArchival,
			Status: types.BackupStatusCompleted,
		}

		timeoutError := &types.BackupError{
			Code:    "CONTEXT_TIMEOUT",
			Message: "context deadline exceeded",
		}
		provider.On("Restore", ctx, record, (*types.BackupRecord)(nil)).Return(timeoutError)

		err := provider.Restore(ctx, record, nil)
		assert.NotNil(t, err)
		assert.Equal(t, timeoutError, err)
		provider.AssertExpectations(t)
	})
}

func TestBackupProvider_IncrementalBackup(t *testing.T) {
	provider := &MockBackupProvider{}
	ctx := context.Background()

	t.Run("should handle incremental backup with previous record", func(t *testing.T) {
		prevRecord := &types.BackupRecord{
			ID:     "prev_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeChainInitial,
			Status: types.BackupStatusCompleted,
		}

		currentRecord := &types.BackupRecord{
			ID:       "current_backup_456",
			Source:   types.MySQL,
			Type:     types.BackupTypeChainIncremental,
			Status:   types.BackupStatusInProgress,
			ParentID: prevRecord.ID,
		}

		provider.On("Backup", ctx, currentRecord, prevRecord).Return((*types.BackupError)(nil))

		err := provider.Backup(ctx, currentRecord, prevRecord)
		assert.Nil(t, err)
		provider.AssertExpectations(t)
	})

	t.Run("should handle incremental restore with previous record", func(t *testing.T) {
		prevRecord := &types.BackupRecord{
			ID:     "prev_backup_123",
			Source: types.MySQL,
			Type:   types.BackupTypeChainInitial,
			Status: types.BackupStatusCompleted,
		}

		currentRecord := &types.BackupRecord{
			ID:       "current_backup_456",
			Source:   types.MySQL,
			Type:     types.BackupTypeChainIncremental,
			Status:   types.BackupStatusCompleted,
			ParentID: prevRecord.ID,
		}

		provider.On("Restore", ctx, currentRecord, prevRecord).Return((*types.BackupError)(nil))

		err := provider.Restore(ctx, currentRecord, prevRecord)
		assert.Nil(t, err)
		provider.AssertExpectations(t)
	})
}

func TestBackupProvider_DifferentSourceTypes(t *testing.T) {
	provider := &MockBackupProvider{}
	ctx := context.Background()

	sourceTypes := []types.SourceType{
		types.MySQL,
		types.Elasticsearch,
	}

	for _, sourceType := range sourceTypes {
		t.Run("should handle "+string(sourceType)+" backup", func(t *testing.T) {
			record := &types.BackupRecord{
				ID:     "test_backup_" + string(sourceType),
				Source: sourceType,
				Type:   types.BackupTypeArchival,
				Status: types.BackupStatusInProgress,
			}

			provider.On("Backup", ctx, record, (*types.BackupRecord)(nil)).Return((*types.BackupError)(nil))

			err := provider.Backup(ctx, record, nil)
			assert.Nil(t, err)
			provider.AssertExpectations(t)
		})
	}
}

func TestBackupProvider_BackupTypes(t *testing.T) {
	provider := &MockBackupProvider{}
	ctx := context.Background()

	backupTypes := []types.BackupType{
		types.BackupTypeArchival,
		types.BackupTypeChainInitial,
		types.BackupTypeChainIncremental,
	}

	for _, backupType := range backupTypes {
		t.Run("should handle "+string(backupType)+" backup", func(t *testing.T) {
			record := &types.BackupRecord{
				ID:     "test_backup_" + string(backupType),
				Source: types.MySQL,
				Type:   backupType,
				Status: types.BackupStatusInProgress,
			}

			provider.On("Backup", ctx, record, (*types.BackupRecord)(nil)).Return((*types.BackupError)(nil))

			err := provider.Backup(ctx, record, nil)
			assert.Nil(t, err)
			provider.AssertExpectations(t)
		})
	}
}

// BenchmarkBackupProvider_Operations 性能基准测试
func BenchmarkBackupProvider_Operations(b *testing.B) {
	provider := &MockBackupProvider{}
	ctx := context.Background()

	record := &types.BackupRecord{
		ID:     "benchmark_backup",
		Source: types.MySQL,
		Type:   types.BackupTypeArchival,
		Status: types.BackupStatusInProgress,
	}

	provider.On("Backup", ctx, record, (*types.BackupRecord)(nil)).Return((*types.BackupError)(nil))
	provider.On("Restore", ctx, record, (*types.BackupRecord)(nil)).Return((*types.BackupError)(nil))
	provider.On("List", ctx).Return([]*types.BackupRecord{record}, (*types.BackupError)(nil))
	provider.On("Delete", ctx, record).Return((*types.BackupError)(nil))

	b.Run("Backup", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			provider.Backup(ctx, record, nil)
		}
	})

	b.Run("Restore", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			provider.Restore(ctx, record, nil)
		}
	})

	b.Run("List", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			provider.List(ctx)
		}
	})

	b.Run("Delete", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			provider.Delete(ctx, record)
		}
	})
}
