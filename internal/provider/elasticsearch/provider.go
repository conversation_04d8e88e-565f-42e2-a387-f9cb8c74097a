// Package elasticsearch 提供Elasticsearch数据源的备份和恢复功能
//
// 云存储支持说明：
// 本包支持两种存储模式：本地文件系统存储和云存储。存储模式通过全局配置自动选择。
//
// 存储模式对比：
// ┌─────────────────┬─────────────────────┬─────────────────────────┐
// │     特性        │      本地存储       │        云存储           │
// ├─────────────────┼─────────────────────┼─────────────────────────┤
// │ ES仓库类型      │ fs (文件系统)       │ s3/gcs/azure (云存储)   │
// │ 配置要求        │ 需配置path.repo     │ 需安装仓库插件          │
// │ 数据存储位置    │ 本地文件系统        │ 云存储服务              │
// │ 灾难恢复能力    │ 依赖本地备份        │ 高可用云存储            │
// │ 扩展性          │ 受本地磁盘限制      │ 几乎无限扩展            │
// └─────────────────┴─────────────────────┴─────────────────────────┘
//
// 配置判断逻辑：
// 当 globalCfg.CloudStorage != nil && globalCfg.CloudStorage.Enabled == true 时，
// 系统自动使用云存储模式，否则使用本地文件系统模式。
//
// 配置字段使用差异：
// - repo_base_path: 本地模式使用，云存储模式忽略
// - archival_repo_name/managed_repo_name: 两种模式都使用
// - cloud_storage配置: 仅云存储模式使用
//
// 部署要求：
// 本地存储：elasticsearch.yml需配置path.repo
// 云存储：  需安装对应插件（如elasticsearch-plugin install repository-s3）
package elasticsearch

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	"go.uber.org/zap"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
)

// 定义与Elasticsearch API交互时使用的超时常量
const (
	// 为删除操作设置较长的超时时间，因为仓库异常时ES API响应可能很慢
	// 特别是在仓库元数据损坏的情况下，ES需要更多时间处理
	deleteTimeout = 2 * time.Minute
)

// elasticImpl 是一个内部接口，定义了特定Elasticsearch备份和恢复策略需要实现的方法
// 目前我们只有一个基于快照的实现，但这种设计允许未来扩展（例如，使用不同的备份工具或逻辑导出）
type elasticImpl interface {
	backup(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError
	restore(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError
	list(ctx context.Context) ([]*types.BackupRecord, *types.BackupError)
	delete(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError
	checkHealth(ctx context.Context) *types.BackupError
}

// Provider 实现了 unibackup/internal/provider.BackupProvider 接口
// 它作为Elasticsearch备份功能的外观模式，将具体实现委托给内部的elasticImpl实例
// 与MySQL Provider不同，ES Provider的List和Delete方法具有实际功能，因为它们直接与ES集群的快照API交互
// 阶段二改造：注入 StorageManager 依赖，消除直接文件系统操作
type Provider struct {
	impl           elasticImpl      // 持有具体的策略实现（目前只有快照实现）
	storageManager *storage.Manager // 阶段二新增：StorageManager 依赖注入
}

// NewProvider 创建一个新的Elasticsearch提供者实例
// 它根据配置初始化Elasticsearch客户端，并实例化基于快照的策略实现
// 如果配置了AutoCreateRepos，会自动创建快照仓库
// 阶段二改造：接受 StorageManager 参数，实现依赖注入
func NewProvider(cfg *types.ESConfig, globalCfg *types.Config, storageManager *storage.Manager, logger *zap.Logger) (*Provider, *types.BackupError) {
	if cfg == nil {
		return nil, newElasticProviderError("INVALID_ARGUMENT", "NewProvider", fmt.Errorf("ES配置不能为nil"), false)
	}
	if storageManager == nil {
		return nil, newElasticProviderError("INVALID_ARGUMENT", "NewProvider", fmt.Errorf("StorageManager不能为nil"), false)
	}
	if logger == nil {
		return nil, newElasticProviderError("INVALID_ARGUMENT", "NewProvider", fmt.Errorf("Logger不能为nil"), false)
	}

	esCfg := elasticsearch.Config{
		Addresses: cfg.Addresses,
		APIKey:    cfg.APIKey,
		Username:  cfg.User,
		Password:  cfg.Password,
	}
	client, err := elasticsearch.NewClient(esCfg)
	if err != nil {
		return nil, newElasticProviderError("ES_CLIENT_INIT_FAILED", "NewProvider", fmt.Errorf("创建Elasticsearch客户端失败: %w", err), false)
	}

	impl := &snapshotImpl{
		client:         client,
		cfg:            cfg,
		globalCfg:      globalCfg,
		logger:         logger.With(zap.String("provider", "elasticsearch"), zap.String("impl", "snapshot")),
		storageManager: storageManager, // 阶段二：注入 StorageManager
	}

	// 如果启用了自动创建仓库，则创建快照仓库
	if cfg.AutoCreateRepos {
		if err := impl.ensureRepositories(context.Background()); err != nil {
			return nil, err
		}
	}

	return &Provider{
		impl:           impl,
		storageManager: storageManager, // 阶段二：注入 StorageManager
	}, nil
}

// Backup 是 unibackup/provider.BackupProvider 接口的实现
// 对于ES，prevRecord参数未被使用，因为ES快照在段级别本质上是增量的
// 每次调用都会创建一个新快照，ES会自动处理与之前快照的差异
func (p *Provider) Backup(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	return p.impl.backup(ctx, record)
}

// Restore 是 unibackup/provider.BackupProvider 接口的实现
func (p *Provider) Restore(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	return p.impl.restore(ctx, record)
}

// List 是 unibackup/provider.BackupProvider 接口的实现
func (p *Provider) List(ctx context.Context) ([]*types.BackupRecord, *types.BackupError) {
	return p.impl.list(ctx)
}

// Delete 将备份记录删除操作委托给内部的具体实现
func (p *Provider) Delete(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError {
	return p.impl.delete(ctx, backupRecord)
}

// CheckHealth 检查Elasticsearch数据源的健康状态。
// 它委托给具体的实现来执行健康检查。
func (p *Provider) CheckHealth(ctx context.Context) *types.BackupError {
	return p.impl.checkHealth(ctx)
}

// --- 快照实现 ---

// snapshotImpl 使用Elasticsearch的快照API实现备份和恢复
// 阶段二改造：注入 StorageManager 依赖，消除直接文件系统操作
type snapshotImpl struct {
	client         *elasticsearch.Client
	cfg            *types.ESConfig
	globalCfg      *types.Config // 添加全局配置以获取超时值
	logger         *zap.Logger
	storageManager *storage.Manager // 阶段二新增：StorageManager 依赖注入
}

// backup 执行Elasticsearch快照备份操作
func (s *snapshotImpl) backup(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError {
	if backupRecord == nil {
		return newElasticProviderError("INVALID_ARGUMENT", "backup", fmt.Errorf("备份记录不能为nil"), false)
	}

	repoName := s.getRepoForType(backupRecord.Type)
	if repoName == "" {
		return newElasticProviderError("INVALID_REPO_NAME", "backup", fmt.Errorf("无法为备份类型确定仓库: %s", backupRecord.Type), false)
	}

	// 确保仓库存在（懒加载机制）
	// 这解决了动态配置更新、ES重启等导致的仓库缺失问题
	if err := s.ensureRepositoryExistsForBackup(ctx, repoName); err != nil {
		return err
	}

	// TODO: 测试时取消注释 - 清理路径变更后的旧仓库配置
	if err := s.cleanupStaleRepositoriesIfNeeded(ctx); err != nil {
		s.logger.Warn("清理旧仓库配置失败", zap.Error(err))
	}

	// 序列化完整的备份元数据并存储在快照的元数据字段中
	// 这是一个好的实践，因为它使快照具有自描述性，包含UniBackup需要的所有上下文
	// 当我们稍后列出快照时，可以直接从快照元数据中恢复完整的BackupRecord
	metaJSON, err := json.Marshal(backupRecord)
	if err != nil {
		return newElasticProviderError("METADATA_SERIALIZE_ERROR", "backup", fmt.Errorf("序列化备份记录到JSON失败: %w", err), false)
	}

	// 获取用户索引列表（排除系统索引）
	userIndices, err := s.getUserIndices(ctx)
	if err != nil {
		return newElasticProviderError("INDEX_LIST_ERROR", "backup", fmt.Errorf("获取用户索引列表失败: %w", err), false)
	}

	body := map[string]interface{}{
		// 在这里，我们将UniBackup的元数据嵌入到快照自己的元数据中
		"metadata": json.RawMessage(metaJSON),
		// 只备份用户索引，排除系统索引
		"indices": userIndices,
		// 如果索引不存在就报错，确保备份的可靠性
		"ignore_unavailable": false,
		// 要求完整备份，确保所有指定索引都被备份
		"partial": false,
	}
	bodyBytes, _ := json.Marshal(body)

	// API调用超时由配置控制
	ctx, cancel := context.WithTimeout(ctx, s.globalCfg.BackupTimeout.ToDuration())
	defer cancel()

	req := esapi.SnapshotCreateRequest{
		Repository:        repoName,
		Snapshot:          strings.ToLower(backupRecord.ID), // ES快照名称必须是小写
		Body:              bytes.NewReader(bodyBytes),
		WaitForCompletion: &[]bool{true}[0], // 设置为true以阻塞API调用直到快照完成
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "backup", fmt.Errorf("快照创建请求失败: %w", err), false)
	}
	defer res.Body.Close()

	if res.IsError() {
		return newElasticProviderError("ES_API_ERROR", "backup", fmt.Errorf("快照创建API返回错误: %s", res.String()), false)
	}

	s.logger.Info("Elasticsearch快照创建成功", zap.String("snapshot_id", backupRecord.ID), zap.String("repository", repoName))
	return nil
}

// restore 从指定快照恢复Elasticsearch集群（使用完全重建策略）
//
// 重构后的恢复流程：
// 1. 删除所有现有索引（确保干净状态）
// 2. 从快照恢复数据
//
// 这种方法确保恢复到确定的、干净的状态，避免索引冲突问题。
func (s *snapshotImpl) restore(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError {
	if backupRecord == nil {
		return newElasticProviderError("INVALID_ARGUMENT", "restore", fmt.Errorf("备份记录不能为nil"), false)
	}

	repoName := s.getRepoForType(backupRecord.Type)
	if repoName == "" {
		return newElasticProviderError("INVALID_REPO_NAME", "restore", fmt.Errorf("无法为恢复类型确定仓库: %s", backupRecord.Type), false)
	}

	// 确保仓库存在（懒加载机制）
	if err := s.ensureRepositoryExistsForBackup(ctx, repoName); err != nil {
		return err
	}

	s.logger.Info("开始完全重建式恢复", zap.String("backup_id", backupRecord.ID), zap.String("repository", repoName))

	// 1. 删除所有现有用户索引（完全重建策略，保留系统索引）
	if err := s.deleteAllUserIndices(ctx); err != nil {
		return newElasticProviderError("INDEX_DELETE_FAILED", "restore", fmt.Errorf("删除现有用户索引失败: %w", err), false)
	}

	// 2. 执行快照恢复（由于备份时只包含用户索引，恢复时也只恢复用户索引）
	return s.executeSnapshotRestore(ctx, backupRecord, repoName)
}

// deleteAllUserIndices 删除ES集群中的所有用户索引（保留系统索引）
// 这是完全重建策略的关键步骤，确保恢复前的干净状态，同时保持系统索引不被影响
func (s *snapshotImpl) deleteAllUserIndices(ctx context.Context) *types.BackupError {
	s.logger.Info("开始删除所有现有用户索引")

	// 1. 获取用户索引列表（复用已有方法）
	userIndices, err := s.getUserIndices(ctx)
	if err != nil {
		return newElasticProviderError("INDEX_LIST_ERROR", "deleteAllUserIndices", fmt.Errorf("获取用户索引列表失败: %w", err), false)
	}

	if len(userIndices) == 0 {
		s.logger.Info("集群中无用户索引，跳过删除")
		return nil
	}

	s.logger.Info("准备删除用户索引", zap.Int("count", len(userIndices)), zap.Strings("indices", userIndices))

	// 2. 删除所有用户索引（批量删除）
	deleteReq := esapi.IndicesDeleteRequest{
		Index: userIndices,
	}

	deleteRes, err := deleteReq.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "deleteAllUserIndices", fmt.Errorf("删除用户索引失败: %w", err), false)
	}
	defer deleteRes.Body.Close()

	if deleteRes.IsError() {
		return newElasticProviderError("ES_API_ERROR", "deleteAllUserIndices", fmt.Errorf("删除用户索引API返回错误: %s", deleteRes.String()), false)
	}

	s.logger.Info("成功删除所有用户索引", zap.Int("count", len(userIndices)))
	return nil
}

// executeSnapshotRestore 执行实际的快照恢复操作
func (s *snapshotImpl) executeSnapshotRestore(ctx context.Context, backupRecord *types.BackupRecord, repoName string) *types.BackupError {
	// API调用超时由配置控制
	ctx, cancel := context.WithTimeout(ctx, s.globalCfg.RestoreTimeout.ToDuration())
	defer cancel()

	// 构建恢复请求body，只恢复用户索引，避免与系统索引冲突
	restoreBody := map[string]interface{}{
		// 只恢复非系统索引（不以.开头的索引）
		"indices": "*,-.*", // 恢复所有索引，但排除以.开头的系统索引
		// 忽略不可用的索引，避免因索引不存在而失败
		"ignore_unavailable": true,
		// 允许部分恢复，提高容错性
		"partial": false,
		// 重命名规则：保持原始索引名
		"rename_pattern":     "",
		"rename_replacement": "",
	}

	bodyBytes, err := json.Marshal(restoreBody)
	if err != nil {
		return newElasticProviderError("JSON_MARSHAL_ERROR", "executeSnapshotRestore",
			fmt.Errorf("序列化恢复请求失败: %w", err), false)
	}

	req := esapi.SnapshotRestoreRequest{
		Repository:        repoName,
		Snapshot:          strings.ToLower(backupRecord.ID), // ES快照名称必须是小写
		Body:              bytes.NewReader(bodyBytes),       // 添加请求body
		WaitForCompletion: &[]bool{true}[0],                 // 阻塞直到恢复完成
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "executeSnapshotRestore", fmt.Errorf("快照恢复请求失败: %w", err), false)
	}
	defer res.Body.Close()

	if res.IsError() {
		return newElasticProviderError("ES_API_ERROR", "executeSnapshotRestore", fmt.Errorf("快照恢复API返回错误: %s", res.String()), false)
	}

	s.logger.Info("从快照成功恢复Elasticsearch", zap.String("snapshot_id", backupRecord.ID), zap.String("repository", repoName))
	return nil
}

// list 通过查询两个仓库（归档和增量链）中的所有快照来检索所有可用的备份记录
func (s *snapshotImpl) list(ctx context.Context) ([]*types.BackupRecord, *types.BackupError) {
	// 同时查询两个仓库
	archivalSnaps, err := s.listFromRepo(ctx, s.cfg.ArchivalRepoName)
	if err != nil {
		return nil, newElasticProviderError("ES_API_ERROR", "list", fmt.Errorf("从归档仓库获取快照失败: %w", err), false)
	}
	managedSnaps, err := s.listFromRepo(ctx, s.cfg.ManagedRepoName)
	if err != nil {
		return nil, newElasticProviderError("ES_API_ERROR", "list", fmt.Errorf("从管理仓库获取快照失败: %w", err), false)
	}

	allRecords := make([]*types.BackupRecord, 0, len(archivalSnaps)+len(managedSnaps))
	allRecords = append(allRecords, archivalSnaps...)
	allRecords = append(allRecords, managedSnaps...)

	return allRecords, nil
}

// delete 删除指定的Elasticsearch快照
func (s *snapshotImpl) delete(ctx context.Context, backupRecord *types.BackupRecord) *types.BackupError {
	if backupRecord == nil {
		return newElasticProviderError("INVALID_ARGUMENT", "delete", fmt.Errorf("备份记录不能为nil"), false)
	}

	repoName := s.getRepoForType(backupRecord.Type)
	if repoName == "" {
		return newElasticProviderError("INVALID_REPO_NAME", "delete", fmt.Errorf("无法为删除类型确定仓库: %s", backupRecord.Type), false)
	}

	// API调用超时由外部上下文控制
	ctx, cancel := context.WithTimeout(ctx, deleteTimeout)
	defer cancel()

	req := esapi.SnapshotDeleteRequest{
		Repository: repoName,
		Snapshot:   []string{strings.ToLower(backupRecord.ID)}, // ES快照名称必须是小写
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "delete", fmt.Errorf("快照删除请求失败: %w", err), false)
	}
	defer res.Body.Close()

	if res.IsError() {
		errorBody := res.String()

		if res.StatusCode == http.StatusNotFound {
			// 区分快照不存在和仓库不存在
			if strings.Contains(errorBody, "repository_missing_exception") {
				// 仓库不存在 - 直接报错，让用户知道需要检查配置
				return newElasticProviderError("REPOSITORY_NOT_FOUND", "delete",
					fmt.Errorf("仓库 %s 不存在，请检查ES配置和仓库设置: %s", repoName, errorBody), false)
			} else {
				// 快照不存在 - 认为删除成功
				s.logger.Info("快照不存在，认为删除成功",
					zap.String("snapshot_id", backupRecord.ID),
					zap.String("repository", repoName))
				return nil
			}
		}

		// 特殊处理仓库异常：对于清理操作，仓库损坏时认为删除成功
		// 这避免了因为仓库元数据问题而阻塞清理流程
		if res.StatusCode == http.StatusInternalServerError &&
			(strings.Contains(errorBody, "repository_exception") ||
				strings.Contains(errorBody, "concurrent modification") ||
				strings.Contains(errorBody, "index-N file")) {
			s.logger.Warn("仓库异常，但认为快照删除成功以避免阻塞清理",
				zap.String("snapshot_id", backupRecord.ID),
				zap.String("repository", repoName),
				zap.String("error", errorBody))
			return nil
		}

		// 其他错误直接报告
		return newElasticProviderError("ES_API_ERROR", "delete",
			fmt.Errorf("快照删除API返回错误: [%d] %s", res.StatusCode, errorBody), false)
	}

	s.logger.Info("Elasticsearch快照删除成功", zap.String("snapshot_id", backupRecord.ID), zap.String("repository", repoName))
	return nil
}

// checkHealth 检查Elasticsearch数据源的健康状态。
// 它尝试连接到Elasticsearch集群并执行一个简单的Info API调用。
func (s *snapshotImpl) checkHealth(ctx context.Context) *types.BackupError {
	res, err := s.client.Info(s.client.Info.WithContext(ctx))
	if err != nil {
		return newElasticProviderError("ES_UNAVAILABLE", "checkHealth", fmt.Errorf("Elasticsearch服务不可用: %v", err), true)
	}
	defer res.Body.Close()

	if res.IsError() {
		return newElasticProviderError("ES_API_ERROR", "checkHealth", fmt.Errorf("Elasticsearch服务返回错误: %s", res.String()), false)
	}
	return nil
}

// ensureRepositoryExistsForBackup 确保备份所需的仓库存在
// 如果仓库不存在，会自动创建。这是一个懒加载机制，解决动态配置更新的问题
func (s *snapshotImpl) ensureRepositoryExistsForBackup(ctx context.Context, repoName string) *types.BackupError {
	// 检查仓库是否存在
	req := esapi.SnapshotGetRepositoryRequest{
		Repository: []string{repoName},
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("REPOSITORY_CHECK_ERROR", "ensureRepositoryExistsForBackup",
			fmt.Errorf("检查仓库 %s 时发生网络错误: %w", repoName, err), false)
	}
	defer res.Body.Close()

	if res.StatusCode == 200 {
		// 仓库存在，直接返回
		s.logger.Debug("仓库已存在，无需创建", zap.String("repository", repoName))
		return nil
	}

	if res.StatusCode == 404 {
		// 仓库不存在，尝试自动创建
		s.logger.Info("检测到仓库不存在，开始自动创建",
			zap.String("repository", repoName))

		// 调用现有的仓库创建逻辑
		if err := s.ensureRepositories(ctx); err != nil {
			return newElasticProviderError("REPOSITORY_AUTO_CREATE_FAILED", "ensureRepositoryExistsForBackup",
				fmt.Errorf("自动创建仓库 %s 失败: %w", repoName, err), false)
		}

		s.logger.Info("仓库自动创建成功", zap.String("repository", repoName))
		return nil
	}

	// 其他HTTP状态码表示ES API错误
	bodyBytes, _ := io.ReadAll(res.Body)
	return newElasticProviderError("REPOSITORY_CHECK_ERROR", "ensureRepositoryExistsForBackup",
		fmt.Errorf("检查仓库 %s 状态失败: [%d] %s", repoName, res.StatusCode, string(bodyBytes)), false)
}

// getRepoForType 是一个辅助函数，根据备份类型返回正确的仓库名称
// UniBackup将归档备份和增量链备份存储在不同的ES快照仓库中，以实现不同的保留策略
func (s *snapshotImpl) getRepoForType(backupType types.BackupType) string {
	switch backupType {
	case types.BackupTypeArchival:
		return s.cfg.ArchivalRepoName
	case types.BackupTypeChainInitial, types.BackupTypeChainIncremental:
		return s.cfg.ManagedRepoName
	default:
		return ""
	}
}

// listFromRepo 从单个仓库检索所有快照并解析其元数据，
// 将它们转换为UniBackup的BackupRecord列表
func (s *snapshotImpl) listFromRepo(ctx context.Context, repoName string) ([]*types.BackupRecord, *types.BackupError) {
	req := esapi.SnapshotGetRequest{
		Repository: repoName,
		Snapshot:   []string{"_all"}, // 获取仓库中的所有快照
	}
	res, err := req.Do(ctx, s.client)
	if err != nil {
		return nil, newElasticProviderError("ES_API_ERROR", "listFromRepo", fmt.Errorf("从仓库 '%s' 获取快照失败: %w", repoName, err), false)
	}
	defer res.Body.Close()

	if res.IsError() {
		// 仓库未找到是常见情况（例如，尚未为此类型进行备份），不应视为致命错误
		if res.StatusCode == http.StatusNotFound {
			s.logger.Warn("快照仓库未找到，返回空列表", zap.String("repository", repoName))
			return []*types.BackupRecord{}, nil
		}
		return nil, newElasticProviderError("ES_API_ERROR", "listFromRepo", fmt.Errorf("获取仓库 '%s' 的快照API返回错误: %s", repoName, res.String()), false)
	}

	var response struct {
		Snapshots []struct {
			Snapshot string                 `json:"snapshot"`
			Metadata map[string]interface{} `json:"metadata"`
		} `json:"snapshots"`
	}

	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return nil, newElasticProviderError("JSON_PARSE_ERROR", "listFromRepo", fmt.Errorf("为仓库 '%s' 解码快照列表响应失败: %w", repoName, err), false)
	}

	records := make([]*types.BackupRecord, 0, len(response.Snapshots))
	for _, snap := range response.Snapshots {
		if snap.Metadata == nil {
			s.logger.Warn("快照缺少元数据，已跳过", zap.String("snapshot_id", snap.Snapshot), zap.String("repository", repoName))
			continue
		}
		var record types.BackupRecord

		// 使用 JSON 序列化/反序列化而不是 mapstructure，因为它能更好地处理 time.Time 等复杂类型
		metadataBytes, err := json.Marshal(snap.Metadata)
		if err != nil {
			s.logger.Warn("无法序列化快照元数据，已跳过", zap.String("snapshot_id", snap.Snapshot), zap.Error(err))
			continue
		}

		if err := json.Unmarshal(metadataBytes, &record); err != nil {
			s.logger.Warn("无法将快照元数据解码为BackupRecord，已跳过", zap.String("snapshot_id", snap.Snapshot), zap.Error(err))
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}

// ensureRepositories 确保快照仓库存在，如果不存在则自动创建
//
// 功能说明：
// - 根据全局配置自动选择本地存储或云存储模式
// - 本地存储：创建文件系统类型(fs)的ES仓库，需要预配置path.repo
// - 云存储：创建云存储类型(s3/gcs/azure)的ES仓库，直接存储到云端
//
// 配置判断逻辑：
// - 当 globalCfg.CloudStorage != nil && globalCfg.CloudStorage.Enabled == true 时使用云存储
// - 否则使用本地文件系统存储
//
// 配置示例：
// 本地存储配置：
//
//	backup_root: "/var/backups/unibackup"
//	es:
//	  addresses: ["http://localhost:9200"]
//	  archival_repo_name: "unibackup-archival"
//	  managed_repo_name: "unibackup-managed"
//	  repo_base_path: "/usr/share/elasticsearch/snapshots"  # 必须在ES中预配置
//
// 云存储配置：
//
//	backup_root: "/var/backups/unibackup"  # 仅用于元数据路径生成
//	cloud_storage:
//	  enabled: true                        # 关键：启用云存储
//	  type: "s3"                          # 支持：s3, gcs, azure
//	  bucket: "my-backup-bucket"          # S3 bucket名称
//	  region: "us-west-2"                 # AWS区域（可选）
//	  endpoint: "http://minio:9000"       # 自定义端点（MinIO等，可选）
//	  access_key: "AKIAIOSFODNN7EXAMPLE"  # 访问密钥
//	  secret_key: "wJalrXUtnFEMI/K7MDENG" # 密钥
//	es:
//	  addresses: ["http://localhost:9200"]
//	  archival_repo_name: "unibackup-archival"  # 仓库名称保持不变
//	  managed_repo_name: "unibackup-managed"    # 仓库名称保持不变
//	  repo_base_path: ""                        # 云存储模式下被忽略
//
// 目录结构对比：
// 本地存储: /usr/share/elasticsearch/snapshots/{archival,managed}/
// 云存储:   s3://my-backup-bucket/elasticsearch/{archival,managed}/
//
// ES集群要求：
// 本地存储：需要在elasticsearch.yml中配置 path.repo: ["/usr/share/elasticsearch/snapshots"]
// 云存储：  需要安装相应插件，如 elasticsearch-plugin install repository-s3
func (s *snapshotImpl) ensureRepositories(ctx context.Context) *types.BackupError {
	// 检查是否启用云存储
	if s.globalCfg.CloudStorage != nil && s.globalCfg.CloudStorage.Enabled {
		s.logger.Info("检测到云存储配置，验证ES云存储支持",
			zap.String("storage_type", s.globalCfg.CloudStorage.Type),
			zap.String("bucket", s.getBucketName()))

		// 验证ES集群是否支持云存储
		if err := s.validateCloudStorageSupport(ctx); err != nil {
			return newElasticProviderError("CLOUD_STORAGE_NOT_SUPPORTED", "validateCloudStorage",
				fmt.Errorf("ES集群不支持云存储: %w", err), false)
		}

		return s.ensureCloudRepositories(ctx)
	}

	// 本地存储：保持现有逻辑
	s.logger.Info("使用本地文件系统仓库模式")
	return s.ensureLocalRepositories(ctx)
}

// ensureLocalRepositories 确保本地文件系统快照仓库存在
//
// 功能说明：
// - 处理本地存储模式下的ES仓库创建逻辑
// - 创建文件系统类型(fs)的ES仓库，数据存储在本地文件系统
// - 需要ES集群预先配置path.repo允许在指定路径创建仓库
//
// 路径优先级：
// 1. 使用 es.repo_base_path 配置（如果有值）
// 2. 使用 backup_root/elasticsearch 作为默认路径
//
// 重要前提条件：
// - ES集群必须在elasticsearch.yml中配置path.repo
// - ES进程对目标路径有读写权限
// - Docker部署需要将容器内快照目录映射到宿主机
func (s *snapshotImpl) ensureLocalRepositories(ctx context.Context) *types.BackupError {
	// 确定仓库基础路径
	repoBasePath := s.cfg.RepoBasePath
	if repoBasePath == "" {
		// 默认使用 backup_root/elasticsearch 目录
		esDataSourcePath := s.storageManager.GetDataSourcePath(types.Elasticsearch)
		repoBasePath = esDataSourcePath
	} else {
		// 如果用户配置了自定义路径，在其下创建elasticsearch子目录
		// 这样保持与任务元数据目录的一致性
		repoBasePath = filepath.Join(repoBasePath, "elasticsearch")
	}

	// 创建归档仓库（用于全量备份，对应BackupTypeArchival）
	archivalPath := filepath.Join(repoBasePath, "snapshots_archival")
	if err := s.createRepositoryIfNotExists(ctx, s.cfg.ArchivalRepoName, archivalPath); err != nil {
		return err
	}

	// 创建托管仓库（用于增量链备份，对应BackupTypeChainInitial和BackupTypeChainIncremental）
	managedPath := filepath.Join(repoBasePath, "snapshots_chains")
	if err := s.createRepositoryIfNotExists(ctx, s.cfg.ManagedRepoName, managedPath); err != nil {
		return err
	}

	s.logger.Info("ES本地快照仓库已确保存在，使用统一的elasticsearch目录结构",
		zap.String("repo_base_path", repoBasePath),
		zap.String("archival_repo", s.cfg.ArchivalRepoName),
		zap.String("archival_path", archivalPath),
		zap.String("managed_repo", s.cfg.ManagedRepoName),
		zap.String("managed_path", managedPath))

	return nil
}

// ensureCloudRepositories 确保云存储快照仓库存在
//
// 功能说明：
// - 处理云存储模式下的ES仓库创建逻辑
// - 创建云存储类型(s3/gcs/azure)的ES仓库，数据直接存储到云端
// - 使用统一的路径结构：elasticsearch/{archival,managed}
//
// 与本地存储的差异：
// - 不依赖本地文件系统路径，忽略 repo_base_path 配置
// - 不需要ES集群预配置path.repo
// - 需要ES集群安装相应的仓库插件(如repository-s3)
//
// 路径结构：
// - 归档仓库：s3://bucket/elasticsearch/archival/
// - 托管仓库：s3://bucket/elasticsearch/managed/
func (s *snapshotImpl) ensureCloudRepositories(ctx context.Context) *types.BackupError {
	// 创建归档仓库：elasticsearch/archival
	if err := s.createCloudRepositoryIfNotExists(ctx, s.cfg.ArchivalRepoName, "archival"); err != nil {
		return err
	}

	// 创建托管仓库：elasticsearch/managed
	if err := s.createCloudRepositoryIfNotExists(ctx, s.cfg.ManagedRepoName, "managed"); err != nil {
		return err
	}

	s.logger.Info("ES云存储快照仓库已确保存在",
		zap.String("archival_repo", s.cfg.ArchivalRepoName),
		zap.String("archival_path", "elasticsearch/archival"),
		zap.String("managed_repo", s.cfg.ManagedRepoName),
		zap.String("managed_path", "elasticsearch/managed"),
		zap.String("storage_type", s.globalCfg.CloudStorage.Type),
		zap.String("bucket", s.getBucketName()))

	return nil
}

// createRepositoryIfNotExists 检查仓库是否存在，如果不存在则创建
func (s *snapshotImpl) createRepositoryIfNotExists(ctx context.Context, repoName, repoPath string) *types.BackupError {
	// 首先检查仓库是否已存在
	checkReq := esapi.SnapshotGetRepositoryRequest{
		Repository: []string{repoName},
	}

	checkRes, err := checkReq.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "checkRepository",
			fmt.Errorf("检查仓库 '%s' 时发生错误: %w", repoName, err), true)
	}
	defer checkRes.Body.Close()

	// 如果仓库已存在，直接返回
	if !checkRes.IsError() {
		s.logger.Debug("快照仓库已存在", zap.String("repository", repoName))
		return nil
	}

	// 如果不是404错误，说明有其他问题
	if checkRes.StatusCode != http.StatusNotFound {
		return newElasticProviderError("ES_API_ERROR", "checkRepository",
			fmt.Errorf("检查仓库 '%s' 时返回错误: %s", repoName, checkRes.String()), true)
	}

	// 仓库不存在，需要创建本地文件系统仓库
	s.logger.Info("本地快照仓库不存在，开始创建", zap.String("repository", repoName), zap.String("path", repoPath))

	// 权限设置由 LocalBackend 在创建目录时自动处理

	// 构建本地文件系统仓库配置
	repoConfig := map[string]interface{}{
		"type": "fs",
		"settings": map[string]interface{}{
			"location": repoPath,
		},
	}

	return s.createRepository(ctx, repoName, repoConfig)
}

// createCloudRepositoryIfNotExists 检查指定的云存储快照仓库是否存在，如果不存在则创建
//
// 功能说明：
// - 专门用于云存储仓库的创建和管理
// - 支持S3、GCS、Azure等云存储类型
// - 不依赖本地文件系统路径，直接使用云存储配置
//
// 与本地仓库的差异：
// - 不需要ES集群预配置path.repo
// - 需要ES集群安装相应的仓库插件
// - 仓库配置包含云存储认证信息
//
// 参数：
//   - repoName: 仓库名称
//   - repoType: 仓库类型（"archival" 或 "managed"）
//
// 返回：
//   - *types.BackupError: 如果操作失败则返回错误，成功则返回nil
func (s *snapshotImpl) createCloudRepositoryIfNotExists(ctx context.Context, repoName, repoType string) *types.BackupError {
	// 云存储支持已在ensureRepositories中验证，这里直接创建仓库

	// 检查仓库是否已存在
	checkReq := esapi.SnapshotGetRepositoryRequest{
		Repository: []string{repoName},
	}

	checkRes, err := checkReq.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "checkRepository",
			fmt.Errorf("检查仓库 '%s' 时发生错误: %w", repoName, err), true)
	}
	defer checkRes.Body.Close()

	// 如果仓库已存在，直接返回
	if !checkRes.IsError() {
		s.logger.Debug("云存储快照仓库已存在", zap.String("repository", repoName))
		return nil
	}

	// 如果不是404错误，说明有其他问题
	if checkRes.StatusCode != http.StatusNotFound {
		return newElasticProviderError("ES_API_ERROR", "checkRepository",
			fmt.Errorf("检查仓库 '%s' 时返回错误: %s", repoName, checkRes.String()), true)
	}

	// 仓库不存在，需要创建云存储仓库
	s.logger.Info("云存储快照仓库不存在，开始创建",
		zap.String("repository", repoName),
		zap.String("type", repoType),
		zap.String("storage_type", s.globalCfg.CloudStorage.Type),
		zap.String("bucket", s.getBucketName()))

	// 构建云存储仓库配置
	repoConfig, err := s.buildCloudRepositoryConfig(repoType)
	if err != nil {
		return newElasticProviderError("REPO_CONFIG_ERROR", "createCloudRepository", err, false)
	}

	// 创建仓库
	return s.createRepository(ctx, repoName, repoConfig)
}

// buildCloudRepositoryConfig 根据云存储配置构建ES仓库配置
//
// 功能说明：
// - 根据全局云存储配置动态构建ES仓库配置
// - 支持多种云存储类型：S3、GCS、Azure
// - 将UniBackup的CloudStorageConfig转换为ES仓库配置格式
//
// 配置映射逻辑：
// - S3类型：使用repository-s3插件配置
// - GCS类型：使用repository-gcs插件配置
// - Azure类型：使用repository-azure插件配置
//
// 参数：
//   - repoType: 仓库类型（"archival" 或 "managed"）
//
// 返回：
//   - map[string]interface{}: ES仓库配置
//   - error: 配置构建错误
func (s *snapshotImpl) buildCloudRepositoryConfig(repoType string) (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage
	if cloudCfg == nil || !cloudCfg.Enabled {
		return nil, fmt.Errorf("云存储未启用")
	}

	switch cloudCfg.Type {
	case "s3":
		return s.buildS3RepositoryConfig(repoType)
	case "gcs":
		return s.buildGCSRepositoryConfig(repoType)
	case "azure":
		return s.buildAzureRepositoryConfig(repoType)
	default:
		return nil, fmt.Errorf("不支持的云存储类型: %s", cloudCfg.Type)
	}
}

// buildS3RepositoryConfig 构建S3仓库配置
//
// 功能说明：
// - 构建repository-s3插件所需的仓库配置
// - 支持AWS S3和S3兼容存储（如MinIO）
// - 使用与本地存储相同的路径结构：elasticsearch/{repoType}
// - 使用传入的配置参数，包括认证信息
//
// 配置参数映射：
// - bucket: 来自 CloudStorage.Bucket
// - region: 来自 CloudStorage.Region
// - endpoint: 来自 CloudStorage.Endpoint（MinIO等）
// - access_key/secret_key: 来自 CloudStorage.AccessKey/SecretKey
// - base_path: 固定为 "elasticsearch/{repoType}"
//
// 注意：ES 7.x版本可能会警告直接使用access_key/secret_key不安全，
// 但为了保持配置的一致性和简化部署，我们仍然使用传入的配置。
//
// 参数：
//   - repoType: 仓库类型（"archival" 或 "managed"）
//
// 返回：
//   - map[string]interface{}: S3仓库配置
//   - error: 配置构建错误
func (s *snapshotImpl) buildS3RepositoryConfig(repoType string) (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage

	// 使用与本地存储相同的路径结构
	basePath := fmt.Sprintf("elasticsearch/%s", repoType)

	settings := map[string]interface{}{
		"bucket":    cloudCfg.Bucket,
		"base_path": basePath,
		"client":    "default", // 指定使用我们配置的S3客户端
	}

	// 区域配置
	if cloudCfg.Region != "" {
		settings["region"] = cloudCfg.Region
	}

	// 自定义端点（MinIO等S3兼容存储）
	if cloudCfg.Endpoint != "" {
		settings["endpoint"] = cloudCfg.Endpoint
		settings["path_style_access"] = true
	}

	// 认证配置处理
	// ES 7.x不支持直接在仓库配置中传递认证信息
	// 认证信息通过S3客户端配置单独设置
	// 这里不包含认证信息，仓库配置只包含bucket、region等基本信息

	return map[string]interface{}{
		"type":     "s3",
		"settings": settings,
	}, nil
}

// createRepository 通用的仓库创建方法
//
// 功能说明：
// - 通用的ES仓库创建实现，支持任意类型的仓库配置
// - 被本地仓库和云存储仓库创建方法共同使用
// - 处理ES API调用和错误处理
//
// 参数：
//   - repoName: 仓库名称
//   - repoConfig: 仓库配置（包含type和settings）
//
// 返回：
//   - *types.BackupError: 如果操作失败则返回错误，成功则返回nil
func (s *snapshotImpl) createRepository(ctx context.Context, repoName string, repoConfig map[string]interface{}) *types.BackupError {
	configJSON, err := json.Marshal(repoConfig)
	if err != nil {
		return newElasticProviderError("JSON_MARSHAL_ERROR", "createRepository",
			fmt.Errorf("序列化仓库配置失败: %w", err), false)
	}

	createReq := esapi.SnapshotCreateRepositoryRequest{
		Repository: repoName,
		Body:       bytes.NewReader(configJSON),
	}

	createRes, err := createReq.Do(ctx, s.client)
	if err != nil {
		return newElasticProviderError("ES_API_ERROR", "createRepository",
			fmt.Errorf("创建仓库 '%s' 时发生错误: %w", repoName, err), true)
	}
	defer createRes.Body.Close()

	if createRes.IsError() {
		return newElasticProviderError("ES_API_ERROR", "createRepository",
			fmt.Errorf("创建仓库 '%s' 时返回错误: %s", repoName, createRes.String()), true)
	}

	s.logger.Info("成功创建ES仓库",
		zap.String("repository", repoName),
		zap.String("type", repoConfig["type"].(string)))
	return nil
}

// getBucketName 获取云存储bucket名称的辅助方法
//
// 功能说明：
// - 安全地获取云存储bucket名称，用于日志记录
// - 处理配置为空的情况
//
// 返回：
//   - string: bucket名称，如果未配置则返回"未配置"
func (s *snapshotImpl) getBucketName() string {
	if s.globalCfg.CloudStorage != nil && s.globalCfg.CloudStorage.Bucket != "" {
		return s.globalCfg.CloudStorage.Bucket
	}
	return "未配置"
}

// buildGCSRepositoryConfig 构建GCS仓库配置
//
// 功能说明：
// - 构建repository-gcs插件所需的仓库配置
// - 支持Google Cloud Storage
// - 使用与本地存储相同的路径结构：elasticsearch/{repoType}
//
// 配置参数映射：
// - bucket: 来自 CloudStorage.Bucket
// - project_id: 来自 CloudStorage.ProjectID
// - credentials_file: 来自 CloudStorage.CredentialsFile
// - base_path: 固定为 "elasticsearch/{repoType}"
//
// 认证方式：
// - 服务账户凭据文件：通过 GOOGLE_APPLICATION_CREDENTIALS 环境变量
// - 默认凭据：在GCP环境中自动获取
//
// 参数：
//   - repoType: 仓库类型（"archival" 或 "managed"）
//
// 返回：
//   - map[string]interface{}: GCS仓库配置
//   - error: 配置构建错误
func (s *snapshotImpl) buildGCSRepositoryConfig(repoType string) (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage

	// 使用与本地存储相同的路径结构
	basePath := fmt.Sprintf("elasticsearch/%s", repoType)

	settings := map[string]interface{}{
		"bucket":    cloudCfg.Bucket,
		"base_path": basePath,
		"client":    "default", // 指定使用默认GCS客户端
	}

	// 项目ID配置（可选，通常通过环境变量或凭据文件获取）
	if cloudCfg.ProjectID != "" {
		settings["project_id"] = cloudCfg.ProjectID
	}

	// 认证配置通过环境变量处理：
	// - GOOGLE_APPLICATION_CREDENTIALS: 服务账户凭据文件路径
	// - GOOGLE_CLOUD_PROJECT: 项目ID（可选）

	return map[string]interface{}{
		"type":     "gcs",
		"settings": settings,
	}, nil
}

// buildAzureRepositoryConfig 构建Azure仓库配置
//
// 功能说明：
// - 构建repository-azure插件所需的仓库配置
// - 支持Azure Blob Storage
// - 使用与本地存储相同的路径结构：elasticsearch/{repoType}
//
// 配置参数映射：
// - container: 来自 CloudStorage.Container
// - base_path: 固定为 "elasticsearch/{repoType}"
//
// 认证方式：
// - 环境变量（推荐）：AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY
// - 默认凭据：在Azure环境中自动获取
//
// 注意：虽然repository-azure插件支持在仓库配置中直接传递认证信息，
// 但为了与S3和GCS保持一致，以及安全考虑，我们不在仓库配置中传递认证信息。
// 认证信息必须通过环境变量或Azure默认凭据配置。
//
// 参数：
//   - repoType: 仓库类型（"archival" 或 "managed"）
//
// 返回：
//   - map[string]interface{}: Azure仓库配置
//   - error: 配置构建错误
func (s *snapshotImpl) buildAzureRepositoryConfig(repoType string) (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage

	// 使用与本地存储相同的路径结构
	basePath := fmt.Sprintf("elasticsearch/%s", repoType)

	settings := map[string]interface{}{
		"container": cloudCfg.Container,
		"base_path": basePath,
		"client":    "default", // 指定使用默认Azure客户端
	}

	// 认证配置处理
	// 与S3和GCS保持一致，不在仓库配置中传递认证信息
	// 认证信息通过Azure客户端配置单独设置
	// 这里不包含认证信息，仓库配置只包含container、base_path等基本信息

	return map[string]interface{}{
		"type":     "azure",
		"settings": settings,
	}, nil
}

// validateCloudStorageSupport 验证ES集群是否支持云存储
//
// 功能说明：
// - 检查ES集群是否安装了必要的仓库插件
// - 验证云存储认证配置是否可用
// - 确保不会在配置云存储时意外使用本地存储
//
// 验证项目：
// 1. 检查repository插件是否已安装（如repository-s3）
// 2. 验证认证信息是否可用（环境变量或keystore）
// 3. 测试基本的云存储连接性
//
// 参数：
//   - ctx: 上下文
//
// 返回：
//   - error: 验证失败的具体原因
func (s *snapshotImpl) validateCloudStorageSupport(ctx context.Context) error {
	cloudCfg := s.globalCfg.CloudStorage

	// 1. 检查插件是否安装
	if err := s.validateRepositoryPlugin(ctx, cloudCfg.Type); err != nil {
		return fmt.Errorf("仓库插件检查失败: %w", err)
	}

	// 2. 验证认证配置
	if err := s.validateCloudAuthentication(ctx, cloudCfg.Type); err != nil {
		return fmt.Errorf("认证配置验证失败: %w", err)
	}

	// 3. 测试云存储连接性（创建临时仓库）
	if err := s.testCloudStorageConnectivity(ctx); err != nil {
		return fmt.Errorf("云存储连接性测试失败: %w", err)
	}

	s.logger.Info("ES云存储支持验证通过",
		zap.String("storage_type", cloudCfg.Type),
		zap.String("bucket", cloudCfg.Bucket))

	return nil
}

// validateRepositoryPlugin 验证仓库插件是否已安装
func (s *snapshotImpl) validateRepositoryPlugin(ctx context.Context, storageType string) error {
	// 获取已安装的插件列表
	req := esapi.CatPluginsRequest{
		Format: "json",
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return fmt.Errorf("获取插件列表失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("获取插件列表返回错误: %s", res.String())
	}

	// 解析插件列表
	var plugins []map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&plugins); err != nil {
		return fmt.Errorf("解析插件列表失败: %w", err)
	}

	// 检查所需插件是否存在
	requiredPlugin := s.getRequiredPlugin(storageType)
	for _, plugin := range plugins {
		// ES插件API返回的字段名是"component"而不是"name"
		if component, ok := plugin["component"].(string); ok && component == requiredPlugin {
			s.logger.Debug("找到所需的仓库插件", zap.String("plugin", requiredPlugin))
			return nil
		}
		// 兼容性检查：也检查"name"字段
		if name, ok := plugin["name"].(string); ok && name == requiredPlugin {
			s.logger.Debug("找到所需的仓库插件", zap.String("plugin", requiredPlugin))
			return nil
		}
	}

	return fmt.Errorf("未找到所需的仓库插件: %s，请安装: elasticsearch-plugin install %s",
		requiredPlugin, requiredPlugin)
}

// getRequiredPlugin 根据存储类型获取所需的插件名称
func (s *snapshotImpl) getRequiredPlugin(storageType string) string {
	switch storageType {
	case "s3":
		return "repository-s3"
	case "gcs":
		return "repository-gcs"
	case "azure":
		return "repository-azure"
	default:
		return "repository-" + storageType
	}
}

// validateCloudAuthentication 验证云存储认证配置
func (s *snapshotImpl) validateCloudAuthentication(ctx context.Context, storageType string) error {
	cloudCfg := s.globalCfg.CloudStorage

	switch storageType {
	case "s3":
		return s.validateS3Authentication(ctx, cloudCfg)
	case "gcs":
		return s.validateGCSAuthentication(ctx, cloudCfg)
	case "azure":
		return s.validateAzureAuthentication(ctx, cloudCfg)
	default:
		return fmt.Errorf("不支持的存储类型: %s", storageType)
	}
}

// validateS3Authentication 验证S3认证配置
//
// 功能说明：
// - ES 7.x不支持在仓库配置中直接传递认证信息
// - 认证信息必须在ES进程层面配置（环境变量或keystore）
// - 我们通过创建测试仓库来验证ES是否有有效的认证配置
//
// 支持的ES认证方式：
// 1. 环境变量：在ES容器启动时设置 AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
// 2. keystore：在ES容器中执行 elasticsearch-keystore add s3.client.default.access_key
// 3. IAM角色：在AWS环境中使用EC2实例角色或EKS服务账户
func (s *snapshotImpl) validateS3Authentication(ctx context.Context, cloudCfg *types.CloudStorageConfig) error {
	// 检查配置中是否有认证信息
	if cloudCfg.AccessKey != "" && cloudCfg.SecretKey != "" {
		s.logger.Warn("配置中提供了S3认证信息，但ES 7.x不支持在仓库配置中直接使用",
			zap.String("建议", "在ES容器层面配置认证信息"))
	}

	// 无论配置中是否有认证信息，都通过创建测试仓库来验证ES的认证配置
	s.logger.Info("验证ES的S3认证配置",
		zap.String("方式", "创建测试仓库"),
		zap.String("说明", "ES必须在进程层面配置认证信息"))

	// 实际验证将在testCloudStorageConnectivity中通过创建测试仓库完成
	return nil
}

// validateS3AuthenticationByTest 通过创建测试仓库验证S3认证
func (s *snapshotImpl) validateS3AuthenticationByTest(ctx context.Context) error {
	// 这个方法将在testCloudStorageConnectivity中实现
	// 这里只是占位，实际验证通过连接性测试完成
	return nil
}

// testCloudStorageConnectivity 测试云存储连接性
func (s *snapshotImpl) testCloudStorageConnectivity(ctx context.Context) error {
	cloudCfg := s.globalCfg.CloudStorage

	// 创建临时测试仓库名称
	testRepoName := "unibackup-connectivity-test"

	// 构建测试仓库配置
	repoConfig, err := s.buildTestRepositoryConfig(cloudCfg.Type)
	if err != nil {
		return fmt.Errorf("构建测试仓库配置失败: %w", err)
	}

	// 尝试创建测试仓库
	if err := s.createRepository(ctx, testRepoName, repoConfig); err != nil {
		return s.buildCloudStorageErrorMessage(cloudCfg.Type, err)
	}

	// 清理测试仓库
	defer func() {
		if err := s.deleteRepository(ctx, testRepoName); err != nil {
			s.logger.Warn("清理测试仓库失败", zap.String("repository", testRepoName), zap.Error(err))
		}
	}()

	s.logger.Info("云存储连接性测试通过", zap.String("test_repository", testRepoName))
	return nil
}

// buildTestRepositoryConfig 构建测试仓库配置
func (s *snapshotImpl) buildTestRepositoryConfig(storageType string) (map[string]interface{}, error) {
	switch storageType {
	case "s3":
		return s.buildTestS3RepositoryConfig()
	case "gcs":
		return s.buildTestGCSRepositoryConfig()
	case "azure":
		return s.buildTestAzureRepositoryConfig()
	default:
		return nil, fmt.Errorf("不支持的存储类型: %s", storageType)
	}
}

// buildTestS3RepositoryConfig 构建S3测试仓库配置
func (s *snapshotImpl) buildTestS3RepositoryConfig() (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage

	settings := map[string]interface{}{
		"bucket":    cloudCfg.Bucket,
		"base_path": "connectivity-test",
		"client":    "default",
	}

	// 区域配置
	if cloudCfg.Region != "" {
		settings["region"] = cloudCfg.Region
	}

	// 自定义端点（MinIO等S3兼容存储）
	if cloudCfg.Endpoint != "" {
		settings["endpoint"] = cloudCfg.Endpoint
		settings["path_style_access"] = true
	}

	return map[string]interface{}{
		"type":     "s3",
		"settings": settings,
	}, nil
}

// deleteRepository 删除仓库
func (s *snapshotImpl) deleteRepository(ctx context.Context, repoName string) error {
	req := esapi.SnapshotDeleteRepositoryRequest{
		Repository: []string{repoName},
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return fmt.Errorf("删除仓库API调用失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != http.StatusNotFound {
		return fmt.Errorf("删除仓库返回错误: %s", res.String())
	}

	return nil
}

// setupS3ClientConfig 设置S3客户端配置（已弃用）
//
// 注意：此方法已不再使用，认证验证已移至validateCloudAuthentication
func (s *snapshotImpl) setupS3ClientConfig(ctx context.Context) error {
	// 此方法已被validateCloudAuthentication替代
	return nil
}

// newElasticProviderError 是一个辅助函数，用于创建与Elasticsearch提供者相关的BackupError实例
func newElasticProviderError(code, operation string, err error, retryable bool) *types.BackupError {
	return &types.BackupError{
		Code:      code,
		Message:   err.Error(),
		Component: "ElasticsearchProvider",
		Operation: operation,
		Timestamp: time.Now(),
		Details:   fmt.Sprintf("%+v", err), // 使用%+v打印详细的错误信息，包括堆栈
		Retryable: retryable,
	}
}

// getUserIndices 获取所有用户索引（排除系统索引）
func (s *snapshotImpl) getUserIndices(ctx context.Context) ([]string, error) {
	s.logger.Info("获取用户索引列表")

	// 1. 获取所有索引
	req := esapi.IndicesGetRequest{
		Index: []string{"*"}, // 获取所有索引
	}

	res, err := req.Do(ctx, s.client)
	if err != nil {
		return nil, fmt.Errorf("获取索引列表失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("获取索引列表API返回错误: %s", res.String())
	}

	// 2. 解析索引响应
	var indices map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&indices); err != nil {
		return nil, fmt.Errorf("解析索引列表响应失败: %w", err)
	}

	// 3. 构建用户索引列表（排除系统索引）
	var userIndices []string
	for indexName := range indices {
		// 跳过系统索引（以.开头的索引，如.kibana, .security等）
		if !strings.HasPrefix(indexName, ".") {
			userIndices = append(userIndices, indexName)
		}
	}

	s.logger.Info("获取用户索引列表完成", zap.Int("user_count", len(userIndices)), zap.Int("total_count", len(indices)))
	return userIndices, nil
}

// validateGCSAuthentication 验证GCS认证配置
func (s *snapshotImpl) validateGCSAuthentication(ctx context.Context, cloudCfg *types.CloudStorageConfig) error {
	// GCS认证方式检查
	// 1. 检查服务账户凭据文件
	if cloudCfg.CredentialsFile != "" {
		if _, err := os.Stat(cloudCfg.CredentialsFile); os.IsNotExist(err) {
			return fmt.Errorf("GCS凭据文件不存在: %s", cloudCfg.CredentialsFile)
		}
		s.logger.Info("检测到GCS凭据文件配置", zap.String("credentials_file", cloudCfg.CredentialsFile))
		return nil
	}

	// 2. 检查环境变量认证
	if credFile := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS"); credFile != "" {
		if _, err := os.Stat(credFile); os.IsNotExist(err) {
			return fmt.Errorf("环境变量GOOGLE_APPLICATION_CREDENTIALS指向的文件不存在: %s", credFile)
		}
		s.logger.Info("检测到GCS环境变量认证", zap.String("credentials_file", credFile))
		return nil
	}

	// 3. 检查项目ID配置
	projectID := cloudCfg.ProjectID
	if projectID == "" {
		projectID = os.Getenv("GOOGLE_CLOUD_PROJECT")
	}
	if projectID == "" {
		return fmt.Errorf("GCS项目ID未配置，请设置project_id或环境变量GOOGLE_CLOUD_PROJECT")
	}

	s.logger.Warn("GCS认证配置不完整，可能依赖默认凭据",
		zap.String("project_id", projectID),
		zap.String("建议", "配置服务账户凭据文件或环境变量GOOGLE_APPLICATION_CREDENTIALS"))

	return nil
}

// validateAzureAuthentication 验证Azure认证配置
func (s *snapshotImpl) validateAzureAuthentication(ctx context.Context, cloudCfg *types.CloudStorageConfig) error {
	// 检查配置中是否有认证信息
	if cloudCfg.AccountName != "" && cloudCfg.AccountKey != "" {
		s.logger.Warn("配置中提供了Azure认证信息，但为了安全考虑，建议在ES容器层面配置",
			zap.String("建议", "在ES容器层面配置环境变量AZURE_STORAGE_ACCOUNT和AZURE_STORAGE_KEY"))
	}

	// 检查容器配置
	if cloudCfg.Container == "" {
		return fmt.Errorf("Azure容器名称未配置，请设置container")
	}

	// 无论配置中是否有认证信息，都通过创建测试仓库来验证ES的认证配置
	s.logger.Info("验证ES的Azure认证配置",
		zap.String("方式", "创建测试仓库"),
		zap.String("说明", "ES必须在进程层面配置认证信息"))

	// 实际验证将在testCloudStorageConnectivity中通过创建测试仓库完成
	return nil
}

// buildTestGCSRepositoryConfig 构建GCS测试仓库配置
func (s *snapshotImpl) buildTestGCSRepositoryConfig() (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage

	settings := map[string]interface{}{
		"bucket":    cloudCfg.Bucket,
		"base_path": "connectivity-test",
		"client":    "default",
	}

	// 项目ID配置
	if cloudCfg.ProjectID != "" {
		settings["project_id"] = cloudCfg.ProjectID
	}

	return map[string]interface{}{
		"type":     "gcs",
		"settings": settings,
	}, nil
}

// buildTestAzureRepositoryConfig 构建Azure测试仓库配置
func (s *snapshotImpl) buildTestAzureRepositoryConfig() (map[string]interface{}, error) {
	cloudCfg := s.globalCfg.CloudStorage

	settings := map[string]interface{}{
		"container": cloudCfg.Container,
		"base_path": "connectivity-test",
		"client":    "default",
	}

	// 认证配置处理
	// 与S3和GCS保持一致，不在仓库配置中传递认证信息
	// 认证信息通过Azure客户端配置单独设置

	return map[string]interface{}{
		"type":     "azure",
		"settings": settings,
	}, nil
}

// buildCloudStorageErrorMessage 构建云存储错误消息
func (s *snapshotImpl) buildCloudStorageErrorMessage(storageType string, originalErr error) error {
	switch storageType {
	case "s3":
		return fmt.Errorf("S3连接测试失败，可能的原因：\n"+
			"1. ES环境变量未设置: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY\n"+
			"2. ES keystore未配置: elasticsearch-keystore add s3.client.default.access_key\n"+
			"3. S3连接问题: 检查bucket、region、endpoint配置\n"+
			"4. 权限问题: 检查S3访问权限\n"+
			"原始错误: %w", originalErr)
	case "gcs":
		return fmt.Errorf("GCS连接测试失败，可能的原因：\n"+
			"1. ES环境变量未设置: GOOGLE_APPLICATION_CREDENTIALS\n"+
			"2. GCS凭据文件不存在或无效\n"+
			"3. GCS连接问题: 检查bucket、project_id配置\n"+
			"4. 权限问题: 检查GCS访问权限\n"+
			"5. ES插件: 确保已安装repository-gcs插件\n"+
			"原始错误: %w", originalErr)
	case "azure":
		return fmt.Errorf("Azure连接测试失败，可能的原因：\n"+
			"1. ES环境变量未设置: AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY\n"+
			"2. Azure认证配置错误: 检查account_name、account_key\n"+
			"3. Azure连接问题: 检查container配置\n"+
			"4. 权限问题: 检查Azure存储访问权限\n"+
			"5. ES插件: 确保已安装repository-azure插件\n"+
			"原始错误: %w", originalErr)
	default:
		return fmt.Errorf("云存储连接测试失败: %w", originalErr)
	}
}

// cleanupStaleRepositoriesIfNeeded 清理路径变更后的旧仓库配置
// 当备份路径发生变更时，ES 中可能还保留着指向旧路径的仓库配置
// 这会导致仓库存在但无法访问的问题，需要删除旧配置让系统重新创建
func (s *snapshotImpl) cleanupStaleRepositoriesIfNeeded(ctx context.Context) error {
	s.logger.Info("检查是否需要清理旧仓库配置")

	// 获取当前应该使用的仓库路径
	expectedPaths := s.getExpectedRepositoryPaths()

	// 检查归档仓库
	if err := s.cleanupStaleRepository(ctx, s.cfg.ArchivalRepoName, expectedPaths.archival); err != nil {
		return fmt.Errorf("清理归档仓库失败: %w", err)
	}

	// 检查托管仓库
	if err := s.cleanupStaleRepository(ctx, s.cfg.ManagedRepoName, expectedPaths.managed); err != nil {
		return fmt.Errorf("清理托管仓库失败: %w", err)
	}

	return nil
}

// repositoryPaths 仓库路径结构
type repositoryPaths struct {
	archival string
	managed  string
}

// getExpectedRepositoryPaths 获取当前配置下期望的仓库路径
func (s *snapshotImpl) getExpectedRepositoryPaths() repositoryPaths {
	// 确定仓库基础路径（与 ensureLocalRepositories 逻辑一致）
	repoBasePath := s.cfg.RepoBasePath
	if repoBasePath == "" {
		esDataSourcePath := s.storageManager.GetDataSourcePath(types.Elasticsearch)
		repoBasePath = esDataSourcePath
	} else {
		repoBasePath = filepath.Join(repoBasePath, "elasticsearch")
	}

	return repositoryPaths{
		archival: filepath.Join(repoBasePath, "snapshots_archival"),
		managed:  filepath.Join(repoBasePath, "snapshots_chains"),
	}
}

// cleanupStaleRepository 检查并清理单个仓库的旧配置
func (s *snapshotImpl) cleanupStaleRepository(ctx context.Context, repoName, expectedPath string) error {
	// 检查仓库是否存在
	checkReq := esapi.SnapshotGetRepositoryRequest{
		Repository: []string{repoName},
	}

	checkRes, err := checkReq.Do(ctx, s.client)
	if err != nil {
		return fmt.Errorf("检查仓库 %s 失败: %w", repoName, err)
	}
	defer checkRes.Body.Close()

	// 如果仓库不存在，无需清理
	if checkRes.StatusCode == 404 {
		s.logger.Debug("仓库不存在，无需清理", zap.String("repository", repoName))
		return nil
	}

	if checkRes.IsError() {
		return fmt.Errorf("检查仓库 %s 时出错: %s", repoName, checkRes.String())
	}

	// 解析仓库配置
	var repoConfig map[string]interface{}
	if err := json.NewDecoder(checkRes.Body).Decode(&repoConfig); err != nil {
		return fmt.Errorf("解析仓库配置失败: %w", err)
	}

	// 检查仓库路径是否匹配期望路径，并且当前路径确实不可访问
	if s.isRepositoryPathStale(repoConfig, repoName, expectedPath) {
		// 额外安全检查：尝试验证仓库是否真的不可访问
		if s.isRepositoryAccessible(ctx, repoName) {
			s.logger.Info("仓库路径不匹配但仓库仍可访问，跳过清理",
				zap.String("repository", repoName),
				zap.String("expected_path", expectedPath))
			return nil
		}

		s.logger.Info("检测到仓库路径已过期且不可访问，删除旧配置",
			zap.String("repository", repoName),
			zap.String("expected_path", expectedPath))

		// 删除旧仓库配置
		if err := s.deleteRepository(ctx, repoName); err != nil {
			return fmt.Errorf("删除旧仓库配置失败: %w", err)
		}

		s.logger.Info("旧仓库配置已删除，系统将自动重新创建", zap.String("repository", repoName))
	}

	return nil
}

// isRepositoryPathStale 检查仓库路径是否已过期
func (s *snapshotImpl) isRepositoryPathStale(repoConfig map[string]interface{}, repoName, expectedPath string) bool {
	// ES 返回的仓库配置结构：
	// {
	//   "repo_name": {
	//     "type": "fs",
	//     "settings": {
	//       "location": "/path/to/repo"
	//     }
	//   }
	// }

	// 获取仓库配置
	repoData, ok := repoConfig[repoName]
	if !ok {
		s.logger.Warn("仓库配置中找不到仓库数据", zap.String("repository", repoName))
		// 打印完整配置用于调试
		s.logger.Debug("完整仓库配置", zap.Any("config", repoConfig))
		return false
	}

	repoInfo, ok := repoData.(map[string]interface{})
	if !ok {
		s.logger.Warn("仓库数据格式不正确", zap.String("repository", repoName), zap.Any("data", repoData))
		return false
	}

	settings, ok := repoInfo["settings"].(map[string]interface{})
	if !ok {
		s.logger.Warn("仓库设置格式不正确", zap.String("repository", repoName), zap.Any("repo_info", repoInfo))
		return false
	}

	currentPath, ok := settings["location"].(string)
	if !ok {
		s.logger.Warn("仓库路径格式不正确", zap.String("repository", repoName), zap.Any("settings", settings))
		return false
	}

	// 比较路径是否匹配
	pathsMatch := currentPath == expectedPath
	if !pathsMatch {
		s.logger.Info("仓库路径不匹配",
			zap.String("repository", repoName),
			zap.String("current_path", currentPath),
			zap.String("expected_path", expectedPath))
	} else {
		s.logger.Debug("仓库路径匹配",
			zap.String("repository", repoName),
			zap.String("path", currentPath))
	}

	return !pathsMatch
}

// isRepositoryAccessible 检查仓库是否可访问
// 通过尝试验证仓库来判断仓库是否真的不可用
func (s *snapshotImpl) isRepositoryAccessible(ctx context.Context, repoName string) bool {
	// 尝试验证仓库
	verifyReq := esapi.SnapshotVerifyRepositoryRequest{
		Repository: repoName,
	}

	verifyRes, err := verifyReq.Do(ctx, s.client)
	if err != nil {
		s.logger.Debug("仓库验证请求失败",
			zap.String("repository", repoName),
			zap.Error(err))
		return false
	}
	defer verifyRes.Body.Close()

	// 如果验证成功，说明仓库可访问
	if !verifyRes.IsError() {
		s.logger.Debug("仓库验证成功，仓库可访问", zap.String("repository", repoName))
		return true
	}

	s.logger.Debug("仓库验证失败，仓库不可访问",
		zap.String("repository", repoName),
		zap.String("error", verifyRes.String()))
	return false
}
