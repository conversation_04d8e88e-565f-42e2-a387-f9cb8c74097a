package elasticsearch

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
)

// TestElasticsearchKeystoreAuth 测试ES keystore认证方式
func TestElasticsearchKeystoreAuth(t *testing.T) {
	fmt.Println("🧪 测试ES keystore认证方式")
	fmt.Println("============================================================")

	// 测试ES云存储支持验证
	fmt.Println("🔍 验证ES云存储支持...")

	// 等待ES完全启动
	time.Sleep(5 * time.Second)
	
	// 检查ES是否可访问
	resp, err := http.Get("http://unibackup-test-elasticsearch:9200/_cluster/health")
	if err != nil {
		t.Fatalf("无法连接到ES: %v", err)
	}
	resp.Body.Close()
	
	if resp.StatusCode != 200 {
		t.Fatalf("ES健康检查失败，状态码: %d", resp.StatusCode)
	}
	
	fmt.Println("✅ ES连接正常")

	// 检查keystore是否设置了S3认证信息
	fmt.Println("🔑 检查ES keystore设置...")
	
	// 创建一个测试S3仓库来验证认证
	repoName := "keystore-auth-test"
	repoConfig := map[string]interface{}{
		"type": "s3",
		"settings": map[string]interface{}{
			"bucket":            "test-backup-bucket",
			"endpoint":          "http://unibackup-test-minio:9000",
			"path_style_access": true,
			"base_path":         "keystore-test",
			"region":            "us-east-1",
			// 不在这里设置access_key和secret_key，应该从keystore读取
		},
	}
	
	repoJSON, _ := json.Marshal(repoConfig)
	
	// 创建仓库
	req, err := http.NewRequest("PUT", 
		fmt.Sprintf("http://unibackup-test-elasticsearch:9200/_snapshot/%s", repoName),
		bytes.NewBuffer(repoJSON))
	if err != nil {
		t.Fatalf("创建请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err = client.Do(req)
	if err != nil {
		t.Fatalf("创建S3仓库请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		var buf bytes.Buffer
		buf.ReadFrom(resp.Body)
		t.Fatalf("创建S3仓库失败，状态码: %d, 响应: %s", resp.StatusCode, buf.String())
	}
	
	fmt.Printf("✅ 成功创建S3仓库: %s (使用keystore认证)\n", repoName)
	
	// 验证仓库
	req, err = http.NewRequest("POST",
		fmt.Sprintf("http://unibackup-test-elasticsearch:9200/_snapshot/%s/_verify", repoName), nil)
	if err != nil {
		t.Fatalf("创建验证请求失败: %v", err)
	}

	resp, err = client.Do(req)
	if err != nil {
		t.Fatalf("验证仓库请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		var buf bytes.Buffer
		buf.ReadFrom(resp.Body)
		t.Fatalf("验证S3仓库失败，状态码: %d, 响应: %s", resp.StatusCode, buf.String())
	}
	
	fmt.Printf("✅ S3仓库验证成功: %s\n", repoName)
	
	// 清理测试仓库
	req, _ = http.NewRequest("DELETE", 
		fmt.Sprintf("http://unibackup-test-elasticsearch:9200/_snapshot/%s", repoName), nil)
	client.Do(req)
	
	fmt.Println("🎉 keystore认证测试通过！")
	
	fmt.Println("🎉 所有测试通过！keystore认证方式工作正常！")
}
