package mysql

import (
	"context"
	"testing"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// TestExtractBinlogSequence 测试binlog序号提取
func TestExtractBinlogSequence(t *testing.T) {
	tool := &toolImpl{}
	
	tests := []struct {
		name     string
		binlog   string
		expected int
	}{
		{"正常格式", "mysql-bin.000123", 123},
		{"小序号", "mysql-bin.000001", 1},
		{"大序号", "mysql-bin.999999", 999999},
		{"无效格式", "invalid-binlog", 0},
		{"空字符串", "", 0},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.extractBinlogSequence(tt.binlog)
			if result != tt.expected {
				t.Errorf("extractBinlogSequence(%s) = %d, want %d", tt.binlog, result, tt.expected)
			}
		})
	}
}

// TestIsBinlogReset 测试binlog重置检测
func TestIsBinlogReset(t *testing.T) {
	tool := &toolImpl{}
	
	tests := []struct {
		name           string
		prevBinlog     string
		currentBinlogs []string
		expected       bool
	}{
		{
			name:           "系统重装场景",
			prevBinlog:     "mysql-bin.000123",
			currentBinlogs: []string{"mysql-bin.000001", "mysql-bin.000002"},
			expected:       true,
		},
		{
			name:           "正常增长",
			prevBinlog:     "mysql-bin.000123",
			currentBinlogs: []string{"mysql-bin.000124", "mysql-bin.000125"},
			expected:       false,
		},
		{
			name:           "小幅回退不算重置",
			prevBinlog:     "mysql-bin.000010",
			currentBinlogs: []string{"mysql-bin.000008", "mysql-bin.000009"},
			expected:       false,
		},
		{
			name:           "空binlog列表",
			prevBinlog:     "mysql-bin.000123",
			currentBinlogs: []string{},
			expected:       false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result := tool.isBinlogReset(ctx, tt.prevBinlog, tt.currentBinlogs)
			if result != tt.expected {
				t.Errorf("isBinlogReset(%s, %v) = %t, want %t",
					tt.prevBinlog, tt.currentBinlogs, result, tt.expected)
			}
		})
	}
}

// TestDetectBinlogScenario 测试场景检测
func TestDetectBinlogScenario(t *testing.T) {
	tool := &toolImpl{}
	
	tests := []struct {
		name           string
		prevRecord     *types.BackupRecord
		currentBinlogs []string
		expected       string
	}{
		{
			name: "binlog重置场景",
			prevRecord: &types.BackupRecord{
				BinlogFile: "mysql-bin.000123",
			},
			currentBinlogs: []string{"mysql-bin.000001", "mysql-bin.000002"},
			expected:       "binlog_reset",
		},
		{
			name: "binlog清理场景",
			prevRecord: &types.BackupRecord{
				BinlogFile: "mysql-bin.000123",
			},
			currentBinlogs: []string{"mysql-bin.000124", "mysql-bin.000125"},
			expected:       "binlog_cleaned",
		},
		{
			name: "正常场景",
			prevRecord: &types.BackupRecord{
				BinlogFile: "mysql-bin.000123",
			},
			currentBinlogs: []string{"mysql-bin.000123", "mysql-bin.000124"},
			expected:       "normal",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result := tool.detectBinlogScenario(ctx, tt.prevRecord, tt.currentBinlogs)
			if result != tt.expected {
				t.Errorf("detectBinlogScenario() = %s, want %s", result, tt.expected)
			}
		})
	}
}

// TestCreateBinlogScenarioError 测试错误信息创建
func TestCreateBinlogScenarioError(t *testing.T) {
	tool := &toolImpl{}
	
	prevRecord := &types.BackupRecord{
		BinlogFile: "mysql-bin.000123",
	}
	currentBinlogs := []string{"mysql-bin.000001", "mysql-bin.000002"}
	
	// 测试binlog重置错误
	resetErr := tool.createBinlogScenarioError("binlog_reset", prevRecord, currentBinlogs)
	if resetErr == nil {
		t.Error("createBinlogScenarioError should return error for binlog_reset")
	}
	if resetErr.Code != "BINLOG_RESET_DETECTED" {
		t.Errorf("Expected error code BINLOG_RESET_DETECTED, got %s", resetErr.Code)
	}
	
	// 测试binlog清理错误
	cleanedErr := tool.createBinlogScenarioError("binlog_cleaned", prevRecord, currentBinlogs)
	if cleanedErr == nil {
		t.Error("createBinlogScenarioError should return error for binlog_cleaned")
	}
	if cleanedErr.Code != "BINLOG_CONTINUITY_BROKEN" {
		t.Errorf("Expected error code BINLOG_CONTINUITY_BROKEN, got %s", cleanedErr.Code)
	}
}

// TestBinlogResetThresholds 测试binlog重置阈值的边界情况
func TestBinlogResetThresholds(t *testing.T) {
	tool := &toolImpl{}

	tests := []struct {
		name           string
		prevBinlog     string
		currentBinlogs []string
		expected       bool
		description    string
	}{
		{
			name:           "刚好达到低阈值",
			prevBinlog:     "mysql-bin.000011",
			currentBinlogs: []string{"mysql-bin.000005"},
			expected:       true,
			description:    "当前序号=5，上次序号=11，应该检测为重置",
		},
		{
			name:           "超过低阈值",
			prevBinlog:     "mysql-bin.000011",
			currentBinlogs: []string{"mysql-bin.000006"},
			expected:       false,
			description:    "当前序号=6，超过低阈值5，不应该检测为重置",
		},
		{
			name:           "刚好达到高阈值",
			prevBinlog:     "mysql-bin.000010",
			currentBinlogs: []string{"mysql-bin.000001"},
			expected:       false,
			description:    "上次序号=10，刚好等于高阈值，不应该检测为重置",
		},
		{
			name:           "超过高阈值",
			prevBinlog:     "mysql-bin.000011",
			currentBinlogs: []string{"mysql-bin.000001"},
			expected:       true,
			description:    "上次序号=11，超过高阈值10，应该检测为重置",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result := tool.isBinlogReset(ctx, tt.prevBinlog, tt.currentBinlogs)
			if result != tt.expected {
				t.Errorf("%s: isBinlogReset(%s, %v) = %t, want %t",
					tt.description, tt.prevBinlog, tt.currentBinlogs, result, tt.expected)
			}
		})
	}
}
