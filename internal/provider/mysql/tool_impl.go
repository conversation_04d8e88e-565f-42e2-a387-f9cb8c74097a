package mysql

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	berrors "git.gobies.org/fobrain/unibackup/internal/errors"
	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"

	_ "github.com/go-sql-driver/mysql" // 注册MySQL驱动
	"go.uber.org/zap"
)

// binlog重置检测现在使用智能算法，不再依赖硬编码阈值

// 预编译的正则表达式，避免重复编译
var (
	binlogSequenceRegex = regexp.MustCompile(`mysql-bin\.(\d+)$`)
)

// toolImpl 使用 mysqldump, mysqlbinlog, mysql 等外部命令行工具来执行备份和恢复。
// 这个策略依赖于执行环境中已安装并正确配置了这些工具。
// 在废弃了有风险的 "native" 实现后，它现在是 UniBackup 中针对 MySQL 的唯一实现。
// 阶段二改造：注入 StorageManager 依赖，消除直接文件系统操作
type toolImpl struct {
	cfg            *types.MySQLConfig
	logger         *zap.Logger
	storageManager *storage.Manager // 阶段二新增：StorageManager 依赖注入
}

// validateMySQLConfig 验证MySQL配置的安全性，防止命令注入
func validateMySQLConfig(cfg *types.MySQLConfig) *types.BackupError {
	// 验证数据库名称只包含安全字符
	if !regexp.MustCompile(`^[a-zA-Z0-9_]+$`).MatchString(cfg.DBName) {
		return berrors.NewMySQLToolImplError("INVALID_CONFIG", "validateMySQLConfig", fmt.Errorf("数据库名称包含非法字符: %s", cfg.DBName), false)
	}
	// 验证主机名
	if !regexp.MustCompile(`^[a-zA-Z0-9.-]+$`).MatchString(cfg.Host) {
		return berrors.NewMySQLToolImplError("INVALID_CONFIG", "validateMySQLConfig", fmt.Errorf("主机名包含非法字符: %s", cfg.Host), false)
	}
	// 验证用户名
	if !regexp.MustCompile(`^[a-zA-Z0-9_]+$`).MatchString(cfg.User) {
		return berrors.NewMySQLToolImplError("INVALID_CONFIG", "validateMySQLConfig", fmt.Errorf("用户名包含非法字符: %s", cfg.User), false)
	}
	return nil
}

// newToolImpl 创建一个新的 toolImpl 实例。
// 在创建实例之前，它会严格检查所有必需的命令行工具是否存在且可执行。
// 这种前置检查可以防止在任务执行中途因环境问题而失败。
// 阶段二改造：接受 StorageManager 参数，实现依赖注入
func newToolImpl(cfg *types.MySQLConfig, storageManager *storage.Manager, logger *zap.Logger) (*toolImpl, *types.BackupError) {
	// 首先验证配置安全性
	if err := validateMySQLConfig(cfg); err != nil {
		return nil, err
	}
	// 检查并验证所有必需工具的路径。
	// 我们不使用 `exec.LookPath`，因为用户应在配置中明确提供工具的完整路径，
	// 这样可以避免依赖不确定的系统PATH环境变量，使行为更可预测。
	// 注意：mysqlbinlog 仅在增量备份时需要，对于全量备份是可选的
	tools := map[string]string{
		"mysqldump":  cfg.ToolsPath.Mysqldump,
		"mysql":      cfg.ToolsPath.Mysql,
		"mysqladmin": cfg.ToolsPath.Mysqladmin,
	}

	for name, path := range tools {
		if path == "" {
			return nil, berrors.NewMySQLToolImplError("TOOL_PATH_MISSING", "newToolImpl", fmt.Errorf("MySQL工具 '%s' 的路径未在配置中提供", name), false)
		}
		info, err := os.Stat(path)
		if os.IsNotExist(err) {
			return nil, berrors.NewMySQLToolImplError("TOOL_NOT_FOUND", "newToolImpl", fmt.Errorf("MySQL工具 '%s' 在指定路径 '%s' 未找到", name, path), false)
		}
		if err != nil {
			return nil, berrors.NewMySQLToolImplError("FILE_SYSTEM_ERROR", "newToolImpl", fmt.Errorf("无法获取MySQL工具 '%s' 的文件状态: %w", name, err), false)
		}
		// 简单的可执行权限检查（检查 'x' 位）。
		if info.Mode()&0111 == 0 {
			return nil, berrors.NewMySQLToolImplError("PERMISSION_DENIED", "newToolImpl", fmt.Errorf("MySQL工具 '%s' 在路径 '%s' 上没有执行权限", name, path), false)
		}
	}

	// 可选检查 mysqlbinlog（增量备份需要）
	if cfg.ToolsPath.Mysqlbinlog != "" {
		if info, err := os.Stat(cfg.ToolsPath.Mysqlbinlog); err == nil && info.Mode()&0111 != 0 {
			// mysqlbinlog 可用，记录日志
			logger.Info("mysqlbinlog工具可用，支持增量备份", zap.String("path", cfg.ToolsPath.Mysqlbinlog))
		} else {
			// mysqlbinlog 不可用，记录警告但不阻止初始化
			logger.Warn("mysqlbinlog工具不可用，仅支持全量备份", zap.String("path", cfg.ToolsPath.Mysqlbinlog), zap.Error(err))
		}
	}

	return &toolImpl{
		cfg:            cfg,
		logger:         logger,
		storageManager: storageManager, // 阶段二：注入 StorageManager
	}, nil
}

// backup 是 toolImpl 的核心备份方法。它根据备份类型选择执行全量备份还是增量备份。
func (t *toolImpl) backup(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	switch record.Type {
	case types.BackupTypeArchival, types.BackupTypeChainInitial:
		return t.fullBackup(ctx, record)
	case types.BackupTypeChainIncremental:
		if prevRecord == nil {
			// 增量备份严格依赖于前一个备份点，如果缺失则无法进行。
			return berrors.NewMySQLToolImplError("INVALID_ARGUMENT", "backup", errors.New("无法在没有上一个备份记录的情况下执行增量备份"), false)
		}
		return t.incrementalBackup(ctx, record, prevRecord)
	default:
		return berrors.NewMySQLToolImplError("UNKNOWN_BACKUP_TYPE", "backup", fmt.Errorf("未知的备份类型: %s", record.Type), false)
	}
}

// detectMysqldumpDataParam 检测mysqldump工具支持的binlog数据参数
func (t *toolImpl) detectMysqldumpDataParam() string {
	// 运行 mysqldump --help 来检测支持的参数
	cmd := exec.Command(t.cfg.ToolsPath.Mysqldump, "--help")
	output, err := cmd.Output()
	if err != nil {
		// 如果检测失败，默认使用较老的--master-data（MariaDB兼容）
		return "--master-data=2"
	}

	outputStr := string(output)
	if strings.Contains(outputStr, "--source-data") {
		// MySQL 8.0+ 支持 --source-data
		return "--source-data=2"
	} else {
		// MariaDB 或较老的 MySQL 使用 --master-data
		return "--master-data=2"
	}
}

// fullBackup 使用 mysqldump 执行一次完整的数据库备份。
func (t *toolImpl) fullBackup(ctx context.Context, record *types.BackupRecord) *types.BackupError {
	t.logger.Info("正在使用 'tool' 方法执行MySQL全量备份", zap.String("backup_id", record.ID))

	// 自动检测使用哪个binlog数据参数
	binlogParam := t.detectMysqldumpDataParam()
	t.logger.Debug("检测到mysqldump binlog参数", zap.String("param", binlogParam))

	// 构建 mysqldump 命令。参数选择是保证备份一致性和可恢复性的关键。
	// --single-transaction: 对于InnoDB表，确保创建一个一致性的快照，避免锁表。
	// --flush-logs: 刷新日志，确保后续的增量备份可以从一个新的、干净的日志文件开始。
	// --master-data=2/--source-data=2: 在输出中包含一个被注释掉的 CHANGE MASTER/SOURCE TO 语句，这是获取当前binlog位置的最可靠方法。
	args := []string{
		"--host", t.cfg.Host,
		"--port", fmt.Sprintf("%d", t.cfg.Port),
		"--user", t.cfg.User,
		"--single-transaction",
		"--flush-logs",
		binlogParam,
		t.cfg.DBName,
	}

	cmd := exec.CommandContext(ctx, t.cfg.ToolsPath.Mysqldump, args...)
	// 安全警告：密码绝不应通过命令行参数传递。使用环境变量是更安全的方式，可以避免在进程列表中暴露密码。
	cmd.Env = append(os.Environ(), fmt.Sprintf("MYSQL_PWD=%s", t.cfg.Password))

	// 获取命令的标准输出和标准错误管道。
	stdout, stdoutErr := cmd.StdoutPipe()
	if stdoutErr != nil {
		return berrors.NewMySQLToolImplError("PIPE_ERROR", "fullBackup", stdoutErr, false)
	}
	stderr, stderrErr := cmd.StderrPipe()
	if stderrErr != nil {
		return berrors.NewMySQLToolImplError("PIPE_ERROR", "fullBackup", stderrErr, false)
	}

	// 启动命令。
	if err := cmd.Start(); err != nil {
		return berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "fullBackup", fmt.Errorf("启动mysqldump命令失败: %w", err), false)
	}

	// 并发地从 stderr 和 stdout 解析 binlog 信息。
	binlogChan := make(chan struct {
		file string
		pos  string
	})

	// 创建一个管道用于同时读取stdout并解析binlog信息
	stdoutReader, stdoutWriter := io.Pipe()

	// 创建一个上下文用于控制goroutine生命周期，防止goroutine泄露
	_, cancelGoroutine := context.WithCancel(ctx)
	defer cancelGoroutine() // 确保在函数退出时取消goroutine

	// 启动goroutine解析binlog信息（同时监听stderr和stdout）
	go func() {
		defer close(binlogChan) // 确保在goroutine退出时关闭通道
		t.parseBinlogInfoFromBoth(stderr, stdoutReader, binlogChan)
	}()

	// 阶段二改造：使用 StorageManager.GetBackupWriter 替代直接文件操作
	writer, backupErr := t.storageManager.GetBackupWriter(record, "data.sql.gz")
	if backupErr != nil {
		stdoutWriter.Close() // 确保关闭writer以避免goroutine泄露
		return berrors.NewMySQLToolImplError("FILE_WRITE_ERROR", "fullBackup", fmt.Errorf("创建备份写入器失败: %w", backupErr), false)
	}
	defer writer.Close()

	// 使用计数写入器跟踪写入的字节数
	countingWriter := &countingWriter{writer: writer}
	gz := gzip.NewWriter(countingWriter)
	defer gz.Close()

	// 使用TeeReader将stdout同时写入文件和管道
	teeReader := io.TeeReader(stdout, stdoutWriter)

	// 将 mysqldump 的输出流式传输到压缩文件中。
	if _, err := io.Copy(gz, teeReader); err != nil {
		stdoutWriter.Close() // 确保关闭writer
		// 给goroutine一些时间来处理剩余数据，然后取消
		select {
		case <-binlogChan: // 尝试接收可能已经解析的数据
		case <-time.After(time.Second): // 最多等待1秒
		}
		return berrors.NewMySQLToolImplError("FILE_WRITE_ERROR", "fullBackup", fmt.Errorf("写入备份数据失败: %w", err), false)
	}

	// 关闭writer以通知reader数据传输完成
	stdoutWriter.Close()

	// 等待命令执行完成。
	if err := cmd.Wait(); err != nil {
		// 等待goroutine完成或超时
		select {
		case <-binlogChan:
		case <-time.After(5 * time.Second): // 最多等待5秒
			t.logger.Warn("Binlog解析goroutine超时，强制退出")
		}
		return berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "fullBackup", fmt.Errorf("mysqldump命令执行失败: %w", err), false)
	}

	// 从通道中获取 binlog 信息并更新备份记录，设置超时防止无限等待。
	select {
	case binlogInfo, ok := <-binlogChan:
		if !ok {
			return berrors.NewMySQLToolImplError("BINLOG_PARSE_ERROR", "fullBackup", errors.New("无法从mysqldump的输出中解析出binlog信息"), false)
		}
		record.BinlogFile = binlogInfo.file
		pos, err := strconv.ParseUint(binlogInfo.pos, 10, 32)
		if err != nil {
			return berrors.NewMySQLToolImplError("BINLOG_PARSE_ERROR", "fullBackup", fmt.Errorf("无法将binlog位置 '%s' 解析为数字: %w", binlogInfo.pos, err), false)
		}
		record.BinlogPos = uint32(pos)
	case <-time.After(10 * time.Second):
		return berrors.NewMySQLToolImplError("BINLOG_PARSE_ERROR", "fullBackup", errors.New("等待binlog信息解析超时"), false)
	}

	// 设置备份文件大小
	record.Size = countingWriter.bytesWritten

	t.logger.Info("MySQL全量备份（使用 'tool' 方法）成功完成",
		zap.String("backup_id", record.ID),
		zap.String("binlog_file", record.BinlogFile),
		zap.Uint32("binlog_pos", record.BinlogPos),
		zap.String("size", types.FormatBackupSize(record.Size)))
	return nil
}

// parseBinlogFromStderr 使用正则表达式简单解析 binlog 信息
func (t *toolImpl) parseBinlogFromStderr(line string) (file, pos string, found bool) {
	// 使用正则表达式匹配常见的 binlog 格式
	// 匹配: MASTER_LOG_FILE='xxx', MASTER_LOG_POS=123 或 SOURCE_LOG_FILE='xxx', SOURCE_LOG_POS=123
	re := regexp.MustCompile(`(?:MASTER|SOURCE)_LOG_FILE='([^']+)'.*?(?:MASTER|SOURCE)_LOG_POS=(\d+)`)
	matches := re.FindStringSubmatch(line)
	if len(matches) == 3 {
		t.logger.Info("通过正则表达式解析到binlog信息", zap.String("file", matches[1]), zap.String("pos", matches[2]))
		return matches[1], matches[2], true
	}
	return "", "", false
}

// getBinlogFromDatabase 直接从数据库获取当前 binlog 信息
func (t *toolImpl) getBinlogFromDatabase() (file, pos string, err error) {
	t.logger.Debug("尝试从数据库获取binlog信息")

	db, dbErr := t.getDBConn(context.Background())
	if dbErr != nil {
		t.logger.Error("获取数据库连接失败", zap.Error(dbErr))
		return "", "", fmt.Errorf("获取数据库连接失败: %w", dbErr)
	}
	defer db.Close()

	binlogFile, binlogPos, bErr := t.getMasterStatusFromConn(context.Background(), db)
	if bErr != nil {
		t.logger.Error("查询主状态失败", zap.Error(bErr))
		return "", "", fmt.Errorf("查询主状态失败: %w", bErr)
	}

	t.logger.Debug("成功从数据库获取binlog信息", zap.String("file", binlogFile), zap.Uint32("pos", binlogPos))
	return binlogFile, strconv.FormatUint(uint64(binlogPos), 10), nil
}

// parseBinlogInfoFromBoth 从stderr和stdout两个流中解析binlog信息
func (t *toolImpl) parseBinlogInfoFromBoth(stderr, stdout io.Reader, ch chan struct {
	file string
	pos  string
}) {
	// 注意：不在这里关闭通道，由调用方的goroutine负责关闭

	var binlogInfo *struct {
		file string
		pos  string
	}

	// 优先尝试从数据库直接获取 binlog 信息（更可靠）
	if file, pos, err := t.getBinlogFromDatabase(); err == nil {
		t.logger.Info("从数据库获取到binlog信息", zap.String("file", file), zap.String("pos", pos))
		binlogInfo = &struct {
			file string
			pos  string
		}{file: file, pos: pos}
		// 注意：不要立即return，需要继续读取stdout流以避免pipe阻塞
	} else {
		t.logger.Warn("数据库查询binlog失败，尝试解析mysqldump输出", zap.Error(err))
	}

	// 无论数据库查询是否成功，都要读取完整个stdout流
	t.logger.Info("从mysqldump stdout输出解析binlog信息")

	scanner := bufio.NewScanner(stdout)
	// 增加缓冲区大小以处理长行（如大的INSERT语句）
	// 设置为1MB缓冲区，足以处理大多数mysqldump输出
	buf := make([]byte, 0, 64*1024)
	scanner.Buffer(buf, 1024*1024) // 1MB最大token大小
	lineCount := 0
	var stdoutBinlogInfo *struct {
		file string
		pos  string
	}

	// 读取整个stdout流，避免提前关闭pipe
	for scanner.Scan() {
		line := scanner.Text()
		lineCount++
		t.logger.Debug("mysqldump stdout", zap.Int("line_num", lineCount), zap.String("line", line))

		// 如果数据库查询失败，从stdout解析binlog信息
		if binlogInfo == nil && stdoutBinlogInfo == nil {
			if file, pos, found := t.parseBinlogFromStderr(line); found {
				t.logger.Info("从stdout解析到binlog信息", zap.String("file", file), zap.String("pos", pos), zap.Int("line_num", lineCount))
				stdoutBinlogInfo = &struct {
					file string
					pos  string
				}{file: file, pos: pos}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		t.logger.Error("扫描stdout时发生错误", zap.Error(err))
	}

	// 选择使用的binlog信息：优先使用数据库查询结果，其次使用stdout解析结果
	var finalBinlogInfo *struct {
		file string
		pos  string
	}

	if binlogInfo != nil {
		finalBinlogInfo = binlogInfo
		t.logger.Info("使用数据库查询到的binlog信息", zap.String("file", binlogInfo.file), zap.String("pos", binlogInfo.pos), zap.Int("total_lines", lineCount))
	} else if stdoutBinlogInfo != nil {
		finalBinlogInfo = stdoutBinlogInfo
		t.logger.Info("使用stdout解析到的binlog信息", zap.String("file", stdoutBinlogInfo.file), zap.String("pos", stdoutBinlogInfo.pos), zap.Int("total_lines", lineCount))
	}

	// 读取完整个stdout后再发送binlog信息
	if finalBinlogInfo != nil {
		ch <- *finalBinlogInfo
	} else {
		t.logger.Warn("未能获取binlog信息", zap.Int("total_lines", lineCount))
	}
}

// incrementalBackup 使用智能检测选择最优的增量备份方式。
// 优先使用直接文件访问（性能最佳），降级到网络流式方案（兼容性最佳）。
func (t *toolImpl) incrementalBackup(ctx context.Context, record, prevRecord *types.BackupRecord) *types.BackupError {
	t.logger.Info("正在执行MySQL增量备份", zap.String("backup_id", record.ID), zap.String("parent_id", prevRecord.ID))

	// 智能检测最佳备份方式
	mode := t.detectBinlogAccessMode(ctx)
	t.logger.Info("检测到binlog访问模式", zap.String("mode", mode))

	switch mode {
	case "direct":
		return t.incrementalBackupDirect(ctx, record, prevRecord)
	case "stream":
		return t.incrementalBackupStream(ctx, record, prevRecord)
	default:
		return berrors.NewMySQLToolImplError("UNSUPPORTED_MODE", "incrementalBackup",
			fmt.Errorf("不支持的binlog访问模式: %s", mode), false)
	}

	// 如果到达这里说明检测逻辑有问题
	return berrors.NewMySQLToolImplError("DETECTION_ERROR", "incrementalBackup",
		fmt.Errorf("检测逻辑错误：无法确定增量备份模式"), false)
}

// detectBinlogAccessMode 智能检测binlog访问模式
func (t *toolImpl) detectBinlogAccessMode(ctx context.Context) string {
	// 1. 检查是否配置了mysqlbinlog工具路径
	if t.cfg.ToolsPath.Mysqlbinlog == "" {
		t.logger.Info("未配置mysqlbinlog工具路径，使用网络流式模式")
		return "stream"
	}

	// 2. 检查mysqlbinlog工具是否可执行
	if !t.isMysqlbinlogExecutable() {
		t.logger.Warn("mysqlbinlog工具不可执行，使用网络流式模式")
		return "stream"
	}

	// 3. 尝试获取binlog文件列表并检查文件访问权限
	if t.canAccessBinlogFiles(ctx) {
		t.logger.Info("检测到binlog文件可直接访问，使用直接文件访问模式（最优性能）")
		return "direct"
	}

	t.logger.Info("binlog文件不可直接访问，使用网络流式模式（降级方案）")
	return "stream"
}

// isMysqlbinlogExecutable 检查mysqlbinlog工具是否可执行
func (t *toolImpl) isMysqlbinlogExecutable() bool {
	cmd := exec.Command(t.cfg.ToolsPath.Mysqlbinlog, "--version")
	err := cmd.Run()
	return err == nil
}

// canAccessBinlogFiles 检查是否可以直接访问binlog文件
func (t *toolImpl) canAccessBinlogFiles(ctx context.Context) bool {
	// 获取当前binlog文件列表
	binlogFiles, err := t.getBinlogFilesList(ctx)
	if err != nil || len(binlogFiles) == 0 {
		return false
	}

	// 尝试访问最新的binlog文件
	latestBinlog := binlogFiles[len(binlogFiles)-1]

	// 构建要尝试的binlog路径列表
	var testPaths []string

	// 1. 优先使用配置中指定的binlog路径
	if t.cfg.BinlogBasePath != "" {
		testPaths = append(testPaths, filepath.Join(t.cfg.BinlogBasePath, latestBinlog))
	}

	// 2. 然后尝试常见的binlog路径
	commonPaths := []string{
		filepath.Join("/var/lib/mysql", latestBinlog),
		filepath.Join("/mysql-data", latestBinlog),
		filepath.Join("/shared/mysql-data", latestBinlog),
		filepath.Join("/data/mysql", latestBinlog),
		filepath.Join("/mysql-binlogs", latestBinlog),
		filepath.Join("/shared/mysql-binlogs", latestBinlog),
		filepath.Join("/tests/mysql-binlogs", latestBinlog), // 添加测试环境路径
	}
	testPaths = append(testPaths, commonPaths...)

	for _, testPath := range testPaths {
		if _, err := os.Stat(testPath); err == nil {
			t.logger.Info("找到可访问的binlog路径", zap.String("path", filepath.Dir(testPath)))
			// 更新配置中的binlog基础路径，供后续使用
			t.cfg.BinlogBasePath = filepath.Dir(testPath)
			return true
		}
	}

	return false
}

// getBinlogFilesList 获取binlog文件列表
func (t *toolImpl) getBinlogFilesList(ctx context.Context) ([]string, error) {
	db, dbErr := t.getDBConn(ctx)
	if dbErr != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", dbErr)
	}
	defer db.Close()

	rows, err := db.QueryContext(ctx, "SHOW BINARY LOGS")
	if err != nil {
		return nil, fmt.Errorf("查询binlog列表失败: %w", err)
	}
	defer rows.Close()

	var binlogFiles []string
	for rows.Next() {
		var file string
		var size int64
		var encrypted interface{}
		if err := rows.Scan(&file, &size, &encrypted); err != nil {
			continue
		}
		binlogFiles = append(binlogFiles, file)
	}

	return binlogFiles, nil
}

// incrementalBackupDirect 使用直接文件访问的方式执行增量备份（原有mysqlbinlog工具逻辑）
func (t *toolImpl) incrementalBackupDirect(ctx context.Context, record, prevRecord *types.BackupRecord) *types.BackupError {
	t.logger.Info("使用直接文件访问模式执行增量备份", zap.String("backup_id", record.ID))

	// 步骤 1: 获取当前的主服务器状态作为本次备份的停止点
	stopFile, stopPos, err := t.getMasterStatus(ctx)
	if err != nil {
		return berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "incrementalBackupDirect", fmt.Errorf("无法获取停止点: %w", err), false)
	}
	t.logger.Info("获取增量备份停止点", zap.String("stop_file", stopFile), zap.Uint32("stop_pos", stopPos))

	// 步骤 2: 刷新日志
	if err := t.flushLogs(ctx); err != nil {
		return berrors.NewMySQLToolImplError("DB_EXECUTION_ERROR", "incrementalBackupDirect", fmt.Errorf("刷新MySQL日志失败: %w", err), false)
	}

	// 步骤 3: 获取刷新后的主服务器状态
	nextBinlogFile, nextBinlogPos, err := t.getMasterStatus(ctx)
	if err != nil {
		return berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "incrementalBackupDirect", fmt.Errorf("获取下一次备份的起点失败: %w", err), false)
	}

	// 步骤 4: 构建binlog文件路径并执行mysqlbinlog命令
	binlogPath := filepath.Join(t.cfg.BinlogBasePath, prevRecord.BinlogFile)
	args := []string{
		"--start-position", fmt.Sprintf("%d", prevRecord.BinlogPos),
		"--stop-position", fmt.Sprintf("%d", stopPos),
	}

	// GTID兼容性处理：如果启用了GTID模式，必须跳过GTID语句以避免恢复失败
	if t.isGTIDModeEnabled(ctx) {
		t.logger.Info("检测到GTID模式，检查mysqlbinlog的GTID兼容性")
		// 检查是否支持 --skip-gtids 参数（MySQL 8.0+）
		if t.supportSkipGtids() {
			// 使用 --skip-gtids 完全跳过GTID相关语句（必需方案）
			args = append(args, "--skip-gtids")
			t.logger.Info("使用 --skip-gtids 参数跳过GTID语句")
		} else {
			// 如果不支持跳过GTID，增量备份将无法正确恢复，必须返回错误
			return berrors.NewMySQLToolImplError("GTID_COMPATIBILITY_ERROR", "incrementalBackupDirect",
				fmt.Errorf("MySQL处于GTID模式，但mysqlbinlog不支持--skip-gtids参数。增量备份无法正确恢复，请升级到MySQL 8.0+或禁用GTID模式"), false)
		}
	}

	args = append(args, binlogPath)

	// 处理多个binlog文件的情况
	if stopFile != prevRecord.BinlogFile {
		intermediateBinlogs, getBinlogsErr := t.getIntermediateBinlogs(ctx, prevRecord.BinlogFile, stopFile)
		if getBinlogsErr != nil {
			// 如果是binlog文件不存在的错误，使用智能处理逻辑
			if getBinlogsErr.Code == "BINLOG_NOT_FOUND" {
				t.logger.Warn("检测到binlog不连续，启动智能处理",
					zap.String("start_file", prevRecord.BinlogFile),
					zap.String("stop_file", stopFile),
					zap.Error(getBinlogsErr))

				// 使用新的智能处理逻辑
				if handleErr := t.handleBinlogDiscontinuity(ctx, record, prevRecord); handleErr != nil {
					return handleErr
				}

				// 如果处理成功（比如自动重置了备份链），直接返回成功
				// 因为 forceChainReset 已经执行了全量备份
				return nil
			} else {
				return berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "incrementalBackupDirect", fmt.Errorf("获取中间binlog文件失败: %w", getBinlogsErr), false)
			}
		}

		// 正常情况：重新构建参数
		args = []string{"--start-position", fmt.Sprintf("%d", prevRecord.BinlogPos)}

		// 添加GTID兼容性参数
		if t.isGTIDModeEnabled(ctx) {
			// 检查是否支持 --skip-gtids 参数（MySQL 8.0+）
			if t.supportSkipGtids() {
				// 使用 --skip-gtids 完全跳过GTID相关语句（必需方案）
				args = append(args, "--skip-gtids")
				t.logger.Info("使用 --skip-gtids 参数跳过GTID语句")
			} else {
				// 如果不支持跳过GTID，增量备份将无法正确恢复，必须返回错误
				return berrors.NewMySQLToolImplError("GTID_COMPATIBILITY_ERROR", "incrementalBackupDirect",
					fmt.Errorf("MySQL处于GTID模式，但mysqlbinlog不支持--skip-gtids参数。增量备份无法正确恢复，请升级到MySQL 8.0+或禁用GTID模式"), false)
			}
		}

		args = append(args, binlogPath)
		for _, intermediateBinlog := range intermediateBinlogs {
			args = append(args, filepath.Join(t.cfg.BinlogBasePath, intermediateBinlog))
		}
	}

	cmd := exec.CommandContext(ctx, t.cfg.ToolsPath.Mysqlbinlog, args...)

	stdout, stdoutErr := cmd.StdoutPipe()
	if stdoutErr != nil {
		return berrors.NewMySQLToolImplError("PIPE_ERROR", "incrementalBackupDirect", stdoutErr, false)
	}

	if err := cmd.Start(); err != nil {
		return berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "incrementalBackupDirect", fmt.Errorf("启动mysqlbinlog命令失败: %w", err), false)
	}

	// 阶段二改造：使用 StorageManager.GetBackupWriter 替代直接文件操作
	writer, backupErr := t.storageManager.GetBackupWriter(record, "binlog.sql.gz")
	if backupErr != nil {
		return berrors.NewMySQLToolImplError("FILE_WRITE_ERROR", "incrementalBackupDirect", fmt.Errorf("创建备份写入器失败: %w", backupErr), false)
	}
	defer writer.Close()

	gz := gzip.NewWriter(writer)
	defer gz.Close()

	if _, err := io.Copy(gz, stdout); err != nil {
		return berrors.NewMySQLToolImplError("FILE_WRITE_ERROR", "incrementalBackupDirect", fmt.Errorf("写入增量备份数据失败: %w", err), false)
	}

	if err := cmd.Wait(); err != nil {
		return berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "incrementalBackupDirect", fmt.Errorf("mysqlbinlog命令执行失败: %w", err), false)
	}

	// 步骤 5: 更新备份记录元数据
	record.BinlogFile = nextBinlogFile
	record.BinlogPos = nextBinlogPos

	t.logger.Info("直接文件访问增量备份成功完成", zap.String("backup_id", record.ID))
	return nil
}

// incrementalBackupStream 使用网络流式方式执行增量备份
func (t *toolImpl) incrementalBackupStream(ctx context.Context, record, prevRecord *types.BackupRecord) *types.BackupError {
	t.logger.Info("使用网络流式模式执行增量备份", zap.String("backup_id", record.ID))

	// 步骤 1: 获取当前的主服务器状态作为停止点
	stopFile, stopPos, err := t.getMasterStatus(ctx)
	if err != nil {
		return berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "incrementalBackupStream", fmt.Errorf("无法获取停止点: %w", err), false)
	}

	// 步骤 2: 刷新日志
	if err := t.flushLogs(ctx); err != nil {
		return berrors.NewMySQLToolImplError("DB_EXECUTION_ERROR", "incrementalBackupStream", fmt.Errorf("刷新MySQL日志失败: %w", err), false)
	}

	// 步骤 3: 获取下一次备份的起点
	nextBinlogFile, nextBinlogPos, err := t.getMasterStatus(ctx)
	if err != nil {
		return berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "incrementalBackupStream", fmt.Errorf("获取下一次备份的起点失败: %w", err), false)
	}

	// 步骤 4: 使用SHOW BINLOG EVENTS获取binlog事件
	events, eventsErr := t.getBinlogEventsAsSQL(ctx, prevRecord.BinlogFile, prevRecord.BinlogPos, stopFile, stopPos)
	if eventsErr != nil {
		// 检查是否是binlog不连续的错误
		if strings.Contains(eventsErr.Error(), "获取中间binlog文件失败") {
			t.logger.Warn("流式备份检测到binlog不连续，启动智能处理", zap.Error(eventsErr))

			// 使用智能处理逻辑
			if handleErr := t.handleBinlogDiscontinuity(ctx, record, prevRecord); handleErr != nil {
				return berrors.NewMySQLToolImplError("BINLOG_EVENTS_ERROR", "incrementalBackupStream",
					fmt.Errorf("流式备份binlog处理失败: %w", handleErr), false)
			}

			// 如果处理成功（比如自动重置了备份链），直接返回成功
			return nil
		}

		return berrors.NewMySQLToolImplError("BINLOG_EVENTS_ERROR", "incrementalBackupStream", fmt.Errorf("获取binlog事件失败: %w", eventsErr), false)
	}

	// 步骤 5: 将SQL事件写入压缩文件
	// 阶段二改造：使用 StorageManager.GetBackupWriter 替代直接文件操作
	writer, backupErr := t.storageManager.GetBackupWriter(record, "binlog.sql.gz")
	if backupErr != nil {
		return berrors.NewMySQLToolImplError("FILE_WRITE_ERROR", "incrementalBackupStream", fmt.Errorf("创建备份写入器失败: %w", backupErr), false)
	}
	defer writer.Close() // 确保资源清理

	// 使用计数写入器跟踪写入的字节数
	countingWriter := &countingWriter{writer: writer}
	gz := gzip.NewWriter(countingWriter)
	defer gz.Close() // 确保gzip资源清理

	if _, err := gz.Write([]byte(events)); err != nil {
		return berrors.NewMySQLToolImplError("FILE_WRITE_ERROR", "incrementalBackupStream", fmt.Errorf("写入增量备份数据失败: %w", err), false)
	}

	// 注意：gz.Close() 和 writer.Close() 由defer语句处理

	// 步骤 6: 更新备份记录元数据
	record.BinlogFile = nextBinlogFile
	record.BinlogPos = nextBinlogPos
	record.Size = countingWriter.bytesWritten // 直接使用写入的字节数

	t.logger.Info("网络流式增量备份成功完成",
		zap.String("backup_id", record.ID),
		zap.String("size", types.FormatBackupSize(record.Size)))
	return nil
}

// getBinlogEventsAsSQL 从binlog事件中提取SQL语句，使用循环获取所有事件以避免数据丢失。
func (t *toolImpl) getBinlogEventsAsSQL(ctx context.Context, startFile string, startPos uint32, stopFile string, stopPos uint32) (string, error) {
	db, dbErr := t.getDBConn(ctx)
	if dbErr != nil {
		return "", fmt.Errorf("获取数据库连接失败: %w", dbErr)
	}
	defer db.Close()

	var sqlStatements []string
	var allFiles []string

	// 1. 获取所有相关的binlog文件列表
	if startFile == stopFile {
		allFiles = []string{startFile}
	} else {
		intermediateFiles, err := t.getIntermediateBinlogs(ctx, startFile, stopFile)
		if err != nil {
			return "", fmt.Errorf("获取中间binlog文件失败: %w", err)
		}
		allFiles = append([]string{startFile}, intermediateFiles...)
	}

	t.logger.Info("开始处理binlog文件以进行流式备份", zap.Strings("files", allFiles))

	// 2. 遍历所有需要处理的binlog文件
	currentPos := startPos
	for i, currentFile := range allFiles {
		isLastFile := (i == len(allFiles)-1)

		t.logger.Debug("正在处理binlog文件", zap.String("file", currentFile), zap.Uint32("start_pos", currentPos))

		// 循环获取当前文件的所有事件
		for {
			// 每次循环获取一批事件，避免一次性加载过多数据到内存
			// 注意: SHOW BINLOG EVENTS不支持使用占位符，需要直接构建查询
			query := fmt.Sprintf("SHOW BINLOG EVENTS IN '%s' FROM %d", currentFile, currentPos)
			rows, err := db.QueryContext(ctx, query)
			if err != nil {
				return "", fmt.Errorf("查询binlog事件失败 (file: %s, pos: %d): %w", currentFile, currentPos, err)
			}

			var eventsInBatch int
			var lastPosInBatch int64
			var reachedStopPos bool

			for rows.Next() {
				eventsInBatch++
				var logName, eventType, serverID, endLogPos, info string
				var pos int64

				if err := rows.Scan(&logName, &pos, &eventType, &serverID, &endLogPos, &info); err != nil {
					t.logger.Warn("扫描binlog事件行失败，跳过", zap.Error(err))
					continue
				}

				// 使用 endLogPos 作为下次查询的起始位置，避免重复处理同一个事件
				endLogPosInt, parseErr := strconv.ParseInt(endLogPos, 10, 64)
				if parseErr != nil {
					t.logger.Warn("解析endLogPos失败，使用pos作为fallback", zap.String("endLogPos", endLogPos), zap.Error(parseErr))
					lastPosInBatch = pos
				} else {
					lastPosInBatch = endLogPosInt
				}

				// 检查是否到达最终的停止位置
				if isLastFile && uint32(pos) >= stopPos {
					reachedStopPos = true
					break // 到达终点，停止处理这个文件
				}

				// 提取有意义的SQL语句
				if t.isRelevantBinlogEvent(eventType, info) {
					sql := t.extractSQLFromBinlogInfo(info)
					if sql != "" {
						sqlStatements = append(sqlStatements, sql)
					}
				}
			}
			rows.Close() // 及时关闭rows

			if reachedStopPos {
				break // 跳出当前文件的处理循环
			}

			// 如果当前批次没有获取到任何事件，说明已经读到文件末尾
			if eventsInBatch == 0 {
				break // 跳出当前文件的处理循环
			}
			// 更新位置到最后一个事件的位置，准备下一次查询
			currentPos = uint32(lastPosInBatch)
		}
		// 为下一个文件重置起始位置
		currentPos = 0
	}

	t.logger.Info("成功提取所有相关的binlog事件", zap.Int("statement_count", len(sqlStatements)))
	return strings.Join(sqlStatements, "\n"), nil
}

// isRelevantBinlogEvent 判断binlog事件是否相关
func (t *toolImpl) isRelevantBinlogEvent(eventType, info string) bool {
	// 过滤出包含实际SQL语句的事件
	relevantTypes := []string{"Query", "Table_map", "Write_rows", "Update_rows", "Delete_rows"}
	for _, rType := range relevantTypes {
		if strings.Contains(eventType, rType) {
			return true
		}
	}

	// 检查info字段是否包含SQL语句
	return strings.Contains(strings.ToUpper(info), "INSERT") ||
		strings.Contains(strings.ToUpper(info), "UPDATE") ||
		strings.Contains(strings.ToUpper(info), "DELETE") ||
		strings.Contains(strings.ToUpper(info), "CREATE") ||
		strings.Contains(strings.ToUpper(info), "ALTER") ||
		strings.Contains(strings.ToUpper(info), "DROP")
}

// extractSQLFromBinlogInfo 从binlog info中提取SQL语句
func (t *toolImpl) extractSQLFromBinlogInfo(info string) string {
	// 简单的SQL提取逻辑
	// 在实际生产环境中，这里需要更复杂的解析逻辑
	if strings.TrimSpace(info) == "" {
		return ""
	}

	// 如果info直接包含SQL语句，返回它
	upperInfo := strings.ToUpper(strings.TrimSpace(info))
	if strings.HasPrefix(upperInfo, "INSERT") ||
		strings.HasPrefix(upperInfo, "UPDATE") ||
		strings.HasPrefix(upperInfo, "DELETE") ||
		strings.HasPrefix(upperInfo, "CREATE") ||
		strings.HasPrefix(upperInfo, "ALTER") ||
		strings.HasPrefix(upperInfo, "DROP") {
		return info + ";"
	}

	return ""
}

// restore 是 toolImpl 的核心恢复方法（重构后使用完全重建策略）。
//
// 重构后的恢复流程：
// 1. 如果是恢复链的第一个备份（prevRecord为nil），删除并重建目标数据库（确保干净状态）
// 2. 通过将解压后的备份文件作为标准输入传递给 `mysql` 命令行工具来执行恢复
//
// 这确保了对于单个归档备份和增量链的第一个备份都能恢复到完全干净的状态，
// 而增量链中的后续备份则在现有数据基础上应用变更，避免数据冲突和表结构不一致问题。
func (t *toolImpl) restore(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	t.logger.Info("正在使用 'tool' 方法恢复MySQL", zap.String("backup_id", record.ID), zap.Bool("is_first_in_chain", prevRecord == nil))

	// 1. 只有在恢复链的第一个备份时才重建数据库
	if prevRecord == nil {
		t.logger.Info("检测到恢复链的第一个备份，将重建数据库", zap.String("backup_id", record.ID))
		if err := t.rebuildDatabase(ctx, record.SourceName); err != nil {
			return berrors.NewMySQLToolImplError("DATABASE_REBUILD_FAILED", "restore", fmt.Errorf("重建数据库失败: %w", err), false)
		}
	} else {
		t.logger.Info("检测到恢复链的后续备份，在现有数据基础上应用变更", zap.String("backup_id", record.ID), zap.String("prev_backup_id", prevRecord.ID))
	}

	// 2. 执行数据恢复
	return t.executeRestore(ctx, record, prevRecord)
}

// executeRestore 执行实际的数据恢复操作（不包含数据库重建）。
func (t *toolImpl) executeRestore(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	// 在GTID模式下进行恢复操作前，检查主从复制状态并提供安全建议
	if t.isGTIDModeEnabled(ctx) {
		isReplicationActive, err := t.checkReplicationStatus(ctx)
		if err != nil {
			t.logger.Warn("检查主从复制状态失败，继续恢复操作", zap.Error(err))
		}
		if isReplicationActive {
			t.logger.Warn("在主从复制环境中进行GTID恢复操作",
				zap.String("backup_id", record.ID),
				zap.String("warning", "GTID恢复可能影响主从一致性，请确保已经采取适当的预防措施"))
		}
	}

	// 根据备份类型确定要恢复的文件名。
	var filename string
	if record.Type == types.BackupTypeChainIncremental {
		// 对于增量备份，要恢复的是包含binlog事件的SQL文件。
		filename = "binlog.sql.gz"
	} else {
		// 对于全量备份，要恢复的是数据SQL文件。
		filename = "data.sql.gz"
	}

	// 使用StorageManager获取备份文件读取器，支持云存储和本地存储的统一抽象
	reader, backupErr := t.storageManager.GetBackupReader(record, filename)
	if backupErr != nil {
		return berrors.NewMySQLToolImplError("FILE_READ_ERROR", "executeRestore", fmt.Errorf("获取备份文件读取器失败: %w", backupErr), false)
	}
	defer reader.Close()

	gz, err := gzip.NewReader(reader)
	if err != nil {
		return berrors.NewMySQLToolImplError("FILE_READ_ERROR", "executeRestore", fmt.Errorf("创建gzip读取器失败: %w", err), false)
	}
	defer gz.Close()

	args := []string{
		"--host", t.cfg.Host,
		"--port", fmt.Sprintf("%d", t.cfg.Port),
		"--user", t.cfg.User,
	}

	// GTID兼容性处理：根据备份类型和在恢复链中的位置添加GTID相关参数
	// 重要：在生产环境中，GTID处理需要格外小心以避免主从不一致
	if record.Type == types.BackupTypeChainIncremental {
		// 增量备份恢复
		if t.isGTIDModeEnabled(ctx) {
			t.logger.Info("检测到GTID模式，为增量恢复添加GTID兼容性参数")
			// 对于增量恢复，保持正常的GTID行为，不禁用binlog
			// 这样可以确保从库能够正确复制恢复操作
			args = append(args, "--init-command=SET SESSION gtid_next='AUTOMATIC';")
		}
	} else {
		// 全量备份恢复（包括增量链的初始备份）
		if t.isGTIDModeEnabled(ctx) {
			// 只有在恢复链的第一个备份时才需要重置GTID状态
			if prevRecord == nil {
				t.logger.Info("检测到GTID模式，为恢复链第一个备份添加GTID兼容性参数")
				// 全量恢复时的GTID处理：
				// 1. 不使用sql_log_bin=0，保持binlog记录以确保主从一致性
				// 2. 使用RESET MASTER清理GTID状态（仅在数据库完全重建时）
				// 3. 设置gtid_next='AUTOMATIC'让MySQL自动分配GTID
				//
				// 注意：在生产环境中，如果有从库，建议在恢复前暂停复制，
				// 恢复后重新配置主从关系
				initCmd := "RESET MASTER; SET SESSION gtid_next='AUTOMATIC';"

				// 如果备份中包含GTID_PURGED信息，需要先重置主库状态
				// RESET MASTER 会清理所有GTID状态，这是最安全的方式
				initCmd = "RESET MASTER; SET SESSION gtid_next='AUTOMATIC';"

				args = append(args, fmt.Sprintf("--init-command=%s", initCmd))
			} else {
				t.logger.Info("检测到GTID模式，为恢复链后续备份添加GTID兼容性参数")
				// 后续备份不需要重置GTID状态，只需要确保GTID自动分配
				args = append(args, "--init-command=SET SESSION gtid_next='AUTOMATIC';")
			}
		}
	}

	args = append(args, record.SourceName) // 使用 record.SourceName 而不是 t.cfg.DBName

	cmd := exec.CommandContext(ctx, t.cfg.ToolsPath.Mysql, args...)
	cmd.Env = append(os.Environ(), fmt.Sprintf("MYSQL_PWD=%s", t.cfg.Password))

	// 根据备份类型处理SQL流：增量备份需要转换INSERT为REPLACE INTO以避免主键冲突
	var processedReader io.Reader = gz
	if record.Type == types.BackupTypeChainIncremental {
		// 增量备份需要预处理SQL语句，将INSERT转换为REPLACE INTO
		processedReader = t.processIncrementalSQL(gz)
		t.logger.Info("增量备份SQL预处理完成，已将INSERT语句转换为REPLACE INTO", zap.String("backup_id", record.ID))
	}

	// 将处理后的SQL数据流作为命令的标准输入。
	cmd.Stdin = processedReader

	// 捕获并记录标准错误输出，以便在出错时提供详细信息。
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "executeRestore", fmt.Errorf("mysql恢复命令失败: %w, stderr: %s", err, stderr.String()), false)
	}

	t.logger.Info("MySQL数据恢复成功完成", zap.String("backup_id", record.ID), zap.String("database", record.SourceName))
	return nil
}

// rebuildDatabase 删除并重新创建指定的数据库。
// 这个操作会清除数据库中的所有数据和结构，为干净的恢复做准备。
func (t *toolImpl) rebuildDatabase(ctx context.Context, dbName string) error {
	// 获取到MySQL服务器的连接（不指定数据库）
	db, err := t.getDBConnectionWithoutDatabase()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}
	defer db.Close()

	t.logger.Info("开始重建数据库", zap.String("database", dbName))

	// 1. 删除数据库（如果存在）
	dropSQL := fmt.Sprintf("DROP DATABASE IF EXISTS `%s`", dbName)
	_, err = db.ExecContext(ctx, dropSQL)
	if err != nil {
		return fmt.Errorf("删除数据库失败: %w", err)
	}
	t.logger.Info("数据库删除完成", zap.String("database", dbName))

	// 2. 添加短暂延迟，确保数据库完全删除
	// 在某些MySQL配置下，DROP DATABASE可能需要一些时间才能完全生效
	time.Sleep(100 * time.Millisecond)

	// 3. 重新创建数据库，使用重试机制处理可能的竞态条件
	createSQL := fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName)
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		_, err = db.ExecContext(ctx, createSQL)
		if err == nil {
			break
		}

		// 检查是否是数据库已存在的错误
		if strings.Contains(err.Error(), "database exists") {
			t.logger.Warn("数据库仍然存在，尝试再次删除",
				zap.String("database", dbName),
				zap.Int("retry", i+1),
				zap.Error(err))

			// 再次尝试删除
			_, dropErr := db.ExecContext(ctx, dropSQL)
			if dropErr != nil {
				t.logger.Error("重试删除数据库失败", zap.Error(dropErr))
			}

			// 等待更长时间
			time.Sleep(time.Duration(i+1) * 200 * time.Millisecond)
			continue
		}

		// 其他类型的错误，直接返回
		return fmt.Errorf("创建数据库失败: %w", err)
	}

	if err != nil {
		return fmt.Errorf("创建数据库失败，已重试%d次: %w", maxRetries, err)
	}

	t.logger.Info("数据库重建完成", zap.String("database", dbName))
	return nil
}

// getDBConnectionWithoutDatabase 获取到MySQL服务器的连接，但不指定特定的数据库。
// 这用于执行数据库级别的操作，如创建、删除数据库。
func (t *toolImpl) getDBConnectionWithoutDatabase() (*sql.DB, error) {
	// 构建不包含数据库名的DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/", t.cfg.User, t.cfg.Password, t.cfg.Host, t.cfg.Port)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("创建数据库连接失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("测试数据库连接失败: %w", err)
	}

	return db, nil
}

// flushLogs 使用 mysqladmin 工具来刷新日志。
func (t *toolImpl) flushLogs(ctx context.Context) *types.BackupError {
	args := []string{
		"--host", t.cfg.Host,
		"--port", fmt.Sprintf("%d", t.cfg.Port),
		"--user", t.cfg.User,
		"flush-logs",
	}
	cmd := exec.CommandContext(ctx, t.cfg.ToolsPath.Mysqladmin, args...)
	cmd.Env = append(os.Environ(), fmt.Sprintf("MYSQL_PWD=%s", t.cfg.Password))

	output, err := cmd.CombinedOutput()
	if err != nil {
		return berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "flushLogs", fmt.Errorf("mysqladmin flush-logs 失败: %w, 输出: %s", err, string(output)), false)
	}
	return nil
}

// getMasterStatus 使用 mysql 命令行工具执行 `SHOW MASTER STATUS`。
func (t *toolImpl) getMasterStatus(ctx context.Context) (string, uint32, *types.BackupError) {
	args := []string{
		"--host", t.cfg.Host,
		"--port", fmt.Sprintf("%d", t.cfg.Port),
		"--user", t.cfg.User,
		"-e", "SHOW MASTER STATUS", // -e 参数用于执行单个查询
	}
	cmd := exec.CommandContext(ctx, t.cfg.ToolsPath.Mysql, args...)
	cmd.Env = append(os.Environ(), fmt.Sprintf("MYSQL_PWD=%s", t.cfg.Password))

	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", 0, berrors.NewMySQLToolImplError("COMMAND_EXECUTION_FAILED", "getMasterStatus", fmt.Errorf("执行 SHOW MASTER STATUS 失败: %w, 输出: %s", err, string(output)), false)
	}

	// 解析输出。
	lines := strings.Split(string(output), "\n")
	if len(lines) < 2 {
		return "", 0, berrors.NewMySQLToolImplError("OUTPUT_PARSE_ERROR", "getMasterStatus", errors.New("SHOW MASTER STATUS 的输出格式不正确"), false)
	}
	fields := strings.Fields(lines[1])
	if len(fields) < 2 {
		return "", 0, berrors.NewMySQLToolImplError("OUTPUT_PARSE_ERROR", "getMasterStatus", errors.New("SHOW MASTER STATUS 的输出字段不足"), false)
	}

	pos, err := strconv.ParseUint(fields[1], 10, 32)
	if err != nil {
		return "", 0, berrors.NewMySQLToolImplError("OUTPUT_PARSE_ERROR", "getMasterStatus", fmt.Errorf("无法将binlog位置 '%s' 解析为数字: %w", fields[1], err), false)
	}

	return fields[0], uint32(pos), nil
}

// getMasterStatusFromConn 通过一个已有的数据库连接获取主服务器状态。
func (t *toolImpl) getMasterStatusFromConn(ctx context.Context, db *sql.DB) (string, uint32, *types.BackupError) {
	var file string
	var pos uint32
	var binlogDoDB, binlogIgnoreDB, executedGtidSet sql.NullString

	// Scan 方法需要与 SHOW MASTER STATUS 返回的列一一对应。
	err := db.QueryRowContext(ctx, "SHOW MASTER STATUS").Scan(&file, &pos, &binlogDoDB, &binlogIgnoreDB, &executedGtidSet)
	if err != nil {
		return "", 0, berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "getMasterStatusFromConn", fmt.Errorf("在现有连接上执行 SHOW MASTER STATUS 失败: %w", err), false)
	}
	return file, pos, nil
}

// getDBConn 获取与MySQL的连接。
func (t *toolImpl) getDBConn(ctx context.Context) (*sql.DB, *types.BackupError) {
	// 使用TCP连接而不是Unix socket，并禁用SSL以避免证书问题
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=5s&tls=false",
		t.cfg.User,
		t.cfg.Password,
		t.cfg.Host,
		t.cfg.Port,
		t.cfg.DBName,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, berrors.NewMySQLToolImplError("DB_CONN_ERROR", "getDBConn", fmt.Errorf("打开数据库连接失败: %w", err), true)
	}

	// 设置连接超时
	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(10)

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return nil, berrors.NewMySQLToolImplError("DB_CONN_ERROR", "getDBConn", fmt.Errorf("测试数据库连接失败: %w", err), true)
	}

	return db, nil
}

// checkReplicationStatus 检查MySQL主从复制状态，为GTID操作提供安全建议
func (t *toolImpl) checkReplicationStatus(ctx context.Context) (bool, *types.BackupError) {
	db, dbErr := t.getDBConn(ctx)
	if dbErr != nil {
		return false, berrors.NewMySQLToolImplError("DB_CONN_ERROR", "checkReplicationStatus", fmt.Errorf("获取数据库连接失败: %w", dbErr), true)
	}
	defer db.Close()

	// 检查是否为主库（有从库连接）
	var slaveCount int
	err := db.QueryRowContext(ctx, "SELECT COUNT(*) FROM information_schema.PROCESSLIST WHERE COMMAND = 'Binlog Dump' OR COMMAND = 'Binlog Dump GTID'").Scan(&slaveCount)
	if err != nil {
		t.logger.Warn("检查从库连接数失败", zap.Error(err))
		// 检查失败不是致命错误，继续执行但记录警告
		return false, nil
	}

	if slaveCount > 0 {
		t.logger.Warn("检测到主从复制环境",
			zap.Int("slave_count", slaveCount),
			zap.String("recommendation", "建议在恢复前暂停从库复制，恢复后重新配置主从关系"))
		return true, nil
	}

	// 检查是否为从库
	var ioRunning, sqlRunning string
	err = db.QueryRowContext(ctx, "SELECT Slave_IO_Running, Slave_SQL_Running FROM SHOW SLAVE STATUS").Scan(&ioRunning, &sqlRunning)
	if err == nil && (ioRunning == "Yes" || sqlRunning == "Yes") {
		t.logger.Warn("检测到从库环境",
			zap.String("io_running", ioRunning),
			zap.String("sql_running", sqlRunning),
			zap.String("recommendation", "建议在恢复前停止从库复制"))
		return true, nil
	}

	return false, nil
}

// getIntermediateBinlogs 查询MySQL实例，获取从 startFile 到 endFile 之间的所有binlog文件名（不包括 startFile，包括 endFile）。
func (t *toolImpl) getIntermediateBinlogs(ctx context.Context, startFile, endFile string) ([]string, *types.BackupError) {
	db, dbErr := t.getDBConn(ctx)
	if dbErr != nil {
		return nil, berrors.NewMySQLToolImplError("DB_CONN_ERROR", "getIntermediateBinlogs", fmt.Errorf("获取数据库连接失败: %w", dbErr), true)
	}
	defer db.Close()

	rows, err := db.QueryContext(ctx, "SHOW BINARY LOGS")
	if err != nil {
		return nil, berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "getIntermediateBinlogs", fmt.Errorf("执行 SHOW BINARY LOGS 失败: %w", err), false)
	}
	defer rows.Close()

	var allBinlogs []string
	var binlogs []string
	foundStart := false

	// 首先收集所有binlog文件
	for rows.Next() {
		var file string
		var size int64
		var encrypted interface{} // MySQL 8.0+可能包含Encrypted列，可能为null
		if err := rows.Scan(&file, &size, &encrypted); err != nil {
			return nil, berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "getIntermediateBinlogs", fmt.Errorf("扫描 BINARY LOGS 结果失败: %w", err), false)
		}
		allBinlogs = append(allBinlogs, file)

		if file == startFile {
			foundStart = true
			continue // 跳过起始文件本身
		}
		if foundStart {
			binlogs = append(binlogs, file)
		}
		if file == endFile {
			break // 达到结束文件
		}
	}
	if err := rows.Err(); err != nil {
		return nil, berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "getIntermediateBinlogs", fmt.Errorf("遍历 BINARY LOGS 结果失败: %w", err), false)
	}

	// 如果没有找到起始文件，这可能是因为binlog已经被清理
	// 在测试环境中这是常见情况，我们应该智能处理
	if !foundStart {
		t.logger.Warn("起始binlog文件不存在，可能已被清理",
			zap.String("start_file", startFile),
			zap.Strings("available_binlogs", allBinlogs),
			zap.String("end_file", endFile))

		// 检查结束文件是否存在
		foundEnd := false
		for _, file := range allBinlogs {
			if file == endFile {
				foundEnd = true
				break
			}
		}

		if !foundEnd {
			return nil, berrors.NewMySQLToolImplError("BINLOG_NOT_FOUND", "getIntermediateBinlogs",
				fmt.Errorf("起始binlog文件 %s 和结束binlog文件 %s 都不存在，可能已被清理", startFile, endFile), false)
		}

		// 如果只有结束文件存在，返回空列表（表示没有中间文件）
		// 这种情况下，增量备份应该从当前可用的最早binlog开始
		t.logger.Info("起始binlog文件已被清理，但结束文件存在，返回空的中间文件列表")
		return []string{}, nil
	}

	if len(binlogs) == 0 && startFile != endFile {
		return nil, berrors.NewMySQLToolImplError("BINLOG_RANGE_ERROR", "getIntermediateBinlogs", fmt.Errorf("在 %s 和 %s 之间未找到中间binlog文件", startFile, endFile), false)
	}

	return binlogs, nil
}

// extractBinlogSequence 从binlog文件名中提取序号
func (t *toolImpl) extractBinlogSequence(binlogFile string) int {
	// 使用预编译的正则表达式匹配 mysql-bin.000123 格式
	matches := binlogSequenceRegex.FindStringSubmatch(binlogFile)
	if len(matches) == 2 {
		if seq, err := strconv.Atoi(matches[1]); err == nil {
			return seq
		}
	}
	return 0
}

// isBinlogReset 检测是否发生了binlog重置（系统重装场景）
// 使用更智能的检测策略，而不是硬编码的阈值
func (t *toolImpl) isBinlogReset(ctx context.Context, prevBinlog string, currentBinlogs []string) bool {
	if len(currentBinlogs) == 0 {
		return false
	}

	prevSeq := t.extractBinlogSequence(prevBinlog)
	currentSeq := t.extractBinlogSequence(currentBinlogs[0])

	// 策略1: 如果当前序号明显小于上次序号（差值较大），可能是重置
	seqDiff := prevSeq - currentSeq
	if seqDiff > 0 && currentSeq <= 5 { // 当前序号很小
		// 进一步检查：获取当前binlog的创建时间
		if binlogCreateTime, err := t.getBinlogCreateTime(ctx, currentBinlogs[0]); err == nil {
			// 如果binlog文件很新（创建时间距离现在很短），很可能是重置
			if time.Since(binlogCreateTime) < 24*time.Hour && seqDiff > 10 {
				t.logger.Warn("检测到可能的binlog重置",
					zap.Int("prev_sequence", prevSeq),
					zap.Int("current_sequence", currentSeq),
					zap.Int("sequence_diff", seqDiff),
					zap.Duration("binlog_age", time.Since(binlogCreateTime)))
				return true
			}
		}

		// 策略2: 如果无法获取时间信息，使用动态阈值
		// 序号差异越大，重置概率越高
		dynamicThreshold := max(10, prevSeq/2) // 动态调整阈值
		if seqDiff >= dynamicThreshold {
			t.logger.Warn("基于序号差异检测到可能的binlog重置",
				zap.Int("prev_sequence", prevSeq),
				zap.Int("current_sequence", currentSeq),
				zap.Int("sequence_diff", seqDiff),
				zap.Int("dynamic_threshold", dynamicThreshold))
			return true
		}
	}

	return false
}

// getBinlogCreateTime 获取binlog文件的创建时间（用于重置检测）
func (t *toolImpl) getBinlogCreateTime(ctx context.Context, binlogFile string) (time.Time, error) {
	db, dbErr := t.getDBConn(ctx)
	if dbErr != nil {
		return time.Time{}, fmt.Errorf("获取数据库连接失败: %w", dbErr)
	}
	defer db.Close()

	// 查询binlog文件的修改时间
	// var modifyTime time.Time  // 暂时未使用
	// query := "SELECT FROM_UNIXTIME(UNIX_TIMESTAMP(SUBSTRING_INDEX(SUBSTRING_INDEX(@@log_bin_basename, '/', -1), '.', 1))) as create_time"  // 暂时未使用

	// 更准确的方法：通过文件系统检查
	rows, err := db.QueryContext(ctx, "SHOW BINARY LOGS")
	if err != nil {
		return time.Time{}, fmt.Errorf("查询binlog列表失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var file string
		var size int64
		var encrypted interface{}
		if err := rows.Scan(&file, &size, &encrypted); err != nil {
			continue
		}
		if file == binlogFile {
			// 使用当前时间减去一个合理的估算值
			// 这是一个简化的实现，实际生产中可能需要更精确的方法
			return time.Now().Add(-time.Hour), nil
		}
	}

	return time.Now(), nil
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// detectBinlogScenario 检测binlog场景类型
func (t *toolImpl) detectBinlogScenario(ctx context.Context, prevRecord *types.BackupRecord, currentBinlogs []string) string {
	// 检测binlog重置（系统重装场景）
	if t.isBinlogReset(ctx, prevRecord.BinlogFile, currentBinlogs) {
		return "binlog_reset"
	}

	// 检测binlog清理（保留策略过短）
	found := false
	for _, binlog := range currentBinlogs {
		if binlog == prevRecord.BinlogFile {
			found = true
			break
		}
	}
	if !found {
		return "binlog_cleaned"
	}

	return "normal"
}

// createBinlogScenarioError 创建针对不同场景的用户友好错误信息
func (t *toolImpl) createBinlogScenarioError(scenario string, prevRecord *types.BackupRecord, currentBinlogs []string) *types.BackupError {
	switch scenario {
	case "binlog_reset":
		message := fmt.Sprintf(`检测到系统重装，备份链将自动重建

原因: binlog从 %s 重置为 %s
处理: 自动创建新备份链，执行全量备份
恢复: 旧链用于重装前数据，新链用于重装后数据

建议: 检查binlog保留配置，避免频繁重置:
  SET GLOBAL expire_logs_days = 7;
  或在my.cnf中配置: expire-logs-days = 7`,
			prevRecord.BinlogFile, currentBinlogs[0])

		return berrors.NewMySQLToolImplError("BINLOG_RESET_DETECTED", "detectBinlogScenario",
			fmt.Errorf(message), false)

	case "binlog_cleaned":
		message := fmt.Sprintf(`检测到binlog文件被清理

需要的binlog: %s
可用的binlog: %v

原因: binlog保留策略过短，文件被自动清理
建议解决方案:
1. 调整binlog保留策略:
   SET GLOBAL expire_logs_days = 7;
2. 在my.cnf中永久配置:
   [mysqld]
   expire-logs-days = 7
   max-binlog-size = 1G
3. 根据备份频率调整保留时间:
   - 每日备份: 保留7-14天
   - 每小时备份: 保留3-7天`,
			prevRecord.BinlogFile, currentBinlogs)

		return berrors.NewMySQLToolImplError("BINLOG_CONTINUITY_BROKEN", "detectBinlogScenario",
			fmt.Errorf(message), false)

	default:
		return berrors.NewMySQLToolImplError("UNKNOWN_BINLOG_SCENARIO", "detectBinlogScenario",
			fmt.Errorf("未知的binlog场景: %s", scenario), false)
	}
}

// handleBinlogDiscontinuity 智能处理binlog不连续的情况
func (t *toolImpl) handleBinlogDiscontinuity(ctx context.Context, record, prevRecord *types.BackupRecord) *types.BackupError {
	// 获取当前binlog列表
	currentBinlogs, err := t.getBinlogFilesList(ctx)
	if err != nil {
		return berrors.NewMySQLToolImplError("DB_QUERY_ERROR", "handleBinlogDiscontinuity",
			fmt.Errorf("获取当前binlog列表失败: %w", err), false)
	}

	// 检测场景类型
	scenario := t.detectBinlogScenario(ctx, prevRecord, currentBinlogs)
	t.logger.Info("检测到binlog场景",
		zap.String("scenario", scenario),
		zap.String("prev_binlog", prevRecord.BinlogFile),
		zap.Strings("current_binlogs", currentBinlogs))

	// 所有binlog不连续场景都自动处理，记录详细日志供技术人员分析
	switch scenario {
	case "binlog_reset":
		t.logger.Warn("检测到系统重装，自动重置备份链",
			zap.String("scenario", "binlog_reset"),
			zap.String("prev_binlog", prevRecord.BinlogFile),
			zap.Strings("current_binlogs", currentBinlogs),
			zap.String("action", "自动创建新备份链"))
		return t.forceChainReset(ctx, record, "系统重装导致binlog重置")
	case "binlog_cleaned":
		t.logger.Warn("检测到binlog文件清理，自动重置备份链",
			zap.String("scenario", "binlog_cleaned"),
			zap.String("prev_binlog", prevRecord.BinlogFile),
			zap.Strings("current_binlogs", currentBinlogs),
			zap.String("action", "自动创建新备份链"),
			zap.String("suggestion", "如需避免此情况，可调整MySQL binlog保留配置"))
		return t.forceChainReset(ctx, record, "binlog文件清理，自动重置备份链")
	default:
		// 正常场景：继续原有逻辑
		return nil
	}
}

// forceChainReset 强制重置备份链（系统重装场景）
func (t *toolImpl) forceChainReset(ctx context.Context, record *types.BackupRecord, reason string) *types.BackupError {
	t.logger.Warn("强制重置备份链",
		zap.String("reason", reason),
		zap.String("new_backup_id", record.ID),
		zap.String("original_type", string(record.Type)))

	// 1. 修改备份类型为链初始备份
	originalType := record.Type
	record.Type = types.BackupTypeChainInitial
	record.ParentID = "" // 清除父备份关系

	// 2. 记录重置信息到Extra字段
	metadata := map[string]interface{}{
		"chain_reset_reason":    reason,
		"original_backup_type":  originalType,
		"chain_reset_timestamp": time.Now().Format(time.RFC3339),
		"auto_reset":            true,
		"warning":               fmt.Sprintf("此备份因%s而自动重置为新的备份链起点", reason),
	}

	// 将元数据序列化为JSON字符串存储在Extra字段
	extraBytes, err := json.Marshal(metadata)
	if err != nil {
		t.logger.Warn("序列化备份链重置元数据失败，使用简化版本", zap.Error(err))
		// 创建安全的fallback JSON，避免注入风险
		fallbackMetadata := map[string]interface{}{
			"chain_reset_reason": reason,
			"auto_reset":         true,
			"error":              "metadata_serialization_failed",
		}
		if fallbackBytes, fallbackErr := json.Marshal(fallbackMetadata); fallbackErr == nil {
			record.Extra = string(fallbackBytes)
		} else {
			// 最后的fallback，使用硬编码的安全JSON
			record.Extra = `{"chain_reset_reason":"unknown","auto_reset":true,"error":"serialization_failed"}`
		}
	} else {
		record.Extra = string(extraBytes)
	}

	// 4. 记录详细日志
	t.logger.Info("备份链重置详情",
		zap.String("action", "创建新的备份链"),
		zap.String("previous_chain", "已保留用于历史恢复"),
		zap.String("new_backup_type", "chain_initial"),
		zap.String("data_safety", "确保数据一致性"))

	// 5. 执行全量备份
	t.logger.Info("开始执行全量备份作为新链的起点")
	return t.fullBackup(ctx, record)
}

// ConfigOptimizationSuggestion 配置优化建议
type ConfigOptimizationSuggestion struct {
	Category  string `json:"category"`  // 建议类别
	Priority  string `json:"priority"`  // 优先级：high, medium, low
	Current   string `json:"current"`   // 当前配置
	Suggested string `json:"suggested"` // 建议配置
	Reason    string `json:"reason"`    // 建议原因
	Impact    string `json:"impact"`    // 预期影响
}

// isGTIDModeEnabled 检测MySQL是否启用了GTID模式
func (t *toolImpl) isGTIDModeEnabled(ctx context.Context) bool {
	db, dbErr := t.getDBConn(ctx)
	if dbErr != nil {
		t.logger.Warn("检测GTID模式时连接数据库失败，假设未启用GTID", zap.Error(dbErr))
		return false
	}
	defer db.Close()

	var gtidMode string
	err := db.QueryRowContext(ctx, "SELECT @@GLOBAL.GTID_MODE").Scan(&gtidMode)
	if err != nil {
		t.logger.Warn("查询GTID模式失败，假设未启用GTID", zap.Error(err))
		return false
	}

	enabled := gtidMode == "ON"
	t.logger.Debug("GTID模式检测结果", zap.String("gtid_mode", gtidMode), zap.Bool("enabled", enabled))
	return enabled
}

// supportSkipGtids 检测mysqlbinlog是否支持--skip-gtids参数
// 这个参数在MySQL 8.0+中可用，MariaDB的mysqlbinlog不支持
func (t *toolImpl) supportSkipGtids() bool {
	if t.cfg.ToolsPath.Mysqlbinlog == "" {
		return false
	}

	// 执行 mysqlbinlog --help 检查是否包含 --skip-gtids 参数
	cmd := exec.Command(t.cfg.ToolsPath.Mysqlbinlog, "--help")
	output, err := cmd.Output()
	if err != nil {
		t.logger.Warn("检测mysqlbinlog参数支持失败", zap.Error(err))
		return false
	}

	hasSkipGtids := strings.Contains(string(output), "--skip-gtids")
	t.logger.Debug("mysqlbinlog --skip-gtids 参数支持检测", zap.Bool("supported", hasSkipGtids))
	return hasSkipGtids
}

// countingWriter 是一个简单的写入器包装器，用于跟踪写入的字节数
type countingWriter struct {
	writer       io.Writer
	bytesWritten int64
}

func (cw *countingWriter) Write(p []byte) (n int, err error) {
	n, err = cw.writer.Write(p)
	cw.bytesWritten += int64(n)
	return n, err
}

// processIncrementalSQL 处理增量备份的SQL流，将INSERT语句转换为REPLACE INTO语句
// 以解决增量恢复时的主键冲突问题
func (t *toolImpl) processIncrementalSQL(input io.Reader) io.Reader {
	pr, pw := io.Pipe()

	go func() {
		defer pw.Close()
		
		scanner := bufio.NewScanner(input)
		scanner.Buffer(make([]byte, 64*1024), 1024*1024) // 增加缓冲区大小处理大SQL
		
		for scanner.Scan() {
			line := scanner.Text()
			processedLine := t.convertInsertToReplace(line)
			
			if _, err := pw.Write([]byte(processedLine + "\n")); err != nil {
				t.logger.Error("写入处理后的SQL失败", zap.Error(err))
				return
			}
		}

		if err := scanner.Err(); err != nil {
			t.logger.Error("读取增量备份SQL流失败", zap.Error(err))
			pw.CloseWithError(err)
			return
		}
	}()

	return pr
}

// convertInsertToReplace 将INSERT语句转换为REPLACE INTO语句
// 处理各种INSERT语法变体，包括INSERT INTO、INSERT IGNORE、INSERT ... VALUES、INSERT ... SELECT等
func (t *toolImpl) convertInsertToReplace(sql string) string {
	trimmed := strings.TrimSpace(sql)
	if trimmed == "" {
		return sql
	}

	// 转换为小写进行匹配，但保留原始大小写
	lower := strings.ToLower(trimmed)

	// 处理INSERT INTO语句
	if strings.HasPrefix(lower, "insert into") {
		// 跳过INSERT关键字，直接替换为REPLACE INTO
		// 这样可以保留原始的大小写和格式
		if idx := strings.Index(strings.ToUpper(trimmed), "INSERT INTO"); idx != -1 {
			prefix := trimmed[:idx]
			suffix := trimmed[idx+len("INSERT INTO"):]
			return prefix + "REPLACE INTO" + suffix
		}
	}

	// 处理INSERT IGNORE INTO语句
	if strings.HasPrefix(lower, "insert ignore into") {
		// 将INSERT IGNORE INTO替换为REPLACE INTO
		if idx := strings.Index(strings.ToUpper(trimmed), "INSERT IGNORE INTO"); idx != -1 {
			prefix := trimmed[:idx]
			suffix := trimmed[idx+len("INSERT IGNORE INTO"):]
			return prefix + "REPLACE INTO" + suffix
		}
	}

	// 处理其他INSERT变体，保持原样
	return sql
}

