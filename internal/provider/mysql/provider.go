package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"git.gobies.org/fobrain/unibackup/internal/storage"
	"git.gobies.org/fobrain/unibackup/pkg/types"
	_ "github.com/go-sql-driver/mysql" // 注册MySQL驱动
	"go.uber.org/zap"
)

// Package mysql 实现了针对 MySQL 数据源的备份和恢复功能。

// MySQLProvider 实现了针对 MySQL 的 BackupProvider 接口。
// 阶段二改造：注入 StorageManager 依赖，消除直接文件系统操作
type MySQLProvider struct {
	cfg            *types.MySQLConfig
	logger         *zap.Logger
	tool           *toolImpl
	storageManager *storage.Manager // 阶段二新增：StorageManager 依赖注入
}

// NewProvider 创建一个新的 MySQLProvider 实例。
// 阶段二改造：接受 StorageManager 参数，实现依赖注入
func NewProvider(cfg *types.MySQLConfig, storageManager *storage.Manager, logger *zap.Logger) (*MySQLProvider, error) {
	if cfg == nil {
		return nil, fmt.Errorf("MySQL配置不能为nil")
	}
	if storageManager == nil {
		return nil, fmt.Errorf("StorageManager不能为nil")
	}
	if logger == nil {
		return nil, fmt.Errorf("Logger不能为nil")
	}

	p := &MySQLProvider{
		cfg:            cfg,
		logger:         logger.With(zap.String("component", "mysql_provider")),
		storageManager: storageManager, // 阶段二：注入 StorageManager
	}

	// 初始化工具实现，传入 StorageManager
	tool, err := newToolImpl(cfg, storageManager, p.logger)
	if err != nil {
		return nil, fmt.Errorf("初始化MySQL工具失败: %w", err)
	}
	p.tool = tool

	return p, nil
}

// Backup 调用 tool 实现来执行备份。
func (p *MySQLProvider) Backup(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	if record == nil {
		return newMySQLProviderError("INVALID_ARGUMENT", "Backup", errors.New("备份记录不能为nil"), false)
	}
	return p.tool.backup(ctx, record, prevRecord)
}

// Restore 调用 tool 实现来执行恢复。
func (p *MySQLProvider) Restore(ctx context.Context, record *types.BackupRecord, prevRecord *types.BackupRecord) *types.BackupError {
	if record == nil {
		return newMySQLProviderError("INVALID_ARGUMENT", "Restore", errors.New("备份记录不能为nil"), false)
	}
	return p.tool.restore(ctx, record, prevRecord)
}

// Delete 对于 MySQL Provider 是一个空操作（no-op）。
// 因为 MySQL 的备份产物是存储在 UniBackup 管理的文件系统中的普通文件，
// 其元数据和物理文件的生命周期由 storage.Manager 统一处理，
// 在数据库层面没有需要额外清理的资源（如快照）。
func (p *MySQLProvider) Delete(ctx context.Context, record *types.BackupRecord) *types.BackupError {
	if record == nil {
		return newMySQLProviderError("INVALID_ARGUMENT", "Delete", errors.New("备份记录不能为nil"), false)
	}
	p.logger.Info("MySQL Provider 的 Delete 操作被调用，这是一个空操作", zap.String("record_id", record.ID))
	return (*types.BackupError)(nil)
}

// List 返回该提供者管理的所有备份记录的元数据列表。
// 注意：对于像 MySQL（文件备份模式）这样将元数据与文件一同存储在本地文件系统的 Provider，此方法返回空列表，
// 因为其列表功能完全由 `storage.Manager` 通过扫描文件系统来实现。
func (p *MySQLProvider) List(ctx context.Context) ([]*types.BackupRecord, *types.BackupError) {
	// MySQL Provider 的列表功能由 storage.Manager 负责，此处返回空列表。
	return []*types.BackupRecord{}, nil
}

// CheckHealth 检查MySQL数据源的健康状态。
// 它尝试连接到MySQL数据库并执行一个简单的ping操作。
func (p *MySQLProvider) CheckHealth(ctx context.Context) *types.BackupError {
	// 使用与toolImpl相同的连接逻辑
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=5s&tls=false",
		p.cfg.User,
		p.cfg.Password,
		p.cfg.Host,
		p.cfg.Port,
		p.cfg.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return newMySQLProviderError("DB_CONN_FAILED", "CheckHealth", fmt.Errorf("MySQL连接失败: %w", err), true)
	}
	defer db.Close()

	// 执行一个简单的ping操作来验证连接的有效性
	if err := db.PingContext(ctx); err != nil {
		return newMySQLProviderError("DB_UNAVAILABLE", "CheckHealth", fmt.Errorf("MySQL服务不可用: %w", err), true)
	}
	return nil
}

// newMySQLProviderError 是一个内部辅助函数，用于创建内容统一、结构化的MySQL Provider相关的错误。
func newMySQLProviderError(code, operation string, err error, retryable bool) *types.BackupError {
	return &types.BackupError{
		Code:      code,
		Message:   err.Error(),
		Component: "MySQLProvider",
		Operation: operation,
		Timestamp: time.Now(),
		Details:   fmt.Sprintf("%+v", err),
		Retryable: retryable,
		OrigErr:   err,
	}
}
