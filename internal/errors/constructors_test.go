package errors

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

func TestNew(t *testing.T) {
	t.Run("应该创建包含所有详细信息的BackupError", func(t *testing.T) {
		originalErr := errors.New("原始错误")

		err := New("TEST_CODE", "TestComponent", "TestOperation", originalErr, true)

		assert.Equal(t, "TEST_CODE", err.Code)
		assert.Equal(t, "原始错误", err.Message)
		assert.Equal(t, "TestComponent", err.Component)
		assert.Equal(t, "TestOperation", err.Operation)
		assert.Equal(t, originalErr, err.Orig<PERSON>rr)
		assert.True(t, err.Retryable)
		assert.NotZero(t, err.Timestamp)
		assert.Contains(t, err.Details, "原始错误") // Details应该包含错误信息
	})

	t.Run("应该处理nil错误", func(t *testing.T) {
		err := New("TEST_CODE", "TestComponent", "TestOperation", nil, false)

		assert.Equal(t, "TEST_CODE", err.Code)
		assert.Equal(t, "TEST_CODE", err.Message) // 当没有原始错误时，使用错误代码作为消息
		assert.Equal(t, "TestComponent", err.Component)
		assert.Equal(t, "TestOperation", err.Operation)
		assert.Nil(t, err.OrigErr)
		assert.False(t, err.Retryable)
		assert.NotZero(t, err.Timestamp)
		assert.Empty(t, err.Details) // 没有原始错误时，Details应该为空
	})
}

func TestNewConfigError(t *testing.T) {
	t.Run("应该创建配置相关的错误", func(t *testing.T) {
		err := NewConfigError("INVALID_CONFIG", "配置无效")

		assert.Equal(t, "INVALID_CONFIG", err.Code)
		assert.Equal(t, "配置无效", err.Message)
		assert.Equal(t, "Config", err.Component)
		assert.Equal(t, "Validate", err.Operation)
		assert.False(t, err.Retryable) // 配置错误默认不可重试
		assert.NotZero(t, err.Timestamp)
		assert.Nil(t, err.OrigErr) // 配置错误通常没有原始错误
	})
}

func TestComponentSpecificConstructors(t *testing.T) {
	originalErr := errors.New("test error")

	testCases := []struct {
		name         string
		constructor  func(string, string, error, bool) *types.BackupError
		expectedComp string
	}{
		{"Storage", NewStorageError, ComponentStorageManager},
		{"MySQLProvider", NewMySQLProviderError, ComponentMySQLProvider},
		{"ElasticsearchProvider", NewElasticsearchProviderError, ComponentElasticsearchProvider},
		{"TaskManager", NewTaskManagerError, ComponentTaskManager},
		{"MySQLToolImpl", NewMySQLToolImplError, ComponentMySQLToolImpl},
		{"APIManager", NewAPIManagerError, ComponentAPIManager},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.constructor("TEST_CODE", "TestOperation", originalErr, true)

			assert.Equal(t, "TEST_CODE", err.Code)
			assert.Equal(t, tc.expectedComp, err.Component)
			assert.Equal(t, "TestOperation", err.Operation)
			assert.Equal(t, originalErr, err.OrigErr)
			assert.True(t, err.Retryable)
		})
	}
}

func TestComponentConstants(t *testing.T) {
	// 验证组件常量的值
	assert.Equal(t, "StorageManager", ComponentStorageManager)
	assert.Equal(t, "MySQLProvider", ComponentMySQLProvider)
	assert.Equal(t, "ElasticsearchProvider", ComponentElasticsearchProvider)
	assert.Equal(t, "TaskManager", ComponentTaskManager)
	assert.Equal(t, "MySQLToolImpl", ComponentMySQLToolImpl)
	assert.Equal(t, "Config", ComponentConfig)
	assert.Equal(t, "APIManager", ComponentAPIManager)
}
