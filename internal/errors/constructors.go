// Package errors 提供统一的错误构造函数，用于SDK内部组件创建规范化的BackupError实例。
// 这些函数仅供内部使用，不对第三方暴露。
package errors

import (
	"fmt"
	"time"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// New 创建一个包含详细信息的 BackupError 实例。
// 这是一个统一的错误构造函数，用于替代各个包中重复的错误创建函数。
//
// 参数：
//   - code: 错误代码，用于程序化处理
//   - component: 错误发生的组件名称
//   - operation: 失败的操作名称
//   - err: 原始错误
//   - retryable: 是否为可重试的错误
//
// 示例：
//
//	errors.New("STORAGE_WRITE_FAILED", "StorageManager", "SaveRecord", err, true)
func New(code, component, operation string, err error, retryable bool) *types.BackupError {
	var message string
	var details string
	var origErr error

	if err != nil {
		message = err.Error()
		details = fmt.Sprintf("%+v", err) // 使用 %+v 打印详细错误信息，包括堆栈
		origErr = err
	} else {
		message = code // 如果没有原始错误，使用错误代码作为消息
	}

	return &types.BackupError{
		Code:      code,
		Message:   message,
		Component: component,
		Operation: operation,
		Timestamp: time.Now(),
		Details:   details,
		Retryable: retryable,
		OrigErr:   origErr,
	}
}

// NewConfigError 创建配置相关的错误。
// 配置错误通常不可重试且没有原始错误。
func NewConfigError(code, message string) *types.BackupError {
	return &types.BackupError{
		Code:      code,
		Message:   message,
		Component: "Config",
		Operation: "Validate",
		Timestamp: time.Now(),
		Retryable: false, // 配置错误通常是不可重试的，需要人工干预修正
	}
}

// 预定义的组件名称常量，确保一致性
const (
	ComponentStorageManager        = "StorageManager"
	ComponentMySQLProvider         = "MySQLProvider"
	ComponentElasticsearchProvider = "ElasticsearchProvider"
	ComponentTaskManager           = "TaskManager"
	ComponentMySQLToolImpl         = "MySQLToolImpl"
	ComponentConfig                = "Config"
	ComponentAPIManager            = "APIManager"
	ComponentStorageBackend        = "StorageBackend"
)

// 便利函数：为常用组件提供专门的构造函数
func NewStorageError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentStorageManager, operation, err, retryable)
}

func NewMySQLProviderError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentMySQLProvider, operation, err, retryable)
}

func NewElasticsearchProviderError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentElasticsearchProvider, operation, err, retryable)
}

func NewTaskManagerError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentTaskManager, operation, err, retryable)
}

func NewStorageBackendError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentStorageBackend, operation, err, retryable)
}

func NewMySQLToolImplError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentMySQLToolImpl, operation, err, retryable)
}

func NewAPIManagerError(code, operation string, err error, retryable bool) *types.BackupError {
	return New(code, ComponentAPIManager, operation, err, retryable)
}
