# UniBackup: 统一备份恢复SDK

UniBackup 是一个企业级的 Go 语言备份恢复 SDK，提供对 MySQL 和 Elasticsearch 的统一备份与恢复能力。

## ✨ 核心特性

- **🔄 双模备份策略**：支持归档全量备份和受保护的增量链备份
- **⚡ 异步任务管理**：非阻塞操作，支持任务状态查询和取消
- **🔒 并发控制**：信号量+细粒度锁，确保数据安全
- **🛡️ 原子性保证**：分组操作支持失败回滚和清理
- **🔄 自动回滚功能**：恢复失败时自动回滚到原始状态，确保数据安全
- **📊 任务持久化**：支持重启后的任务状态恢复
- **🔌 接口驱动**：模块化设计，易于扩展和测试
- **☁️ 云存储支持**：支持 AWS S3、Google Cloud Storage、Azure Blob Storage

## 🚀 快速开始

> 💡 **新用户推荐**：查看 **[快速开始指南](docs/QUICK_START.md)** 获得更详细的入门教程

### 安装和构建

```bash
# 获取代码
git clone https://git.gobies.org/fobrain/unibackup.git
cd unibackup

# 构建主程序和工具
make build
make build-admin
```

### 配置准备

```bash
# 生成示例配置文件
./build/unibackup-admin -generate config.json

# 根据实际环境修改配置文件
# 验证配置文件
./build/unibackup-admin -validate config.json
```

### 基本使用

```go
package main

import (
    "context"
    "log/slog"
    "os"

    "git.gobies.org/fobrain/unibackup/pkg/unibackup"
    "git.gobies.org/fobrain/unibackup/pkg/types"
)

func main() {
    // 基础配置 - 详细配置示例请参考：docs/USER_GUIDE.md
    cfg := &types.Config{
        BackupRoot: "/data/backups",
        Logger:     slog.Default(),
        MySQL: &types.MySQLConfig{
            Host:     "localhost",
            Port:     3306,
            User:     "backup_user",
            Password: os.Getenv("MYSQL_PASSWORD"),
            DBName:   "mydb",
        },
        // 可选：云存储配置（支持 S3、GCS、Azure）
        CloudStorage: &types.CloudStorageConfig{
            Enabled: true,
            Type:    "s3",
            Bucket:  "my-backup-bucket",
            Region:  "us-west-2",
            AccessKey: os.Getenv("AWS_ACCESS_KEY_ID"),
            SecretKey: os.Getenv("AWS_SECRET_ACCESS_KEY"),
        },
    }

    // 创建备份管理器
    manager, err := unibackup.NewManager(cfg)
    if err != nil {
        panic(err)
    }
    defer manager.Shutdown()

    ctx := context.Background()

    // 异步备份 MySQL
    taskID, err := manager.BackupAsync(ctx, types.MySQL, "mydb", types.BackupTypeArchival, "每日备份")
    if err != nil {
        panic(err)
    }

    // 查询任务状态
    task, err := manager.GetTask(taskID)
    if err != nil {
        panic(err)
    }
    
    println("任务状态:", string(task.Status))
}
```

## 📖 核心概念

### 备份类型

1. **归档全量备份** (`types.BackupTypeArchival`)
   - 完全独立的备份，可随时删除
   - 适用于长期存档和灾难恢复

2. **受保护的增量链** (`types.BackupTypeChainInitial` + `types.BackupTypeChainIncremental`)
   - 初始全量备份 + 后续增量备份
   - 整条链受保护，不可单独删除部分备份
   - 适用于最小化 RPO（恢复点目标）

### 操作模式

- **异步操作**：`BackupAsync`, `RestoreAsync`, `BackupAllAsync`, `RestoreAllAsync`
- **同步操作**：`Backup`, `Restore`（向后兼容）
- **分组操作**：同时备份/恢复多个数据源
- **事件驱动**：支持恢复成功后的自动回调处理

### 🔄 自动回滚功能

UniBackup 为所有恢复操作提供自动回滚功能，确保数据安全：

**默认安全配置**：
- `CreateSafetyBackup: true` - 恢复前自动创建安全备份
- `RollbackOnFailure: true` - 失败时自动回滚到原始状态

**支持的恢复模式**：
- ✅ 单个恢复任务 - 完整回滚支持
- ✅ 分组串行恢复 - 完整回滚支持
- ✅ 分组并行恢复 - 完整回滚支持

**回滚流程**：
1. 恢复前创建预恢复快照
2. 执行恢复操作
3. 如果失败，自动使用快照回滚
4. 成功后自动清理临时快照

## 🔧 配置说明

### MySQL 配置

> 详细配置说明请参考：[用户指南](docs/USER_GUIDE.md)

#### 增量备份模式说明

UniBackup 为 MySQL 增量备份提供了两种实现模式，SDK 会自动检测并选择最优模式：

1.  **`direct` 模式 (首选)**: 直接访问并解析 MySQL 服务器上的 `binlog` 文件。这是**性能最高、最可靠**的模式。SDK 会智能检测 `binlog` 文件位置，首先尝试配置中的 `BinlogBasePath`，然后自动尝试常见路径（如 `/var/lib/mysql`、`/mysql-data`、`/shared/mysql-data` 等）。适用于本地部署、Docker 挂载卷或共享存储场景。

2.  **`stream` 模式 (降级)**: 当无法直接访问 `binlog` 文件时，SDK 会自动降级到此模式。它通过执行 `SHOW BINLOG EVENTS` 命令从数据库网络连接中流式获取增量事件。此模式兼容性更好（无需文件系统访问权限），但性能较低，且在极端情况下（如超高频写入导致单次增量事件超过 MySQL 命令返回上限）可能存在数据不一致的风险。**建议仅在无法使用 `direct` 模式时作为备选方案。**

#### ⚠️ 重要：Binlog保留策略配置

**增量备份依赖binlog文件的连续性**，如果binlog文件被MySQL自动清理，会导致增量备份失败。

**必需配置**：
```sql
-- 设置binlog保留时间（根据备份频率调整）
SET GLOBAL expire_logs_days = 7;  -- 保留7天
-- 或者使用MySQL 8.0+新参数
SET GLOBAL binlog_expire_logs_seconds = 604800;  -- 7天

-- 在my.cnf中永久配置
[mysqld]
expire-logs-days = 7
max-binlog-size = 1G
```

**保留策略建议**：
- 每日增量备份：保留至少3-7天binlog
- 每小时增量备份：保留至少1-3天binlog
- 生产环境：建议保留7-14天，确保有足够时间处理故障

> 📖 详细配置说明请参考：[用户指南](docs/USER_GUIDE.md)

```go
MySQL: &types.MySQLConfig{
    Host:           "localhost",
    Port:           3306,
    User:           "backup_user",
    Password:       os.Getenv("MYSQL_PASSWORD"),
    DBName:         "database_name",
    BinlogBasePath: "/var/lib/mysql",  // 增量备份binlog路径，可选，未配置时自动检测
    ToolsPath: types.MySQLToolsPath{
        Mysqldump:   "/usr/bin/mysqldump",   // 可选，自动检测
        Mysql:       "/usr/bin/mysql",       // 可选，自动检测
        Mysqlbinlog: "/usr/bin/mysqlbinlog", // 可选，自动检测
        Mysqladmin:  "/usr/bin/mysqladmin",  // 可选，自动检测
    },
}
```

### Elasticsearch 配置

> 详细配置说明请参考：[用户指南](docs/USER_GUIDE.md)

```go
ES: &types.ESConfig{
    Addresses:        []string{"http://localhost:9200"},
    APIKey:           os.Getenv("ES_API_KEY"),   // 可选
    User:             "elastic",                 // 可选
    Password:         os.Getenv("ES_PASSWORD"),  // 可选
    ArchivalRepoName: "archival_backups",        // 归档备份仓库
    ManagedRepoName:  "incremental_chains",      // 增量链仓库
    AutoCreateRepos:  true,                      // 可选, 自动创建仓库, 默认true
    RepoBasePath:     "/path/to/es/repos",       // 可选, 仓库基础路径
}
```

### 任务管理与超时配置

> 详细配置说明请参考：[用户指南](docs/USER_GUIDE.md)

```go
// 在 unibackup.NewManager(cfg) 中配置
cfg := &types.Config{
    // ... 其他配置
    MaxConcurrentTasks: 5,                  // 最大并发任务数，默认为5
    TaskRetentionDays:  365,                // 任务记录保留天数，默认为365天（一年）
    MaxTaskHistory:     0,                  // 内存和文件中保留的最大任务历史数量，默认为0（不限制数量）
                                           // 注意：设置为0表示只按时间清理，不限制数量
    CleanupBackupData:  false,              // 是否清理备份数据，默认为false（只清理任务记录）
                                           // 注意：启用后会清理成功备份的数据，失败备份始终会被清理
                                           // 注意：TaskRetentionDays和MaxTaskHistory协同工作，任一条件满足都会触发清理
    BackupTimeout:      "24h",              // 备份操作超时时间, 默认24h
    RestoreTimeout:     "24h",              // 恢复操作超时时间, 默认24h
}
```

## 📁 项目结构

### 代码目录结构

```
unibackup/
├── cmd/                    # 命令行工具
│   ├── unibackup/          # 主要CLI工具
│   └── unibackup-admin/    # 配置管理工具
├── pkg/                    # 公共API
│   ├── types/              # 公共数据类型
│   └── unibackup/          # SDK主入口
├── internal/               # 内部实现
│   ├── api/                # API层实现
│   ├── task/               # 任务管理
│   ├── storage/            # 存储管理
│   ├── provider/           # 数据源提供者
│   │   ├── mysql/          # MySQL实现
│   │   └── elasticsearch/  # Elasticsearch实现
│   ├── lock/               # 并发控制
│   ├── config/             # 配置验证
│   └── testutil/           # 测试工具
├── examples/               # 示例代码
├── tests/                  # 集成测试
├── docs/                   # 文档
├── scripts/                # 开发脚本
└── CLAUDE.md               # 项目指导
```

### 备份数据目录结构

## 📁 目录结构

```
/backup_root/
├── tasks.json                    # 任务状态持久化
├── mysql/                        # MySQL 备份
│   ├── archival/                 # 归档备份
│   │   └── {backup_id}/
│   │       ├── metadata.json
│   │       └── data.sql.gz
│   ├── chains/                   # 增量链
│   │   └── {chain_id}/
│   │       ├── chain_meta.json
│   │       └── {backup_id}/
│   └── status.json               # 数据源状态
└── elasticsearch/                # Elasticsearch 备份
    ├── archival/
    ├── chains/
    └── status.json
```

## 🧪 测试

```bash
# 运行所有测试
make test

# 运行特定模块测试
make test-module MODULE=pkg/types

# 生成覆盖率报告
make test-coverage

# 运行竞态检测
make test-race
```

## 📚 API 文档

### 高级特性

#### 任务状态持久化与恢复
UniBackup 支持任务状态持久化，确保 SDK 重启后能够：
- 自动恢复所有任务状态到内存
- 将中断的任务标记为失败状态
- 支持任务历史查询和管理
- 提供任务清理机制避免内存泄漏

#### MySQL 工具自动检测
SDK 会按以下优先级自动检测 MySQL 工具路径：
1. 配置文件中显式指定的工具路径
2. 系统 PATH 环境变量中的工具
3. 常见安装路径（/usr/bin、/usr/local/bin、/opt/mysql/bin 等）

#### 增量备份模式自动选择
MySQL 增量备份支持两种模式，SDK 会智能选择：
- **Direct 模式（推荐）**：直接访问 binlog 文件，性能最优
- **Stream 模式（备用）**：通过网络连接获取增量数据，兼容性更好

#### 健康检查机制
每次操作前自动执行健康检查：
- 检查数据源锁定状态（status.json）
- 验证远程服务连接性（MySQL/Elasticsearch）
- 分组操作会检查所有涉及的数据源

#### 并发控制策略
- **全局信号量**：限制最大并发任务数（可配置，默认 5）
- **细粒度锁**：按数据源名称加锁，同一数据源串行，不同数据源并行
- **上下文取消**：支持通过 context 取消正在执行的任务

### 核心接口

```go
type BackupManager interface {
    // === 异步操作接口 ===
    BackupAsync(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (string, error)
    RestoreAsync(ctx context.Context, config types.RestoreConfig) (string, error)
    // BackupAllAsync 异步分组备份。
    // req.CleanupOnFailure: 失败时是否清理部分成功的备份（注意：不适用于增量备份以防止破坏数据链）
    // req.Sources: 要备份的数据源列表，每个包含 SourceType、SourceName、BackupType、Description
    BackupAllAsync(ctx context.Context, req types.BackupAllRequest) (string, error)
    RestoreAllAsync(ctx context.Context, config types.BatchRestoreConfig) (string, error)

    // === 任务管理接口 ===
    GetTask(taskID string) (*types.Task, error)
    ListTasks() ([]*types.Task, error)
    CancelTask(taskID string) error
    ClearOldTasks() error

    // === 同步操作接口（向后兼容） ===
    Backup(ctx context.Context, sourceType types.SourceType, sourceName string, backupType types.BackupType, description string) (*types.BackupRecord, *types.BackupError)
    // Restore 同步恢复操作。操作成功时返回 nil。
    Restore(ctx context.Context, config types.RestoreConfig) *types.BackupError

    // === 原有接口（保留支持，但推荐使用新接口）===

    // Deprecated: 使用 ListAllBackups 替代，性能更好且支持更灵活的过滤条件
    // ListArchivalBackups 列出指定数据源的所有独立归档备份
    ListArchivalBackups(ctx context.Context, sourceType types.SourceType) ([]types.ArchivalBackup, *types.BackupError)

    // Deprecated: 使用 ListAllBackups 替代，性能更好且支持更灵活的过滤条件
    // ListIncrementalChains 列出指定数据源的所有受保护的增量恢复链
    ListIncrementalChains(ctx context.Context, sourceType types.SourceType) ([]types.IncrementalChain, *types.BackupError)

    // Deprecated: 使用 DeleteBackupByTaskID 替代，提供更好的用户体验和错误处理
    // DeleteBackup 删除指定的备份
    // 警告：若 backupID 属于某条增量链，此操作将删除整条链，而不仅仅是该备份点
    DeleteBackup(ctx context.Context, sourceType types.SourceType, backupID string) *types.BackupError

    // === 系统管理接口 ===
    Shutdown() error

    // === 动态配置接口 ===

    // UpdateConfig 动态更新配置，支持运行时配置变更
    UpdateConfig(cfg *types.Config) error

    // === 统一任务查询接口（重构后）===

    // ListAllBackups 统一的任务查询接口（重构后）
    // 默认查询所有任务类型，支持多值过滤、时间范围查询和搜索功能
    // 新增功能：多值过滤、时间范围查询、按开始时间排序（最新在前）
    ListAllBackups(ctx context.Context, filter types.BackupFilter) (*types.BackupListResult, error)

    // GetBackupDetails 获取备份任务的详细信息
    // 支持单个备份和分组备份的详情获取，分组备份会递归显示所有子任务
    GetBackupDetails(ctx context.Context, taskID string) (*types.Task, error)

    // DeleteBackupByTaskID 基于taskID删除备份，自动识别备份类型
    // 重要说明：增量备份删除时会删除整条增量链，保持数据完整性
    DeleteBackupByTaskID(ctx context.Context, taskID string) error

    // GetBackupDeletionInfo 获取备份删除的影响信息
    // 帮助SDK调用方向用户展示删除操作的详细影响，包括增量链信息等
    GetBackupDeletionInfo(ctx context.Context, taskID string) (*types.BackupDeletionInfo, error)

    // RestoreByTaskID 基于taskID恢复备份到原始位置
    // 从任务记录中自动获取备份信息和原始数据源名称，恢复到原始位置
    // 重要说明：
    // - 自动识别备份类型（归档/增量链）并构建正确的恢复链
    // - 恢复到备份时的原始数据源名称
    // - 使用完全重建策略确保数据一致性
    // 返回恢复任务ID，可用于监控恢复进度
    RestoreByTaskID(ctx context.Context, taskID string) (string, error)
}
```

## 🏗️ 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐
│   CLI工具       │    │   第三方应用     │
│ (unibackup)     │    │  (SDK集成)      │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
          ┌──────────▼───────────┐
          │    pkg/unibackup     │  公共API
          │   (BackupManager)    │
          └──────────┬───────────┘
                     │
          ┌──────────▼───────────┐
          │   internal/api       │  API层实现
          └──────────┬───────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌───▼───┐    ┌──────▼──────┐    ┌────▼────┐
│任务管理│    │   存储管理   │    │并发控制 │
│层     │    │   层        │    │层       │
└───┬───┘    └──────┬──────┘    └────┬────┘
    │               │                │
    └───────────────┼────────────────┘
                    │
        ┌───────────▼───────────┐
        │    Provider 层       │  数据源接口
        │ (MySQL/Elasticsearch) │
        └─────────────────────┘
```

### 模块职责

1. **pkg/unibackup**: 公共SDK接口，提供`BackupManager`接口
2. **internal/api**: API层具体实现，协调各个内部模块
3. **internal/task**: 异步任务管理，支持持久化和故障恢复
4. **internal/storage**: 元数据和文件系统管理
5. **internal/provider**: 数据源抽象层，支持MySQL和Elasticsearch
6. **internal/lock**: 并发控制和资源锁定
7. **internal/config**: 配置验证和默认值设置
8. **cmd/unibackup**: CLI命令行工具
9. **cmd/unibackup-admin**: 配置管理工具

## 🛠️ 工具

### 配置管理工具

UniBackup提供了配置管理工具来简化配置过程：

```bash
# 生成示例配置文件
./build/unibackup-admin -generate config.json

# 验证配置文件
./build/unibackup-admin -validate config.json

# 查看工具帮助
./build/unibackup-admin -help
```

### 文档工具

```bash
# 生成API文档
make docs

# 启动文档服务器
make docs-serve
# 访问 http://localhost:6060/pkg/unibackup/
```

### 开发工具

```bash
# 运行测试
make test

# 查看测试覆盖率
make test-coverage

# 代码格式化
make fmt

# 代码检查
make lint
```

## ☁️ 云存储支持

UniBackup 支持多种云存储服务，提供企业级的备份存储解决方案：

### 支持的云存储服务

- **AWS S3** - Amazon Simple Storage Service
- **Google Cloud Storage (GCS)** - Google 云存储
- **Azure Blob Storage** - Microsoft Azure 对象存储
- **MinIO** - S3 兼容的开源对象存储

### 🔧 Elasticsearch 云存储特殊配置

**重要说明**：ES 云存储功能需要特殊的配置方式：

1. **ES 插件要求**：需要安装 `repository-s3` 插件
2. **认证配置**：ES 的 S3 认证需要在 ES 容器/进程层面配置
3. **自动验证**：UniBackup 会自动验证 ES 插件和云存储连接

**快速配置示例**：
```bash
# 1. 安装 ES S3 插件
elasticsearch-plugin install repository-s3

# 2. 配置 ES 认证（环境变量方式）
export AWS_ACCESS_KEY_ID="your_access_key"
export AWS_SECRET_ACCESS_KEY="your_secret_key"

# 3. UniBackup 配置（不包含 S3 认证）
{
  "cloud_storage": {
    "enabled": true,
    "type": "s3",
    "bucket": "es-backup-bucket",
    "region": "us-east-1"
  },
  "es": {
    "addresses": ["http://localhost:9200"],
    "auto_create_repos": true
  }
}
```

📖 **详细部署指南**：[部署指南](docs/DEPLOYMENT_GUIDE.md)

### 配置示例

#### AWS S3 配置
```yaml
backup_root: "/var/backups/unibackup"  # 用于路径生成
cloud_storage:
  enabled: true
  type: "s3"
  bucket: "my-backup-bucket"
  region: "us-west-2"
  access_key: "AKIAIOSFODNN7EXAMPLE"
  secret_key: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
  endpoint: ""  # 可选，用于 MinIO 等 S3 兼容存储
```

#### Google Cloud Storage 配置
```yaml
backup_root: "/var/backups/unibackup"
cloud_storage:
  enabled: true
  type: "gcs"
  bucket: "my-backup-bucket"
  project_id: "my-project-id"
  credentials_file: "/path/to/service-account.json"
```

#### Azure Blob Storage 配置
```yaml
backup_root: "/var/backups/unibackup"
cloud_storage:
  enabled: true
  type: "azure"
  container: "my-container"
  account_name: "mystorageaccount"
  account_key: "Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw=="
```

### 安全性和认证

- **敏感信息保护**：认证密钥不会出现在日志或API响应中
- **多种认证方式**：支持配置文件、环境变量、服务账户等认证方式
- **向后兼容**：现有本地存储配置无需修改，云存储为可选功能

### 存储优先级

当同时配置本地存储和云存储时：
- 启用云存储时，优先使用云存储进行实际数据存储
- `backup_root` 在云存储模式下主要用于路径生成和元数据管理
- 可以通过修改配置文件在本地存储和云存储之间切换

## 📚 文档

UniBackup 提供完整的文档体系：

### 📖 核心文档
- **[快速开始指南](docs/QUICK_START.md)** - 5分钟快速上手 ⭐
- **[用户指南](docs/USER_GUIDE.md)** - 完整的配置和使用指南
- **[部署指南](docs/DEPLOYMENT_GUIDE.md)** - Docker 和云存储部署
- **[开发指南](docs/DEVELOPER_GUIDE.md)** - 开发、测试和贡献指南
- **[设计概览](docs/DESIGN_OVERVIEW.md)** - 架构设计和实现方案
- **[故障排除](docs/TROUBLESHOOTING.md)** - 常见问题诊断和解决

### 💡 示例代码
- **[配置示例](examples/configurations/)** - 各种配置场景
- **[完整演示](examples/complete_demo/)** - 完整功能演示
- **[错误处理](examples/error_handling/)** - 错误处理最佳实践
- **[集成测试](examples/integration_test/)** - 集成测试示例

### 🌐 在线文档
```bash
# 生成并查看 API 文档
make docs
make docs-serve
# 访问 http://localhost:6060/pkg/unibackup/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [设计概览](docs/DESIGN_OVERVIEW.md)
- [开发指南](docs/DEVELOPER_GUIDE.md)
- [用户指南](docs/USER_GUIDE.md)
