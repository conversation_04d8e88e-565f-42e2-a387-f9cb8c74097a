package main

import (
	"flag"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMain_Version 测试版本显示
func TestMain_Version(t *testing.T) {
	// 保存原始命令行参数
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	// 设置测试参数
	os.Args = []string{"unibackup-admin", "-version"}

	// 重置flag包状态
	flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

	// 运行main函数 - 这里需要捕获输出
	// 由于main函数直接打印并return，我们主要测试不会panic
	assert.NotPanics(t, func() {
		main()
	}, "显示版本信息不应该panic")
}

// TestMain_Help 测试帮助信息显示
func TestMain_Help(t *testing.T) {
	tests := []struct {
		name string
		args []string
	}{
		{
			name: "显式help参数",
			args: []string{"unibackup-admin", "-help"},
		},
		{
			name: "无参数",
			args: []string{"unibackup-admin"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 保存原始命令行参数
			oldArgs := os.Args
			defer func() { os.Args = oldArgs }()

			// 设置测试参数
			os.Args = tt.args

			// 重置flag包状态
			flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

			// 测试不会panic
			assert.NotPanics(t, func() {
				main()
			}, "显示帮助信息不应该panic")
		})
	}
}

// TestMain_ValidateConfig 测试配置验证命令
func TestMain_ValidateConfig(t *testing.T) {
	// 创建一个有效的配置文件
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "valid-config.json")

	validConfig := `{
		"backup_root": "` + tempDir + `",
		"max_concurrent_tasks": 3,
		"es": {
			"addresses": ["http://localhost:9200"],
			"archival_repo_name": "test-archival",
			"managed_repo_name": "test-managed",
			"auto_create_repos": true,
			"repo_base_path": "` + tempDir + `"
		}
	}`

	err := os.WriteFile(configFile, []byte(validConfig), 0644)
	require.NoError(t, err)

	// 保存原始命令行参数
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	// 设置测试参数
	os.Args = []string{"unibackup-admin", "-validate", configFile}

	// 重置flag包状态
	flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

	// 测试不会panic
	assert.NotPanics(t, func() {
		main()
	}, "验证有效配置不应该panic")
}

// TestMain_ValidateConfig_Invalid 测试验证无效配置
func TestMain_ValidateConfig_Invalid(t *testing.T) {
	// 创建一个无效的配置文件
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "invalid-config.json")

	invalidConfig := `{
		"backup_root": "",
		"mysql": {
			"host": "",
			"port": 99999
		}
	}`

	err := os.WriteFile(configFile, []byte(invalidConfig), 0644)
	require.NoError(t, err)

	// 使用子进程测试main函数，因为它会调用os.Exit(1)
	if os.Getenv("TEST_MAIN_INVALID_CONFIG") == "1" {
		// 保存原始命令行参数
		oldArgs := os.Args
		defer func() { os.Args = oldArgs }()

		// 设置测试参数
		os.Args = []string{"unibackup-admin", "-validate", configFile}

		// 重置flag包状态
		flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

		main()
		return
	}

	// 在主测试进程中，我们只测试validateConfigCommand函数
	err = validateConfigCommand(configFile)
	assert.Error(t, err, "无效配置应该返回错误")
}

// TestMain_GenerateConfig 测试配置生成命令
func TestMain_GenerateConfig(t *testing.T) {
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "generated-config.json")

	// 保存原始命令行参数
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	// 设置测试参数
	os.Args = []string{"unibackup-admin", "-generate", configFile}

	// 重置flag包状态
	flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

	// 测试不会panic
	assert.NotPanics(t, func() {
		main()
	}, "生成配置不应该panic")

	// 验证文件是否创建
	_, err := os.Stat(configFile)
	assert.NoError(t, err, "配置文件应该被创建")
}

// TestMain_GenerateConfig_InvalidPath 测试生成配置到无效路径
func TestMain_GenerateConfig_InvalidPath(t *testing.T) {
	// 使用一个不存在的深层路径
	invalidPath := "/nonexistent/deep/path/config.json"

	// 使用子进程测试main函数，因为它会调用os.Exit(1)
	if os.Getenv("TEST_MAIN_INVALID_PATH") == "1" {
		// 保存原始命令行参数
		oldArgs := os.Args
		defer func() { os.Args = oldArgs }()

		// 设置测试参数
		os.Args = []string{"unibackup-admin", "-generate", invalidPath}

		// 重置flag包状态
		flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

		main()
		return
	}

	// 在主测试进程中，我们只测试generateSampleConfig函数
	err := generateSampleConfig(invalidPath)
	if err != nil {
		assert.Contains(t, err.Error(), "写入配置文件失败", "应该返回写入失败错误")
	}
	// 注意：在某些环境下，这个路径可能实际上是可写的，所以不强制要求错误
}

// TestShowHelpInfo 测试帮助信息显示
func TestShowHelpInfo(t *testing.T) {
	// 主要测试不会panic
	assert.NotPanics(t, func() {
		showHelpInfo()
	}, "showHelpInfo不应该panic")
}

// TestFlagParsing 测试命令行参数解析
func TestFlagParsing(t *testing.T) {
	tests := []struct {
		name     string
		args     []string
		validate func(t *testing.T)
	}{
		{
			name: "解析version参数",
			args: []string{"-version"},
			validate: func(t *testing.T) {
				// 创建新的FlagSet来测试
				fs := flag.NewFlagSet("test", flag.ContinueOnError)
				version := fs.Bool("version", false, "显示版本信息")

				err := fs.Parse([]string{"-version"})
				assert.NoError(t, err)
				assert.True(t, *version)
			},
		},
		{
			name: "解析validate参数",
			args: []string{"-validate", "config.json"},
			validate: func(t *testing.T) {
				fs := flag.NewFlagSet("test", flag.ContinueOnError)
				validateConfig := fs.String("validate", "", "验证配置文件")

				err := fs.Parse([]string{"-validate", "config.json"})
				assert.NoError(t, err)
				assert.Equal(t, "config.json", *validateConfig)
			},
		},
		{
			name: "解析generate参数",
			args: []string{"-generate", "output.json"},
			validate: func(t *testing.T) {
				fs := flag.NewFlagSet("test", flag.ContinueOnError)
				generateConfig := fs.String("generate", "", "生成示例配置文件")

				err := fs.Parse([]string{"-generate", "output.json"})
				assert.NoError(t, err)
				assert.Equal(t, "output.json", *generateConfig)
			},
		},
		{
			name: "解析help参数",
			args: []string{"-help"},
			validate: func(t *testing.T) {
				fs := flag.NewFlagSet("test", flag.ContinueOnError)
				help := fs.Bool("help", false, "显示帮助信息")

				err := fs.Parse([]string{"-help"})
				assert.NoError(t, err)
				assert.True(t, *help)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.validate(t)
		})
	}
}

// TestCommandPriority 测试命令优先级
func TestCommandPriority(t *testing.T) {
	// 这个测试验证当多个参数同时提供时的行为
	// 根据main函数的逻辑，version优先级最高，然后是help等

	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "test-config.json")

	validConfig := `{"backup_root": "/test", "mysql": {"host": "localhost", "port": 3306, "user": "test", "db_name": "test"}}`
	err := os.WriteFile(configFile, []byte(validConfig), 0644)
	require.NoError(t, err)

	tests := []struct {
		name        string
		args        []string
		description string
	}{
		{
			name:        "version优先级最高",
			args:        []string{"unibackup-admin", "-version", "-help", "-validate", configFile},
			description: "即使提供了其他参数，version也应该优先执行",
		},
		{
			name:        "help次优先级",
			args:        []string{"unibackup-admin", "-help", "-validate", configFile},
			description: "在没有version时，help应该优先",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 保存原始命令行参数
			oldArgs := os.Args
			defer func() { os.Args = oldArgs }()

			// 设置测试参数
			os.Args = tt.args

			// 重置flag包状态
			flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

			// 测试不会panic
			assert.NotPanics(t, func() {
				main()
			}, tt.description)
		})
	}
}

// TestConstants 测试常量定义
func TestConstants(t *testing.T) {
	assert.Equal(t, "1.0.0", version, "版本常量应该正确")
	assert.NotEmpty(t, version, "版本不应该为空")

	// 验证版本格式（简单的语义版本检查）
	parts := strings.Split(version, ".")
	assert.Equal(t, 3, len(parts), "版本应该是x.y.z格式")

	for _, part := range parts {
		assert.NotEmpty(t, part, "版本的每个部分都不应该为空")
	}
}

// TestMainEdgeCases 测试边界情况
func TestMainEdgeCases(t *testing.T) {
	tests := []struct {
		name string
		args []string
	}{
		{
			name: "只有程序名",
			args: []string{"unibackup-admin"},
		},
		{
			name: "空字符串参数",
			args: []string{"unibackup-admin", "-validate", ""},
		},
		{
			name: "空字符串生成路径",
			args: []string{"unibackup-admin", "-generate", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 保存原始命令行参数
			oldArgs := os.Args
			defer func() { os.Args = oldArgs }()

			// 设置测试参数
			os.Args = tt.args

			// 重置flag包状态
			flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

			// 主要测试不会panic
			assert.NotPanics(t, func() {
				main()
			}, "边界情况不应该panic")
		})
	}
}

// BenchmarkMain_Help 性能基准测试
func BenchmarkMain_Help(b *testing.B) {
	// 保存原始命令行参数
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 重置参数
		os.Args = []string{"unibackup-admin", "-help"}
		flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

		main()
	}
}

// BenchmarkMain_Version 版本显示性能基准测试
func BenchmarkMain_Version(b *testing.B) {
	// 保存原始命令行参数
	oldArgs := os.Args
	defer func() { os.Args = oldArgs }()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 重置参数
		os.Args = []string{"unibackup-admin", "-version"}
		flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

		main()
	}
}
