package main

import (
	"encoding/json"
	"fmt"
	"os"

	"git.gobies.org/fobrain/unibackup/internal/config"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// validateConfigCommand 验证配置文件
func validateConfigCommand(configPath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析配置
	var cfg types.Config
	if err := json.Unmarshal(data, &cfg); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	config.SetDefaults(&cfg)

	// 验证配置
	if err := config.Validate(&cfg); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	fmt.Printf("✅ 配置文件验证通过: %s\n", configPath)
	return nil
}

// generateSampleConfig 生成示例配置文件
func generateSampleConfig(configPath string) error {
	// 创建示例配置
	sampleConfig := &types.Config{
		BackupRoot: "/path/to/backup/root",
		// 注意：不在这里设置任务管理相关的默认值，而是通过 SetDefaults 来确保一致性
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "backup_user",
			DBName: "your_database",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "unibackup-archival",
			ManagedRepoName:  "unibackup-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     "/path/to/backup/root/elasticsearch",
		},
		// 云存储配置（可选）
		// 取消注释并配置以启用云存储功能
		// CloudStorage: &types.CloudStorageConfig{
		//     Enabled:   true,
		//     Type:      "s3",  // 支持: s3, gcs, azure
		//     Bucket:    "your-backup-bucket",
		//     Region:    "us-east-1",  // AWS S3 需要
		//     ProjectID: "your-gcp-project",  // Google Cloud Storage 需要
		//     AccountName: "your-storage-account",  // Azure Blob Storage 需要
		//     // 认证信息通过环境变量提供，不在配置文件中设置
		// },
	}

	// 应用默认值，确保与 config.SetDefaults 保持一致
	config.SetDefaults(sampleConfig)

	// 序列化为JSON
	data, err := json.MarshalIndent(sampleConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	fmt.Printf("✅ 示例配置文件已生成: %s\n", configPath)
	return nil
}

// generateCloudConfig 生成专门的云存储配置文件
func generateCloudConfig(configPath, cloudType string) error {
	var cloudConfig *types.CloudStorageConfig
	var esConfig *types.ESConfig

	switch cloudType {
	case "s3":
		cloudConfig = &types.CloudStorageConfig{
			Enabled: true,
			Type:    "s3",
			Bucket:  "your-s3-backup-bucket",
			Region:  "us-east-1",
		}
		esConfig = &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "s3-archival",
			ManagedRepoName:  "s3-managed",
			AutoCreateRepos:  true,
		}
	case "gcs":
		cloudConfig = &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "gcs",
			Bucket:    "your-gcs-backup-bucket",
			ProjectID: "your-gcp-project",
		}
		esConfig = &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "gcs-archival",
			ManagedRepoName:  "gcs-managed",
			AutoCreateRepos:  true,
		}
	case "azure":
		cloudConfig = &types.CloudStorageConfig{
			Enabled:     true,
			Type:        "azure",
			Bucket:      "your-azure-container",
			AccountName: "your-storage-account",
		}
		esConfig = &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "azure-archival",
			ManagedRepoName:  "azure-managed",
			AutoCreateRepos:  true,
		}
	default:
		return fmt.Errorf("不支持的云存储类型: %s，支持的类型: s3, gcs, azure", cloudType)
	}

	// 创建云存储配置
	sampleConfig := &types.Config{
		BackupRoot:   "/var/lib/unibackup",
		CloudStorage: cloudConfig,
		ES:           esConfig,
		// MySQL 配置（将使用相同的云存储后端）
		MySQL: &types.MySQLConfig{
			Host:   "localhost",
			Port:   3306,
			User:   "backup_user",
			DBName: "your_database",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},
	}

	// 应用默认值
	config.SetDefaults(sampleConfig)

	// 序列化为JSON
	data, err := json.MarshalIndent(sampleConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	fmt.Printf("✅ %s 云存储配置文件已生成: %s\n", cloudType, configPath)
	fmt.Printf("\n💡 使用说明:\n")

	switch cloudType {
	case "s3":
		fmt.Printf("   设置环境变量:\n")
		fmt.Printf("     export AWS_ACCESS_KEY_ID=\"your-access-key\"\n")
		fmt.Printf("     export AWS_SECRET_ACCESS_KEY=\"your-secret-key\"\n")
	case "gcs":
		fmt.Printf("   设置环境变量:\n")
		fmt.Printf("     export GOOGLE_APPLICATION_CREDENTIALS=\"/path/to/service-account.json\"\n")
	case "azure":
		fmt.Printf("   设置环境变量:\n")
		fmt.Printf("     export AZURE_STORAGE_ACCOUNT=\"your-storage-account\"\n")
		fmt.Printf("     export AZURE_STORAGE_KEY=\"your-storage-key\"\n")
	}

	fmt.Printf("   修改配置文件中的存储桶名称和其他参数\n")
	fmt.Printf("   云存储主要用于 Elasticsearch 备份，MySQL 仍使用本地存储\n")

	return nil
}

// provideSuggestions 根据错误类型提供修复建议
func provideSuggestions(err *types.BackupError) {
	fmt.Printf("\n💡 修复建议:\n")

	switch err.Code {
	case "CONFIG_VALIDATION_FAILED":
		if contains(err.Message, "BackupRoot") {
			fmt.Printf("   - 确保备份根目录存在且可写\n")
			fmt.Printf("   - 使用绝对路径\n")
			fmt.Printf("   - 检查目录权限\n")
		} else if contains(err.Message, "数据源") {
			fmt.Printf("   - 至少配置一个数据源 (MySQL 或 Elasticsearch)\n")
			fmt.Printf("   - 检查配置文件格式\n")
		}

	case "MYSQL_CONFIG_INVALID":
		fmt.Printf("   - 检查MySQL连接参数 (Host, User, DBName)\n")
		fmt.Printf("   - 确保端口号在有效范围内 (1-65535)\n")
		fmt.Printf("   - 验证数据库用户权限\n")

	case "MYSQL_TOOLS_MISSING":
		fmt.Printf("   - 安装MySQL客户端工具\n")
		fmt.Printf("   - 在配置中指定工具的完整路径\n")
		fmt.Printf("   - 检查系统PATH环境变量\n")

	case "MYSQL_TOOLS_NOT_FOUND":
		fmt.Printf("   - 检查工具路径是否正确\n")
		fmt.Printf("   - 确保MySQL客户端已安装\n")
		fmt.Printf("   - 使用 'which mysqldump' 查找工具位置\n")

	case "MYSQL_TOOLS_NOT_EXECUTABLE":
		fmt.Printf("   - 检查文件权限: chmod +x /path/to/tool\n")
		fmt.Printf("   - 确保当前用户有执行权限\n")

	case "ES_CONFIG_INVALID":
		if contains(err.Message, "Addresses") {
			fmt.Printf("   - 提供至少一个有效的Elasticsearch地址\n")
			fmt.Printf("   - 使用完整的URL格式 (http://host:port)\n")
		} else if contains(err.Message, "RepoName") {
			fmt.Printf("   - 配置归档和管理仓库名称\n")
			fmt.Printf("   - 使用不同的仓库名称\n")
			fmt.Printf("   - 使用描述性名称 (如 'app-archival', 'app-managed')\n")
		} else if contains(err.Message, "认证") {
			fmt.Printf("   - 选择一种认证方式: API Key 或 用户名/密码\n")
			fmt.Printf("   - 不要同时配置多种认证方式\n")
		}

	default:
		fmt.Printf("   - 检查配置文件语法\n")
		fmt.Printf("   - 参考文档中的配置示例\n")
		fmt.Printf("   - 使用配置验证工具\n")
	}
}

// showConfigSummary 显示配置摘要
func showConfigSummary(cfg *types.Config) {
	fmt.Printf("\n📋 配置摘要:\n")
	fmt.Printf("   备份根目录: %s\n", cfg.BackupRoot)
	fmt.Printf("   最大并发任务: %d\n", cfg.MaxConcurrentTasks)
	fmt.Printf("   任务保留天数: %d\n", cfg.TaskRetentionDays)
	fmt.Printf("   最大任务历史: %d\n", cfg.MaxTaskHistory)

	if cfg.MySQL != nil {
		fmt.Printf("   MySQL: %s:%d/%s (用户: %s)\n",
			cfg.MySQL.Host, cfg.MySQL.Port, cfg.MySQL.DBName, cfg.MySQL.User)
		fmt.Printf("     工具路径: %s\n", cfg.MySQL.ToolsPath.Mysqldump)
	}

	if cfg.ES != nil {
		fmt.Printf("   Elasticsearch: %d个节点\n", len(cfg.ES.Addresses))
		fmt.Printf("     归档仓库: %s\n", cfg.ES.ArchivalRepoName)
		fmt.Printf("     管理仓库: %s\n", cfg.ES.ManagedRepoName)
		fmt.Printf("     自动创建仓库: %v\n", cfg.ES.AutoCreateRepos)
	}

	if cfg.CloudStorage != nil {
		fmt.Printf("   云存储: %s\n", cfg.CloudStorage.Type)
		fmt.Printf("     启用状态: %v\n", cfg.CloudStorage.Enabled)
		fmt.Printf("     存储桶: %s\n", cfg.CloudStorage.Bucket)
		if cfg.CloudStorage.Region != "" {
			fmt.Printf("     区域: %s\n", cfg.CloudStorage.Region)
		}
		if cfg.CloudStorage.ProjectID != "" {
			fmt.Printf("     项目ID: %s\n", cfg.CloudStorage.ProjectID)
		}
		if cfg.CloudStorage.AccountName != "" {
			fmt.Printf("     账户名: %s\n", cfg.CloudStorage.AccountName)
		}
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsInMiddle(s, substr))))
}

// containsInMiddle 检查字符串中间是否包含子字符串
func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
