package main

import (
	"flag"
	"fmt"
	"os"
)

const (
	version = "1.0.0"
)

func main() {
	// 定义命令行参数
	var (
		showVersion      = flag.Bool("version", false, "显示版本信息")
		validateConfig   = flag.String("validate", "", "验证配置文件")
		generateConfig   = flag.String("generate", "", "生成示例配置文件")
		generateCloud    = flag.String("generate-cloud", "", "生成云存储配置文件")
		cloudType        = flag.String("cloud-type", "s3", "云存储类型: s3, gcs, azure")
		showHelp         = flag.Bool("help", false, "显示帮助信息")
	)

	flag.Parse()

	// 显示版本信息
	if *showVersion {
		fmt.Printf("UniBackup Admin Tool v%s\n", version)
		return
	}

	// 显示帮助信息
	if *showHelp || (len(os.Args) == 1) {
		showHelpInfo()
		return
	}

	// 验证配置文件
	if *validateConfig != "" {
		if err := validateConfigCommand(*validateConfig); err != nil {
			fmt.Printf("配置验证失败: %v\n", err)
			os.Exit(1)
		}
		return
	}

	// 生成示例配置文件
	if *generateConfig != "" {
		if err := generateSampleConfig(*generateConfig); err != nil {
			fmt.Printf("生成配置文件失败: %v\n", err)
			os.Exit(1)
		}
		return
	}

	// 生成云存储配置文件
	if *generateCloud != "" {
		if err := generateCloudConfig(*generateCloud, *cloudType); err != nil {
			fmt.Printf("生成云存储配置文件失败: %v\n", err)
			os.Exit(1)
		}
		return
	}

	// 默认显示帮助信息
	showHelpInfo()
}

// showHelpInfo 显示帮助信息
func showHelpInfo() {
	fmt.Printf(`UniBackup Admin Tool v%s

用法:
  unibackup-admin [选项]

选项:
  -version                显示版本信息
  -validate <config>      验证配置文件
  -generate <config>      生成示例配置文件
  -generate-cloud <config> 生成云存储配置文件
  -cloud-type <type>      云存储类型 (s3, gcs, azure)
  -help                   显示帮助信息

功能特性:
  - 生成包含云存储配置的示例配置文件
  - 验证配置文件的完整性和正确性
  - 支持 MySQL、Elasticsearch 和云存储配置验证
  - 提供详细的配置错误诊断和修复建议

示例:
  # 生成示例配置文件（包含云存储配置）
  unibackup-admin -generate config.json

  # 生成专门的云存储配置文件
  unibackup-admin -generate-cloud cloud-config.json -cloud-type s3
  unibackup-admin -generate-cloud gcs-config.json -cloud-type gcs
  unibackup-admin -generate-cloud azure-config.json -cloud-type azure

  # 验证配置文件
  unibackup-admin -validate config.json

  # 显示版本信息
  unibackup-admin -version

云存储支持:
  生成的配置文件包含云存储配置示例，支持:
  - AWS S3 和 S3 兼容存储
  - Google Cloud Storage
  - Azure Blob Storage

  认证信息通过环境变量提供，不在配置文件中设置。
`, version)
}
