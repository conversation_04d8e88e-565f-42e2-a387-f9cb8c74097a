package main

import (
	"encoding/json"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"git.gobies.org/fobrain/unibackup/pkg/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestValidateConfigCommand_BasicSuccess 测试基础配置验证成功场景（跳过工具检查）
func TestValidateConfigCommand_BasicSuccess(t *testing.T) {
	// 创建仅ES配置的测试，因为ES不需要验证本地工具
	tempDir := t.TempDir()
	config := &types.Config{
		BackupRoot: tempDir,
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "test-archival",
			ManagedRepoName:  "test-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     tempDir,
		},
	}

	// 创建临时配置文件
	tempFile := createTempConfigFile(t, config)
	defer os.Remove(tempFile)

	// 测试配置验证
	err := validateConfigCommand(tempFile)
	assert.NoError(t, err, "有效的ES配置应该通过验证")
}

// TestValidateConfigCommand_Errors 测试配置验证错误场景
func TestValidateConfigCommand_Errors(t *testing.T) {
	tests := []struct {
		name        string
		setupFunc   func(t *testing.T) string // 返回配置文件路径
		expectError string
	}{
		{
			name: "文件不存在",
			setupFunc: func(t *testing.T) string {
				return "/nonexistent/config.json"
			},
			expectError: "配置文件不存在",
		},
		{
			name: "无效JSON格式",
			setupFunc: func(t *testing.T) string {
				tempFile := filepath.Join(t.TempDir(), "invalid.json")
				err := os.WriteFile(tempFile, []byte(`{invalid json}`), 0644)
				require.NoError(t, err)
				return tempFile
			},
			expectError: "解析配置文件失败",
		},
		{
			name: "空备份根目录",
			setupFunc: func(t *testing.T) string {
				config := &types.Config{
					BackupRoot: "", // 空根目录
					ES: &types.ESConfig{
						Addresses:        []string{"http://localhost:9200"},
						ArchivalRepoName: "test-archival",
						ManagedRepoName:  "test-managed",
					},
				}
				return createTempConfigFile(t, config)
			},
			expectError: "配置验证失败",
		},
		// 注释掉这个测试，因为现在允许启动时不配置数据源，支持动态配置更新
		// {
		// 	name: "无数据源配置",
		// 	setupFunc: func(t *testing.T) string {
		// 		config := &types.Config{
		// 			BackupRoot: t.TempDir(),
		// 			// 没有MySQL或ES配置
		// 		}
		// 		return createTempConfigFile(t, config)
		// 	},
		// 	expectError: "配置验证失败",
		// },
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configPath := tt.setupFunc(t)
			if strings.Contains(configPath, t.TempDir()) {
				defer os.Remove(configPath)
			}

			err := validateConfigCommand(configPath)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tt.expectError)
		})
	}
}

// TestGenerateSampleConfig_Success 测试配置生成成功场景
func TestGenerateSampleConfig_Success(t *testing.T) {
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "sample-config.json")

	err := generateSampleConfig(configPath)
	assert.NoError(t, err, "生成示例配置应该成功")

	// 验证文件是否创建
	_, err = os.Stat(configPath)
	assert.NoError(t, err, "配置文件应该被创建")

	// 验证文件内容
	data, err := os.ReadFile(configPath)
	require.NoError(t, err)

	var config types.Config
	err = json.Unmarshal(data, &config)
	assert.NoError(t, err, "生成的配置应该是有效的JSON")

	// 验证配置内容（现在使用 SetDefaults 设置的默认值）
	assert.NotEmpty(t, config.BackupRoot)
	assert.Equal(t, 5, config.MaxConcurrentTasks)    // SetDefaults 默认值
	assert.Equal(t, 365, config.TaskRetentionDays)   // SetDefaults 默认值：365天
	assert.Equal(t, 0, config.MaxTaskHistory)        // SetDefaults 默认值：0（不限制数量）

	// 验证MySQL配置
	require.NotNil(t, config.MySQL)
	assert.Equal(t, "localhost", config.MySQL.Host)
	assert.Equal(t, 3306, config.MySQL.Port)
	assert.Equal(t, "backup_user", config.MySQL.User)
	assert.Equal(t, "your_database", config.MySQL.DBName)

	// 验证ES配置
	require.NotNil(t, config.ES)
	assert.Equal(t, []string{"http://localhost:9200"}, config.ES.Addresses)
	assert.Equal(t, "unibackup-archival", config.ES.ArchivalRepoName)
	assert.Equal(t, "unibackup-managed", config.ES.ManagedRepoName)
	assert.True(t, config.ES.AutoCreateRepos)
}

// TestGenerateSampleConfig_Errors 测试配置生成错误场景
func TestGenerateSampleConfig_Errors(t *testing.T) {
	tests := []struct {
		name        string
		configPath  string
		expectError string
	}{
		{
			name:        "只读目录",
			configPath:  "/root/config.json", // 通常无写权限的目录
			expectError: "写入配置文件失败",
		},
		{
			name:        "无效路径",
			configPath:  "/nonexistent/deep/path/config.json",
			expectError: "写入配置文件失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := generateSampleConfig(tt.configPath)
			if err != nil {
				assert.Contains(t, err.Error(), tt.expectError)
			}
			// 注意：在某些环境下，这些路径可能实际上是可写的，所以不强制要求错误
		})
	}
}

// TestGenerateSampleConfig_Overwrite 测试覆盖现有配置文件
func TestGenerateSampleConfig_Overwrite(t *testing.T) {
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "existing-config.json")

	// 先创建一个已存在的文件
	existingContent := `{"backup_root": "old_path"}`
	err := os.WriteFile(configPath, []byte(existingContent), 0644)
	require.NoError(t, err)

	// 生成新配置（应该覆盖）
	err = generateSampleConfig(configPath)
	assert.NoError(t, err, "应该能够覆盖现有配置文件")

	// 验证内容被更新
	data, err := os.ReadFile(configPath)
	require.NoError(t, err)

	var config types.Config
	err = json.Unmarshal(data, &config)
	assert.NoError(t, err)
	assert.NotEqual(t, "old_path", config.BackupRoot, "配置应该被更新")
}

// TestProvideSuggestions 测试修复建议功能
func TestProvideSuggestions(t *testing.T) {
	tests := []struct {
		name   string
		error  *types.BackupError
		verify func(t *testing.T) // 验证输出的函数
	}{
		{
			name: "CONFIG_VALIDATION_FAILED with BackupRoot",
			error: &types.BackupError{
				Code:    "CONFIG_VALIDATION_FAILED",
				Message: "BackupRoot is required",
			},
			verify: func(t *testing.T) {
				// 这里可以添加输出验证逻辑
				// 由于provideSuggestions只是打印到stdout，测试有限
			},
		},
		{
			name: "MYSQL_CONFIG_INVALID",
			error: &types.BackupError{
				Code:    "MYSQL_CONFIG_INVALID",
				Message: "Invalid MySQL configuration",
			},
			verify: func(t *testing.T) {
				// 输出验证逻辑
			},
		},
		{
			name: "ES_CONFIG_INVALID with Addresses",
			error: &types.BackupError{
				Code:    "ES_CONFIG_INVALID",
				Message: "Addresses list is empty",
			},
			verify: func(t *testing.T) {
				// 输出验证逻辑
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 由于provideSuggestions只是打印输出，我们主要测试它不会panic
			assert.NotPanics(t, func() {
				provideSuggestions(tt.error)
			}, "provideSuggestions不应该panic")

			if tt.verify != nil {
				tt.verify(t)
			}
		})
	}
}

// TestShowConfigSummary 测试配置摘要显示
func TestShowConfigSummary(t *testing.T) {
	tempDir := t.TempDir()
	tests := []struct {
		name   string
		config *types.Config
	}{
		{
			name: "完整配置",
			config: &types.Config{
				BackupRoot:         tempDir,
				MaxConcurrentTasks: 5,
				TaskRetentionDays:  14,
				MaxTaskHistory:     200,
				MySQL: &types.MySQLConfig{
					Host:   "mysql.example.com",
					Port:   3306,
					User:   "backup_user",
					DBName: "production_db",
					ToolsPath: types.MySQLToolsPath{
						Mysqldump: "/usr/local/bin/mysqldump",
					},
				},
				ES: &types.ESConfig{
					Addresses:        []string{"http://es1.example.com:9200", "http://es2.example.com:9200"},
					ArchivalRepoName: "prod-archival",
					ManagedRepoName:  "prod-managed",
					AutoCreateRepos:  true,
				},
			},
		},
		{
			name: "仅ES配置",
			config: &types.Config{
				BackupRoot:         tempDir,
				MaxConcurrentTasks: 2,
				TaskRetentionDays:  30,
				MaxTaskHistory:     1000,
				ES: &types.ESConfig{
					Addresses:        []string{"http://localhost:9200"},
					ArchivalRepoName: "test-archival",
					ManagedRepoName:  "test-managed",
					AutoCreateRepos:  false,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 主要测试不会panic
			assert.NotPanics(t, func() {
				showConfigSummary(tt.config)
			}, "showConfigSummary不应该panic")
		})
	}
}

// TestContains 测试字符串包含函数
func TestContains(t *testing.T) {
	tests := []struct {
		name     string
		s        string
		substr   string
		expected bool
	}{
		{
			name:     "包含在开头",
			s:        "hello world",
			substr:   "hello",
			expected: true,
		},
		{
			name:     "包含在结尾",
			s:        "hello world",
			substr:   "world",
			expected: true,
		},
		{
			name:     "包含在中间",
			s:        "hello world",
			substr:   "o w",
			expected: true,
		},
		{
			name:     "完全匹配",
			s:        "test",
			substr:   "test",
			expected: true,
		},
		{
			name:     "不包含",
			s:        "hello world",
			substr:   "xyz",
			expected: false,
		},
		{
			name:     "空字符串",
			s:        "hello",
			substr:   "",
			expected: true,
		},
		{
			name:     "子字符串较长",
			s:        "hi",
			substr:   "hello",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := contains(tt.s, tt.substr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestContainsInMiddle 测试中间包含函数
func TestContainsInMiddle(t *testing.T) {
	tests := []struct {
		name     string
		s        string
		substr   string
		expected bool
	}{
		{
			name:     "在中间",
			s:        "abcdef",
			substr:   "cd",
			expected: true,
		},
		{
			name:     "在开头",
			s:        "abcdef",
			substr:   "ab",
			expected: true,
		},
		{
			name:     "在结尾",
			s:        "abcdef",
			substr:   "ef",
			expected: true,
		},
		{
			name:     "不存在",
			s:        "abcdef",
			substr:   "xyz",
			expected: false,
		},
		{
			name:     "多次出现",
			s:        "ababab",
			substr:   "ab",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := containsInMiddle(tt.s, tt.substr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestValidateConfig_GeneratedSample 测试生成的示例配置能否被验证（修改路径后）
func TestValidateConfig_GeneratedSample(t *testing.T) {
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "integration-test.json")

	// 生成配置
	err := generateSampleConfig(configPath)
	require.NoError(t, err, "生成配置应该成功")

	// 读取生成的配置
	data, err := os.ReadFile(configPath)
	require.NoError(t, err)

	var config types.Config
	err = json.Unmarshal(data, &config)
	require.NoError(t, err)

	// 修改为实际存在的路径
	config.BackupRoot = tempDir
	if config.ES != nil {
		config.ES.RepoBasePath = tempDir
	}

	// 重新写入修改后的配置
	modifiedData, err := json.MarshalIndent(config, "", "  ")
	require.NoError(t, err)

	modifiedConfigPath := filepath.Join(tempDir, "modified-config.json")
	err = os.WriteFile(modifiedConfigPath, modifiedData, 0644)
	require.NoError(t, err)

	// 验证修改后的配置
	err = validateConfigCommand(modifiedConfigPath)
	// 注意：由于MySQL工具可能不存在，我们只检查不是路径相关的错误
	if err != nil {
		// 如果是工具相关错误，认为配置结构是正确的
		if strings.Contains(err.Error(), "工具") || strings.Contains(err.Error(), "tool") {
			t.Logf("配置结构正确，但MySQL工具不存在: %v", err)
		} else {
			t.Errorf("配置验证失败: %v", err)
		}
	}
}

// 辅助函数：创建临时配置文件
func createTempConfigFile(t *testing.T, config *types.Config) string {
	tempFile := filepath.Join(t.TempDir(), "test-config.json")

	data, err := json.MarshalIndent(config, "", "  ")
	require.NoError(t, err)

	err = os.WriteFile(tempFile, data, 0644)
	require.NoError(t, err)

	return tempFile
}

// BenchmarkValidateConfigCommand 性能基准测试
func BenchmarkValidateConfigCommand(b *testing.B) {
	tempDir := b.TempDir()
	config := &types.Config{
		BackupRoot: tempDir,
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "bench-archival",
			ManagedRepoName:  "bench-managed",
			AutoCreateRepos:  true,
			RepoBasePath:     tempDir,
		},
	}

	configPath := filepath.Join(tempDir, "bench-config.json")

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		b.Fatal(err)
	}

	err = os.WriteFile(configPath, data, 0644)
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		err := validateConfigCommand(configPath)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkGenerateSampleConfig 配置生成性能基准测试
func BenchmarkGenerateSampleConfig(b *testing.B) {
	tempDir := b.TempDir()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		configPath := filepath.Join(tempDir, "bench-sample.json")
		err := generateSampleConfig(configPath)
		if err != nil {
			b.Fatal(err)
		}

		// 清理文件以避免影响下次测试
		os.Remove(configPath)
	}
}
