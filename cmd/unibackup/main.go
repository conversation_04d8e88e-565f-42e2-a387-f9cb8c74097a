package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"

	_ "gocloud.dev/blob/azureblob" // Azure Blob Storage
	_ "gocloud.dev/blob/gcsblob"   // Google Cloud Storage
	// 导入云存储驱动以支持多云存储
	_ "gocloud.dev/blob/s3blob" // AWS S3 和 S3 兼容存储（如 MinIO）

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
	"go.uber.org/zap"
)

var (
	configFile string
	backupRoot string
	verbose    bool
	timeout    time.Duration

	// Global manager instance
	manager unibackup.BackupManager
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "unibackup",
	Short: "UniBackup - 企业级多数据源备份恢复工具",
	Long: `UniBackup 是一个支持 MySQL 和 Elasticsearch 的企业级备份恢复工具。
它提供了统一的API来管理不同数据源的备份和恢复操作，支持增量备份、
分组操作、异步任务管理和云存储集成。

功能特性:
  - 支持 MySQL 和 Elasticsearch 备份恢复
  - 增量备份链和归档备份
  - 云存储支持 (AWS S3, Google Cloud Storage, Azure Blob Storage)
  - 异步任务管理和监控
  - 分组操作和原子性保证

配置文件:
  支持 JSON 和 YAML 格式的配置文件。如果未指定配置文件，
  工具会自动在以下位置查找:
  - ./unibackup.json, ./unibackup.yaml, ./config.json, ./config.yaml
  - ~/.unibackup.json, ~/.unibackup.yaml
  - /etc/unibackup/config.json, /etc/unibackup/config.yaml

使用示例:
  # 使用默认配置
  unibackup backup mysql --source mydb --type archival

  # 指定配置文件（支持云存储）
  unibackup -c config.json backup mysql --source mydb

  # 其他操作
  unibackup restore mysql --backup-id abc123 --target newdb
  unibackup list backups mysql
  unibackup tasks list

云存储配置:
  通过配置文件启用云存储功能，认证信息通过环境变量提供:
  - AWS S3: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
  - Google Cloud: GOOGLE_APPLICATION_CREDENTIALS
  - Azure Blob: AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY`,
	PersistentPreRunE: initializeManager,
	PersistentPostRun: cleanupManager,
}

// backupCmd represents the backup command
var backupCmd = &cobra.Command{
	Use:   "backup [mysql|elasticsearch]",
	Short: "执行备份操作",
	Long:  "为指定的数据源执行备份操作，支持归档备份和增量链备份",
}

// mysqlBackupCmd represents the mysql backup command
var mysqlBackupCmd = &cobra.Command{
	Use:   "mysql",
	Short: "备份 MySQL 数据库",
	RunE:  runMySQLBackup,
}

// esBackupCmd represents the elasticsearch backup command
var esBackupCmd = &cobra.Command{
	Use:   "elasticsearch",
	Short: "备份 Elasticsearch 索引",
	RunE:  runESBackup,
}

// restoreCmd represents the restore command
var restoreCmd = &cobra.Command{
	Use:   "restore [mysql|elasticsearch]",
	Short: "执行恢复操作",
	Long:  "从备份中恢复数据，支持完全重建策略",
}

// mysqlRestoreCmd represents the mysql restore command
var mysqlRestoreCmd = &cobra.Command{
	Use:   "mysql",
	Short: "恢复 MySQL 数据库",
	RunE:  runMySQLRestore,
}

// esRestoreCmd represents the elasticsearch restore command
var esRestoreCmd = &cobra.Command{
	Use:   "elasticsearch",
	Short: "恢复 Elasticsearch 索引",
	RunE:  runESRestore,
}

// listCmd represents the list command
var listCmd = &cobra.Command{
	Use:        "list [backups|tasks]",
	Short:      "列出备份或任务",
	Long:       "列出备份记录、增量链或任务信息",
	Deprecated: "使用新的统一命令 'unibackup list' 替代，性能更好且支持更灵活的过滤条件",
}

// listBackupsCmd represents the list backups command
var listBackupsCmd = &cobra.Command{
	Use:        "backups [mysql|elasticsearch]",
	Short:      "列出备份记录",
	Long:       "Deprecated: 使用 'unibackup list' 替代，提供更好的性能和更灵活的过滤功能",
	Deprecated: "使用 'unibackup list' 替代，支持统一查询和高性能过滤",
	RunE:       runListBackups,
}

// listTasksCmd represents the list tasks command
var listTasksCmd = &cobra.Command{
	Use:   "tasks",
	Short: "列出任务",
	RunE:  runListTasks,
}

// tasksCmd represents the tasks command
var tasksCmd = &cobra.Command{
	Use:   "tasks",
	Short: "任务管理",
	Long:  "管理备份和恢复任务",
}

// taskStatusCmd represents the task status command
var taskStatusCmd = &cobra.Command{
	Use:   "status [task-id]",
	Short: "查看任务状态",
	RunE:  runTaskStatus,
}

// taskCancelCmd represents the task cancel command
var taskCancelCmd = &cobra.Command{
	Use:   "cancel [task-id]",
	Short: "取消任务",
	RunE:  runTaskCancel,
}

// deleteCmd represents the delete command
var deleteCmd = &cobra.Command{
	Use:        "delete",
	Short:      "删除操作",
	Long:       "删除备份或清理任务",
	Deprecated: "使用新的统一命令 'unibackup delete <task_id>' 替代，提供更好的用户体验和安全性",
}

// deleteBackupCmd represents the delete backup command
var deleteBackupCmd = &cobra.Command{
	Use:        "backup [mysql|elasticsearch] [backup-id]",
	Short:      "删除备份",
	Long:       "Deprecated: 使用 'unibackup delete <task_id>' 替代，提供更安全的删除操作和影响预览",
	Deprecated: "使用 'unibackup delete <task_id>' 替代，支持自动类型识别和影响预览",
	RunE:       runDeleteBackup,
}

// groupCmd represents the group command
var groupCmd = &cobra.Command{
	Use:   "group",
	Short: "分组操作",
	Long:  "执行分组备份和恢复操作，可以同时操作多个数据源",
}

// groupBackupCmd represents the group backup command
var groupBackupCmd = &cobra.Command{
	Use:   "backup",
	Short: "分组备份",
	Long:  "同时备份多个数据源，创建一致性快照",
	RunE:  runGroupBackup,
}

// groupRestoreCmd represents the group restore command
var groupRestoreCmd = &cobra.Command{
	Use:   "restore",
	Short: "分组恢复",
	Long:  "批量恢复多个备份到指定目标",
	RunE:  runGroupRestore,
}

// === 新的统一命令（推荐使用）===

// unifiedListCmd represents the new unified list command
var unifiedListCmd = &cobra.Command{
	Use:   "backups",
	Short: "统一备份查询命令（推荐使用）",
	Long: `基于tasks.json的高性能备份查询命令。

支持灵活的过滤条件和搜索功能：
  - 按数据源类型过滤 (--source mysql|elasticsearch)
  - 按备份类型过滤 (--type archival|chain-initial|chain-incremental)
  - 按任务类型过滤 (--task-type backup|restore|backup_all|restore_all)
  - 包含恢复任务记录 (--include-restores)
  - 只显示分组备份 (--group)
  - 基于备份名称搜索 (--search "关键词")
  - 分页显示 (--limit, --offset)

性能优势：
  相比传统的 'list backups' 命令，此命令基于内存查询，
  性能显著提升，特别适合大量备份记录的场景。

使用示例：
  unibackup backups                           # 列出所有任务（默认行为，重构后）
  unibackup backups --source mysql           # 只显示MySQL任务
  unibackup backups --type archival          # 只显示归档备份
  unibackup backups --group                  # 只显示分组任务（备份+恢复）
  unibackup backups --search "mydb"          # 搜索描述包含"mydb"的任务
  unibackup backups --task-type backup       # 只查看备份任务
  unibackup backups --task-type restore      # 只查看恢复任务
  unibackup backups --limit 10 --offset 20   # 分页显示

注意：重构后默认查询所有任务类型，按开始时间倒序排列（最新在前）`,
	RunE: runUnifiedList,
}

// unifiedShowCmd represents the new unified show command
var unifiedShowCmd = &cobra.Command{
	Use:   "show <task_id>",
	Short: "查看备份详情（推荐使用）",
	Long: `查看备份任务的详细信息，支持单个备份和分组备份。

功能特性：
  - 自动识别单个备份和分组备份
  - 分组备份递归显示所有子任务详情
  - 验证备份文件的存在性
  - 显示增量链信息和汇总统计

使用示例：
  unibackup show task-123                    # 查看单个备份详情
  unibackup show group-task-456              # 查看分组备份详情`,
	Args: cobra.ExactArgs(1),
	RunE: runUnifiedShow,
}

// unifiedDeleteCmd represents the new unified delete command
var unifiedDeleteCmd = &cobra.Command{
	Use:   "remove <task_id>",
	Short: "统一删除命令（推荐使用）",
	Long: `基于任务ID删除备份，自动识别备份类型。

重要说明：
  - 归档备份：直接删除单个备份
  - 增量备份：删除整条增量链（保持数据完整性）
  - 分组备份：删除所有子任务的备份数据

安全特性：
  - 删除前显示详细的影响信息
  - 增量链删除时提供明确警告
  - 支持交互式确认机制

使用示例：
  unibackup remove task-123                  # 删除单个备份
  unibackup remove group-task-456            # 删除分组备份`,
	Args: cobra.ExactArgs(1),
	RunE: runUnifiedDelete,
}

// unifiedRestoreCmd represents the new unified restore command
var unifiedRestoreCmd = &cobra.Command{
	Use:   "restore-by-task <task_id>",
	Short: "基于任务ID恢复备份（推荐使用）",
	Long: `基于任务ID恢复备份到原始位置，自动识别备份类型。

功能特性：
  - 自动从任务记录中获取备份信息和原始数据源名称
  - 自动识别备份类型（归档/增量链）并构建正确的恢复链
  - 使用完全重建策略确保数据一致性
  - 恢复到备份时的原始数据源位置

重要说明：
  - 只支持单个备份任务的恢复，分组备份请使用分组恢复功能
  - 恢复操作会覆盖目标数据源的现有数据
  - 建议在恢复前创建当前数据的备份

使用示例：
  unibackup restore-by-task task-123         # 恢复单个备份到原始位置`,
	Args: cobra.ExactArgs(1),
	RunE: runUnifiedRestore,
}

// unifiedDeletionInfoCmd represents the deletion info command
var unifiedDeletionInfoCmd = &cobra.Command{
	Use:   "deletion-info <task_id>",
	Short: "查看删除影响信息",
	Long: `查看删除操作的详细影响信息，帮助用户了解删除范围。

显示信息包括：
  - 备份类型和基本信息
  - 增量链详情（如果适用）
  - 分组备份的子任务信息
  - 删除操作的影响范围

建议在执行删除前使用此命令了解影响范围。

使用示例：
  unibackup deletion-info task-123           # 查看删除影响
  unibackup deletion-info group-task-456     # 查看分组删除影响`,
	Args: cobra.ExactArgs(1),
	RunE: runUnifiedDeletionInfo,
}

func init() {
	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "配置文件路径")
	rootCmd.PersistentFlags().StringVar(&backupRoot, "backup-root", "/app/backup", "备份根目录")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "详细输出")
	rootCmd.PersistentFlags().DurationVar(&timeout, "timeout", 30*time.Minute, "操作超时时间")

	// Backup command flags
	mysqlBackupCmd.Flags().String("source", "", "数据源名称 (必须)")
	mysqlBackupCmd.Flags().String("type", "archival", "备份类型: archival, chain-initial, chain-incremental")
	mysqlBackupCmd.Flags().String("description", "", "备份描述")
	mysqlBackupCmd.Flags().Bool("async", true, "异步执行")
	mysqlBackupCmd.MarkFlagRequired("source")

	esBackupCmd.Flags().String("source", "default", "数据源名称")
	esBackupCmd.Flags().String("type", "archival", "备份类型: archival, chain-initial, chain-incremental")
	esBackupCmd.Flags().String("description", "", "备份描述")
	esBackupCmd.Flags().Bool("async", true, "异步执行")

	// Restore command flags
	mysqlRestoreCmd.Flags().String("backup-id", "", "备份ID (必须)")
	mysqlRestoreCmd.Flags().String("target", "", "目标数据源名称 (必须)")
	mysqlRestoreCmd.Flags().String("description", "", "恢复描述")
	mysqlRestoreCmd.Flags().Bool("async", true, "异步执行")
	mysqlRestoreCmd.Flags().Bool("force", false, "强制恢复：中断正在运行的备份任务")
	mysqlRestoreCmd.MarkFlagRequired("backup-id")
	mysqlRestoreCmd.MarkFlagRequired("target")

	esRestoreCmd.Flags().String("backup-id", "", "备份ID (必须)")
	esRestoreCmd.Flags().String("target", "default", "目标数据源名称")
	esRestoreCmd.Flags().String("description", "", "恢复描述")
	esRestoreCmd.Flags().Bool("async", true, "异步执行")
	esRestoreCmd.Flags().Bool("force", false, "强制恢复：中断正在运行的备份任务")
	esRestoreCmd.MarkFlagRequired("backup-id")

	// Group backup command flags
	groupBackupCmd.Flags().StringSlice("mysql-sources", []string{}, "MySQL数据源列表 (例如: db1,db2,db3)")
	groupBackupCmd.Flags().StringSlice("es-sources", []string{}, "Elasticsearch数据源列表 (例如: cluster1,cluster2)")
	groupBackupCmd.Flags().String("type", "archival", "备份类型: archival, chain-initial, chain-incremental")
	groupBackupCmd.Flags().String("description", "", "分组备份描述")
	groupBackupCmd.Flags().Bool("async", true, "异步执行")
	groupBackupCmd.Flags().Bool("atomic", false, "原子性操作")
	groupBackupCmd.Flags().Bool("cleanup-on-failure", true, "失败时清理")

	// Group restore command flags
	groupRestoreCmd.Flags().StringSlice("mysql-restores", []string{}, "MySQL恢复配置 (格式: backup-id:target-name)")
	groupRestoreCmd.Flags().StringSlice("es-restores", []string{}, "Elasticsearch恢复配置 (格式: backup-id:target-name)")
	groupRestoreCmd.Flags().String("description", "", "分组恢复描述")
	groupRestoreCmd.Flags().Bool("async", true, "异步执行")
	groupRestoreCmd.Flags().Bool("atomic", true, "原子性操作")
	groupRestoreCmd.Flags().Bool("force", false, "强制恢复：中断正在运行的备份任务")

	// === 新统一命令的标志配置 ===

	// Unified list command flags（重构后）
	unifiedListCmd.Flags().String("source", "", "过滤数据源类型: mysql, elasticsearch")
	unifiedListCmd.Flags().String("type", "", "过滤备份类型: archival, chain-initial, chain-incremental")
	unifiedListCmd.Flags().Bool("group", false, "只显示分组任务（备份+恢复）")
	unifiedListCmd.Flags().String("search", "", "基于任务描述搜索")
	unifiedListCmd.Flags().Int("limit", 20, "分页大小（默认50，最大1000）")
	unifiedListCmd.Flags().Int("offset", 0, "分页偏移量")
	unifiedListCmd.Flags().Bool("include-restores", false, "包含恢复任务记录（已废弃，默认包含所有任务类型）")
	unifiedListCmd.Flags().String("task-type", "", "过滤任务类型: backup, restore, backup_all, restore_all")

	// Unified restore command flags
	unifiedRestoreCmd.Flags().Bool("force", false, "强制恢复：中断正在运行的备份任务")

	// Build command tree
	backupCmd.AddCommand(mysqlBackupCmd, esBackupCmd)
	restoreCmd.AddCommand(mysqlRestoreCmd, esRestoreCmd)
	listCmd.AddCommand(listBackupsCmd, listTasksCmd)
	tasksCmd.AddCommand(taskStatusCmd, taskCancelCmd)
	deleteCmd.AddCommand(deleteBackupCmd)
	groupCmd.AddCommand(groupBackupCmd, groupRestoreCmd)

	// === 添加新的统一命令到根命令 ===
	rootCmd.AddCommand(backupCmd, restoreCmd, listCmd, tasksCmd, deleteCmd, groupCmd)
	rootCmd.AddCommand(unifiedListCmd, unifiedShowCmd, unifiedDeleteCmd, unifiedDeletionInfoCmd, unifiedRestoreCmd)
}

func initializeManager(cmd *cobra.Command, args []string) error {
	// 设置日志级别
	var logger *zap.Logger
	if verbose {
		logger, _ = zap.NewDevelopment()
	} else {
		logger, _ = zap.NewProduction()
	}

	// 创建配置
	var cfg *types.Config
	var err error

	if configFile != "" {
		// 从配置文件加载
		cfg, err = loadConfigFromFile(configFile)
		if err != nil {
			return fmt.Errorf("加载配置文件失败: %w", err)
		}
	} else {
		// 使用默认配置
		cfg = createDefaultConfig()
	}

	// 设置备份根目录和日志
	cfg.BackupRoot = backupRoot
	cfg.Logger = logger

	// 确保备份目录存在
	if err := os.MkdirAll(cfg.BackupRoot, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 初始化管理器
	manager, err = unibackup.NewManager(cfg)
	if err != nil {
		return fmt.Errorf("初始化 UniBackup 管理器失败: %w", err)
	}

	return nil
}

func cleanupManager(cmd *cobra.Command, args []string) {
	if manager != nil {
		manager.Shutdown()
	}
}

func createDefaultConfig() *types.Config {
	return &types.Config{
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "root",
			Password: "password",
			DBName:   "mysql",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "mysqldump",
				Mysql:       "mysql",
				Mysqlbinlog: "mysqlbinlog",
				Mysqladmin:  "mysqladmin",
			},
		},
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "unibackup-archival",
			ManagedRepoName:  "unibackup-managed",
		},
		// 云存储配置默认禁用，需要在配置文件中启用
		// CloudStorage: &types.CloudStorageConfig{
		//     Enabled: false,
		// },
		MaxConcurrentTasks: 5,    // 使用与 config.SetDefaults 一致的默认值
		TaskRetentionDays:  365,  // 使用与 config.SetDefaults 一致的默认值
		MaxTaskHistory:     0,    // 使用与 config.SetDefaults 一致的默认值
	CleanupBackupData:  false, // 默认不清理备份数据
	}
}

func loadConfigFromFile(filename string) (*types.Config, error) {
	// 如果没有指定配置文件，尝试在常见位置查找
	if filename == "" {
		for _, defaultPath := range getDefaultConfigPaths() {
			if _, err := os.Stat(defaultPath); err == nil {
				filename = defaultPath
				break
			}
		}
		if filename == "" {
			return nil, fmt.Errorf("未找到配置文件，请使用 -c 参数指定配置文件路径")
		}
	}

	// 检查文件是否存在
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", filename)
	}

	// 读取文件内容
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 根据文件扩展名确定格式
	ext := strings.ToLower(filepath.Ext(filename))
	var cfg types.Config

	switch ext {
	case ".json":
		if err := json.Unmarshal(data, &cfg); err != nil {
			return nil, fmt.Errorf("解析JSON配置文件失败: %w", err)
		}
	case ".yaml", ".yml":
		if err := yaml.Unmarshal(data, &cfg); err != nil {
			return nil, fmt.Errorf("解析YAML配置文件失败: %w", err)
		}
	default:
		// 尝试自动检测格式，首先尝试JSON
		if err := json.Unmarshal(data, &cfg); err != nil {
			// JSON失败，尝试YAML
			if yamlErr := yaml.Unmarshal(data, &cfg); yamlErr != nil {
				return nil, fmt.Errorf("无法解析配置文件，尝试JSON失败: %v, 尝试YAML失败: %v", err, yamlErr)
			}
		}
	}

	return &cfg, nil
}

// getDefaultConfigPaths 返回配置文件的默认查找路径
func getDefaultConfigPaths() []string {
	homeDir, _ := os.UserHomeDir()
	return []string{
		"./unibackup.json",
		"./unibackup.yaml",
		"./unibackup.yml",
		"./config.json",
		"./config.yaml",
		"./config.yml",
		filepath.Join(homeDir, ".unibackup.json"),
		filepath.Join(homeDir, ".unibackup.yaml"),
		filepath.Join(homeDir, ".unibackup.yml"),
		"/etc/unibackup/config.json",
		"/etc/unibackup/config.yaml",
		"/etc/unibackup/config.yml",
	}
}

func parseBackupType(typeStr string) types.BackupType {
	switch strings.ToLower(typeStr) {
	case "archival":
		return types.BackupTypeArchival
	case "chain-initial":
		return types.BackupTypeChainInitial
	case "chain-incremental":
		return types.BackupTypeChainIncremental
	default:
		return types.BackupTypeArchival
	}
}

func parseTaskType(typeStr string) types.TaskType {
	switch strings.ToLower(typeStr) {
	case "backup":
		return types.BackupTask
	case "restore":
		return types.RestoreTask
	case "backup_all", "backup-all":
		return types.BackupAllTask
	case "restore_all", "restore-all":
		return types.RestoreAllTask
	default:
		return types.BackupTask
	}
}

func runMySQLBackup(cmd *cobra.Command, args []string) error {
	source, _ := cmd.Flags().GetString("source")
	typeStr, _ := cmd.Flags().GetString("type")
	description, _ := cmd.Flags().GetString("description")
	async, _ := cmd.Flags().GetBool("async")

	backupType := parseBackupType(typeStr)

	if description == "" {
		description = fmt.Sprintf("CLI备份 - %s (%s)", source, typeStr)
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if async {
		taskID, err := manager.BackupAsync(ctx, types.MySQL, source, backupType, description)
		if err != nil {
			return fmt.Errorf("启动异步备份失败: %w", err)
		}

		fmt.Printf("备份任务已启动，任务ID: %s\n", taskID)
		fmt.Printf("使用以下命令查看任务状态:\n")
		fmt.Printf("  unibackup tasks status %s\n", taskID)

		return nil
	} else {
		record, err := manager.Backup(ctx, types.MySQL, source, backupType, description)
		if err != nil {
			return fmt.Errorf("同步备份失败: %s", err.Message)
		}

		fmt.Printf("备份完成！\n")
		fmt.Printf("备份ID: %s\n", record.ID)
		fmt.Printf("状态: %s\n", record.Status)
		fmt.Printf("类型: %s\n", record.Type)

		return nil
	}
}

func runESBackup(cmd *cobra.Command, args []string) error {
	source, _ := cmd.Flags().GetString("source")
	typeStr, _ := cmd.Flags().GetString("type")
	description, _ := cmd.Flags().GetString("description")
	async, _ := cmd.Flags().GetBool("async")

	backupType := parseBackupType(typeStr)

	if description == "" {
		description = fmt.Sprintf("CLI备份 - %s (%s)", source, typeStr)
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if async {
		taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, source, backupType, description)
		if err != nil {
			return fmt.Errorf("启动异步备份失败: %w", err)
		}

		fmt.Printf("备份任务已启动，任务ID: %s\n", taskID)
		fmt.Printf("使用以下命令查看任务状态:\n")
		fmt.Printf("  unibackup tasks status %s\n", taskID)

		return nil
	} else {
		record, err := manager.Backup(ctx, types.Elasticsearch, source, backupType, description)
		if err != nil {
			return fmt.Errorf("同步备份失败: %s", err.Message)
		}

		fmt.Printf("备份完成！\n")
		fmt.Printf("备份ID: %s\n", record.ID)
		fmt.Printf("状态: %s\n", record.Status)
		fmt.Printf("类型: %s\n", record.Type)

		return nil
	}
}

func runMySQLRestore(cmd *cobra.Command, args []string) error {
	backupID, _ := cmd.Flags().GetString("backup-id")
	target, _ := cmd.Flags().GetString("target")
	description, _ := cmd.Flags().GetString("description")
	async, _ := cmd.Flags().GetBool("async")
	force, _ := cmd.Flags().GetBool("force")

	if description == "" {
		description = fmt.Sprintf("CLI恢复 - %s", target)
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	restoreConfig := types.NewRestoreConfig(types.MySQL, target, backupID)
	restoreConfig.Description = description
	restoreConfig.Force = force

	// 如果启用强制恢复，给用户警告
	if force {
		fmt.Printf("⚠️  强制恢复模式：将中断数据源 %s 正在运行的备份任务\n", target)
		fmt.Print("确认继续？(y/N): ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "y" && confirm != "Y" {
			fmt.Println("恢复操作已取消")
			return nil
		}
	}

	if async {
		taskID, err := manager.RestoreAsync(ctx, restoreConfig)
		if err != nil {
			return fmt.Errorf("启动异步恢复失败: %w", err)
		}

		fmt.Printf("恢复任务已启动，任务ID: %s\n", taskID)
		fmt.Printf("使用以下命令查看任务状态:\n")
		fmt.Printf("  unibackup tasks status %s\n", taskID)

		return nil
	} else {
		err := manager.Restore(ctx, restoreConfig)
		if err != nil {
			return fmt.Errorf("同步恢复失败: %s", err.Message)
		}

		fmt.Printf("恢复完成！\n")
		fmt.Printf("目标: %s\n", target)
		fmt.Printf("备份ID: %s\n", backupID)

		return nil
	}
}

func runESRestore(cmd *cobra.Command, args []string) error {
	backupID, _ := cmd.Flags().GetString("backup-id")
	target, _ := cmd.Flags().GetString("target")
	description, _ := cmd.Flags().GetString("description")
	async, _ := cmd.Flags().GetBool("async")
	force, _ := cmd.Flags().GetBool("force")

	if description == "" {
		description = fmt.Sprintf("CLI恢复 - %s", target)
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	restoreConfig := types.NewRestoreConfig(types.Elasticsearch, target, backupID)
	restoreConfig.Description = description
	restoreConfig.Force = force

	// 如果启用强制恢复，给用户警告
	if force {
		fmt.Printf("⚠️  强制恢复模式：将中断数据源 %s 正在运行的备份任务\n", target)
		fmt.Print("确认继续？(y/N): ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "y" && confirm != "Y" {
			fmt.Println("恢复操作已取消")
			return nil
		}
	}

	if async {
		taskID, err := manager.RestoreAsync(ctx, restoreConfig)
		if err != nil {
			return fmt.Errorf("启动异步恢复失败: %w", err)
		}

		fmt.Printf("恢复任务已启动，任务ID: %s\n", taskID)
		fmt.Printf("使用以下命令查看任务状态:\n")
		fmt.Printf("  unibackup tasks status %s\n", taskID)

		return nil
	} else {
		err := manager.Restore(ctx, restoreConfig)
		if err != nil {
			return fmt.Errorf("同步恢复失败: %s", err.Message)
		}

		fmt.Printf("恢复完成！\n")
		fmt.Printf("目标: %s\n", target)
		fmt.Printf("备份ID: %s\n", backupID)

		return nil
	}
}

func runListBackups(cmd *cobra.Command, args []string) error {
	if len(args) == 0 {
		return fmt.Errorf("请指定数据源类型: mysql 或 elasticsearch")
	}

	var sourceType types.SourceType
	switch strings.ToLower(args[0]) {
	case "mysql":
		sourceType = types.MySQL
	case "elasticsearch", "es":
		sourceType = types.Elasticsearch
	default:
		return fmt.Errorf("不支持的数据源类型: %s", args[0])
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 列出归档备份
	archival, err := manager.ListArchivalBackups(ctx, sourceType)
	if err != nil {
		return fmt.Errorf("列出归档备份失败: %s", err.Message)
	}

	// 列出增量链
	chains, err := manager.ListIncrementalChains(ctx, sourceType)
	if err != nil {
		return fmt.Errorf("列出增量链失败: %s", err.Message)
	}

	fmt.Printf("=== %s 备份列表 ===\n\n", strings.ToUpper(args[0]))

	if len(archival) > 0 {
		fmt.Printf("归档备份 (%d 个):\n", len(archival))
		for _, backup := range archival {
			fmt.Printf("  ID: %s\n", backup.Record.ID)
			fmt.Printf("  描述: %s\n", backup.Record.Description)
			fmt.Printf("  状态: %s\n", backup.Record.Status)
			fmt.Printf("  时间: %s\n", backup.Record.Timestamp.Format("2006-01-02 15:04:05"))
			fmt.Printf("  来源: %s\n", backup.Record.Source)
			fmt.Println()
		}
	} else {
		fmt.Println("归档备份: 无")
	}

	if len(chains) > 0 {
		fmt.Printf("增量链 (%d 个):\n", len(chains))
		for _, chain := range chains {
			fmt.Printf("  链ID: %s\n", chain.ChainID)
			fmt.Printf("  备份数量: %d\n", len(chain.Backups))
			if len(chain.Backups) > 0 {
				fmt.Printf("  最新备份: %s (%s)\n",
					chain.Backups[len(chain.Backups)-1].ID,
					chain.Backups[len(chain.Backups)-1].Timestamp.Format("2006-01-02 15:04:05"))
			}
			fmt.Println()
		}
	} else {
		fmt.Println("增量链: 无")
	}

	return nil
}

func runListTasks(cmd *cobra.Command, args []string) error {
	tasks, err := manager.ListTasks()
	if err != nil {
		return fmt.Errorf("列出任务失败: %w", err)
	}

	if len(tasks) == 0 {
		fmt.Println("暂无任务")
		return nil
	}

	fmt.Printf("=== 任务列表 (%d 个) ===\n\n", len(tasks))

	for _, task := range tasks {
		fmt.Printf("ID: %s\n", task.ID)
		fmt.Printf("类型: %s\n", task.Type)
		fmt.Printf("状态: %s\n", task.Status)
		fmt.Printf("进度: %.0f%%\n", task.Progress)
		fmt.Printf("创建时间: %s\n", task.StartTime.Format("2006-01-02 15:04:05"))
		if !task.EndTime.IsZero() {
			fmt.Printf("完成时间: %s\n", task.EndTime.Format("2006-01-02 15:04:05"))
		}
		if task.Error != "" {
			fmt.Printf("错误: %s\n", task.Error)
		}
		fmt.Println()
	}

	return nil
}

func runTaskStatus(cmd *cobra.Command, args []string) error {
	if len(args) == 0 {
		return fmt.Errorf("请提供任务ID")
	}

	taskID := args[0]
	task, err := manager.GetTask(taskID)
	if err != nil {
		return fmt.Errorf("获取任务状态失败: %w", err)
	}

	fmt.Printf("=== 任务状态 ===\n")
	fmt.Printf("ID: %s\n", task.ID)
	fmt.Printf("类型: %s\n", task.Type)
	fmt.Printf("状态: %s\n", task.Status)
	fmt.Printf("进度: %.0f%%\n", task.Progress)
	fmt.Printf("创建时间: %s\n", task.StartTime.Format("2006-01-02 15:04:05"))

	if !task.EndTime.IsZero() {
		fmt.Printf("完成时间: %s\n", task.EndTime.Format("2006-01-02 15:04:05"))
		duration := task.EndTime.Sub(task.StartTime)
		fmt.Printf("执行时长: %s\n", duration.Round(time.Second))
	}

	if task.Error != "" {
		fmt.Printf("错误信息: %s\n", task.Error)
	}

	if len(task.SubTaskIDs) > 0 {
		fmt.Printf("子任务: %d 个\n", len(task.SubTaskIDs))
		for i, subTaskID := range task.SubTaskIDs {
			fmt.Printf("  %d. %s\n", i+1, subTaskID)
		}
	}

	if len(task.Metadata) > 0 {
		fmt.Printf("元数据:\n")
		for key, value := range task.Metadata {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}

	return nil
}

func runTaskCancel(cmd *cobra.Command, args []string) error {
	if len(args) == 0 {
		return fmt.Errorf("请提供任务ID")
	}

	taskID := args[0]
	err := manager.CancelTask(taskID)
	if err != nil {
		return fmt.Errorf("取消任务失败: %w", err)
	}

	fmt.Printf("任务 %s 已取消\n", taskID)
	return nil
}

func runDeleteBackup(cmd *cobra.Command, args []string) error {
	if len(args) < 2 {
		return fmt.Errorf("请提供数据源类型和备份ID")
	}

	var sourceType types.SourceType
	switch strings.ToLower(args[0]) {
	case "mysql":
		sourceType = types.MySQL
	case "elasticsearch", "es":
		sourceType = types.Elasticsearch
	default:
		return fmt.Errorf("不支持的数据源类型: %s", args[0])
	}

	backupID := args[1]

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	err := manager.DeleteBackup(ctx, sourceType, backupID)
	if err != nil {
		return fmt.Errorf("删除备份失败: %s", err.Message)
	}

	fmt.Printf("备份 %s 已删除\n", backupID)
	return nil
}

func runGroupBackup(cmd *cobra.Command, args []string) error {
	mysqlSources, _ := cmd.Flags().GetStringSlice("mysql-sources")
	esSources, _ := cmd.Flags().GetStringSlice("es-sources")
	typeStr, _ := cmd.Flags().GetString("type")
	description, _ := cmd.Flags().GetString("description")
	async, _ := cmd.Flags().GetBool("async")
	atomic, _ := cmd.Flags().GetBool("atomic")
	cleanupOnFailure, _ := cmd.Flags().GetBool("cleanup-on-failure")

	// 验证至少有一个数据源
	if len(mysqlSources) == 0 && len(esSources) == 0 {
		return fmt.Errorf("请至少指定一个数据源：--mysql-sources 或 --es-sources")
	}

	backupType := parseBackupType(typeStr)

	if description == "" {
		description = fmt.Sprintf("CLI分组备份 (%s)", typeStr)
	}

	// 构建备份请求
	var sources []types.BackupRequest

	// 添加MySQL数据源
	for _, source := range mysqlSources {
		if source = strings.TrimSpace(source); source != "" {
			sources = append(sources, types.BackupRequest{
				SourceType:  types.MySQL,
				SourceName:  source,
				BackupType:  backupType,
				Description: fmt.Sprintf("MySQL备份 - %s", source),
			})
		}
	}

	// 添加Elasticsearch数据源
	for _, source := range esSources {
		if source = strings.TrimSpace(source); source != "" {
			sources = append(sources, types.BackupRequest{
				SourceType:  types.Elasticsearch,
				SourceName:  source,
				BackupType:  backupType,
				Description: fmt.Sprintf("Elasticsearch备份 - %s", source),
			})
		}
	}

	if len(sources) == 0 {
		return fmt.Errorf("没有有效的数据源")
	}

	// 创建分组备份请求
	req := types.BackupAllRequest{
		Sources:          sources,
		Description:      description,
		Atomic:           atomic,
		CleanupOnFailure: cleanupOnFailure,
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if async {
		taskID, err := manager.BackupAllAsync(ctx, req)
		if err != nil {
			return fmt.Errorf("启动分组备份失败: %w", err)
		}

		fmt.Printf("分组备份任务已启动，任务ID: %s\n", taskID)
		fmt.Printf("备份数据源数量: %d\n", len(sources))
		fmt.Printf("使用以下命令查看任务状态:\n")
		fmt.Printf("  unibackup tasks status %s\n", taskID)

		return nil
	} else {
		return fmt.Errorf("分组备份暂不支持同步模式，请使用 --async=true")
	}
}

func runGroupRestore(cmd *cobra.Command, args []string) error {
	mysqlRestores, _ := cmd.Flags().GetStringSlice("mysql-restores")
	esRestores, _ := cmd.Flags().GetStringSlice("es-restores")
	description, _ := cmd.Flags().GetString("description")
	async, _ := cmd.Flags().GetBool("async")
	atomic, _ := cmd.Flags().GetBool("atomic")
	force, _ := cmd.Flags().GetBool("force")

	// 验证至少有一个恢复配置
	if len(mysqlRestores) == 0 && len(esRestores) == 0 {
		return fmt.Errorf("请至少指定一个恢复配置：--mysql-restores 或 --es-restores")
	}

	if description == "" {
		description = "CLI分组恢复"
	}

	// 解析并构建恢复配置
	var restoreConfigs []types.RestoreConfig

	// 解析MySQL恢复配置
	for _, restore := range mysqlRestores {
		if restore = strings.TrimSpace(restore); restore != "" {
			parts := strings.Split(restore, ":")
			if len(parts) != 2 {
				return fmt.Errorf("MySQL恢复配置格式错误: %s (正确格式: backup-id:target-name)", restore)
			}

			backupID := strings.TrimSpace(parts[0])
			target := strings.TrimSpace(parts[1])

			if backupID == "" || target == "" {
				return fmt.Errorf("MySQL恢复配置不能包含空值: %s", restore)
			}

			config := types.NewRestoreConfig(types.MySQL, target, backupID)
			config.Description = fmt.Sprintf("MySQL恢复 - %s", target)
			restoreConfigs = append(restoreConfigs, config)
		}
	}

	// 解析Elasticsearch恢复配置
	for _, restore := range esRestores {
		if restore = strings.TrimSpace(restore); restore != "" {
			parts := strings.Split(restore, ":")
			if len(parts) != 2 {
				return fmt.Errorf("Elasticsearch恢复配置格式错误: %s (正确格式: backup-id:target-name)", restore)
			}

			backupID := strings.TrimSpace(parts[0])
			target := strings.TrimSpace(parts[1])

			if backupID == "" || target == "" {
				return fmt.Errorf("Elasticsearch恢复配置不能包含空值: %s", restore)
			}

			config := types.NewRestoreConfig(types.Elasticsearch, target, backupID)
			config.Description = fmt.Sprintf("Elasticsearch恢复 - %s", target)
			restoreConfigs = append(restoreConfigs, config)
		}
	}

	if len(restoreConfigs) == 0 {
		return fmt.Errorf("没有有效的恢复配置")
	}

	// 创建分组恢复请求
	batchConfig := types.NewBatchRestoreConfig(restoreConfigs, atomic)
	batchConfig.Description = description
	batchConfig.Force = force

	// 如果启用强制恢复，给用户警告
	if force {
		fmt.Printf("⚠️  强制恢复模式：将中断涉及数据源正在运行的备份任务\n")
		fmt.Print("确认继续？(y/N): ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "y" && confirm != "Y" {
			fmt.Println("分组恢复操作已取消")
			return nil
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if async {
		taskID, err := manager.RestoreAllAsync(ctx, batchConfig)
		if err != nil {
			return fmt.Errorf("启动分组恢复失败: %w", err)
		}

		fmt.Printf("分组恢复任务已启动，任务ID: %s\n", taskID)
		fmt.Printf("恢复配置数量: %d\n", len(restoreConfigs))
		fmt.Printf("原子性操作: %t\n", atomic)
		fmt.Printf("使用以下命令查看任务状态:\n")
		fmt.Printf("  unibackup tasks status %s\n", taskID)

		return nil
	} else {
		return fmt.Errorf("分组恢复暂不支持同步模式，请使用 --async=true")
	}
}

// === 新统一命令的实现函数 ===

// runUnifiedList 实现统一的备份查询命令
func runUnifiedList(cmd *cobra.Command, args []string) error {
	// 获取过滤参数
	sourceStr, _ := cmd.Flags().GetString("source")
	typeStr, _ := cmd.Flags().GetString("type")
	groupOnly, _ := cmd.Flags().GetBool("group")
	searchText, _ := cmd.Flags().GetString("search")
	limit, _ := cmd.Flags().GetInt("limit")
	offset, _ := cmd.Flags().GetInt("offset")
	includeRestores, _ := cmd.Flags().GetBool("include-restores")
	taskTypeStr, _ := cmd.Flags().GetString("task-type")

	// 构建过滤器
	filter := types.BackupFilter{
		SearchText: searchText,
		Limit:      limit,
		Offset:     offset,
	}

	// 处理包含恢复任务的逻辑（重构后）
	// 注意：新的接口默认查询所有任务类型，includeRestores 参数已不再需要
	// 但为了向后兼容，我们仍然处理这个参数
	if includeRestores && taskTypeStr == "" && !groupOnly {
		// 如果用户明确要求包含恢复任务，且没有指定其他任务类型过滤
		filter.TaskTypes = []types.TaskType{
			types.BackupTask,
			types.RestoreTask,
			types.BackupAllTask,
			types.RestoreAllTask,
		}
	}

	// 解析数据源类型（使用新的多值字段）
	if sourceStr != "" {
		switch strings.ToLower(sourceStr) {
		case "mysql":
			filter.SourceTypes = []types.SourceType{types.MySQL}
		case "elasticsearch", "es":
			filter.SourceTypes = []types.SourceType{types.Elasticsearch}
		default:
			return fmt.Errorf("不支持的数据源类型: %s", sourceStr)
		}
	}

	// 解析备份类型（使用新的多值字段）
	if typeStr != "" {
		backupType := parseBackupType(typeStr)
		filter.BackupTypes = []types.BackupType{backupType}
	}

	// 解析任务类型（使用新的多值字段）
	if taskTypeStr != "" {
		taskType := parseTaskType(taskTypeStr)
		filter.TaskTypes = []types.TaskType{taskType}
	}

	// 设置分组过滤（使用新的多值字段）
	if groupOnly {
		filter.TaskTypes = []types.TaskType{
			types.BackupAllTask,
			types.RestoreAllTask,
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 调用新的统一查询接口
	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil {
		return fmt.Errorf("查询备份失败: %w", err)
	}

	// 显示结果
	if len(result.Tasks) == 0 {
		fmt.Println("未找到匹配的备份记录")
		return nil
	}

	fmt.Printf("=== 备份列表 ===\n")
	fmt.Printf("总数: %d, 当前显示: %d\n", result.Total, len(result.Tasks))
	if result.HasMore {
		fmt.Printf("提示: 还有更多记录，使用 --offset %d 查看下一页\n", offset+limit)
	}
	fmt.Println()

	for i, task := range result.Tasks {
		fmt.Printf("%d. 任务ID: %s\n", i+1, task.ID)

		if backupName, ok := task.Metadata["backup_name"].(string); ok {
			fmt.Printf("   名称: %s\n", backupName)
		}

		fmt.Printf("   类型: %s\n", task.Type)
		fmt.Printf("   状态: %s\n", task.Status)
		fmt.Printf("   数据源: %s\n", task.Source)

		if sourceName, ok := task.Metadata["source_name"].(string); ok {
			fmt.Printf("   实例: %s\n", sourceName)
		}

		if backupType, ok := task.Metadata["backup_type"].(string); ok {
			fmt.Printf("   备份类型: %s\n", backupType)
		}

		if backupSize, ok := task.Metadata["backup_size"].(string); ok {
			fmt.Printf("   大小: %s\n", backupSize)
		}

		fmt.Printf("   创建时间: %s\n", task.StartTime.Format("2006-01-02 15:04:05"))

		if !task.EndTime.IsZero() {
			fmt.Printf("   完成时间: %s\n", task.EndTime.Format("2006-01-02 15:04:05"))
		}

		// 分组备份显示子任务数量
		if task.Type == types.BackupAllTask {
			if sourceCount, ok := task.Metadata["source_count"].(int); ok {
				fmt.Printf("   子任务数: %d\n", sourceCount)
			}
			if totalSize, ok := task.Metadata["total_size"].(string); ok {
				fmt.Printf("   总大小: %s\n", totalSize)
			}
		}

		fmt.Println()
	}

	return nil
}

// runUnifiedShow 实现统一的备份详情查看命令
func runUnifiedShow(cmd *cobra.Command, args []string) error {
	taskID := args[0]

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 调用新的统一详情接口
	task, err := manager.GetBackupDetails(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取备份详情失败: %w", err)
	}

	fmt.Printf("=== 备份详情 ===\n")
	fmt.Printf("任务ID: %s\n", task.ID)
	fmt.Printf("类型: %s\n", task.Type)
	fmt.Printf("状态: %s\n", task.Status)
	fmt.Printf("数据源: %s\n", task.Source)

	if backupName, ok := task.Metadata["backup_name"].(string); ok {
		fmt.Printf("备份名称: %s\n", backupName)
	}

	if sourceName, ok := task.Metadata["source_name"].(string); ok {
		fmt.Printf("实例名称: %s\n", sourceName)
	}

	if backupType, ok := task.Metadata["backup_type"].(string); ok {
		fmt.Printf("备份类型: %s\n", backupType)
	}

	fmt.Printf("创建时间: %s\n", task.StartTime.Format("2006-01-02 15:04:05"))

	if !task.EndTime.IsZero() {
		fmt.Printf("完成时间: %s\n", task.EndTime.Format("2006-01-02 15:04:05"))
		duration := task.EndTime.Sub(task.StartTime)
		fmt.Printf("执行时长: %s\n", duration.Round(time.Second))
	}

	if task.Progress > 0 {
		fmt.Printf("进度: %.0f%%\n", task.Progress)
	}

	if task.Error != "" {
		fmt.Printf("错误信息: %s\n", task.Error)
	}

	// 显示备份相关信息
	if backupRecordID, ok := task.Metadata["backup_record_id"].(string); ok {
		fmt.Printf("备份记录ID: %s\n", backupRecordID)
	}

	if backupSize, ok := task.Metadata["backup_size"].(string); ok {
		fmt.Printf("备份大小: %s\n", backupSize)
	}

	// 检查备份文件状态
	if backupFileMissing, ok := task.Metadata["backup_file_missing"].(bool); ok && backupFileMissing {
		fmt.Printf("⚠️  警告: 备份文件缺失\n")
		if errorMsg, ok := task.Metadata["backup_file_error"].(string); ok {
			fmt.Printf("   错误: %s\n", errorMsg)
		}
	}

	// 分组备份的特殊处理
	if task.Type == types.BackupAllTask {
		fmt.Printf("\n=== 分组备份信息 ===\n")

		if sourceCount, ok := task.Metadata["source_count"].(int); ok {
			fmt.Printf("数据源数量: %d\n", sourceCount)
		}

		if completedCount, ok := task.Metadata["completed_count"].(int); ok {
			fmt.Printf("已完成: %d\n", completedCount)
		}

		if failedCount, ok := task.Metadata["failed_count"].(int); ok {
			fmt.Printf("失败: %d\n", failedCount)
		}

		if totalSize, ok := task.Metadata["total_size"].(string); ok {
			fmt.Printf("总大小: %s\n", totalSize)
		}

		// 显示子任务详情
		if subTasks, ok := task.Metadata["sub_tasks"].([]*types.Task); ok && len(subTasks) > 0 {
			fmt.Printf("\n=== 子任务详情 ===\n")
			for i, subTask := range subTasks {
				fmt.Printf("%d. 任务ID: %s\n", i+1, subTask.ID)
				fmt.Printf("   状态: %s\n", subTask.Status)
				fmt.Printf("   数据源: %s\n", subTask.Source)

				if sourceName, ok := subTask.Metadata["source_name"].(string); ok {
					fmt.Printf("   实例: %s\n", sourceName)
				}

				if backupSize, ok := subTask.Metadata["backup_size"].(string); ok {
					fmt.Printf("   大小: %s\n", backupSize)
				}

				if subTask.Error != "" {
					fmt.Printf("   错误: %s\n", subTask.Error)
				}

				fmt.Println()
			}
		} else if len(task.SubTaskIDs) > 0 {
			fmt.Printf("\n=== 子任务ID列表 ===\n")
			for i, subTaskID := range task.SubTaskIDs {
				fmt.Printf("%d. %s\n", i+1, subTaskID)
			}
		}
	}

	return nil
}

// runUnifiedDeletionInfo 实现查看删除影响信息命令
func runUnifiedDeletionInfo(cmd *cobra.Command, args []string) error {
	taskID := args[0]

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 调用删除影响信息接口
	info, err := manager.GetBackupDeletionInfo(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取删除影响信息失败: %w", err)
	}

	fmt.Printf("=== 删除影响信息 ===\n")
	fmt.Printf("任务ID: %s\n", info.TaskID)
	fmt.Printf("任务类型: %s\n", info.TaskType)
	fmt.Printf("数据源类型: %s\n", info.SourceType)
	fmt.Printf("任务状态: %s\n", info.Status)
	fmt.Printf("有备份数据: %t\n", info.HasBackupData)

	if info.Error != "" {
		fmt.Printf("⚠️  错误: %s\n", info.Error)
	}

	// 分组备份的影响信息
	if info.IsGroupBackup {
		fmt.Printf("\n=== 分组备份删除影响 ===\n")
		fmt.Printf("子任务数量: %d\n", info.SubTaskCount)

		if len(info.SubBackups) > 0 {
			fmt.Printf("\n子备份详情:\n")
			for i, subBackup := range info.SubBackups {
				fmt.Printf("%d. 备份ID: %s\n", i+1, subBackup.BackupID)
				fmt.Printf("   备份类型: %s\n", subBackup.BackupType)
				fmt.Printf("   备份时间: %s\n", subBackup.Timestamp.Format("2006-01-02 15:04:05"))

				if subBackup.IsIncrementalChain {
					fmt.Printf("   ⚠️  增量链: %s (包含 %d 个备份点)\n", subBackup.ChainID, subBackup.ChainBackupCount)
					fmt.Printf("   链时间范围: %s 到 %s\n",
						subBackup.ChainStartTime.Format("2006-01-02 15:04:05"),
						subBackup.ChainEndTime.Format("2006-01-02 15:04:05"))
				}
				fmt.Println()
			}
		}
	} else {
		// 单个备份的影响信息
		if info.HasBackupData {
			fmt.Printf("\n=== 单个备份删除影响 ===\n")
			fmt.Printf("备份ID: %s\n", info.BackupID)
			fmt.Printf("备份类型: %s\n", info.BackupType)
			fmt.Printf("备份时间: %s\n", info.Timestamp.Format("2006-01-02 15:04:05"))

			if info.IsIncrementalChain {
				fmt.Printf("\n⚠️  重要警告: 增量链删除影响\n")
				fmt.Printf("链ID: %s\n", info.ChainID)
				fmt.Printf("链中备份数量: %d\n", info.ChainBackupCount)
				fmt.Printf("链时间范围: %s 到 %s\n",
					info.ChainStartTime.Format("2006-01-02 15:04:05"),
					info.ChainEndTime.Format("2006-01-02 15:04:05"))
				fmt.Printf("\n🚨 删除此备份将删除整条增量链的所有 %d 个备份点！\n", info.ChainBackupCount)
				fmt.Printf("   这是为了保持增量链的完整性，避免破坏链的连续性。\n")
				fmt.Printf("   如果您只想删除任务记录而保留备份数据，请联系管理员。\n")
			} else {
				fmt.Printf("\n✅ 这是一个独立的归档备份，删除不会影响其他备份。\n")
			}
		}
	}

	fmt.Printf("\n=== 删除操作说明 ===\n")
	fmt.Printf("执行删除命令: unibackup remove %s\n", taskID)
	fmt.Printf("删除顺序: 先删除备份数据，再删除任务记录\n")

	if info.IsIncrementalChain || info.IsGroupBackup {
		fmt.Printf("⚠️  建议: 删除前请确认影响范围，删除操作不可恢复\n")
	}

	return nil
}

// runUnifiedDelete 实现统一的删除命令
func runUnifiedDelete(cmd *cobra.Command, args []string) error {
	taskID := args[0]

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 首先获取删除影响信息
	info, err := manager.GetBackupDeletionInfo(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取删除影响信息失败: %w", err)
	}

	// 显示删除影响信息
	fmt.Printf("=== 删除确认 ===\n")
	fmt.Printf("任务ID: %s\n", info.TaskID)
	fmt.Printf("任务类型: %s\n", info.TaskType)
	fmt.Printf("数据源: %s\n", info.SourceType)

	if !info.HasBackupData {
		fmt.Printf("⚠️  此任务没有关联的备份数据，只会删除任务记录。\n")
	} else {
		// 显示删除影响
		if info.IsGroupBackup {
			fmt.Printf("📦 分组备份删除影响:\n")
			fmt.Printf("   - 将删除 %d 个子任务的备份数据\n", info.SubTaskCount)

			incrementalChainCount := 0
			for _, subBackup := range info.SubBackups {
				if subBackup.IsIncrementalChain {
					incrementalChainCount++
				}
			}

			if incrementalChainCount > 0 {
				fmt.Printf("   - 包含 %d 个增量链，将删除整条链的所有备份点\n", incrementalChainCount)
			}
		} else if info.IsIncrementalChain {
			fmt.Printf("🚨 增量链删除影响:\n")
			fmt.Printf("   - 链ID: %s\n", info.ChainID)
			fmt.Printf("   - 将删除整条链的所有 %d 个备份点\n", info.ChainBackupCount)
			fmt.Printf("   - 时间范围: %s 到 %s\n",
				info.ChainStartTime.Format("2006-01-02 15:04:05"),
				info.ChainEndTime.Format("2006-01-02 15:04:05"))
			fmt.Printf("   - 这是为了保持增量链的完整性\n")
		} else {
			fmt.Printf("✅ 独立归档备份，删除不会影响其他备份\n")
		}
	}

	// 用户确认
	fmt.Printf("\n⚠️  删除操作不可恢复！\n")
	fmt.Printf("是否确认删除？(输入 'yes' 确认): ")

	var confirmation string
	fmt.Scanln(&confirmation)

	if strings.ToLower(confirmation) != "yes" {
		fmt.Println("删除操作已取消")
		return nil
	}

	// 执行删除
	fmt.Printf("\n正在删除备份 %s...\n", taskID)

	err = manager.DeleteBackupByTaskID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("删除备份失败: %w", err)
	}

	fmt.Printf("✅ 备份 %s 删除成功\n", taskID)

	if info.IsIncrementalChain {
		fmt.Printf("   已删除增量链 %s 的所有 %d 个备份点\n", info.ChainID, info.ChainBackupCount)
	} else if info.IsGroupBackup {
		fmt.Printf("   已删除分组备份的所有 %d 个子任务\n", info.SubTaskCount)
	}

	return nil
}

// runUnifiedRestore 实现基于任务ID的恢复命令
func runUnifiedRestore(cmd *cobra.Command, args []string) error {
	taskID := args[0]
	force, _ := cmd.Flags().GetBool("force")

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 首先获取任务信息进行验证和显示
	task, err := manager.GetTask(taskID)
	if err != nil {
		return fmt.Errorf("获取任务信息失败: %w", err)
	}

	// 显示恢复信息
	fmt.Printf("=== 恢复确认 ===\n")
	fmt.Printf("任务ID: %s\n", task.ID)
	fmt.Printf("任务类型: %s\n", task.Type)
	fmt.Printf("数据源类型: %s\n", task.Source)

	if sourceName, ok := task.Metadata["source_name"].(string); ok {
		fmt.Printf("目标数据源: %s\n", sourceName)
	}

	if backupName, ok := task.Metadata["backup_name"].(string); ok {
		fmt.Printf("备份名称: %s\n", backupName)
	}

	if backupType, ok := task.Metadata["backup_type"].(string); ok {
		fmt.Printf("备份类型: %s\n", backupType)
	}

	fmt.Printf("创建时间: %s\n", task.StartTime.Format("2006-01-02 15:04:05"))

	// 强制恢复警告
	if force {
		if sourceName, ok := task.Metadata["source_name"].(string); ok {
			fmt.Printf("\n⚠️  强制恢复模式：将中断数据源 %s 正在运行的备份任务\n", sourceName)
		} else {
			fmt.Printf("\n⚠️  强制恢复模式：将中断相关数据源正在运行的备份任务\n")
		}
	}

	// 用户确认
	fmt.Printf("\n⚠️  恢复操作将覆盖目标数据源的现有数据！\n")
	if force {
		fmt.Printf("确认强制恢复？(输入 'yes' 确认): ")
	} else {
		fmt.Printf("是否确认恢复？(输入 'yes' 确认): ")
	}

	var confirmation string
	fmt.Scanln(&confirmation)

	if strings.ToLower(confirmation) != "yes" {
		fmt.Println("恢复操作已取消")
		return nil
	}

	// 执行恢复
	fmt.Printf("\n正在启动恢复任务 %s...\n", taskID)

	restoreTaskID, err := manager.RestoreByTaskID(ctx, taskID, force)
	if err != nil {
		return fmt.Errorf("启动恢复失败: %w", err)
	}

	fmt.Printf("恢复任务已启动，任务ID: %s\n", restoreTaskID)
	fmt.Printf("使用以下命令查看恢复进度:\n")
	fmt.Printf("  unibackup tasks status %s\n", restoreTaskID)

	return nil
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "错误: %v\n", err)
		os.Exit(1)
	}
}
