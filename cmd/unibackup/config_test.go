package main

import (
	"os"
	"path/filepath"
	"testing"
)

func TestLoadConfigFromFile(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "unibackup_test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name             string
		configContent    string
		configFile       string
		expectError      bool
		expectBackupRoot string
	}{
		{
			name: "有效的JSON配置",
			configContent: `{
				"backup_root": "/test/backup",
				"backup_timeout": "2h",
				"restore_timeout": "1h",
				"max_concurrent_tasks": 5,
				"mysql": {
					"host": "localhost",
					"port": 3306,
					"user": "testuser",
					"password": "testpass",
					"db_name": "testdb"
				},
				"es": {
					"addresses": ["http://localhost:9200"],
					"archival_repo_name": "test-archival",
					"managed_repo_name": "test-managed"
				}
			}`,
			configFile:       "config.json",
			expectError:      false,
			expectBackupRoot: "/test/backup",
		},
		{
			name: "有效的YAML配置",
			configContent: `backup_root: "/yaml/backup"
backup_timeout: "3h"
restore_timeout: "2h"
max_concurrent_tasks: 10
mysql:
  host: "localhost"
  port: 3306
  user: "yamluser"
  password: "yamlpass"
  db_name: "yamldb"
es:
  addresses:
    - "http://localhost:9200"
  archival_repo_name: "yaml-archival"
  managed_repo_name: "yaml-managed"`,
			configFile:       "config.yaml",
			expectError:      false,
			expectBackupRoot: "/yaml/backup",
		},
		{
			name: "无效的JSON配置",
			configContent: `{
				"backup_root": "/test/backup"
				"invalid_json": true
			}`,
			configFile:  "invalid.json",
			expectError: true,
		},
		{
			name:        "不存在的配置文件",
			configFile:  "nonexistent.json",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var configPath string
			if tt.configContent != "" {
				// 创建配置文件
				configPath = filepath.Join(tempDir, tt.configFile)
				err := os.WriteFile(configPath, []byte(tt.configContent), 0644)
				if err != nil {
					t.Fatalf("创建配置文件失败: %v", err)
				}
			} else {
				configPath = filepath.Join(tempDir, tt.configFile)
			}

			cfg, err := loadConfigFromFile(configPath)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误")
				}
				return
			}

			if err != nil {
				t.Errorf("不期望的错误: %v", err)
				return
			}

			if cfg.BackupRoot != tt.expectBackupRoot {
				t.Errorf("BackupRoot = %v, 期望 %v", cfg.BackupRoot, tt.expectBackupRoot)
			}

			// 基本验证
			if cfg.MySQL == nil && cfg.ES == nil {
				t.Errorf("配置中应该至少有一个数据源")
			}
		})
	}
}

func TestGetDefaultConfigPaths(t *testing.T) {
	paths := getDefaultConfigPaths()

	// 检查是否返回了预期的路径
	expectedPaths := []string{
		"./unibackup.json",
		"./unibackup.yaml",
		"./config.json",
		"./config.yaml",
	}

	for _, expectedPath := range expectedPaths {
		found := false
		for _, path := range paths {
			if path == expectedPath {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("默认路径中未找到: %s", expectedPath)
		}
	}

	// 验证路径数量
	if len(paths) < 6 {
		t.Errorf("默认路径数量太少: %d", len(paths))
	}
}

func TestAutoDetectConfigFormat(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "unibackup_test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 测试没有扩展名的文件，使用JSON格式
	jsonContent := `{
		"backup_root": "/test/backup",
		"backup_timeout": "1h",
		"mysql": {
			"host": "localhost",
			"port": 3306,
			"user": "user",
			"password": "pass",
			"db_name": "db"
		}
	}`

	configPath := filepath.Join(tempDir, "config")
	err = os.WriteFile(configPath, []byte(jsonContent), 0644)
	if err != nil {
		t.Fatalf("创建配置文件失败: %v", err)
	}

	cfg, err := loadConfigFromFile(configPath)
	if err != nil {
		t.Errorf("自动检测JSON格式失败: %v", err)
		return
	}

	if cfg.BackupRoot != "/test/backup" {
		t.Errorf("自动检测的配置解析错误")
	}
}

func TestLoadConfigFromFile_EmptyFilename(t *testing.T) {
	// 测试空文件名的情况（应该触发默认路径查找）
	cfg, err := loadConfigFromFile("")

	// 这应该返回错误，因为默认路径中没有配置文件
	if err == nil {
		t.Errorf("期望出现错误，但加载成功了")
	}

	if cfg != nil {
		t.Errorf("期望返回nil配置，但得到了: %+v", cfg)
	}

	// 错误消息应该提示使用 -c 参数
	if err != nil && !contains(err.Error(), "-c") {
		t.Errorf("错误消息应该提示使用 -c 参数: %v", err)
	}
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			contains(s[1:], substr))))
}
