package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// quickStartExample 演示 UniBackup SDK 的快速开始使用
func RunQuickStartExample() {
	// 简单的日志配置
	logger := zap.NewNop()

	// 最小化配置
	cfg := &types.Config{
		BackupRoot: "/tmp/unibackup_quick_start",
		Logger:     logger,

		// 只配置 MySQL（根据实际情况修改）
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "root",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test",
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		fmt.Printf("创建备份管理器失败: %v\n", err)
		os.Exit(1)
	}
	defer manager.Shutdown()

	ctx := context.Background()

	fmt.Println("🚀 开始快速备份演示...")

	// 1. 创建一个简单的备份
	fmt.Println("\n📦 创建备份...")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "test", types.BackupTypeArchival, "快速开始示例")
	if err != nil {
		fmt.Printf("启动备份失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 备份任务已启动，任务ID: %s\n", taskID)

	// 2. 监控任务进度
	fmt.Println("\n⏳ 监控任务进度...")
	for {
		task, err := manager.GetTask(taskID)
		if err != nil {
			fmt.Printf("获取任务状态失败: %v\n", err)
			return
		}

		fmt.Printf("📊 任务状态: %s, 进度: %.1f%%\n", task.Status, task.Progress)

		if task.Status.IsTerminal() {
			if task.Status == types.TaskStatusCompleted {
				fmt.Println("🎉 备份完成!")
			} else {
				fmt.Printf("❌ 备份失败: %s\n", task.Error)
				return
			}
			break
		}

		time.Sleep(2 * time.Second)
	}

	// 3. 列出备份
	fmt.Println("\n📋 列出所有备份...")

	// ⚠️  注意：ListArchivalBackups 已废弃，推荐使用 ListAllBackups(ctx, filter)
	// 现代化示例请查看 examples/modern_usage.go
	fmt.Println("⚠️  使用已废弃接口 ListArchivalBackups，推荐查看 examples/modern_usage.go 获取现代化示例")

	backups, backErr := manager.ListArchivalBackups(ctx, types.MySQL)
	if backErr != nil {
		fmt.Printf("获取备份列表失败: %v\n", backErr.Message)
		return
	}

	fmt.Printf("📁 找到 %d 个备份:\n", len(backups))
	for i, backup := range backups {
		fmt.Printf("  %d. ID: %s, 时间: %s, 描述: %s\n",
			i+1,
			backup.Record.ID,
			backup.Record.Timestamp.Format("2006-01-02 15:04:05"),
			backup.Record.Description,
		)
	}

	// 4. 演示恢复（可选，注释掉以避免实际恢复）
	if len(backups) > 0 {
		fmt.Println("\n🔄 演示恢复功能...")
		latestBackup := backups[len(backups)-1]

		fmt.Printf("准备从备份 %s 恢复...\n", latestBackup.Record.ID)

		// 注意：这里只是演示，实际使用时请谨慎
		// restoreTaskID, err := manager.RestoreAsync(ctx, types.MySQL, "test", latestBackup.Record.ID)
		// if err != nil {
		//     fmt.Printf("启动恢复失败: %v\n", err)
		//     return
		// }
		// fmt.Printf("✅ 恢复任务已启动，任务ID: %s\n", restoreTaskID)

		fmt.Println("⚠️  恢复功能已跳过（演示模式）")
	}

	fmt.Println("\n🎯 快速开始演示完成!")
	fmt.Printf("📂 备份文件位置: %s\n", cfg.BackupRoot)
}

func main() {
	RunQuickStartExample()
}
