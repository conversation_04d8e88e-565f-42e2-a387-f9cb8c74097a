# 废弃接口迁移指南

## 📢 重要通知

examples 目录中的某些示例使用了已废弃的接口。为了提供更好的用户体验和性能，我们引入了新的统一查询接口。

## 🔍 废弃接口列表

| 废弃接口 | 替代接口 | 状态 |
|---------|----------|------|
| `ListArchivalBackups(ctx, sourceType)` | `ListAllBackups(ctx, filter)` | 已废弃 |
| `ListIncrementalChains(ctx, sourceType)` | `ListAllBackups(ctx, filter)` | 已废弃 |
| `DeleteBackup(ctx, sourceType, backupID)` | `DeleteBackupByTaskID(ctx, taskID)` | 已废弃 |

## 🚀 新接口优势

### 1. 统一查询接口 `ListAllBackups`
- ✅ **性能更好**：基于内存的tasks.json查询，比扫描文件系统更快
- ✅ **功能更强**：支持复杂的过滤条件、搜索和分页
- ✅ **统一体验**：所有备份类型使用相同的查询接口
- ✅ **类型安全**：使用结构化的过滤参数

### 2. 改进的删除接口 `DeleteBackupByTaskID`
- ✅ **更好的用户体验**：基于任务ID操作，更直观
- ✅ **更强的错误处理**：提供更详细的错误信息
- ✅ **更好的安全性**：避免误删操作

## 🔄 迁移示例

### 从废弃接口到新接口

#### 旧方式（已废弃）
```go
// ❌ 已废弃
backups, err := manager.ListArchivalBackups(ctx, types.MySQL)
chains, err := manager.ListIncrementalChains(ctx, types.MySQL)
err := manager.DeleteBackup(ctx, types.MySQL, "backup-id")
```

#### 新方式（推荐）
```go
// ✅ 推荐方式（重构后）
filter := types.BackupFilter{
    SourceTypes: []types.SourceType{types.MySQL},
    BackupTypes: []types.BackupType{types.BackupTypeArchival},
    Limit:       10,
}
result, err := manager.ListAllBackups(ctx, filter)

// 删除备份
err := manager.DeleteBackupByTaskID(ctx, taskID)
```

## 📋 现代化示例

我们提供了全新的现代化示例文件：

- `examples/modern_usage.go` - 完整的现代化接口使用示例
- `examples/configurations/cloud_storage.go` - 云存储配置示例 ✅ 已更新为现代化接口
- 包含统一查询、高级过滤、任务管理等新功能

## ⚠️ 兼容性说明

- **废弃接口仍可使用**：为了保持向后兼容，废弃接口仍可使用
- **建议迁移**：新项目中强烈建议使用新接口
- **未来计划**：废弃接口将在未来主要版本中移除

## 📚 相关文件

- `examples/modern_usage.go` - 现代化接口完整示例
- `examples/README.md` - 示例文件使用说明
- `docs/DEVELOPER_GUIDE.md` - 完整的API文档和开发指南

## 🆘 获取帮助

如果在迁移过程中遇到问题，请：

1. 查看 `examples/modern_usage.go` 中的完整示例
2. 参考 `docs/DEVELOPER_GUIDE.md` 中的API文档
3. 提交issue获取技术支持