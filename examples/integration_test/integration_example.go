package main

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// integrationTestExample 演示完整的集成测试流程
// 注意：这个示例需要实际的 MySQL 和 Elasticsearch 环境
func RunIntegrationTestExample() {
	// 设置测试环境
	logger := zap.NewNop()

	// 创建临时测试目录
	testDir := filepath.Join(os.TempDir(), "unibackup_integration_test")
	if err := os.RemoveAll(testDir); err != nil {
		logger.Error("清理测试目录失败", zap.Error(err))
	}
	if err := os.MkdirAll(testDir, 0755); err != nil {
		logger.Error("创建测试目录失败", zap.Error(err))
		os.Exit(1)
	}
	defer func() {
		if err := os.RemoveAll(testDir); err != nil {
			logger.Error("清理测试目录失败", zap.Error(err))
		}
	}()

	fmt.Printf("🧪 开始集成测试，测试目录: %s\n", testDir)

	// 配置（请根据实际环境修改）
	cfg := &types.Config{
		BackupRoot:         testDir,
		Logger:             logger,
		MaxConcurrentTasks: 2,
		TaskRetentionDays:  1,
		MaxTaskHistory:     50,

		// MySQL 配置 - 请根据实际环境修改
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "test_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test_db",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/opt/homebrew/opt/mysql@8.0/bin/mysqldump",
				Mysql:       "/opt/homebrew/opt/mysql@8.0/bin/mysql",
				Mysqlbinlog: "/opt/homebrew/opt/mysql@8.0/bin/mysqlbinlog",
				Mysqladmin:  "/opt/homebrew/opt/mysql@8.0/bin/mysqladmin",
			},
		},

		// Elasticsearch 配置 - 请根据实际环境修改
		// 重要：如果ES运行在Docker中，需要配置卷映射确保快照存储在统一的备份目录
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			User:             "elastic",
			Password:         os.Getenv("UNIBACKUP_ES_PASSWORD"),
			ArchivalRepoName: "test_archival",
			ManagedRepoName:  "test_managed",
			AutoCreateRepos:  true, // 推荐设置，自动创建仓库确保统一存储
			// RepoBasePath留空使用默认路径：BackupRoot/elasticsearch
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		fmt.Printf("❌ 创建备份管理器失败: %v\n", err)
		fmt.Println("💡 提示：请确保配置正确，或者跳过需要实际服务的测试")
		return
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 测试1: 基本功能测试
	fmt.Println("\n📋 测试1: 基本功能测试")
	if err := testBasicFunctionality(ctx, manager); err != nil {
		fmt.Printf("❌ 基本功能测试失败: %v\n", err)
		return
	}
	fmt.Println("✅ 基本功能测试通过")

	// 测试2: 并发测试
	fmt.Println("\n🔄 测试2: 并发测试")
	if err := testConcurrency(ctx, manager); err != nil {
		fmt.Printf("❌ 并发测试失败: %v\n", err)
		return
	}
	fmt.Println("✅ 并发测试通过")

	// 测试3: 错误处理测试
	fmt.Println("\n⚠️  测试3: 错误处理测试")
	if err := testErrorHandling(ctx, manager); err != nil {
		fmt.Printf("❌ 错误处理测试失败: %v\n", err)
		return
	}
	fmt.Println("✅ 错误处理测试通过")

	// 测试4: 任务管理测试
	fmt.Println("\n📊 测试4: 任务管理测试")
	if err := testTaskManagement(ctx, manager); err != nil {
		fmt.Printf("❌ 任务管理测试失败: %v\n", err)
		return
	}
	fmt.Println("✅ 任务管理测试通过")

	// 测试5: 混合类型分组备份
	fmt.Println("\n📋 测试5: 混合类型分组备份")
	if err := testMixedTypeGroupBackup(ctx, manager, cfg); err != nil {
		fmt.Printf("❌ 混合类型分组备份测试失败: %v\n", err)
		return
	}
	fmt.Println("✅ 混合类型分组备份测试通过")

	fmt.Println("\n🎉 所有集成测试通过！")
}

// testMixedTypeGroupBackup 测试混合类型分组备份
func testMixedTypeGroupBackup(ctx context.Context, manager unibackup.BackupManager, cfg *types.Config) error {
	// 1. 创建一个 MySQL 归档备份请求
	mysqlArchivalReq := types.BackupRequest{
		SourceType:  types.MySQL,
		SourceName:  cfg.MySQL.DBName,
		BackupType:  types.BackupTypeArchival,
		Description: "分组归档备份",
	}

	// 2. 创建一个 MySQL 链初始备份请求
	mysqlChainInitialReq := types.BackupRequest{
		SourceType:  types.MySQL,
		SourceName:  cfg.MySQL.DBName,
		BackupType:  types.BackupTypeChainInitial,
		Description: "分组链初始备份",
	}

	// 3. 创建一个包含混合类型子备份的分组备份请求
	req := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			mysqlArchivalReq,
			mysqlChainInitialReq,
		},
		Description:      "混合类型分组备份",
		Atomic:           false, // 简化测试，不测试原子性清理
		CleanupOnFailure: false, // 简化测试，不测试原子性清理
	}

	fmt.Println("  🚀 启动混合类型分组备份...")
	taskID, err := manager.BackupAllAsync(ctx, req)
	if err != nil {
		return fmt.Errorf("启动混合类型分组备份失败: %w", err)
	}

	fmt.Printf("  ⏳ 等待分组备份任务 %s 完成...\n", taskID)
	if err := waitForTaskCompletion(manager, taskID, 60*time.Second); err != nil {
		return fmt.Errorf("等待混合类型分组备份完成失败: %w", err)
	}

	// 验证主任务成功完成
	mainTask, err := manager.GetTask(taskID)
	if err != nil {
		return fmt.Errorf("获取主任务状态失败: %w", err)
	}
	if mainTask.Status != types.TaskStatusCompleted {
		return fmt.Errorf("主任务未成功完成，状态: %s, 错误: %s", mainTask.Status, mainTask.Error)
	}
	fmt.Println("  ✅ 混合类型分组备份任务成功完成。")

	return nil
}

// testBasicFunctionality 测试基本功能
func testBasicFunctionality(ctx context.Context, manager unibackup.BackupManager) error {
	// 测试异步备份
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeArchival, "集成测试备份")
	if err != nil {
		return fmt.Errorf("启动异步备份失败: %w", err)
	}

	// 等待任务完成
	if err := waitForTaskCompletion(manager, taskID, 30*time.Second); err != nil {
		return fmt.Errorf("等待备份完成失败: %w", err)
	}

	// 列出备份
	// ⚠️  注意：ListArchivalBackups 已废弃，推荐使用 ListAllBackups(ctx, filter)
	fmt.Println("  ⚠️  使用已废弃接口 ListArchivalBackups")

	backups, backupErr := manager.ListArchivalBackups(ctx, types.MySQL)
	if backupErr != nil {
		return fmt.Errorf("列出备份失败: %s", backupErr.Message)
	}

	if len(backups) == 0 {
		return fmt.Errorf("备份列表为空")
	}

	fmt.Printf("  📁 找到 %d 个备份\n", len(backups))
	return nil
}

// testConcurrency 测试并发功能
func testConcurrency(ctx context.Context, manager unibackup.BackupManager) error {
	// 启动多个并发备份任务
	var taskIDs []string
	for i := 0; i < 3; i++ {
		taskID, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeArchival, fmt.Sprintf("并发测试备份 %d", i+1))
		if err != nil {
			fmt.Printf("  ⚠️  并发任务 %d 启动失败: %v\n", i+1, err)
			continue
		}
		taskIDs = append(taskIDs, taskID)
	}

	if len(taskIDs) == 0 {
		return fmt.Errorf("没有成功启动任何并发任务")
	}

	fmt.Printf("  🔄 启动了 %d 个并发任务\n", len(taskIDs))

	// 等待所有任务完成
	for i, taskID := range taskIDs {
		if err := waitForTaskCompletion(manager, taskID, 30*time.Second); err != nil {
			fmt.Printf("  ⚠️  任务 %d 完成失败: %v\n", i+1, err)
		}
	}

	return nil
}

// testErrorHandling 测试错误处理
func testErrorHandling(ctx context.Context, manager unibackup.BackupManager) error {
	// 测试无效的备份ID恢复
	restoreConfig := types.NewRestoreConfig(types.MySQL, "test_db", "non-existent-backup-id")
	restoreErr := manager.Restore(ctx, restoreConfig)
	if restoreErr == nil {
		return fmt.Errorf("期望恢复无效备份ID时返回错误，但没有返回错误")
	}
	fmt.Printf("  ✅ 正确处理了无效备份ID错误: %s\n", restoreErr.Message)

	// 测试删除不存在的备份
	// ⚠️  注意：DeleteBackup 已废弃，推荐使用 DeleteBackupByTaskID(ctx, taskID)
	fmt.Println("  ⚠️  使用已废弃接口 DeleteBackup")

	err2 := manager.DeleteBackup(ctx, types.MySQL, "non-existent-backup-id")
	if err2 == nil {
		return fmt.Errorf("期望删除不存在的备份时返回错误，但没有返回错误")
	}
	fmt.Printf("  ✅ 正确处理了删除不存在备份的错误: %v\n", err2.Error())

	return nil
}

// testTaskManagement 测试任务管理
func testTaskManagement(ctx context.Context, manager unibackup.BackupManager) error {
	// 列出所有任务
	tasks, err := manager.ListTasks()
	if err != nil {
		return fmt.Errorf("列出任务失败: %w", err)
	}

	fmt.Printf("  📊 当前有 %d 个任务\n", len(tasks))

	// 清理旧任务
	if err := manager.ClearOldTasks(); err != nil {
		return fmt.Errorf("清理旧任务失败: %w", err)
	}

	fmt.Println("  🧹 旧任务清理完成")
	return nil
}

// waitForTaskCompletion 等待任务完成的辅助函数
func waitForTaskCompletion(manager unibackup.BackupManager, taskID string, timeout time.Duration) error {
	start := time.Now()
	for time.Since(start) < timeout {
		task, err := manager.GetTask(taskID)
		if err != nil {
			return fmt.Errorf("获取任务状态失败: %w", err)
		}

		if task.Status.IsTerminal() {
			if task.Status == types.TaskStatusCompleted {
				return nil
			}
			return fmt.Errorf("任务失败: %s", task.Error)
		}

		time.Sleep(1 * time.Second)
	}

	return fmt.Errorf("任务超时")
}

// 注意：运行此集成测试需要：
// 1. 实际的 MySQL 服务器和测试数据库
// 2. 实际的 Elasticsearch 集群
// 3. 正确的连接配置
// 4. 适当的权限设置
//
// Docker 部署示例（推荐用于测试）：
//
// docker-compose.yml:
// version: '3.8'
// services:
//   elasticsearch:
//     image: elasticsearch:8.x
//     environment:
//       - discovery.type=single-node
//       - path.repo=/usr/share/elasticsearch/snapshots
//       - xpack.security.enabled=false
//     volumes:
//       - /tmp/unibackup_integration_test/elasticsearch:/usr/share/elasticsearch/snapshots
//     ports:
//       - "9200:9200"
//
//   mysql:
//     image: mysql:8.0
//     environment:
//       - MYSQL_ROOT_PASSWORD=test_password
//       - MYSQL_DATABASE=test_db
//     ports:
//       - "3306:3306"
//
// 启动命令：
// mkdir -p /tmp/unibackup_integration_test/elasticsearch
// chown -R 1000:1000 /tmp/unibackup_integration_test/elasticsearch
// docker-compose up -d
//
// 如果没有这些环境，可以：
// 1. 使用上述 Docker 配置启动测试服务
// 2. 修改配置以匹配你的环境
// 3. 或者跳过需要实际服务的部分

func main() {
	RunIntegrationTestExample()
}
