# UniBackup 使用示例

本目录包含了 UniBackup SDK 的使用示例，展示了如何在实际项目中使用备份和恢复功能。

## 📁 示例列表

### 🆕 现代化示例
**推荐使用这些新示例，它们使用了最新的API接口**

#### 1. 现代化使用示例 (`modern_usage.go`)
展示使用推荐的新接口的完整示例：
- ✅ 统一查询接口 `ListAllBackups`
- ✅ 高级过滤和搜索功能
- ✅ 现代化任务管理
- ✅ 类型安全的API调用
- ✅ **自动回滚功能** - 恢复失败时自动回滚保护

**运行方式：**
```bash
go run modern_usage.go
```

## 🔄 新功能亮点

### 自动回滚功能
所有恢复示例现在都包含自动回滚功能：
- **预恢复快照**：恢复前自动创建安全备份
- **失败回滚**：恢复失败时自动回滚到原始状态
- **智能清理**：成功后自动清理临时快照
- **全面支持**：单个恢复、分组串行恢复、分组并行恢复

### 使用方式
```bash
# 所有恢复操作都自动包含回滚保护
unibackup restore-by-task task-123

# 即使强制恢复也有回滚保护
unibackup restore-by-task task-123 --force
```

### ⚠️  传统示例（含废弃接口警告）
**这些示例包含已废弃接口的调用，仅供兼容性参考**

#### 2. 完整使用示例 (`complete_demo/`)
完整的功能演示，但使用了部分已废弃接口。

**⚠️  废弃接口警告：**
- `ListArchivalBackups` → 使用 `ListAllBackups(ctx, filter)`
- `ListIncrementalChains` → 使用 `ListAllBackups(ctx, filter)`

**运行方式：**
```bash
cd complete_demo
go run basic_usage.go
```

#### 3. 快速开始示例 (`quick_start/`)
简化版演示，但使用了已废弃接口 `ListArchivalBackups`。

**⚠️  废弃接口警告：**
- `ListArchivalBackups` → 使用 `ListAllBackups(ctx, filter)`

**运行方式：**
```bash
cd quick_start
go run quick_start.go
```

#### 4. 集成测试示例 (`integration_test/`)
完整的集成测试流程，但使用了已废弃接口 `ListArchivalBackups` 和 `DeleteBackup`。

**⚠️  废弃接口警告：**
- `ListArchivalBackups` → 使用 `ListAllBackups(ctx, filter)`
- `DeleteBackup` → 使用 `DeleteBackupByTaskID(ctx, taskID)`

**运行方式：**
```bash
cd integration_test
go run integration_example.go
```

### 4. 配置示例 (`configurations/`)
展示不同场景下的配置模式：
- **基础配置** (`basic_configs.go`): 仅MySQL、仅ES、多数据源配置
- **云存储配置** (`cloud_storage.go`): AWS S3、Google Cloud Storage、Azure Blob Storage ✅ 已使用现代化接口
- **生产环境配置** (`production.go`): 高可用、安全优化、监控配置

**运行方式：**
```bash
cd configurations

# 基础配置示例
go run basic_configs.go

# 云存储配置示例（需要设置认证信息）
# AWS S3
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret"

# Google Cloud Storage
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

# Azure Blob Storage
export AZURE_STORAGE_ACCOUNT="your-storage-account"
export AZURE_STORAGE_KEY="your-storage-key"

go run cloud_storage.go

# 生产环境配置示例
export UNIBACKUP_MYSQL_PASSWORD="your-mysql-password"
export UNIBACKUP_ES_API_KEY="your-elasticsearch-api-key"
go run production.go
```

### 5. 错误处理示例 (`error_handling/`)
演示错误处理的最佳实践：
- 配置错误处理
- 连接错误处理
- 任务错误处理
- 结构化错误信息解析

**运行方式：**
```bash
cd error_handling
go run error_handling.go
```

## 🚀 快速运行

### 环境准备
详细的环境配置和安装指南请参考：**[快速开始指南](../docs/QUICK_START.md)**

### 基本步骤
1. 根据需要修改示例中的配置参数
2. 设置必要的环境变量（如数据库密码）
3. 运行对应的示例代码

## 📚 更多资源

- **[快速开始指南](../docs/QUICK_START.md)** - 5分钟快速上手
- **[用户指南](../docs/USER_GUIDE.md)** - 完整配置说明和API使用
- **[故障排除指南](../docs/TROUBLESHOOTING.md)** - 常见问题解决

## 🚨 废弃接口迁移

**重要：** 传统示例中使用的部分接口已废弃，建议使用现代化示例。

**查看详细迁移指南：** [废弃接口迁移指南](DEPRECATED_INTERFACES_NOTICE.md)

## 💡 使用建议

1. **新项目强烈推荐**：从 `modern_usage.go` 开始，使用最新的API接口
2. **现有项目迁移**：参考 [废弃接口迁移指南](DEPRECATED_INTERFACES_NOTICE.md) 进行平滑迁移
3. **兼容性需求**：可以继续使用传统示例，但建议逐步迁移
4. **生产环境**：参考 `configurations/production.go` 中的配置示例

## 🔧 快速开始（推荐流程）

```bash
# 1. 使用现代化示例
export UNIBACKUP_MYSQL_PASSWORD="your-mysql-password"
export UNIBACKUP_ES_API_KEY="your-elasticsearch-api-key"
go run modern_usage.go

# 2. 查看迁移指南
cat DEPRECATED_INTERFACES_NOTICE.md
```
