package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// basicUsageExample 演示 UniBackup SDK 的基本使用方法
//
// 使用前请设置环境变量：
// export MYSQL_PASSWORD="your_mysql_password"
func RunBasicUsageExample() {
	// 设置日志记录器
	logger := zap.NewNop()

	// 配置备份根目录（请根据实际情况修改）
	backupRoot := "/tmp/unibackup_examples"

	// 确保备份目录存在
	if err := os.MkdirAll(backupRoot, 0755); err != nil {
		logger.Error("创建备份目录失败", zap.Error(err))
		os.Exit(1)
	}

	// 创建 SDK 配置
	cfg := &types.Config{
		BackupRoot:         backupRoot,
		Logger:             logger,
		MaxConcurrentTasks: 3,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		// MySQL 配置（请根据实际环境修改）
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "backup_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"), // 从环境变量获取密码，提高安全性
			DBName:   "test_db",
			// 工具路径通常可以自动检测，无需手动配置
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "mysqldump",
				Mysql:       "mysql",
				Mysqlbinlog: "mysqlbinlog",
				Mysqladmin:  "mysqladmin",
			},
		},

		// Elasticsearch 配置（请根据实际环境修改）
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			User:             "elastic",
			Password:         os.Getenv("UNIBACKUP_ES_PASSWORD"),
			ArchivalRepoName: "unibackup_archival",
			ManagedRepoName:  "unibackup_managed",
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		logger.Error("创建备份管理器失败", zap.Error(err))
		os.Exit(1)
	}
	defer func() {
		if err := manager.Shutdown(); err != nil {
			logger.Error("关闭备份管理器失败", zap.Error(err))
		}
	}()

	ctx := context.Background()

	// 示例1: 单个 MySQL 归档备份
	fmt.Println("\n=== 示例1: MySQL 归档备份 ===")
	mysqlBackupExample(ctx, manager, logger)

	// 示例2: MySQL 增量链备份
	fmt.Println("\n=== 示例2: MySQL 增量链备份 ===")
	mysqlIncrementalExample(ctx, manager, logger)

	// 示例3: Elasticsearch 备份
	fmt.Println("\n=== 示例3: Elasticsearch 备份 ===")
	elasticsearchExample(ctx, manager, logger)

	// 示例4: 分组备份
	fmt.Println("\n=== 示例4: 分组备份 ===")
	groupBackupExample(ctx, manager, logger)

	// 示例5: 任务管理
	fmt.Println("\n=== 示例5: 任务管理 ===")
	taskManagementExample(ctx, manager, logger)

	// 示例6: 备份列表和删除
	fmt.Println("\n=== 示例6: 备份管理 ===")
	backupManagementExample(ctx, manager, logger)
}

// mysqlBackupExample 演示 MySQL 归档备份
func mysqlBackupExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 异步启动备份
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeArchival, "示例归档备份")
	if err != nil {
		logger.Error("启动 MySQL 备份失败", zap.Error(err))
		return
	}

	logger.Info("MySQL 备份任务已启动", zap.String("task_id", taskID))

	// 监控任务进度
	waitForTask(manager, taskID, logger)
}

// mysqlIncrementalExample 演示 MySQL 增量链备份
func mysqlIncrementalExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 创建增量链的初始备份
	taskID1, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeChainInitial, "增量链初始备份")
	if err != nil {
		logger.Error("启动增量链初始备份失败", zap.Error(err))
		return
	}

	logger.Info("增量链初始备份已启动", zap.String("task_id", taskID1))
	waitForTask(manager, taskID1, logger)

	// 创建增量备份
	taskID2, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeChainIncremental, "第一个增量备份")
	if err != nil {
		logger.Error("启动增量备份失败", zap.Error(err))
		return
	}

	logger.Info("增量备份已启动", zap.String("task_id", taskID2))
	waitForTask(manager, taskID2, logger)
}

// elasticsearchExample 演示 Elasticsearch 备份
func elasticsearchExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 异步启动 ES 备份
	taskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "my_cluster", types.BackupTypeArchival, "ES 示例备份")
	if err != nil {
		logger.Error("启动 Elasticsearch 备份失败", zap.Error(err))
		return
	}

	logger.Info("Elasticsearch 备份任务已启动", zap.String("task_id", taskID))
	waitForTask(manager, taskID, logger)
}

// groupBackupExample 演示分组备份
func groupBackupExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	req := types.BackupAllRequest{
		Sources: []types.BackupRequest{
			{
				SourceType:  types.MySQL,
				SourceName:  "test_db",
				BackupType:  types.BackupTypeArchival,
				Description: "MySQL 分组备份",
			},
			{
				SourceType:  types.Elasticsearch,
				SourceName:  "my_cluster",
				BackupType:  types.BackupTypeArchival,
				Description: "ES 分组备份",
			},
		},
		Description:      "每日分组备份",
		Atomic:           false,
		CleanupOnFailure: true,
	}

	taskID, err := manager.BackupAllAsync(ctx, req)
	if err != nil {
		logger.Error("启动分组备份失败", zap.Error(err))
		return
	}

	logger.Info("分组备份任务已启动", zap.String("task_id", taskID))
	waitForTask(manager, taskID, logger)
}

// taskManagementExample 演示任务管理功能
func taskManagementExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 列出所有任务
	tasks, err := manager.ListTasks()
	if err != nil {
		logger.Error("获取任务列表失败", zap.Error(err))
		return
	}

	logger.Info("当前任务列表", zap.Int("task_count", len(tasks)))
	for _, task := range tasks {
		logger.Info("任务信息",
			zap.String("id", task.ID),
			zap.String("type", string(task.Type)),
			zap.String("status", string(task.Status)),
			zap.Float64("progress", task.Progress),
			zap.String("description", task.Description),
		)
	}

	// 清理旧任务
	if err := manager.ClearOldTasks(); err != nil {
		logger.Error("清理旧任务失败", zap.Error(err))
	} else {
		logger.Info("旧任务清理完成")
	}
}

// backupManagementExample 演示备份管理功能
//
// ⚠️  注意：此函数使用了已废弃的接口
// 推荐使用现代化的统一查询接口：ListAllBackups(ctx, filter)
// 示例请查看 modern_usage.go 中的 modernBackupManagement 函数
func backupManagementExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	fmt.Println("⚠️  正在使用已废弃的接口，推荐查看 modern_usage.go 中的现代化示例")

	// 列出归档备份（已废弃接口）
	archivalBackups, err := manager.ListArchivalBackups(ctx, types.MySQL)
	if err != nil {
		logger.Error("获取归档备份列表失败", zap.Error(err))
	} else {
		logger.Info("MySQL 归档备份", zap.Int("count", len(archivalBackups)))
		for _, backup := range archivalBackups {
			logger.Info("归档备份",
				zap.String("id", backup.Record.ID),
				zap.Time("timestamp", backup.Record.Timestamp),
				zap.String("description", backup.Record.Description),
			)
		}
	}

	// 列出增量链（已废弃接口）
	chains, err := manager.ListIncrementalChains(ctx, types.MySQL)
	if err != nil {
		logger.Error("获取增量链列表失败", zap.Error(err))
	} else {
		logger.Info("MySQL 增量链", zap.Int("count", len(chains)))
		for _, chain := range chains {
			logger.Info("增量链",
				zap.String("chain_id", chain.ChainID),
				zap.Int("backup_count", len(chain.Backups)),
			)
		}
	}
}

// waitForTask 等待任务完成的辅助函数
func waitForTask(manager unibackup.BackupManager, taskID string, logger *zap.Logger) {
	for {
		task, err := manager.GetTask(taskID)
		if err != nil {
			logger.Error("获取任务状态失败", zap.String("task_id", taskID), zap.Error(err))
			return
		}

		logger.Info("任务进度",
			zap.String("task_id", taskID),
			zap.String("status", string(task.Status)),
			zap.Float64("progress", task.Progress),
		)

		if task.Status.IsTerminal() {
			if task.Status == types.TaskStatusCompleted {
				logger.Info("任务完成", zap.String("task_id", taskID))
			} else {
				logger.Error("任务失败", zap.String("task_id", taskID), zap.String("error", task.Error))
			}
			return
		}

		time.Sleep(2 * time.Second)
	}
}

func main() {
	RunBasicUsageExample()
}
