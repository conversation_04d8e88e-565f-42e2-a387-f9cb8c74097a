package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("=== UniBackup 示例程序 ===")
	fmt.Println("请选择要运行的示例：")
	fmt.Println("1. 现代化接口使用示例 (推荐)")
	fmt.Println("2. 快速开始")
	fmt.Println("3. 基础配置")
	fmt.Println("4. 生产配置")
	fmt.Println("5. 云存储配置")
	fmt.Println("6. 错误处理")
	fmt.Println("7. 集成测试")
	fmt.Println("0. 退出")

	if len(os.Args) > 1 {
		// 命令行参数模式
		choice := os.Args[1]
		runExample(choice)
		return
	}

	// 交互模式
	fmt.Print("\n请输入选项编号 (1-7): ")
	var choice string
	fmt.Scanln(&choice)
	runExample(choice)
}

func runExample(choice string) {
	switch choice {
	case "1":
		fmt.Println("\n🚀 运行现代化接口使用示例...")
		RunModernUsageExample()
	case "0":
		fmt.Println("退出程序")
		os.Exit(0)
	default:
		fmt.Printf("\n⚠️  请直接运行对应的示例文件：\n")
		fmt.Printf("  go run modern_usage.go                              # 现代化接口示例\n")
		fmt.Printf("  go run ./quick_start/quick_start.go                 # 快速开始\n")
		fmt.Printf("  go run ./configurations/basic_configs.go           # 基础配置\n")
		fmt.Printf("  go run ./configurations/production.go              # 生产配置\n")
		fmt.Printf("  go run ./configurations/cloud_storage.go           # 云存储配置\n")
		fmt.Printf("  go run ./error_handling/error_handling.go          # 错误处理\n")
		fmt.Printf("  go run ./integration_test/integration_example.go   # 集成测试\n")
	}
}
