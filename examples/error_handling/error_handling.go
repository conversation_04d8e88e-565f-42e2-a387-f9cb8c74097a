package main

import (
	"context"
	"errors"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// 演示错误处理的最佳实践
func RunErrorHandlingExample() {
	fmt.Println("=== UniBackup 错误处理示例 ===")

	// 创建测试配置
	cfg := createTestConfig()

	// 演示不同类型的错误处理
	demonstrateConfigErrors()
	demonstrateConnectionErrors(cfg)
	demonstrateTaskErrors(cfg)
}

// 创建测试配置
func createTestConfig() *types.Config {
	return &types.Config{
		BackupRoot: "/tmp/unibackup_error_test",
		Logger:     zap.NewNop(),
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "test_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test_db",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},
	}
}

// 演示配置错误处理
func demonstrateConfigErrors() {
	fmt.Println("\n--- 配置错误处理 ---")

	// 错误1：空配置
	fmt.Println("1. 测试空配置:")
	_, err := unibackup.NewManager(nil)
	handleError("空配置", err)

	// 错误2：缺少备份根目录
	fmt.Println("2. 测试缺少备份根目录:")
	cfg := &types.Config{}
	_, err = unibackup.NewManager(cfg)
	handleError("缺少备份根目录", err)
}

// 演示连接错误处理
func demonstrateConnectionErrors(cfg *types.Config) {
	fmt.Println("\n--- 连接错误处理 ---")

	// 确保备份目录存在
	os.MkdirAll(cfg.BackupRoot, 0755)
	defer os.RemoveAll(cfg.BackupRoot)

	// 创建管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		handleError("创建管理器", err)
		return
	}
	defer manager.Shutdown()

	fmt.Println("管理器创建成功")
}

// 演示任务错误处理
func demonstrateTaskErrors(cfg *types.Config) {
	fmt.Println("\n--- 任务错误处理 ---")

	// 确保备份目录存在
	os.MkdirAll(cfg.BackupRoot, 0755)
	defer os.RemoveAll(cfg.BackupRoot)

	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		handleError("创建管理器", err)
		return
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 尝试备份
	fmt.Println("1. 尝试异步备份:")
	taskID, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeArchival, "测试备份")
	if err != nil {
		handleError("启动备份任务", err)
	} else {
		fmt.Printf("备份任务已启动，任务ID: %s\n", taskID)
		monitorTask(manager, taskID)
	}

	// 尝试获取不存在的任务
	fmt.Println("\n2. 尝试获取不存在的任务:")
	_, err = manager.GetTask("nonexistent-task-id")
	handleError("获取不存在的任务", err)
}

// 监控任务状态
func monitorTask(manager unibackup.BackupManager, taskID string) {
	fmt.Printf("监控任务 %s 的状态...\n", taskID)

	for i := 0; i < 5; i++ { // 检查5次
		task, err := manager.GetTask(taskID)
		if err != nil {
			handleError("获取任务状态", err)
			return
		}

		fmt.Printf("任务状态: %s, 进度: %.1f%%\n", task.Status, task.Progress)

		if task.Status.IsTerminal() {
			if task.Status == types.TaskStatusFailed {
				fmt.Printf("任务失败: %s\n", task.Error)
			} else {
				fmt.Printf("任务完成，状态: %s\n", task.Status)
			}
			return
		}

		time.Sleep(1 * time.Second)
	}

	fmt.Println("任务监控结束")
}

// 处理错误
func handleError(operation string, err error) {
	if err == nil {
		fmt.Printf("✅ %s: 成功\n", operation)
		return
	}

	fmt.Printf("❌ %s 失败: %v\n", operation, err)

	// 检查是否是结构化错误
	var backupErr *types.BackupError
	if errors.As(err, &backupErr) {
		fmt.Printf("   错误代码: %s\n", backupErr.Code)
		fmt.Printf("   组件: %s\n", backupErr.Component)
		fmt.Printf("   操作: %s\n", backupErr.Operation)
		fmt.Printf("   可重试: %v\n", backupErr.Retryable)

		// 根据错误类型给出建议
		switch backupErr.Code {
		case "DB_CONN_FAILED":
			fmt.Println("💡 建议: 检查数据库连接配置和网络连通性")
		case "PERMISSION_DENIED":
			fmt.Println("💡 建议: 检查用户权限和文件系统权限")
		case "CONFIG_VALIDATION_FAILED":
			fmt.Println("💡 建议: 检查配置参数的正确性")
		case "COMMAND_EXECUTION_FAILED":
			fmt.Println("💡 建议: 检查MySQL工具是否正确安装")
		default:
			if backupErr.Retryable {
				fmt.Println("💡 建议: 这是一个临时错误，可以稍后重试")
			} else {
				fmt.Println("💡 建议: 这是一个永久性错误，需要修复配置或环境")
			}
		}
	}
}

func main() {
	RunErrorHandlingExample()
}
