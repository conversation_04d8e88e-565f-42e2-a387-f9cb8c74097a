package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// RunModernUsageExample 演示使用推荐的新接口的完整示例
// 这个示例展示了如何使用统一的查询接口和现代化的API
func RunModernUsageExample() {
	// 设置日志记录器
	logger := zap.NewNop()

	// 配置备份根目录
	backupRoot := "/tmp/unibackup_modern"
	if err := os.MkdirAll(backupRoot, 0755); err != nil {
		logger.Error("创建备份目录失败", zap.Error(err))
		os.Exit(1)
	}

	// 创建现代化配置
	cfg := &types.Config{
		BackupRoot:         backupRoot,
		Logger:             logger,
		MaxConcurrentTasks: 3,
		TaskRetentionDays:  7,
		MaxTaskHistory:     100,

		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "backup_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test_db",
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "mysqldump",
				Mysql:       "mysql",
				Mysqlbinlog: "mysqlbinlog",
				Mysqladmin:  "mysqladmin",
			},
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			User:             "elastic",
			Password:         os.Getenv("UNIBACKUP_ES_PASSWORD"),
			ArchivalRepoName: "unibackup_archival",
			ManagedRepoName:  "unibackup_managed",
			AutoCreateRepos:  true,
		},
	}

	// 创建备份管理器
	manager, err := unibackup.NewManager(cfg)
	if err != nil {
		logger.Error("创建备份管理器失败", zap.Error(err))
		os.Exit(1)
	}
	defer manager.Shutdown()

	ctx := context.Background()

	// 示例1: 使用新接口进行备份管理
	fmt.Println("=== 示例1: 现代化备份管理 ===")
	modernBackupManagement(ctx, manager, logger)

	// 示例2: 使用统一查询接口
	fmt.Println("\n=== 示例2: 统一查询接口 ===")
	unifiedQueryExample(ctx, manager, logger)

	// 示例3: 高级过滤和搜索
	fmt.Println("\n=== 示例3: 高级过滤和搜索 ===")
	advancedFilteringExample(ctx, manager, logger)

	// 示例4: 任务详情查询
	fmt.Println("\n=== 示例4: 任务详情查询 ===")
	taskDetailsExample(ctx, manager, logger)

	// 示例5: 恢复操作
	fmt.Println("\n=== 示例5: 恢复操作 ===")
	restoreExample(ctx, manager, logger)

	// 示例6: 任务管理
	fmt.Println("\n=== 示例6: 任务管理 ===")
	taskManagementExample(ctx, manager, logger)

	// 示例7: 备份删除
	fmt.Println("\n=== 示例7: 备份删除 ===")
	backupDeletionExample(ctx, manager, logger)
}

// modernBackupManagement 演示现代化的备份管理方式
func modernBackupManagement(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 创建一些测试备份
	fmt.Println("📦 创建测试备份...")

	// MySQL归档备份
	mysqlTaskID, err := manager.BackupAsync(ctx, types.MySQL, "test_db", types.BackupTypeArchival, "现代化MySQL备份")
	if err != nil {
		logger.Error("MySQL备份失败", zap.Error(err))
		return
	}
	waitForTaskCompletion(manager, mysqlTaskID, logger)

	// Elasticsearch归档备份
	esTaskID, err := manager.BackupAsync(ctx, types.Elasticsearch, "my_cluster", types.BackupTypeArchival, "现代化ES备份")
	if err != nil {
		logger.Error("ES备份失败", zap.Error(err))
		return
	}
	waitForTaskCompletion(manager, esTaskID, logger)

	// 使用重构后的统一查询接口获取任务
	fmt.Println("🔍 使用重构后的统一查询接口获取任务列表...")
	filter := types.BackupFilter{
		SourceTypes: []types.SourceType{types.MySQL},
		Limit:       10,
	}

	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil {
		logger.Error("查询任务失败", zap.Error(err))
		return
	}

	fmt.Printf("📊 找到 %d 个MySQL任务:\n", len(result.Tasks))
	for _, task := range result.Tasks {
		fmt.Printf("  - %s [%s] %s - %s\n",
			task.ID[:8],
			task.Type,
			task.StartTime.Format("2006-01-02 15:04:05"),
			task.Description,
		)
	}
}

// unifiedQueryExample 演示重构后的统一查询接口使用
func unifiedQueryExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 查询MySQL归档备份
	fmt.Println("🔍 查询MySQL归档备份...")
	mysqlFilter := types.BackupFilter{
		SourceTypes: []types.SourceType{types.MySQL},
		BackupTypes: []types.BackupType{types.BackupTypeArchival},
		Limit:       5,
	}

	mysqlResult, err := manager.ListAllBackups(ctx, mysqlFilter)
	if err != nil {
		logger.Error("查询MySQL备份失败", zap.Error(err))
		return
	}
	fmt.Printf("📊 MySQL归档备份: %d 个\n", len(mysqlResult.Tasks))

	// 查询Elasticsearch所有备份
	fmt.Println("🔍 查询Elasticsearch备份...")
	esFilter := types.BackupFilter{
		SourceTypes: []types.SourceType{types.Elasticsearch},
		Limit:       5,
	}

	esResult, err := manager.ListAllBackups(ctx, esFilter)
	if err != nil {
		logger.Error("查询ES备份失败", zap.Error(err))
		return
	}
	fmt.Printf("📊 Elasticsearch备份: %d 个\n", len(esResult.Tasks))
}

// advancedFilteringExample 演示重构后的高级过滤功能
func advancedFilteringExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 演示多值过滤功能
	filter := types.BackupFilter{
		TaskTypes: []types.TaskType{
			types.BackupTask,
			types.BackupAllTask,
		},
		SourceTypes: []types.SourceType{types.MySQL},
		BackupTypes: []types.BackupType{types.BackupTypeArchival},
		Statuses:    []types.TaskStatus{types.TaskStatusCompleted},
		Limit:       20,
	}

	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil {
		logger.Error("高级过滤查询失败", zap.Error(err))
		return
	}

	fmt.Printf("📊 高级过滤结果: %d 个备份\n", len(result.Tasks))
	for _, backup := range result.Tasks {
		sourceType := backup.Source
		backupType := "unknown"
		if backup.Metadata != nil {
			if bt, ok := backup.Metadata["backup_type"]; ok {
				backupType = fmt.Sprintf("%v", bt)
			}
		}
		fmt.Printf("  %s [%s] %s - %s\n",
			sourceType,
			backupType,
			backup.StartTime.Format("01-02 15:04"),
			backup.Description,
		)
	}
}

// taskDetailsExample 演示获取任务详情
func taskDetailsExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	// 获取最新任务列表
	filter := types.BackupFilter{
		Limit: 1,
	}

	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil || len(result.Tasks) == 0 {
		logger.Info("暂无备份任务")
		return
	}

	// 获取任务详情
	latestBackup := result.Tasks[0]
	details, err := manager.GetTask(latestBackup.ID)
	if err != nil {
		logger.Error("获取任务详情失败", zap.Error(err))
		return
	}

	fmt.Printf("📋 任务详情:\n")
	fmt.Printf("  任务ID: %s\n", details.ID)
	fmt.Printf("  状态: %s\n", details.Status)
	fmt.Printf("  开始时间: %s\n", details.StartTime.Format("2006-01-02 15:04:05"))
	if !details.EndTime.IsZero() {
		fmt.Printf("  结束时间: %s\n", details.EndTime.Format("2006-01-02 15:04:05"))
	}
	fmt.Printf("  描述: %s\n", details.Description)
}

// restoreExample 演示恢复操作
func restoreExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	fmt.Println("\n=== 恢复操作示例 ===")

	// 查询可用的MySQL备份
	filter := types.BackupFilter{
		SourceTypes: []types.SourceType{types.MySQL},
		Statuses:    []types.TaskStatus{types.TaskStatusCompleted},
		Limit:       5,
	}

	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil {
		logger.Error("查询备份失败", zap.Error(err))
		return
	}

	if len(result.Tasks) == 0 {
		logger.Info("没有找到可用的备份")
		return
	}

	// 使用最新备份进行恢复
	latestBackup := result.Tasks[0]
	fmt.Printf("🔄 使用备份进行恢复: %s\n", latestBackup.Description)

	// 基于任务ID恢复（推荐方式）
	restoreTaskID, err := manager.RestoreByTaskID(ctx, latestBackup.ID, false)
	if err != nil {
		logger.Error("启动恢复任务失败", zap.Error(err))
		return
	}

	fmt.Printf("✅ 恢复任务已启动，任务ID: %s\n", restoreTaskID)

	// 监控恢复进度
	waitForTaskCompletion(manager, restoreTaskID, logger)
}

// taskManagementExample 演示任务管理操作
func taskManagementExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	fmt.Println("\n=== 任务管理示例 ===")

	// 列出所有任务
	tasks, err := manager.ListTasks()
	if err != nil {
		logger.Error("列出任务失败", zap.Error(err))
		return
	}

	fmt.Printf("📋 当前任务总数: %d\n", len(tasks))

	// 显示最近的几个任务
	for i, task := range tasks {
		if i >= 5 { // 只显示前5个
			break
		}
		fmt.Printf("  任务 %d: %s (%s) - %s\n",
			i+1, task.ID, task.Status, task.Description)
	}

	// 演示任务取消（如果有正在运行的任务）
	for _, task := range tasks {
		if task.Status == types.TaskStatusRunning {
			fmt.Printf("🛑 发现正在运行的任务，演示取消操作: %s\n", task.ID)

			err := manager.CancelTask(task.ID)
			if err != nil {
				logger.Error("取消任务失败", zap.Error(err))
			} else {
				fmt.Printf("✅ 任务 %s 已成功取消\n", task.ID)
			}
			break
		}
	}

	// 清理旧任务
	fmt.Println("🧹 清理旧任务...")
	err = manager.ClearOldTasks()
	if err != nil {
		logger.Error("清理旧任务失败", zap.Error(err))
	} else {
		fmt.Println("✅ 旧任务清理完成")
	}

	// 显示清理后的任务数量
	tasksAfterCleanup, err := manager.ListTasks()
	if err == nil {
		fmt.Printf("📊 清理后任务数量: %d\n", len(tasksAfterCleanup))
	}
}

// backupDeletionExample 演示备份删除操作
func backupDeletionExample(ctx context.Context, manager unibackup.BackupManager, logger *zap.Logger) {
	fmt.Println("\n=== 备份删除示例 ===")

	// 查询可删除的备份（查找较旧的备份）
	filter := types.BackupFilter{
		Limit: 10,
	}

	result, err := manager.ListAllBackups(ctx, filter)
	if err != nil {
		logger.Error("查询备份失败", zap.Error(err))
		return
	}

	if len(result.Tasks) < 2 {
		logger.Info("备份数量不足，跳过删除示例")
		return
	}

	// 选择一个较旧的备份进行删除演示
	targetBackup := result.Tasks[len(result.Tasks)-1] // 选择最后一个（最旧的）

	fmt.Printf("🗑️  准备删除备份: %s (%s)\n", targetBackup.ID, targetBackup.Description)

	// 获取删除影响信息
	deletionInfo, err := manager.GetBackupDeletionInfo(ctx, targetBackup.ID)
	if err != nil {
		logger.Error("获取删除影响信息失败", zap.Error(err))
		return
	}

	fmt.Printf("📊 删除影响分析:\n")
	fmt.Printf("  任务类型: %s\n", deletionInfo.TaskType)
	fmt.Printf("  数据源类型: %s\n", deletionInfo.SourceType)
	fmt.Printf("  备份类型: %s\n", deletionInfo.BackupType)
	fmt.Printf("  是否有备份数据: %v\n", deletionInfo.HasBackupData)

	if deletionInfo.IsGroupBackup {
		fmt.Printf("  📦 这是分组备份，包含 %d 个子任务\n", deletionInfo.SubTaskCount)
		for i, subBackup := range deletionInfo.SubBackups {
			fmt.Printf("    子备份 %d: %s (%s)\n", i+1, subBackup.BackupID, subBackup.BackupType)
		}
	}

	if deletionInfo.IsIncrementalChain {
		fmt.Printf("  ⚠️  这是增量备份，删除将影响整条增量链\n")
		fmt.Printf("  链中备份数量: %d\n", deletionInfo.ChainBackupCount)
	}

	// 在实际示例中，我们不执行删除操作，只演示流程
	fmt.Println("ℹ️  在演示中不执行实际删除操作")
}

// waitForTaskCompletion 等待任务完成的辅助函数
func waitForTaskCompletion(manager unibackup.BackupManager, taskID string, logger *zap.Logger) {
	for {
		task, err := manager.GetTask(taskID)
		if err != nil {
			logger.Error("获取任务状态失败", zap.Error(err))
			return
		}

		if task.Status.IsTerminal() {
			if task.Status == types.TaskStatusCompleted {
				logger.Info("任务完成", zap.String("task_id", taskID))
			} else {
				logger.Error("任务失败", zap.String("task_id", taskID), zap.String("error", task.Error))
			}
			return
		}

		fmt.Printf("⏳ 任务进行中: %s\n", task.Progress)
		time.Sleep(2 * time.Second)
	}
}
