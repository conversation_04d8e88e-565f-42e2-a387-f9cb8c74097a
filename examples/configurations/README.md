# UniBackup 配置示例

本目录包含 UniBackup 的各种配置示例，帮助您快速了解和配置不同场景下的备份系统。

## 📁 文件说明

### `basic_configs.go`
基础配置示例，包含：
- 仅 MySQL 配置
- 仅 Elasticsearch 配置  
- MySQL + ES 多数据源配置

**适用场景**：
- 开发环境
- 测试环境
- 简单的生产环境

### `cloud_storage.go`
云存储配置示例，包含：
- AWS S3 配置
- Google Cloud Storage 配置
- Azure Blob Storage 配置

**适用场景**：
- 需要云端备份存储
- 多地域备份需求
- 大规模数据备份

### `production.go`
生产环境配置示例，包含：
- 高可用生产环境配置
- 云存储生产环境配置
- 安全和性能优化配置

**适用场景**：
- 生产环境部署
- 高可用性要求
- 企业级备份方案

## 🚀 快速开始

### 1. 运行基础配置示例
```bash
cd examples/configurations
go run basic_configs.go
```

### 2. 运行云存储配置示例
```bash
# 设置云存储认证信息
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"

# 运行示例
go run cloud_storage.go
```

### 3. 运行生产环境配置示例
```bash
# 设置生产环境变量
export MYSQL_BACKUP_PASSWORD="your-mysql-password"
export ES_API_KEY="your-es-api-key"

# 运行示例
go run production.go
```

## 🔧 配置要点

### 基础配置
- `BackupRoot`: 备份文件存储根目录
- `Logger`: 日志记录器配置
- `MaxConcurrentTasks`: 最大并发任务数

### MySQL 配置
- `Host`, `Port`, `User`, `Password`, `DBName`: 数据库连接信息
- `ToolsPath`: MySQL 工具路径配置
- `BinlogBasePath`: Binlog 文件路径（Docker 环境）

### Elasticsearch 配置
- `Addresses`: ES 集群地址列表
- `User`, `Password` 或 `APIKey`: 认证信息
- `ArchivalRepoName`, `ManagedRepoName`: 备份仓库名称
- `AutoCreateRepos`: 是否自动创建仓库

### 云存储配置
- `Enabled`: 是否启用云存储
- `Type`: 云存储类型（s3, gcs, azure）
- `Bucket`/`Container`: 存储桶或容器名称
- `Region`: 云存储区域

## 🔒 安全建议

1. **密码管理**：
   - 使用环境变量存储敏感信息
   - 定期轮换密码和API密钥
   - 避免在配置文件中硬编码密码

2. **网络安全**：
   - 使用 HTTPS/TLS 连接
   - 配置防火墙规则
   - 限制备份服务的网络访问

3. **权限控制**：
   - 使用最小权限原则
   - 为备份服务创建专用用户
   - 定期审查权限配置

## 📊 监控和维护

1. **备份监控**：
   - 监控备份任务成功率
   - 设置备份失败告警
   - 跟踪备份文件大小和数量

2. **性能优化**：
   - 根据系统资源调整并发任务数
   - 优化备份时间窗口
   - 监控存储空间使用

3. **数据验证**：
   - 定期验证备份完整性
   - 测试恢复流程
   - 检查备份文件可读性

## 📚 相关文档

- [用户指南](../../docs/USER_GUIDE.md) - 完整的配置说明和使用指南
- [部署指南](../../docs/DEPLOYMENT_GUIDE.md) - 生产环境部署指南
- [开发指南](../../docs/DEVELOPER_GUIDE.md) - API 接口文档和开发指南

## ❓ 常见问题

**Q: 如何选择合适的配置？**
A: 根据您的环境选择：开发/测试用 basic_configs，云端存储用 cloud_storage，生产环境用 production。

**Q: 云存储配置失败怎么办？**
A: 检查认证信息、网络连接和权限配置，参考云存储指南进行排查。

**Q: 如何优化备份性能？**
A: 调整 MaxConcurrentTasks、优化网络带宽、使用 SSD 存储、合理设置备份时间窗口。
