package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"git.gobies.org/fobrain/unibackup/pkg/unibackup"
)

// 云存储配置示例
// 演示如何配置和使用云存储功能
func RunCloudStorageExample() {
	fmt.Println("=== UniBackup 云存储配置示例 ===")

	// 检查环境变量
	if !checkCloudCredentials() {
		fmt.Println("⚠️  未检测到云存储认证信息，将跳过实际操作")
		fmt.Println("💡 请设置相应的环境变量后重试")
		fmt.Println("")
		fmt.Println("AWS S3 环境变量:")
		fmt.Println("  export AWS_ACCESS_KEY_ID=\"your-access-key\"")
		fmt.Println("  export AWS_SECRET_ACCESS_KEY=\"your-secret-key\"")
		fmt.Println("")
		fmt.Println("Google Cloud Storage 环境变量:")
		fmt.Println("  export GOOGLE_APPLICATION_CREDENTIALS=\"/path/to/service-account.json\"")
		fmt.Println("")
		fmt.Println("Azure Blob Storage 环境变量:")
		fmt.Println("  export AZURE_STORAGE_ACCOUNT=\"your-storage-account\"")
		fmt.Println("  export AZURE_STORAGE_KEY=\"your-storage-key\"")
		return
	}

	// 示例1：AWS S3 配置
	fmt.Println("\n--- 示例1：AWS S3 配置 ---")
	s3Example()

	// 示例2：Google Cloud Storage 配置
	fmt.Println("\n--- 示例2：Google Cloud Storage 配置 ---")
	gcsExample()

	// 示例3：Azure Blob Storage 配置
	fmt.Println("\n--- 示例3：Azure Blob Storage 配置 ---")
	azureExample()

	fmt.Println("\n✅ 云存储配置示例演示完成")
}

// AWS S3 配置示例
func s3Example() {
	cfg := &types.Config{
		BackupRoot: "/tmp/unibackup-s3",
		Logger:     createLogger(),

		// AWS S3 云存储配置
		CloudStorage: &types.CloudStorageConfig{
			Enabled: true,
			Type:    "s3",
			Bucket:  "my-backup-bucket",
			Region:  "us-east-1",
			// 认证信息通过环境变量提供：
			// AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
		},

		// ES 配置（云存储主要用于 ES 备份）
		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "s3-archival",
			ManagedRepoName:  "s3-managed",
			AutoCreateRepos:  true,
		},

		// MySQL 也会使用云存储
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "backup_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test_db",
		},
	}

	printConfigAndDemo("AWS S3", cfg)
}

// Google Cloud Storage 配置示例
func gcsExample() {
	cfg := &types.Config{
		BackupRoot: "/tmp/unibackup-gcs",
		Logger:     createLogger(),

		// Google Cloud Storage 配置
		CloudStorage: &types.CloudStorageConfig{
			Enabled:   true,
			Type:      "gcs",
			Bucket:    "my-gcs-backup-bucket",
			ProjectID: "my-gcp-project",
			// 认证信息通过环境变量提供：
			// GOOGLE_APPLICATION_CREDENTIALS
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "gcs-archival",
			ManagedRepoName:  "gcs-managed",
			AutoCreateRepos:  true,
		},

		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "backup_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test_db",
		},
	}

	printConfigAndDemo("Google Cloud Storage", cfg)
}

// Azure Blob Storage 配置示例
func azureExample() {
	cfg := &types.Config{
		BackupRoot: "/tmp/unibackup-azure",
		Logger:     createLogger(),

		// Azure Blob Storage 配置
		CloudStorage: &types.CloudStorageConfig{
			Enabled:     true,
			Type:        "azure",
			Container:   "my-azure-container",
			AccountName: "mystorageaccount",
			// 认证信息通过环境变量提供：
			// AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			ArchivalRepoName: "azure-archival",
			ManagedRepoName:  "azure-managed",
			AutoCreateRepos:  true,
		},

		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "backup_user",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "test_db",
		},
	}

	printConfigAndDemo("Azure Blob Storage", cfg)
}

// 创建日志记录器
func createLogger() *zap.Logger {
	return zap.NewNop()
}

// 打印配置并演示
func printConfigAndDemo(title string, cfg *types.Config) {
	fmt.Printf("\n🔧 %s 配置:\n", title)
	fmt.Printf("  📂 备份根目录: %s\n", cfg.BackupRoot)
	fmt.Printf("  ☁️  云存储类型: %s\n", cfg.CloudStorage.Type)

	if cfg.CloudStorage.Bucket != "" {
		fmt.Printf("  🪣 存储桶: %s\n", cfg.CloudStorage.Bucket)
	}
	if cfg.CloudStorage.Container != "" {
		fmt.Printf("  📦 容器: %s\n", cfg.CloudStorage.Container)
	}

	fmt.Printf("  🗄️  MySQL: %s:%d/%s\n", cfg.MySQL.Host, cfg.MySQL.Port, cfg.MySQL.DBName)
	fmt.Printf("  🔍 ES: %v\n", cfg.ES.Addresses)

	// 验证配置
	fmt.Printf("  ✅ 配置验证成功\n")

	// 如果有 ES 配置，可以尝试列出备份
	if cfg.ES != nil {
		fmt.Printf("  📋 尝试列出 ES 备份...\n")
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		manager, err := unibackup.NewManager(cfg)
		if err != nil {
			fmt.Printf("  ⚠️  创建管理器失败: %s\n", err.Error())
			return
		}
		defer manager.Shutdown()

		// 使用现代化接口查询ES备份
		filter := types.BackupFilter{
			SourceTypes: []types.SourceType{types.Elasticsearch},
			Limit:       10,
		}
		result, backupErr := manager.ListAllBackups(ctx, filter)
		if backupErr != nil {
			fmt.Printf("  ⚠️  列出备份失败: %s\n", backupErr.Error())
			return
		}
		fmt.Printf("  📊 找到 %d 个 ES 备份\n", len(result.Tasks))
	}
}

// 检查云存储认证信息
func checkCloudCredentials() bool {
	// 检查 AWS 认证
	if os.Getenv("AWS_ACCESS_KEY_ID") != "" && os.Getenv("AWS_SECRET_ACCESS_KEY") != "" {
		return true
	}

	// 检查 GCS 认证
	if os.Getenv("GOOGLE_APPLICATION_CREDENTIALS") != "" {
		return true
	}

	// 检查 Azure 认证
	if os.Getenv("AZURE_STORAGE_ACCOUNT") != "" && os.Getenv("AZURE_STORAGE_KEY") != "" {
		return true
	}

	return false
}

// main 函数在 basic_configs.go 中定义
