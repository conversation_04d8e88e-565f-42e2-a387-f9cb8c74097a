package main

import (
	"fmt"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// 基础配置示例
// 演示不同场景下的基础配置模式
func main() {
	fmt.Println("=== UniBackup 配置示例 ===")
	fmt.Println("请选择要运行的配置示例：")
	fmt.Println("1. 基础配置示例")
	fmt.Println("2. 生产环境配置示例")
	fmt.Println("3. 云存储配置示例")
	fmt.Println("0. 退出")

	fmt.Print("\n请输入选项编号: ")
	var choice string
	fmt.Scanln(&choice)

	switch choice {
	case "1":
		runBasicConfigExamples()
	case "2":
		RunProductionConfig()
	case "3":
		RunCloudStorageExample()
	case "0":
		fmt.Println("退出程序")
		return
	default:
		fmt.Println("无效选项，运行基础配置示例")
		runBasicConfigExamples()
	}
}

func runBasicConfigExamples() {
	fmt.Println("\n=== UniBackup 基础配置示例 ===")

	// 示例1：仅MySQL配置
	mysqlOnlyConfig()

	// 示例2：仅Elasticsearch配置
	esOnlyConfig()

	// 示例3：MySQL + ES 多数据源配置
	mixedConfig()

	fmt.Println("\n✅ 基础配置示例演示完成")
}

// 示例1：仅MySQL配置
func mysqlOnlyConfig() {
	fmt.Println("\n--- 示例1：仅MySQL配置 ---")

	cfg := &types.Config{
		BackupRoot: "/data/backups",
		Logger:     zap.NewNop(),

		// 仅配置MySQL
		MySQL: &types.MySQLConfig{
			Host:     "mysql.example.com",
			Port:     3306,
			User:     "backup_user",
			Password: "secure_password",
			DBName:   "production_db",
			// 使用默认工具路径
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},
	}

	printConfig("仅MySQL配置", cfg)
}

// 示例2：仅Elasticsearch配置
func esOnlyConfig() {
	fmt.Println("\n--- 示例2：仅Elasticsearch配置 ---")

	cfg := &types.Config{
		BackupRoot: "/data/backups",
		Logger:     zap.NewNop(),

		// 仅配置Elasticsearch
		ES: &types.ESConfig{
			Addresses:        []string{"http://es.example.com:9200"},
			User:             "elastic",
			Password:         "es_password",
			ArchivalRepoName: "backup-archival",
			ManagedRepoName:  "backup-managed",
			AutoCreateRepos:  true,
		},
	}

	printConfig("仅Elasticsearch配置", cfg)
}

// 示例3：MySQL + ES 多数据源配置
func mixedConfig() {
	fmt.Println("\n--- 示例3：MySQL + ES 多数据源配置 ---")

	cfg := &types.Config{
		BackupRoot:         "/data/backups",
		Logger:             zap.NewNop(),
		MaxConcurrentTasks: 5,
		TaskRetentionDays:  14,
		MaxTaskHistory:     2000,

		// 同时配置MySQL和ES
		MySQL: &types.MySQLConfig{
			Host:     "localhost",
			Port:     3306,
			User:     "backup_user",
			Password: "mysql_password",
			DBName:   "app_db",
		},

		ES: &types.ESConfig{
			Addresses:        []string{"http://localhost:9200"},
			APIKey:           "base64_encoded_api_key",
			ArchivalRepoName: "app-archival",
			ManagedRepoName:  "app-managed",
			AutoCreateRepos:  true,
		},
	}

	printConfig("多数据源配置", cfg)
}

// 打印配置信息
func printConfig(title string, cfg *types.Config) {
	fmt.Printf("\n🔧 %s:\n", title)
	fmt.Printf("  📂 备份根目录: %s\n", cfg.BackupRoot)

	if cfg.MySQL != nil {
		fmt.Printf("  🗄️  MySQL: %s:%d/%s\n", cfg.MySQL.Host, cfg.MySQL.Port, cfg.MySQL.DBName)
	}

	if cfg.ES != nil {
		fmt.Printf("  🔍 Elasticsearch: %v\n", cfg.ES.Addresses)
	}

	if cfg.CloudStorage != nil && cfg.CloudStorage.Enabled {
		fmt.Printf("  ☁️  云存储: %s (%s)\n", cfg.CloudStorage.Type, cfg.CloudStorage.Bucket)
	}

	fmt.Printf("  ⚙️  最大并发任务: %d\n", getMaxConcurrentTasks(cfg))
	fmt.Printf("  📊 任务保留天数: %d\n", getTaskRetentionDays(cfg))
}

// 获取最大并发任务数（带默认值）
func getMaxConcurrentTasks(cfg *types.Config) int {
	if cfg.MaxConcurrentTasks > 0 {
		return cfg.MaxConcurrentTasks
	}
	return 5 // 默认值
}

// 获取任务保留天数（带默认值）
func getTaskRetentionDays(cfg *types.Config) int {
	if cfg.TaskRetentionDays > 0 {
		return cfg.TaskRetentionDays
	}
	return 365 // 默认值
}
