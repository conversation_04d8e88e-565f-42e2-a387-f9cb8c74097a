package main

import (
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// 生产环境配置示例
// 演示生产环境的完整配置，包括安全、性能和监控考虑
func RunProductionConfig() {
	fmt.Println("=== UniBackup 生产环境配置示例 ===")

	// 示例1：高可用生产环境配置
	productionConfig()

	// 示例2：云存储生产环境配置
	cloudProductionConfig()

	fmt.Println("\n✅ 生产环境配置示例演示完成")
}

// 示例1：高可用生产环境配置
func productionConfig() {
	fmt.Println("\n--- 示例1：高可用生产环境配置 ---")

	cfg := &types.Config{
		BackupRoot: "/var/lib/unibackup",
		Logger:     createProductionLogger(),

		// 生产环境任务管理配置
		MaxConcurrentTasks: 10,                            // 根据服务器性能调整
		TaskRetentionDays:  90,                            // 保留3个月的任务历史
		MaxTaskHistory:     5000,                          // 限制内存中的任务数量
		BackupTimeout:      types.Duration(6 * time.Hour), // 大型数据库备份超时时间
		RestoreTimeout:     types.Duration(4 * time.Hour), // 恢复操作超时时间

		// 生产MySQL配置
		MySQL: &types.MySQLConfig{
			Host:     "mysql-primary.internal",
			Port:     3306,
			User:     "backup_service",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"), // 从环境变量读取
			DBName:   "production_db",

			// 生产环境工具路径
			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/local/mysql/bin/mysqldump",
				Mysql:       "/usr/local/mysql/bin/mysql",
				Mysqlbinlog: "/usr/local/mysql/bin/mysqlbinlog",
				Mysqladmin:  "/usr/local/mysql/bin/mysqladmin",
			},

			// Docker环境下的binlog路径
			BinlogBasePath: "/var/lib/mysql",
		},

		// 生产ES配置
		ES: &types.ESConfig{
			Addresses: []string{
				"https://es-node1.internal:9200",
				"https://es-node2.internal:9200",
				"https://es-node3.internal:9200",
			},
			APIKey:           os.Getenv("UNIBACKUP_ES_API_KEY"), // 从环境变量读取
			ArchivalRepoName: "prod-archival-backups",
			ManagedRepoName:  "prod-managed-backups",
			AutoCreateRepos:  true,
			RepoBasePath:     "/var/lib/elasticsearch/snapshots",
		},
	}

	printProductionConfig("高可用生产环境配置", cfg)
}

// 示例2：云存储生产环境配置
func cloudProductionConfig() {
	fmt.Println("\n--- 示例2：云存储生产环境配置 ---")

	cfg := &types.Config{
		BackupRoot: "/var/lib/unibackup",
		Logger:     createProductionLogger(),

		// 生产环境任务管理配置
		MaxConcurrentTasks: 8,
		TaskRetentionDays:  180, // 保留6个月的任务历史
		MaxTaskHistory:     10000,
		BackupTimeout:      types.Duration(8 * time.Hour), // 云存储可能需要更长时间
		RestoreTimeout:     types.Duration(6 * time.Hour),

		// 云存储配置
		CloudStorage: &types.CloudStorageConfig{
			Enabled: true,
			Type:    "s3",
			Bucket:  "company-backup-production",
			Region:  "us-east-1",
			// 生产环境认证通过 IAM 角色，不需要显式配置密钥
		},

		// 生产MySQL配置
		MySQL: &types.MySQLConfig{
			Host:     "mysql-cluster.internal",
			Port:     3306,
			User:     "backup_service",
			Password: os.Getenv("UNIBACKUP_MYSQL_PASSWORD"),
			DBName:   "production_db",

			ToolsPath: types.MySQLToolsPath{
				Mysqldump:   "/usr/bin/mysqldump",
				Mysql:       "/usr/bin/mysql",
				Mysqlbinlog: "/usr/bin/mysqlbinlog",
				Mysqladmin:  "/usr/bin/mysqladmin",
			},
		},

		// 生产ES配置（云存储）
		ES: &types.ESConfig{
			Addresses: []string{
				"https://es-cluster.internal:9200",
			},
			APIKey:           os.Getenv("UNIBACKUP_ES_API_KEY"),
			ArchivalRepoName: "cloud-prod-archival",
			ManagedRepoName:  "cloud-prod-managed",
			AutoCreateRepos:  true,
		},
	}

	printProductionConfig("云存储生产环境配置", cfg)
}

// 创建生产环境日志记录器
func createProductionLogger() *zap.Logger {
	return zap.NewNop()
}

// 打印生产环境配置信息
func printProductionConfig(title string, cfg *types.Config) {
	fmt.Printf("\n🔧 %s:\n", title)
	fmt.Printf("  📂 备份根目录: %s\n", cfg.BackupRoot)
	fmt.Printf("  ⚙️  最大并发任务: %d\n", cfg.MaxConcurrentTasks)
	fmt.Printf("  📊 任务保留天数: %d\n", cfg.TaskRetentionDays)
	fmt.Printf("  📈 最大任务历史: %d\n", cfg.MaxTaskHistory)

	if cfg.BackupTimeout != 0 {
		fmt.Printf("  ⏱️  备份超时: %s\n", cfg.BackupTimeout)
	}
	if cfg.RestoreTimeout != 0 {
		fmt.Printf("  ⏱️  恢复超时: %s\n", cfg.RestoreTimeout)
	}

	if cfg.MySQL != nil {
		fmt.Printf("  🗄️  MySQL: %s:%d/%s\n", cfg.MySQL.Host, cfg.MySQL.Port, cfg.MySQL.DBName)
		fmt.Printf("    👤 用户: %s\n", cfg.MySQL.User)
		if cfg.MySQL.BinlogBasePath != "" {
			fmt.Printf("    📝 Binlog路径: %s\n", cfg.MySQL.BinlogBasePath)
		}
	}

	if cfg.ES != nil {
		fmt.Printf("  🔍 Elasticsearch: %v\n", cfg.ES.Addresses)
		fmt.Printf("    📦 归档仓库: %s\n", cfg.ES.ArchivalRepoName)
		fmt.Printf("    🔗 管理仓库: %s\n", cfg.ES.ManagedRepoName)
		if cfg.ES.RepoBasePath != "" {
			fmt.Printf("    📁 仓库路径: %s\n", cfg.ES.RepoBasePath)
		}
	}

	if cfg.CloudStorage != nil && cfg.CloudStorage.Enabled {
		fmt.Printf("  ☁️  云存储: %s\n", cfg.CloudStorage.Type)
		if cfg.CloudStorage.Bucket != "" {
			fmt.Printf("    🪣 存储桶: %s\n", cfg.CloudStorage.Bucket)
		}
		if cfg.CloudStorage.Region != "" {
			fmt.Printf("    🌍 区域: %s\n", cfg.CloudStorage.Region)
		}
	}

	// 生产环境安全提醒
	fmt.Printf("\n  🔒 安全提醒:\n")
	fmt.Printf("    • 密码通过环境变量提供，不在配置中硬编码\n")
	fmt.Printf("    • 建议使用 IAM 角色或服务账户进行云存储认证\n")
	fmt.Printf("    • 定期轮换备份用户密码和API密钥\n")
	fmt.Printf("    • 启用备份数据加密和传输加密\n")

	// 生产环境监控建议
	fmt.Printf("\n  📊 监控建议:\n")
	fmt.Printf("    • 监控备份任务成功率和执行时间\n")
	fmt.Printf("    • 设置备份失败告警\n")
	fmt.Printf("    • 定期验证备份数据完整性\n")
	fmt.Printf("    • 监控存储空间使用情况\n")
}

// main 函数在 basic_configs.go 中定义
